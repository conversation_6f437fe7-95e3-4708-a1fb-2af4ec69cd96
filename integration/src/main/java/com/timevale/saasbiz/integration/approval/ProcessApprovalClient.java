package com.timevale.saasbiz.integration.approval;

import com.timevale.account.flow.service.model.approval.model.ApprovalGroupModel;
import com.timevale.contractapproval.facade.dto.*;
import com.timevale.contractapproval.facade.input.*;
import com.timevale.contractapproval.facade.output.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-03-30 14:45
 */
public interface ProcessApprovalClient {

    /**
     * 分页获取审批列表数据
     *
     * @param input input
     * @return 一页列表数据
     */
    PageApprovalFlowOutput listApproval(ApprovalPageInput input);

    /** 批量获取审批信息 */
    List<ApprovalDTO> batchGetApproval(ApprovalBatchGetInfoInput input);

    /** 批量获取审批基本信息, Map<approvalId, ApprovalDTO> */
    Map<String, ApprovalDTO> mapApproval(List<String> approvalIds);

    /** 用户在审批流程中的任务类型 */
    Map<String, List<String>> mapUserApprovalTaskType(
            String subjectGid, String operatorGid, List<String> approvalCodes);

    /** 批量获取合同审批组详细信息 */
    List<ApprovalGroupDTO> batchGetProcessApprovalGroup(List<String> bizGroupIds);

    ApprovalGroupDTO getGroupByBizGroupId(String bizGroupIds);

    /** 查询审批组信息 -> Map<groupId, ApprovalGroupDTO> */
    Map<String, ApprovalGroupDTO> mapGetApprovalGroup(List<String> bizGroupIds);

    /** 获取一页审批组信息 */
    PageApprovalGroupOutput listApprovalGroup(ApprovalPageInput input);

    /** 按照主体统计审批数量 */
    List<ApprovalAggregateDTO> countApprovalBySubject(String accountGid, String approvalType);

    /** 根据审批组id集合查询老审批组信息 */
    List<ApprovalGroupModel> listOldApprovalGroup(List<String> bizGroupIds);

    /** 根据审批组id集合查询老审批组信息 -> Map<uuid, ApprovalGroupModel>, 老数据的uuid就是迁移后的groupId */
    Map<String, ApprovalGroupModel> mapGetOldApprovalGroup(List<String> bizGroupIds);

    /** 查询审批 */
    ApprovalDTO getApprovalByCode(String approvalCode);

    ApprovalDetailDTO getApprovalDetailByCode(String approvalCode);

    ApprovalDetailWithLogDTO getApprovalDetailWithLog(String approvalCode);

    void sendApprovalTaskReadMsg(String approvalCode,String operatorOid,String operatorGid);

    /** 虚拟发起详细信息 */
    VirtualStartApprovalOutput virtualStartApprovalDetail(VirtualStartApprovalInput input);

    /** 撤回 */
    void approvalRevoke(ApprovalRevokeInput input);

    /**
     * 重新发起
     *
     * @return 新的审批
     */
    String approvalRestart(ApprovalRestartInput input);

    /** 同意 */
    void approvalAgree(ApprovalAgreeInput input);

    /** 拒绝 */
    void approvalRefuse(ApprovalRefuseInput input);

    /** 删除 */
    void approvalHidden(ApprovalHiddenInput input);

    /** 用于适配新老合同审批逻辑 */
    ApprovalDTO getByAdaptApprovalId(Long adaptApprovalId);

    /** 根据业务信息查询 */
    ApprovalDetailDTO getApprovalDetailByBizInfo(String approvalType, String bizId);

    ApprovalDetailWithLogDTO getApprovalDetailWithLogByBizInfo(String approvalType, String bizId);

    /** 获取审批链接 */
    ApprovalUrlOutput approvalUrl(ApprovalUrlInput input);

    /** 获取审批列表链接 */
    ApprovalUrlOutput approvalListUrl(ApprovalListUrlInput input);

    /** 催办 */
    void approvalUrge(ApprovalUrgeInput input);

    /**
     * 查询审批流程业务数据
     *
     * @param input
     * @return
     */
    String getApprovalBizData(GetApprovalBizDataInput input);

    /**
     * 查询同一批次待我操作的审批列表
     *
     * @param input
     * @return
     */
    List<ApprovalFlowDTO> queryApprovalInBatch(QueryApprovalInBatchInput input);

    /**
     * 获取审批意愿地址
     *
     * @param input
     * @return
     */
    QueryApprovalWillAuthUrlOutput queryApprovalWillAuthUrl(ApprovalWillAuthUrlInput input);

    /**
     * 查询用户在审批流程中的权限
     *
     * @param input
     * @return
     */
    QueryApprovalPermissionOutput queryApprovalPermission(QueryApprovalPermissionInput input);


    /**
     * 增加流程审批节点
     * @param input
     * @return
     */
    ApprovalAddOutput addApproval(ApprovalAddInput input);

    /**
     * 减少流程审批节点
     * @param input
     * @return
     */
    ApprovalReduceOutput reduceApproval(ApprovalReduceInput input);


    /**
     * 添加评论
     * @param input
     * @return
     */
    ApprovalAddCommentOutput addComment(ApprovalAddCommentInput input);

    /**
     * 根据业务信息查询是否审批人
     * @param input
     * @return
     */
    boolean isApproverByBizInfo(CheckAccountIsApproverByBizIdInput input);
}
