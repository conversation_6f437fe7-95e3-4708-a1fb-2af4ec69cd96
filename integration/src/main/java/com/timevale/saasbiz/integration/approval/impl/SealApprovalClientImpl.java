package com.timevale.saasbiz.integration.approval.impl;

import com.timevale.account.flow.service.api.RpcApprovalFlowService;
import com.timevale.account.flow.service.api.RpcSealApprovalV2Service;
import com.timevale.account.flow.service.model.approval.input.BizApprovalFlowQueryInput;
import com.timevale.account.flow.service.model.approval.input.BizApprovalFlowRemindInput;
import com.timevale.account.flow.service.model.approval.input.task.QueryApprovalTypeInput;
import com.timevale.account.flow.service.model.approval.input.task.QueryTaskLogInput;
import com.timevale.account.flow.service.model.approval.model.ApprovalTypeModel;
import com.timevale.account.flow.service.model.approval.output.BizApprovalFlowBaseInfoOutput;
import com.timevale.account.flow.service.model.approval.output.task.QueryApprovalTypeOutput;
import com.timevale.account.flow.service.model.approval.output.task.QueryTaskLogOutput;
import com.timevale.flow.facade.service.api.RpcApprovalFlowV2ForSealService;
import com.timevale.flow.facade.service.model.input.seal.*;
import com.timevale.flow.facade.service.model.output.seal.SealApprovalUrlOutput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.integration.approval.SealApprovalClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-03-31 13:47
 */
@Component
public class SealApprovalClientImpl implements SealApprovalClient {
    @Autowired private RpcSealApprovalV2Service rpcSealApprovalV2Service;
    @Autowired private RpcApprovalFlowV2ForSealService rpcApprovalFlowV2ForSealService;
    @Autowired private RpcApprovalFlowService rpcApprovalFlowService;

    @Override
    public Map<String, Set<String>> queryApprovalType(String accountGid, Set<String> approvalIds) {
        QueryApprovalTypeInput input = new QueryApprovalTypeInput();
        input.setAccountGid(accountGid);
        input.setApprovalIds(approvalIds);

        QueryApprovalTypeOutput output = rpcSealApprovalV2Service.queryApprovalType(input);
        if (Objects.isNull(output) || CollectionUtils.isEmpty(output.getApprovalTypeList())) {
            return Collections.emptyMap();
        }
        return output.getApprovalTypeList().stream()
                .collect(
                        Collectors.toMap(
                                ApprovalTypeModel::getApprovalId,
                                ApprovalTypeModel::getType,
                                (a, b) -> b));
    }


    @Override
    public void approvalAgree(SealApprovalAgreeV2Input input) {
        rpcApprovalFlowV2ForSealService.approvalAgree(input);
    }

    @Override
    public void approvalRefuse(SealApprovalRefuseV2Input input) {
        rpcApprovalFlowV2ForSealService.approvalRefuse(input);
    }

    @Override
    public void approvalRevoke(SealApprovalRevokeV2Input input) {
        rpcApprovalFlowV2ForSealService.approvalRevoke(input);
    }

    @Override
    public BizApprovalFlowBaseInfoOutput queryApprovalFlowBaseInfo(String approvalId) {
        BizApprovalFlowQueryInput input = new BizApprovalFlowQueryInput();
        input.setApprovalFlowId(approvalId);
        return rpcApprovalFlowService.queryApprovalFlowBaseInfo(input);
    }

    @Override
    public SealApprovalUrlOutput queryApprovalUrl(SealApprovalUrlV2Input input) {
        return rpcApprovalFlowV2ForSealService.queryApprovalUrl(input);
    }

    @Override
    public QueryTaskLogOutput queryApprovalLog(String approvalId) {
        QueryTaskLogInput input = new QueryTaskLogInput();
        input.setApprovalId(approvalId);
        return rpcSealApprovalV2Service.queryApprovalLog(input);
    }

    @Override
    public void urge(BizApprovalFlowRemindInput input) {
        rpcApprovalFlowService.remindApprovalFlow(input);
    }

    @Override
    public String queryApprovalWillAuthId(String serialId) {
        SealApprovalWillAuthIdInput input = new SealApprovalWillAuthIdInput();
        input.setSerialId(serialId);
        return rpcApprovalFlowV2ForSealService.queryApprovalWillAuthId(input).getWillAuthId();
    }
}
