package com.timevale.saasbiz.integration.contractanalysis.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.timevale.contractanalysis.facade.api.compare.IntelligentCompareFacadeService;
import com.timevale.contractanalysis.facade.api.dto.compare.IntelligentCompareInputDTO;
import com.timevale.contractanalysis.facade.api.dto.compare.IntelligentCompareOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.compare.IntelligentCompareQueryStatusInputDTO;
import com.timevale.contractanalysis.facade.api.dto.compare.IntelligentCompareQueryStatusOutputDTO;
import com.timevale.saasbiz.integration.contractanalysis.IntelligentCompareClient;

/**
 * 
 * <AUTHOR>
 * @date 2024-04-03
 */
@Service
public class IntelligentCompareClientImpl implements IntelligentCompareClient {
    
    @Autowired
    private IntelligentCompareFacadeService intelligentCompareFacadeService;

    @Override
    public String createCompare(IntelligentCompareInputDTO inputDTO) {
        IntelligentCompareOutputDTO output = intelligentCompareFacadeService.asyncCompare(inputDTO);
        return output.getCompareId();
    }

    @Override
    public IntelligentCompareQueryStatusOutputDTO queryCompareStatus(String compareId, String operatorGid, String tenantGid) {
        IntelligentCompareQueryStatusInputDTO inputDTO = new IntelligentCompareQueryStatusInputDTO();
        inputDTO.setCompareId(compareId);
        inputDTO.setOperatorGid(operatorGid);
        inputDTO.setTenantGid(tenantGid);
        return intelligentCompareFacadeService.queryCompareStatus(inputDTO);
    }
}
