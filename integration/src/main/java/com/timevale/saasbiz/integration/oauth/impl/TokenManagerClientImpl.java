package com.timevale.saasbiz.integration.oauth.impl;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.saasbiz.integration.oauth.TokenManagerClient;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.token.service.api.RpcUserAgreementService;
import com.timevale.token.service.model.input.LatestUserAgreementQueryInput;
import com.timevale.token.service.model.input.UserAgreementSaveInput;
import com.timevale.token.service.model.output.LatestUserAgreementQueryOutput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.timevale.saasbiz.model.exception.SaasBizResultCode.SAAS_BIZ_ERROR;

/**
 * token-manager服务底层接口
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
@Component
public class TokenManagerClientImpl implements TokenManagerClient {

    @Autowired RpcUserAgreementService rpcUserAgreementService;

    @Override
    public void saveUserAgreement(UserAgreementSaveInput input) {
        rpcUserAgreementService.saveUserAgreement(new RpcInput<>(input));
    }

    @Override
    public LatestUserAgreementQueryOutput queryLatestUserAgreement(
            LatestUserAgreementQueryInput input) {
        RpcOutput<LatestUserAgreementQueryOutput> rpcOutput =
                rpcUserAgreementService.queryLatestUserAgreement(new RpcInput<>(input));
        if (!rpcOutput.isSuccess()) {
            throw new SaasBizException(SAAS_BIZ_ERROR.getCode(), "查询用户同意协议失败");
        }
        return rpcOutput.getData();
    }
}
