package com.timevale.saasbiz.integration.process;

import com.timevale.contractmanager.common.service.bean.permission.GetAllMenuPermissionsModel;
import com.timevale.contractmanager.common.service.model.ProcessStartFlowTemplateModel;
import com.timevale.contractmanager.common.service.model.ProcessUrlModel;
import com.timevale.contractmanager.common.service.model.QueryProcessFileAuthModel;
import com.timevale.contractmanager.common.service.model.QueryProcessSecretConfigModel;
import com.timevale.contractmanager.common.service.model.StartBizProcessModel;
import com.timevale.contractmanager.common.service.model.processpermission.CheckUserProcessViewableModel;
import com.timevale.contractmanager.common.service.model.processpermission.GetProcessAccountRoleModel;
import com.timevale.contractmanager.common.service.result.*;
import com.timevale.contractmanager.common.service.result.processpermission.CheckUserProcessViewableResult;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-04-04 11:38
 */
public interface ProcessClient {

    /** 发起流程 */
    StartProcessResult startProcess(StartBizProcessModel model);

    /** 校验用户是否可查看指定流程 */
    CheckUserProcessViewableResult checkUserProcessViewable(CheckUserProcessViewableModel model);

    /** 校验用户是否可查看指定流程 */
    CheckUserProcessViewableResult checkUserProcessViewable(
            String processId, String subjectId, String accountId, String menuId);

    /** 获取流程详情 */
    QueryProcessDetailResult queryProcessDetail(String processId);

    /** 获取流程基本信息 */
    ProcessBaseResult queryProcessBase(String processId);

    /** 检验是否为合同相关人 */
    boolean checkProcessRelevant(
            String processId, String accountId, String subjectId, boolean involveCC);

    /** 判断这个主流程是否有指定类型的子流程 */
    boolean hasSubProcess(String processId, int subProcessType);

    /** 检验是否为合同相关人 */
    boolean checkProcessRelevant(String processId, String accountId, String subjectId);

    /** 校验企业是否为合同相关方 */
    boolean checkProcessSubjectRelevant(String processId, String subjectId);

    /** 校验企业是否为合同发起方 */
    boolean checkInitiatorSubject(String processId, String subjectId);

    /** 获取用户在企业下菜单的权限 */
    Set<String> getMenuOperationPermissions(GetAllMenuPermissionsModel model);

    /** 获取流程配置信息 */
    ProcessConfigResult queryProcessConfig(String processId);

    /** 批量获取流程配置信息列表 */
    List<ProcessConfigResult> batchQueryProcessConfig(List<String> processIds);

    /** 从hbase中查询合同详情 */
    ContractProcessDTO getByProcessId(String processId);

    List<ContractProcessDTO> listByProcessIds(List<String> processIds);

    /**
     * 获取用户在企业下指定菜单的所有权限
     *
     * @param model
     * @return
     */
    Set<String> getAllMenuPermissions(GetAllMenuPermissionsModel model);

    /** 获取合同用户角色列表 */
    public List<String> getProcessAccountRoles(GetProcessAccountRoleModel model);

    /**
     * 获取保密配置
     *
     * @param model
     * @return
     */
    QueryProcessSecretConfigResult queryProcessSecretConfig(QueryProcessSecretConfigModel model);

    /**
     * 查询流程文件列表
     *
     * @param processId
     * @return
     */
    List<QueryProcessFilesResult.ProcessFile> getProcessFiles(String processId);

    /**
     * 查询文件权限
     * @param model
     * @return
     */
    ProcessFileAuthResult queryProcessFileAuth(QueryProcessFileAuthModel model);

    /**
     * 查询流程合同文件列表
     * @param processId
     * @return
     */
    ProcessContractResult listProcessContract(String processId);

    /**
     * 查询非标模板发起时的模板id
     * @param input
     * @return
     */
    ProcessStartFlowTemplateResult queryProcessUnStandardInfo(ProcessStartFlowTemplateModel input);

    /**
     * 获取url
     * @param model
     * @return
     */
    ProcessUrlResult processUrl(ProcessUrlModel model);
}
