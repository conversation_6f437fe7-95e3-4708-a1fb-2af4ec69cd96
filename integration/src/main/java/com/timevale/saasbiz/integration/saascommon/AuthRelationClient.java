package com.timevale.saasbiz.integration.saascommon;

import com.timevale.saas.common.manage.common.service.model.input.authrelation.*;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationDTO;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.GetAuthRelationShareConfigOutput;

import java.util.List;

/**
 * AuthRelationClinet
 *
 * <AUTHOR>
 * @since 2023/4/3 11:36 上午
 */
public interface AuthRelationClient {

    /**
     * 获取子企业对应的主企业配置信息
     *
     * @param tenantGid
     * @param configKey
     * @return
     */
    GetAuthRelationShareConfigOutput getAuthRelationShareConfig(String tenantGid, String configKey);

    AuthRelationDTO getEffectiveAuthRelationByChildTenantGid(String childTenantGid);

    SearchAuthRelationLastEffectiveTimeOutput searchAuthRelationLastEffectiveTime(SearchAuthRelationLastEffectiveTimeInput input);

    PageAuthRelationLastEffectiveTimeOutput pageAuthRelationLastEffectiveTime(PageAuthRelationLastEffectiveTimeInput input);

    PageEffectiveAuthRelationOutput pageEffectiveAuthRelation(PageEffectiveAuthRelationInput input);

    /**
     * 历史上有效的企业
     */
    List<AuthRelationDTO> historyOrNowEffectiveAuthRelationByChildTenantGid(String childTenantGid);

    List<AuthRelationDTO> getEffectiveAndUnEffectiveAuthRelationByChildTenantGid(String childTenantGid);

    /**
     * 企业是否成为过主企业
     *
     * @param tenantGid 企业oid
     * @return true 成为过，false没有成为过
     */
    Boolean checkTenantWasParent(String tenantGid);

    /**
     * 校验授权关系，及是否总部oid为正式总部，及授权场景是否存在
     * @param input 校验请求
     * 校验失败抛出对应异常
     */
    void checkHeadOfficeAndAuthResources(CheckHeadOfficeAndAuthResourcesInput input );

    /**
     * 查询有效的关联企业列表
     * @param input
     * @return
     */
    List<AuthRelationDTO> queryEffectiveAuthRelationListByParentTenantGid(
            QueryEffectiveAuthRelationByParentTenantGidInput input);
}
