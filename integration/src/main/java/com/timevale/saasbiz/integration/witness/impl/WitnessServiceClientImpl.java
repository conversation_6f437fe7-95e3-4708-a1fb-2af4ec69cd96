package com.timevale.saasbiz.integration.witness.impl;

import com.timevale.footstone.witness.api.common.service.api.WitnessApiService;
import com.timevale.footstone.witness.api.common.service.query.FlowInfoQueryParam;
import com.timevale.footstone.witness.api.common.service.result.WitnessFlowInfo;
import com.timevale.saasbiz.integration.witness.WitnessServiceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-03-06
 */
@Component
public class WitnessServiceClientImpl implements WitnessServiceClient {

    @Autowired WitnessApiService witnessApiService;

    @Override
    public WitnessFlowInfo queryByFlowId(String flowId) {
        FlowInfoQueryParam queryParam = new FlowInfoQueryParam();
        queryParam.setFlowId(flowId);

        return witnessApiService.queryWitnessFlowInfo(queryParam).getData();
    }
}
