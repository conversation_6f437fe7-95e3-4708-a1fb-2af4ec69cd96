package com.timevale.saasbiz.integration.dedicatedcloud;

import com.timevale.saas.integration.service.model.input.CreateDedicatedProjectInput;
import com.timevale.saas.integration.service.model.input.QueryDedicatedProjectListInput;
import com.timevale.saas.integration.service.model.input.ToggleDedicatedProjectStatusInput;
import com.timevale.saas.integration.service.model.input.UnbindDedicatedProjectAuthInput;
import com.timevale.saas.integration.service.model.input.UpdateDedicatedProjectInput;
import com.timevale.saas.integration.service.model.output.model.QueryDedicatedProjectListOutput;
import com.timevale.saas.integration.service.model.output.model.QueryDedicatedProjectOutput;

/**
 * <AUTHOR>
 * @since 2024/2/1 10:46
 */
public interface DedicatedCloudClient {

    /**
     * 创建
     */
    String createDedicatedProject(CreateDedicatedProjectInput input);

    /**
     * 更新
     */
    void updateDedicatedProject(UpdateDedicatedProjectInput input);

    /**
     * 修改状态
     */
    void updateStatus(ToggleDedicatedProjectStatusInput input);

    /**
     * 列表查询
     */
    QueryDedicatedProjectListOutput listDedicatedProject(QueryDedicatedProjectListInput input);

    /**
     * 获取专属云，目前一个企业只会查一个
     */
    QueryDedicatedProjectOutput getDedicatedProjectByGid(String subjectGid);

    /**
     * 是否已经存在专属云
     */
    boolean existDedicatedCloud(String subjectGid);

    /**
     * 详情查询
     */
    QueryDedicatedProjectOutput getDedicatedProject(String dedicatedCloudId);

    /**
     * 下载配置
     */
    String downloadDedicatedConfig(String dedicatedCloudId);

    /**
     * 解绑授权的AppId
     */
    void unBindDedicatedProjectAuth(UnbindDedicatedProjectAuthInput input);

    /**
     * 查询是否有有效订单
     */
    boolean queryDedicatedActivation(String subjectGid);


}
