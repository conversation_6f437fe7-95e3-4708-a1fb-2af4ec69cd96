package com.timevale.saasbiz.integration.auditlog.impl;

import com.timevale.dayu.config.result.EventDataResult;
import com.timevale.dayu.config.service.DataService;
import com.timevale.dayu.facade.api.AuditLogService;
import com.timevale.dayu.facade.enums.AuditLogBizTypeEnum;
import com.timevale.dayu.facade.model.AuditLogVo;
import com.timevale.dayu.facade.request.ConfigQueryEventRequest;
import com.timevale.dayu.facade.request.DownloadStatusRequest;
import com.timevale.dayu.facade.request.NewAuditLogDownloadRequest;
import com.timevale.dayu.facade.request.NewAuditLogQueryRequest;
import com.timevale.dayu.facade.request.SubscribeRecordQueryDetailRequest;
import com.timevale.dayu.facade.request.SubscribeRecordQueryRequest;
import com.timevale.dayu.facade.result.AuditLogDownloadResult;
import com.timevale.dayu.facade.result.AuditLogQueryResult;
import com.timevale.dayu.facade.result.ConfigQueryEventResult;
import com.timevale.dayu.facade.result.DownloadStatusResult;
import com.timevale.dayu.facade.result.SubscribeRecordQueryResult;
import com.timevale.saasbiz.integration.auditlog.AuditLogClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 审计日志服务
 *
 * <AUTHOR>
 * @since 2023-10-11 17:23
 */
@Component
public class AuditLogClientImpl implements AuditLogClient {
    @Autowired private AuditLogService auditLogService;
    @Autowired private DataService dataService;

    @Override
    public AuditLogQueryResult queryAuditLogNew(NewAuditLogQueryRequest request) {
        return auditLogService.queryAuditLogNew(request);
    }

    @Override
    public AuditLogDownloadResult downloadAuditLogNew(NewAuditLogDownloadRequest request) {
        return auditLogService.downloadAuditLogNew(request);
    }

    @Override
    public ConfigQueryEventResult queryEvent(ConfigQueryEventRequest configQueryEventRequest) {
        return auditLogService.queryEvent(configQueryEventRequest);
    }

    @Override
    public DownloadStatusResult queryDownloadStatus(DownloadStatusRequest request) {
        return auditLogService.queryDownloadStatus(request);
    }

    @Override
    public SubscribeRecordQueryResult querySubscribeRecordList(SubscribeRecordQueryRequest request) {
        request.setBizType(AuditLogBizTypeEnum.SUBSCRIBE.getType());
        return auditLogService.querySubscribeRecordList(request);
    }

    @Override
    public AuditLogVo getRecordDetail(SubscribeRecordQueryDetailRequest request) {
        return auditLogService.getRecordDetail(request);
    }

    @Override
    public EventDataResult querySubscribeEvent() {
        return dataService.querySubscribeEvent();
    }
}
