package com.timevale.saasbiz.integration.process.impl;

import com.timevale.contractmanager.common.service.api.RpcProcessSummaryService;
import com.timevale.contractmanager.common.service.model.processsummary.CreateProcessSummaryModel;
import com.timevale.contractmanager.common.service.model.processsummary.QueryProcessSummaryDetailModel;
import com.timevale.contractmanager.common.service.model.processsummary.QueryProcessSummaryOverAllStatusModel;
import com.timevale.contractmanager.common.service.model.processsummary.RefreshProcessSummaryModel;
import com.timevale.contractmanager.common.service.model.processsummary.UpdateProcessSummaryModel;
import com.timevale.contractmanager.common.service.result.processsummary.ProcessSummaryDetailResult;
import com.timevale.contractmanager.common.service.result.processsummary.ProcessSummaryOverAllStatusResult;
import com.timevale.saasbiz.integration.process.ProcessSummaryClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 合同摘要客户端实现
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Service
public class ProcessSummaryClientImpl implements ProcessSummaryClient {

    @Autowired private RpcProcessSummaryService rpcProcessSummaryService;

    @Override
    public void createProcessSummary(
            String processId, String fileId, String subjectId, String subjectGid) {
        CreateProcessSummaryModel model = new CreateProcessSummaryModel();
        model.setProcessId(processId);
        model.setSubjectId(subjectId);
        model.setFileId(fileId);
        model.setSubjectGid(subjectGid);
        rpcProcessSummaryService.createProcessSummary(model);
    }

    @Override
    public void createProcessAllFileSummary(String processId, String subjectId, String subjectGid) {
        CreateProcessSummaryModel model = new CreateProcessSummaryModel();
        model.setProcessId(processId);
        model.setSubjectId(subjectId);
        model.setSubjectGid(subjectGid);
        rpcProcessSummaryService.createProcessAllFileSummary(model);
    }

    @Override
    public void refreshProcessSummary(RefreshProcessSummaryModel model) {
        rpcProcessSummaryService.refreshProcessSummary(model);
    }

    @Override
    public ProcessSummaryDetailResult queryProcessSummaryDetail(QueryProcessSummaryDetailModel model) {
        return rpcProcessSummaryService.queryProcessSummaryDetail(model);
    }

    @Override
    public void updateProcessSummary(UpdateProcessSummaryModel model) {
        rpcProcessSummaryService.updateProcessSummary(model);
    }

    @Override
    public String queryProcessSummaryOverAllStatus(String processId, String subjectGid) {
        QueryProcessSummaryOverAllStatusModel model = new QueryProcessSummaryOverAllStatusModel();
        model.setProcessId(processId);
        model.setSubjectGid(subjectGid);
        ProcessSummaryOverAllStatusResult result = rpcProcessSummaryService.queryProcessSummaryOverAllStatus(model);
        return result.getStatus();
    }
}
