package com.timevale.saasbiz.integration.process.impl;

import com.timevale.saasbiz.integration.process.ProcessSearchClient;
import com.timevale.signflow.search.docSearchService.api.DocSearchApi;
import com.timevale.signflow.search.docSearchService.param.QueryByProcessIdParam;
import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdResult;
import com.timevale.signflow.search.service.api.v2.RpcProcessSearchService;
import com.timevale.signflow.search.service.model.v2.CountTenantProcessModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 流程检索服务
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Component
public class ProcessSearchClientImpl implements ProcessSearchClient {

    @Autowired
    RpcProcessSearchService rpcProcessSearchService;

    @Autowired
    private DocSearch<PERSON>pi docSearch<PERSON>pi;

    @Override
    public long countTenantProcess(CountTenantProcessModel model) {
        return rpcProcessSearchService.countTenantInitProcesses(model).getTotal();
    }

    @Override
    public QueryByProcessIdResult queryById(String processId) {
        QueryByProcessIdParam param = new QueryByProcessIdParam();
        param.setProcessId(processId);
        return docSearchApi.queryById(param);
    }
}
