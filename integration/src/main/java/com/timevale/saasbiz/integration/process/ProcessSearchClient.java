package com.timevale.saasbiz.integration.process;

import com.timevale.signflow.search.docSearchService.result.QueryByProcessIdResult;
import com.timevale.signflow.search.service.model.v2.CountTenantProcessModel;

/**
 * 流程检索服务
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
public interface ProcessSearchClient {

    /**
     * 统计主体下的流程总数
     * @param model
     * @return
     */
    long countTenantProcess(CountTenantProcessModel model);

    /**
     * 根据流程id查询流程信息
     * @param processId
     * @return
     */
    QueryByProcessIdResult queryById(String processId);
}
