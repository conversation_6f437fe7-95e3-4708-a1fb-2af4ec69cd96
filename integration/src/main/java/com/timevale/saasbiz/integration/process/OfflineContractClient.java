package com.timevale.saasbiz.integration.process;

import com.timevale.contractmanager.common.service.model.offlinecontract.*;
import com.timevale.contractmanager.common.service.result.offlinecontract.BatchQueryOfflineContractRecordInfoResult;
import com.timevale.contractmanager.common.service.result.offlinecontract.QueryOfflineContractRecordContractsResult;
import com.timevale.contractmanager.common.service.result.offlinecontract.QueryOfflineContractRecordInfoResult;
import com.timevale.contractmanager.common.service.result.offlinecontract.QueryOfflineContractRecordsResult;

/**
 * 线下合同
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public interface OfflineContractClient {

    /**
     * 导入线下合同
     * @param model
     * @return
     */
    String importOfflineContractRecord(SaveOfflineContractRecordModel model);

    /**
     * 查询线下合同导入记录列表
     * @param model
     * @return
     */
    QueryOfflineContractRecordsResult queryOfflineContractRecords(
            QueryOfflineContractRecordsModel model);

    /**
     * 查询导入记录基本信息
     * @param model
     * @return
     */
    QueryOfflineContractRecordInfoResult queryOfflineContractRecordInfo(
            QueryOfflineContractRecordInfoModel model);

    /**
     * 批量查询导入记录基本信息
     * @param model
     * @return
     */
    BatchQueryOfflineContractRecordInfoResult batchQueryOfflineContractRecordInfo(
            BatchQueryOfflineContractRecordInfoModel model);

    /**
     * 查询导入记录合同列表
     * @param model
     * @return
     */
    QueryOfflineContractRecordContractsResult queryOfflineContractRecordContracts(
            QueryOfflineContractRecordContractsModel model);

    /**
     * 批量删除导入记录
     * @param model
     */
    void deleteOfflineContractRecord(DeleteOfflineContractRecordsModel model);

    /**
     * 批量停止导入
     * @param model
     */
    void stopImportOfflineContractRecord(StopImportOfflineContractModel model);

    /**
     * 恢复导入, 停止导入的线下合同继续开始导入
     * @param model
     */
    void recoverImportOfflineContractRecord(RecoverImportOfflineContractModel model);

    /**
     * 重新导入记录中导入失败的线下合同
     * @param model
     */
    void restartImportFailedOfflineContract(RestartImportFailedOfflineContractModel model);

    /**
     * 查询是否存在欠费的导入记录
     * @param recordId
     * @return
     */
    boolean queryExistsArrearsImportProcess(String recordId, String tenantGid);
}
