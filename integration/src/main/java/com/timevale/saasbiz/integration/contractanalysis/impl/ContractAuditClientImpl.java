package com.timevale.saasbiz.integration.contractanalysis.impl;

import com.timevale.contractanalysis.facade.api.contractaudit.ContractAuditFacadeService;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcContractAuditRecordPageQueryInput;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcContractAuditRecordPageQueryOutput;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateAuditResultInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateAuditResultOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateContractAuditWebInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateContractAuditWebOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcDeleteContractAuditRecordInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcGetContractAuditWebUrlInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcGetContractAuditWebUrlOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditListsInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditListsOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditResultRuleInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditResultRuleOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditRuleTreeInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditRuleTreeOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryEmbedRecordInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.bean.RpcContractAuditRecordDTO;
import com.timevale.saasbiz.integration.contractanalysis.ContractAuditClient;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Component
public class ContractAuditClientImpl implements ContractAuditClient {

    @Autowired
    private ContractAuditFacadeService contractAuditFacadeService;

    @Override
    public RpcCreateContractAuditWebOutputDTO createContractAudit(RpcCreateContractAuditWebInputDTO input) {
        return contractAuditFacadeService.createContractAuditWeb(input);
    }

    @Override
    public RpcContractAuditRecordPageQueryOutput contractAuditPageRecord(RpcContractAuditRecordPageQueryInput input) {
        return contractAuditFacadeService.pageQueryRecord(input);
    }

    @Override
    public String getContractAuditWebUrl(String recordId, String operatorGid, String tenantGid) {
        RpcGetContractAuditWebUrlInputDTO inputDTO = new RpcGetContractAuditWebUrlInputDTO();
        inputDTO.setRecordId(recordId);
        inputDTO.setOperatorGid(operatorGid);
        inputDTO.setTenantGid(tenantGid);
        RpcGetContractAuditWebUrlOutputDTO outputDTO = contractAuditFacadeService.getContractAuditWebUrl(inputDTO);
        return outputDTO.getWebUrl();
    }

    @Override
    public void deleteContractAuditRecord(String recordId, String operatorGid, String tenantGid) {
        RpcDeleteContractAuditRecordInputDTO inputDTO = new RpcDeleteContractAuditRecordInputDTO();
        inputDTO.setRecordId(recordId);
        inputDTO.setOperatorGid(operatorGid);
        inputDTO.setTenantGid(tenantGid);
        contractAuditFacadeService.deleteRecord(inputDTO);
    }

    @Override
    public RpcQueryAuditListsOutputDTO queryAuditRuleLists(String operatorGid, String tenantGid){
        RpcQueryAuditListsInputDTO input = new RpcQueryAuditListsInputDTO();
        input.setOperatorGid(operatorGid);
        input.setTenantGid(tenantGid);
        return contractAuditFacadeService.queryAuditLists(input);
    }

    @Override
    public RpcContractAuditRecordDTO queryEmbedContractAuditRecord(String processId, String fileId, String operatorGid, String tenantGid) {
        RpcQueryEmbedRecordInputDTO inputDTO = new RpcQueryEmbedRecordInputDTO();
        inputDTO.setProcessId(processId);
        inputDTO.setFileId(fileId);
        inputDTO.setOperatorGid(operatorGid);
        inputDTO.setTenantGid(tenantGid);
        return contractAuditFacadeService.queryEmbedRecord(inputDTO);
    }

    @Override
    public RpcCreateAuditResultOutputDTO createAuditResult(RpcCreateAuditResultInputDTO inputDTO) {
        return contractAuditFacadeService.createAuditResult(inputDTO);
    }

    @Override
    public RpcQueryAuditRuleTreeOutputDTO queryAuditRuleTree(RpcQueryAuditRuleTreeInputDTO input) {
        RpcQueryAuditRuleTreeInputDTO inputDTO = new RpcQueryAuditRuleTreeInputDTO();
        inputDTO.setRecordId(input.getRecordId());
        inputDTO.setProcessId(input.getProcessId());
        inputDTO.setFileId(input.getFileId());
        inputDTO.setOperatorGid(input.getOperatorGid());
        inputDTO.setTenantGid(input.getTenantGid());
        return contractAuditFacadeService.queryAuditRuleTree(inputDTO);
    }

    @Override
    public RpcQueryAuditResultRuleOutputDTO queryAuditRuleResult(RpcQueryAuditResultRuleInputDTO inputDTO) {
        return contractAuditFacadeService.queryAuditResultRule(inputDTO);
    }
}
