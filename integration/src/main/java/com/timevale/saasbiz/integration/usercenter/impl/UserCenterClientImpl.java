package com.timevale.saasbiz.integration.usercenter.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.account.organization.service.api.RpcInnerOrgMemberService;
import com.timevale.account.organization.service.api.RpcInnerOrgService;
import com.timevale.account.organization.service.exception.OrganErrors;
import com.timevale.account.organization.service.model.service.biz.input.BizGetOrganInfoInput;
import com.timevale.account.organization.service.model.service.newbiz.input.BizMemberGuidsBatchGetInput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizGetOrganOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizMemberGetOutput;
import com.timevale.account.realname.service.api.RpcAccountRealnameService;
import com.timevale.account.realname.service.model.service.biz.rlnm.BizRealnameGetInput;
import com.timevale.account.realname.service.model.service.mods.rlnm.AccountRealnameInfos;
import com.timevale.account.realname.service.model.service.mods.rlnm.ServiceIdInfo;
import com.timevale.account.service.api.RpcICUserService;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.exception.Errors;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUserAccountGetInput;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUserGetInput;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUsersGetInput;
import com.timevale.account.service.model.service.biz.icuser.output.BizFatICUserOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.icuser.ICUserOpenId;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.easun.service.api.RpcDeptPlusService;
import com.timevale.easun.service.api.RpcDeptPlusV3Service;
import com.timevale.easun.service.api.RpcEsAccountService;
import com.timevale.easun.service.api.RpcIcOrgPlusService;
import com.timevale.easun.service.api.RpcIcUserPlusService;
import com.timevale.easun.service.exception.EasunErrors;
import com.timevale.easun.service.api.RpcMultiSpaceService;
import com.timevale.easun.service.api.RpcOrgPlusService;
import com.timevale.easun.service.model.account.input.BatchOrganInput;
import com.timevale.easun.service.model.account.input.BizGetIcUserOrgListInput;
import com.timevale.easun.service.model.account.input.BizSpaceDeptInput;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.easun.service.model.account.output.BizOrganOuid;
import com.timevale.easun.service.model.account.output.EasunSpaceDeptOutput;
import com.timevale.easun.service.model.account.output.UserSpacePrivilegeOutput;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsOrgSearchInput;
import com.timevale.easun.service.model.esearch.EsSearchInput;
import com.timevale.easun.service.model.esearch.input.BizEsAccountIdcardSearchInput;
import com.timevale.easun.service.model.esearch.output.BatchOrganRoles;
import com.timevale.easun.service.model.esearch.output.EsAccountIdcardInfoOutput;
import com.timevale.easun.service.model.esearch.output.EsAggOutput;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.input.*;
import com.timevale.easun.service.model.organization.output.*;
import com.timevale.easun.service.model.organization.output.v3.BizDeptListOutputV3;
import com.timevale.easun.service.model.organization.output.v3.BizOrgSummaryOutputV3;
import com.timevale.easun.service.model.role.input.BizGetOrgInput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.page.Pages;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.privilege.service.enums.BuiltinRoleType;
import com.timevale.saasbiz.integration.usercenter.UserCenterClient;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.timevale.saasbiz.model.exception.SaasBizResultCode.USER_ACCOUNT_NOT_EXIST;

/**
 * 用户中心
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Slf4j
@Component
public class UserCenterClientImpl implements UserCenterClient {

    @Autowired RpcICUserService rpcICUserService;
    @Autowired RpcIcUserPlusService rpcIcUserPlusService;
    @Autowired private RpcInnerOrgMemberService rpcInnerOrgMemberService;
    @Autowired private RpcDeptPlusService rpcDeptPlusService;
    @Autowired private RpcIcOrgPlusService rpcIcOrgPlusService;
    @Autowired private RpcInnerOrgService rpcInnerOrgService;
    @Autowired private RpcAccountRealnameService rpcAccountRealnameService;
    @Autowired private RpcDeptPlusV3Service rpcDeptPlusV3Service;
    @Autowired private RpcMultiSpaceService rpcMultiSpaceService;
    @Autowired private RpcOrgPlusService rpcOrgPlusService;
    @Autowired private RpcEsAccountService rpcEsAccountService;

    @Override
    public BizICUserOutput getICUserBaseInfo(String oid) {
        // 根据ouid查询账号详情
        BizICUserGetInput input = new BizICUserGetInput(oid);
        try {
            return rpcICUserService.getICUser(new RpcInput<>(input)).getData();
        } catch (Errors.AccOpenUserNotExistWith e) {
            throw new SaasBizException(USER_ACCOUNT_NOT_EXIST);
        }
    }

    @Override
    public BizAccountRealNameOutput getRealNameByOid(String oid) {
        BizICUserGetInput input = new BizICUserGetInput();
        ICUserOpenId icUserOpenId = new ICUserOpenId();
        icUserOpenId.setOuid(oid);
        input.setData(icUserOpenId);
        RpcOutput<BizAccountRealNameOutput> rpcOutput;
        try {
            rpcOutput = rpcIcUserPlusService.getRealNameByOuid(new RpcInput<>(input));
            return rpcOutput.getData();
        } catch (Exception e) {
            if (e instanceof Errors.AccOpenUserNotExistWith
                    || e instanceof Errors.AccAccountNotExistWith) {
                throw new SaasBizException(USER_ACCOUNT_NOT_EXIST);
            }
            throw e;
        }
    }

    @Override
    public List<BizAccountRealNameOutput> batchGetRealNameByOid(List<String> oidList) {
        RpcInput<BizICUsersGetInput> rpcInput = new RpcInput<>();
        BizICUsersGetInput userGetInput = new BizICUsersGetInput(oidList);
        rpcInput.setInput(userGetInput);
        RpcOutput<List<BizAccountRealNameOutput>> rpcOutput;
        try {
            rpcOutput = rpcIcUserPlusService.getRealNameByOuidsSimple(rpcInput);
        } catch (BaseRuntimeException e) {
            log.warn(" batchGetRealNameByOid error , msg:{}", e.getMessage());
            throw new SaasBizException(USER_ACCOUNT_NOT_EXIST, e.getMessage());
        }

        return rpcOutput.getData();
    }

    /**
     * 根据oid批量查询账号信息
     *
     * @param oidList
     * @return
     */
    @Override
    public List<BizAccountRealNameOutput> batchGetRealNameByOidByES(List<String> oidList) {
        RpcInput<BizICUsersGetInput> rpcInput = new RpcInput<>();
        BizICUsersGetInput userGetInput = new BizICUsersGetInput(oidList);
        rpcInput.setInput(userGetInput);
        RpcOutput<List<BizAccountRealNameOutput>> rpcOutput;
        try {
            rpcOutput = rpcIcUserPlusService.getRealNameByOuidsSimpleByEs(rpcInput);
        } catch (BaseRuntimeException e) {
            log.warn(" batchGetRealNameByOidByES error , msg:{}", e.getMessage());
            throw new SaasBizException(USER_ACCOUNT_NOT_EXIST, e.getMessage());
        }

        return rpcOutput.getData();
    }

    /**
     * 根据企业oid批量查询部门信息 //
     *
     * @param orgOidList
     * @return
     */
    @Override
    public BizDeptListOutputV3 batchGetDeptDetailByOid(List<String> orgOidList) {
        RpcInput<BizEasunGetDeptDetailsV3Input> rpcInput = new RpcInput<>();
        BizEasunGetDeptDetailsV3Input input = new BizEasunGetDeptDetailsV3Input();
        input.setDeptIds(orgOidList);
        input.setAppendCurrentDept(true);
        rpcInput.setInput(input);
        return rpcDeptPlusV3Service.getBatchDeptDetails(rpcInput).getData();
    }

    @Override
    public List<BizAccountRealNameOutput> getRealNameByOuidsSimple(List<String> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }

        RpcInput<BizICUsersGetInput> rpcInput = new RpcInput<>();
        BizICUsersGetInput userGetInput = new BizICUsersGetInput(accountIds);
        rpcInput.setInput(userGetInput);

        try {
            // 查询用户信息
            RpcOutput<List<BizAccountRealNameOutput>> rpcOutput =
                    rpcIcUserPlusService.getRealNameByOuidsSimple(rpcInput);

            return Optional.ofNullable(rpcOutput)
                    .map(RpcOutput::getData)
                    .orElse(Collections.emptyList());
        } catch (BaseRuntimeException e) {
            log.warn(" getRealNameByOuidsSimple error ,  msg:{}", e.getMessage());
            throw new SaasBizException(USER_ACCOUNT_NOT_EXIST, e.getMessage());
        }
    }

    @Override
    public List<BizDeptInfoOutput> userDirectDept(String subjectOid, String userOid) {
        RpcInput<BizDeptInfosByMemberInput> input = new RpcInput<>();
        BizDeptInfosByMemberInput bizDeptInfosByMemberInput = new BizDeptInfosByMemberInput();
        bizDeptInfosByMemberInput.setOrgId(subjectOid);
        bizDeptInfosByMemberInput.setMemberOid(userOid);
        input.setInput(bizDeptInfosByMemberInput);
        RpcOutput<BizDeptInfosByMemberOutput> deptInfosByMemberOutput =
                rpcDeptPlusService.getDeptInfosByMember(input);
        if (null == deptInfosByMemberOutput ||
                CollectionUtils.isEmpty(deptInfosByMemberOutput.getData().getDeptInfoOutputs())) {
            return new ArrayList<>();
        }

        return deptInfosByMemberOutput.getData().getDeptInfoOutputs();
    }

    @Override
    public boolean checkMemberInSubject(String subjectOid, String personOid) {
        if (StringUtils.isBlank(subjectOid) || StringUtils.isBlank(personOid)) {
            return false;
        }

        if (Objects.equals(subjectOid, personOid)) {
            return true;
        }

        BizCheckMemberorCreatorInput bizCheckMemberorCreatorInput = new BizCheckMemberorCreatorInput();
        bizCheckMemberorCreatorInput.setOrgId(subjectOid);
        bizCheckMemberorCreatorInput.setMemberAccountId(personOid);

        RpcInput<BizCheckMemberorCreatorInput> input = new RpcInput<>();
        input.setInput(bizCheckMemberorCreatorInput);
        try {
            RpcOutput<Boolean> booleanRpcOutput = rpcIcOrgPlusService.checkMemberOrCreator(input);
            return booleanRpcOutput.getData();
        } catch (EasunErrors.OrgNotExist e) {
            throw new SaasBizException(SaasBizResultCode.ORG_NOT_EXIST);
        }
    }




    @Override
    public String subjectOidToOrganId(String subjectOid) {
        if (StringUtils.isBlank(subjectOid)) {
            return null;
        }
        RpcInput<BizGetOrganInfoInput> input = new RpcInput<>();
        BizGetOrganInfoInput bizGetOrganInfoInput = new BizGetOrganInfoInput();
        bizGetOrganInfoInput.setOid(subjectOid);
        input.setInput(bizGetOrganInfoInput);
        return rpcInnerOrgService.getOragnIdByOid(input).getData();
    }


    @Override
    public List<BizMemberGetOutput> batchGetMembersByGuid(String subjectOid, List<String> gids) {
        BizMemberGuidsBatchGetInput input = new BizMemberGuidsBatchGetInput();
        input.setOrganOid(subjectOid);
        input.setGuids(gids);
        RpcOutput<List<BizMemberGetOutput>> output = rpcInnerOrgMemberService.batchGetMembersByGuid(new RpcInput<>(input));
        if (!output.isSuccess()) {
            log.info("usercenter batchGetMembersByGuid req : {} res : {}", JSON.toJSONString(input),
                    JSON.toJSONString(output));
            throw new SaasBizException(SaasBizResultCode.USER_CENTER_ACCOUNT_QUERY_FAIL.getCode(), output.getMessage());
        }
        return output.getData();
    }

    @Override
    public List<BizICUserOutput> queryAccountAdminOrgs(String accountId) {

        List<BizICUserOutput> orgs = Lists.newArrayList();
        RpcInput<BizGetIcUserOrgListInput> rpcInput = new RpcInput<>();
        BizGetIcUserOrgListInput input = new BizGetIcUserOrgListInput();
        input.setAccountId(accountId);
        input.setPages(new Pages());
        rpcInput.setInput(input);
        PagerResult<BizOrgBaseInfoOutput> organList =
                rpcIcOrgPlusService.getIcUserOrganList(rpcInput).getData();
        for (BizOrgBaseInfoOutput item : organList.getItems()) {
            if (item.getRoleDetailList().stream()
                    .anyMatch(role -> "ADMIN".equals(role.getRoleKey()))) {
                orgs.add(item.getAccount());
            }
        }
        return orgs;
    }

    @Override
    public boolean checkIsFirstRealNameByAccountUid(String accountUid) {
        RpcInput<BizRealnameGetInput> rpcInput = new RpcInput<>();
        BizRealnameGetInput input = new BizRealnameGetInput();
        input.setData(accountUid);
        rpcInput.setInput(input);
        RpcOutput<AccountRealnameInfos> allServiceInfo =
                rpcAccountRealnameService.getAllServiceInfo(rpcInput);
        List<ServiceIdInfo> serviceIds = allServiceInfo.getData().getServiceIds();
        if (CollectionUtils.isEmpty(serviceIds)) {
            return true;
        }
        return serviceIds.stream()
                        .filter(item -> RealnameStatus.ACCEPT.name().equals(item.getStatus()))
                        .count()
                <= 1;
    }

    @Override
    public BizICUserBaseOutput getICUserBaseByAccountUid(String accountUid) {
        RpcInput<BizICUserAccountGetInput> rpcInput = new RpcInput<>();
        BizICUserAccountGetInput input = new BizICUserAccountGetInput();
        input.setData(accountUid);
        rpcInput.setInput(input);
        return rpcICUserService.getICUserBaseByAccount(rpcInput).getData();
    }

    @Override
    public boolean checkDeptsBelongToTheOrg(List<String> deptIdList, String orgOid) {
        RpcInput<BizEasunDeptsBelongsToTheOrgInput> rpcInput = new RpcInput<>();
        BizEasunDeptsBelongsToTheOrgInput input = new BizEasunDeptsBelongsToTheOrgInput();
        input.setOrgId(orgOid);
        input.setDeptIds(deptIdList);
        rpcInput.setInput(input);
        return rpcDeptPlusV3Service.belongsToTheOrg(rpcInput).getData();
    }

    @Override
    public boolean checkMembersBelongToTheOrg(List<String> memeberIdList, String orgOid) {
        RpcInput<BizEasunMembersBelongsToOrgInput> rpcInput = new RpcInput<>();
        BizEasunMembersBelongsToOrgInput input = new BizEasunMembersBelongsToOrgInput();
        input.setOrgId(orgOid);
        input.setMemberOids(memeberIdList);
        rpcInput.setInput(input);
        return rpcIcOrgPlusService.membersBelongsToTheOrg(rpcInput).getData();
    }

    public BizFatICUserOutput getFatAccountDetailByOid(String userOid) {
        RpcInput<BizICUserGetInput> out = new RpcInput<>();
        BizICUserGetInput input = new BizICUserGetInput();
        ICUserOpenId icUserOpenId = new ICUserOpenId();
        icUserOpenId.setOuid(userOid);
        input.setData(icUserOpenId);
        out.setInput(input);
        RpcOutput<BizFatICUserOutput> rpcOutput;
        try {
            rpcOutput = rpcICUserService.getFullyFatICUser(out);
            return rpcOutput.getData();
        } catch (Exception e) {
            if (e instanceof Errors.AccOpenUserNotExistWith
                    || e instanceof Errors.AccAccountNotExistWith) {
                throw new SaasBizException(SaasBizResultCode.USER_ACCOUNT_NOT_EXIST);
            }
            throw e;
        }
    }

    @Override
    public Map<String, EasunSpaceDeptOutput> checkMultiBizZonePermission(String subjectOid, String operatorOid, List<String> deptIds) {
        if (StringUtils.isEmpty(subjectOid) || StringUtils.isBlank(operatorOid) || CollectionUtils.isEmpty(deptIds)) {
            return new HashMap<>();
        }
        BizSpaceDeptInput param = new BizSpaceDeptInput();
        param.setOrganOuid(subjectOid);
        param.setMemberOuid(operatorOid);
        param.setDeptIds(deptIds);
        RpcInput<BizSpaceDeptInput> input = new RpcInput<>();
        input.setInput(param);
        RpcOutput<UserSpacePrivilegeOutput> result = rpcMultiSpaceService.getMemberSpaceDept(input);
        return Optional.ofNullable(result.getData()).map(UserSpacePrivilegeOutput::getUserDeptPrivilege).orElse(new HashMap<>());
    }

    @Override
    public BizDeptListOutputV3 getBatchDeptDetails(List<String> deptIds) {
        RpcInput<BizEasunGetDeptDetailsV3Input> rpcInput = new RpcInput();
        BizEasunGetDeptDetailsV3Input inputV3 = new BizEasunGetDeptDetailsV3Input();
        inputV3.setDeptIds(deptIds);
        rpcInput.setInput(inputV3);
        RpcOutput<BizDeptListOutputV3> rpcOutput = rpcDeptPlusV3Service.getBatchDeptDetails(rpcInput);
        return rpcOutput.getData();
    }

    @Override
    public BizOrgSummaryOutputV3 getOrgSummary(String subjectOid) {
        RpcInput<BizOrganOuid> input = new RpcInput<>();
        BizOrganOuid bizOrganOuid = new BizOrganOuid();
        bizOrganOuid.setOuid(subjectOid);
        input.setInput(bizOrganOuid);
        RpcOutput<BizOrgSummaryOutputV3> rpcOutput = rpcIcOrgPlusService.getOrgSummary(input);
        return rpcOutput.getData();
    }


    @Override
    public BizGetOrganOutput getOrganByOid(String oid) {
        //获取orgId
        RpcInput<BizGetOrgInput> rpcInput = new RpcInput<>();
        BizGetOrgInput input = new BizGetOrgInput();
        input.setOrganOid(oid);
        rpcInput.setInput(input);
        try{
            RpcOutput<BizGetOrganOutput> rpcOutput = rpcOrgPlusService.getOrganByAccountId(rpcInput);
            if (null == rpcOutput || !rpcOutput.isSuccess() || null == rpcOutput.getData()) {
                throw new BizContractManagerException(
                        BizContractManagerResultCodeEnum.ORG_MEMBER_LIST_FAILED,oid);
            }
            return rpcOutput.getData();
        }catch (Exception e){
            if (e instanceof OrganErrors.AccOrganNotExistForAccountUid) {
                throw new BizContractManagerException(BizContractManagerResultCodeEnum.ORG_MEMBER_LIST_FAILED,oid);
            }
            throw e;
        }
    }

    @Override
    public BizDeptMemberOutput getDeptAndMemberInfoOrgan(BizEasunOrganInputV2 input) {
        RpcInput<BizEasunOrganInputV2> rpcInput = new RpcInput<>();
        rpcInput.setInput(input);
        RpcOutput<BizDeptMemberOutput> output = rpcDeptPlusService.getDeptAndMemberInfoOrgan(rpcInput);
        if (null == output) {
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.GET_DEPT_INFO_FAILED, input.getOrganId());
        }
        return output.getData();
    }

    @Override
    public List<MemberDetail> getDeptAndMemberInfoByIds(String oid, List<String> memberOidList) {

        RpcInput<BizGetOrgInput> input = new RpcInput<>();
        BizGetOrgInput bizGetOrgInput = new BizGetOrgInput();
        bizGetOrgInput.setOrganOid(oid);
        input.setInput(bizGetOrgInput);
        RpcOutput<BizGetOrganOutput> organ = rpcOrgPlusService.getOrganByAccountId(input);
        if (organ == null || organ.getData() == null) {
            return new ArrayList<>();
        }

        BizEasunOrganInputV2 inputV2 = new BizEasunOrganInputV2();
        inputV2.setMemeberList(memberOidList);
        inputV2.setOrganId(organ.getData().getOrganId());
        inputV2.setSourceData(Lists.newArrayList("user.id.ouid"));
        RpcInput<BizEasunOrganInputV2> rpcInput = new RpcInput<>();
        rpcInput.setInput(inputV2);
        RpcOutput<BizDeptMemberOutput> result = rpcDeptPlusService.getDeptAndMemberInfoByIds(rpcInput);
        if (result == null || result.getData() == null) {
            return new ArrayList<>();
        }
        return result.getData().getMemberList();
    }

    @Override
    public String queryAccountGidByIdNo(String idNo) {

        RpcInput<EsSearchInput> input = new RpcInput<>();

        EsSearchInput esSearchInput = new EsSearchInput();
        esSearchInput.setCodeValue(idNo);
        input.setInput(esSearchInput);

        RpcOutput<PagerResult<EasunEsearchAccountOutput>> rpcOutput =
                rpcEsAccountService.getEsAccount(input);

        if (null == rpcOutput.getData()
                || CollectionUtils.isEmpty(rpcOutput.getData().getItems())) {
            return null;
        }
        for (EasunEsearchAccountOutput item : rpcOutput.getData().getItems()) {
            if (StringUtils.isNotBlank(item.getId().getGuid())) {
                return item.getId().getGuid();
            }
        }
        return null;
    }

    @Override
    public List<EsAccountIdcardInfoOutput> getAccountIdCardInfoByEs(String accountOid) {

        BizEsAccountIdcardSearchInput input = new BizEsAccountIdcardSearchInput();
        input.setOuid(accountOid);
        // 调用 RpcEsAccountService 的 getAccountIdcardInfoByEs 方法
        RpcOutput<PagerResult<EsAccountIdcardInfoOutput>> rpcOutput =
                rpcEsAccountService.getAccountIdcardInfoByEs(new RpcInput<>(input));
        // 检查 rpcOutput 是否为空
        if (rpcOutput == null || rpcOutput.getData() == null) {
            return Lists.newArrayList();
        }
        // 返回结果
        return rpcOutput.getData().getItems();
    }
    @Override
    public List<BizDeptBaseInfo> getMemberAllDept(String operatorId, String tenantId) {
        RpcInput<BizMemberAllDeptGetInput> rpcInput = new RpcInput<>();
        BizMemberAllDeptGetInput input = new BizMemberAllDeptGetInput();
        input.setMemberOid(operatorId);
        input.setOrgId(tenantId);
        input.setNeedChild(false);
        rpcInput.setInput(input);
        log.warn("getMemberAllDept() request operatorId={},tenantId={}", operatorId, tenantId);
        RpcOutput<BizMemberDeptListOutput> rpcOutput = rpcDeptPlusService.getMemberAllDept(rpcInput);
        BizMemberDeptListOutput listOutput = rpcOutput.getData();
        log.warn("getMemberAllDept() response listOutput={}", JsonUtils.obj2json(listOutput));
        List<BizDeptBaseInfo> deptList = listOutput.getDeptList();
        // 非空校验
        if (CollectionUtils.isEmpty(deptList)) {
            return new ArrayList<>();
        }
        return deptList;
    }

    @Override
    public Map<String, Long> getRoleMemberCountWithList(String subjectOid) {
        RpcInput<BizEsMemberCountInput> input = new RpcInput<>();
        BizEsMemberCountInput bizEsMemberCountInput = new BizEsMemberCountInput();
        bizEsMemberCountInput.setOrganOuid(subjectOid);
        bizEsMemberCountInput.setGroupByName("select_count_group_by_id");
        bizEsMemberCountInput.setOffset(0);
        bizEsMemberCountInput.setSize(99);
        input.setInput(bizEsMemberCountInput);
        EsAggOutput output = rpcEsAccountService.getRoleMemberCountWithList(input).getData();
        if (null == output || MapUtils.isEmpty(output.getRoleMemberCountMap())) {
            return Maps.newHashMap();
        }
        return output.getRoleMemberCountMap();
    }

    @Override
    public Map<String, List<MemberDetail>> batchQueryOrgAdmins(List<String> orgIds) {
        EsOrgSearchInput esOrgSearchInput = new EsOrgSearchInput();
        esOrgSearchInput.setOuidList(orgIds);
        esOrgSearchInput.setPages(new Pages(0, orgIds.size()));
        esOrgSearchInput.setNeedDefalutAppId(false);
        PagerResult<EsOrgSearhOutput> orgSearchOutput = rpcEsAccountService.getOrgsByEs(new RpcInput<>(esOrgSearchInput)).getData();
        if (null == orgSearchOutput || CollectionUtils.isEmpty(orgSearchOutput.getItems())) {
            return Maps.newHashMap();
        }
        Map<String, String> organIdMap = orgSearchOutput.getItems().stream()
                .filter(i -> StringUtils.isNotBlank(i.getId()))
                .collect(Collectors.toMap(i -> i.getUser().getId().getOuid(), i-> i.getId()));
        if (MapUtils.isEmpty(organIdMap)) {
            return Maps.newHashMap();
        }
        BatchOrganInput batchOrganInput = new BatchOrganInput();
        batchOrganInput.setOrganIds(Lists.newArrayList(organIdMap.values()));
        batchOrganInput.setRoleKey(BuiltinRoleType.ADMIN);
        RpcOutput<BatchOrganRoles> output = rpcIcOrgPlusService.getBatchOrganRoleList(new RpcInput<>(batchOrganInput));
        if (null == output.getData() || MapUtils.isEmpty(output.getData().getOrganRoleMap())) {
            return Maps.newHashMap();
        }
        Map<String, List<MemberDetail>> orgAdminMap = Maps.newHashMap();
        orgIds.forEach(orgId -> {
            String organId = organIdMap.get(orgId);
            if (StringUtils.isBlank(organId)) {
                return;
            }
            orgAdminMap.put(orgId, output.getData().getOrganRoleMap().getOrDefault(organId, Lists.newArrayList()));
        });
        return orgAdminMap;
    }

}
