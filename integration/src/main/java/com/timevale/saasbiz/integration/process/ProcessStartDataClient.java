package com.timevale.saasbiz.integration.process;

import com.timevale.contractmanager.common.service.bean.ProcessStartDataDTO;
import com.timevale.contractmanager.common.service.model.ProcessStartDataDataIdQueryModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataDeleteModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataOuterDataIdAndTemplateIdQueryModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataQueryModel;
import com.timevale.contractmanager.common.service.result.ProcessStartDataDeleteResult;
import com.timevale.contractmanager.common.service.result.ProcessStartDataQueryByDataIdResult;
import com.timevale.contractmanager.common.service.result.ProcessStartDataQueryResult;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 09:45
 */
public interface ProcessStartDataClient {


    /**
     * 删除
     */
    void deleteData(List<String> dataIdList);

    /**
     * 查询详情
     */
    List<ProcessStartDataDTO> listByDataIds(List<String> dataIdList);

    /**
     * 查询详情
     */
    ProcessStartDataDTO getByDataId(String dataId);


    /**
     * 分页查询
     */
    ProcessStartDataQueryResult listByQuery(ProcessStartDataQueryModel queryRequest);

    /**
     * 根据三方数据id和模板id查询,如果有模板id会返回唯一发起数据，没有则返回所有和这个三方数据id有关的发起数据
     */
    ProcessStartDataQueryByDataIdResult getByOuterDataIdAndTemplateId(ProcessStartDataOuterDataIdAndTemplateIdQueryModel model);
}
