package com.timevale.saasbiz.integration.contractdfrating;

import com.timevale.sterna.contract.drafting.client.dto.draft.ContractDraftingDTO;
import com.timevale.sterna.contract.drafting.client.dto.draft.ContractDraftingUrlDTO;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.req.FileQueryByIdReq;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.req.UploadFileReq;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.resp.GetFileInfoResp;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.resp.UploadFileResp;

public interface EpaasContractDraftingClient {

    /**
     * 获取在线编辑地址
     * @param input
     * @return
     */
    ContractDraftingUrlDTO createContractDrafting(ContractDraftingDTO input);

    /**
     * 上传并生成在线编辑文件id
     * @param input 请求参数
     * @return 响应数据
     */
    UploadFileResp uploadDraftingFile(UploadFileReq input);

    /**
     * 获取在线编辑文件信息
     * @param input 请求参数
     * @return 文件信息
     */
    GetFileInfoResp queryDraftingFile(FileQueryByIdReq input);
}
