package com.timevale.saasbiz.integration.ledger.impl;

import com.timevale.contractanalysis.facade.api.bo.FormListResult;
import com.timevale.contractanalysis.facade.api.form.FormFacadeService;
import com.timevale.saasbiz.integration.ledger.RpcLedgerClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * RpcLedgerClientImpl
 *
 * <AUTHOR>
 * @since 2023/10/18 4:09 下午
 */
@Service
public class RpcLedgerClientImpl implements RpcLedgerClient {

    @Autowired
    private FormFacadeService formFacadeService;

    @Override
    public FormListResult listAllForms(String tenantOid, Integer formType) {
        return formFacadeService.listForms(tenantOid, formType);
    }
}
