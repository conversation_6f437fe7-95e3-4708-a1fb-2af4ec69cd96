package com.timevale.saasbiz.integration.saascommon.impl;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.api.*;
import com.timevale.saas.common.manage.common.service.model.bean.bill.BillIsolateDTO;
import com.timevale.saas.common.manage.common.service.model.input.*;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.EffectiveAuthRelationByChildTenantGidInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.QueryEffectiveAuthRelationByParentTenantGidInput;
import com.timevale.saas.common.manage.common.service.model.input.bean.SubTaskAddBean;
import com.timevale.saas.common.manage.common.service.model.input.saasbizconfig.ChangeSaasBizConfigsInput;
import com.timevale.saas.common.manage.common.service.model.input.saasbizconfig.GetSaasBizConfigsInput;
import com.timevale.saas.common.manage.common.service.model.input.watermark.*;
import com.timevale.saas.common.manage.common.service.model.output.*;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.EffectiveAuthRelationByChildTenantGidOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.QueryEffectiveAuthRelationByParentTenantGidOutput;
import com.timevale.saas.common.manage.common.service.model.output.saasbizconfig.ChangeSaasBizConfigsOutput;
import com.timevale.saas.common.manage.common.service.model.output.saasbizconfig.GetSaasBizConfigsOutput;
import com.timevale.saas.common.manage.common.service.model.output.vip.GetSaasCommoditiesCategoryOutput;
import com.timevale.saas.common.manage.common.service.model.output.watermark.*;
import com.timevale.saasbiz.integration.saascommon.SaasCommonClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-03-09 10:28
 */
@Slf4j
@Component
public class SaasCommonClientImpl implements SaasCommonClient {

    @Autowired private SaasVipRpcService saasVipRpcService;

    @Autowired private SaasTaskRpcService saasTaskRpcService;

    @Autowired WatermarkTemplateRpcService watermarkTemplateRpcService;

    @Autowired private SaasBizConfigRpcService saasBizConfigRpcService;
    @Autowired private AuthRelationRpcService authRelationRpcService;

    @Autowired private SaasFunctionIllustrateRpcService saasFunctionIllustrateRpcService;

    @Autowired private SaasBillingRpcService saasBillingRpcService;
    
    @Autowired private SaasTrialRpcService saasTrialRpcService;

    @Override
    public AccountVipQueryOutput queryAccountVip(AccountVipQueryInput input) {
        return saasVipRpcService.queryAccountVip(input);
    }

    @Override
    public VipFunctionQueryOutput queryVipFunction(
            String tenantGid, String clientId, String functionKey) {
        VipFunctionQueryByGidInput input = new VipFunctionQueryByGidInput();
        input.setAccountGid(tenantGid);
        input.setClientId(clientId);
        input.setFunctionCode(functionKey);
        return saasVipRpcService.queryVipFunctionInfoByGid(input);
    }

    @Override
    public NextVipQueryOutput queryNextVip(String vipCode) {
        AccountCurrentVipInput input = new AccountCurrentVipInput();
        input.setVipCode(vipCode);
        return saasVipRpcService.queryNextVip(input);
    }

    @Override
    public VipFunctionQueryOutput queryVipFunctionByOid(String accountOid, String functionKey) {
        VipFunctionQueryInput input = new VipFunctionCheckInput();
        input.setAccountId(accountOid);
        input.setFunctionCode(functionKey);
        return saasVipRpcService.queryVipFunctionInfo(input);
    }

    @Override
    public AccountVipFunctionsOutput queryVipFunctions(
            String tenantId, String clientId, List<String> functionKeys) {
        AccountVipFunctionsInput input = new AccountVipFunctionsInput();
        input.setAccountId(tenantId);
        input.setClientId(clientId);
        input.setFunctionCodes(functionKeys);
        return saasVipRpcService.queryAccountFunctions(input);
    }

    @Override
    public boolean supportFunction(String tenantId, String clientId, String functionKey) {
        VipFunctionCheckInput input = new VipFunctionCheckInput();
        input.setAccountId(tenantId);
        input.setClientId(clientId);
        input.setFunctionCode(functionKey);
        input.setCheckBatchLimit(false);
        VipcheckFunctionValidOutput output = saasVipRpcService.checkFunctionValidResult(input);

        return Optional.ofNullable(output)
                .map(VipcheckFunctionValidOutput::isValidFlag)
                .filter(Objects::nonNull)
                .orElse(false);
    }

    @Override
    public void addTasks(SaasTaskAddInput input) {
        saasTaskRpcService.addTask(input);
    }

    @Override
    public Boolean existExecutingTaskByAccountIdAndType(String accountId, Integer taskType) {
        return saasTaskRpcService.existExecutingTaskByAccountIdAndType(accountId, taskType);
    }

    @Override
    public void updateTask(SaasTaskUpdateInput input) {
        saasTaskRpcService.updateTask(input);
    }

    @Override
    public void addSubTask(SubTaskAddBean subTaskAddBean) {
        saasTaskRpcService.addSubTask(subTaskAddBean);
    }

    @Override
    public SaasTaskResultOutput queryResultByBizId(
            String bizId, Integer taskType, String accountId) {
        return saasTaskRpcService.queryResultByBizId(bizId, taskType, accountId);
    }

    @Override
    public Map<String, String> taskAddSubTask(SaasTaskBatchAddSubTaskInput batchAddInput) {
        return saasTaskRpcService.taskAddSubTask(batchAddInput).getBizIdUUidMap();
    }

    @Override
    public SaasTaskInfoOutput querySaasTask(String bizId, Integer taskType) {
        return saasTaskRpcService.queryByBizId(bizId, taskType);
    }

    @Override
    public WatermarkTemplatePageOutput listWatermarkTemplates(WatermarkTemplatePageInput input) {
        return watermarkTemplateRpcService.listWatermarkTemplates(input);
    }

    @Override
    public String saveWatermarkTemplate(SaveWatermarkTemplateInput input) {
        return watermarkTemplateRpcService.saveWatermarkTemplate(input).getWatermarkId();
    }

    @Override
    public void changeWatermarkTemplateStatus(ChangeWatermarkTemplateStatusInput input) {
        watermarkTemplateRpcService.changeWatermarkTemplateStatus(input);
    }

    @Override
    public WatermarkTemplateDetailOutput getWatermarkTemplateDetail(
            WatermarkTemplateDetailInput input) {
        return watermarkTemplateRpcService.getWatermarkTemplateDetail(input);
    }

    @Override
    public WatermarkTemplateViewImageOutput viewWatermarkImage(
            WatermarkTemplateViewImageInput input) {
        return watermarkTemplateRpcService.viewWatermarkImage(input);
    }

    @Override
    public WatermarkTemplatePreviewOutput getPreviewWatermarkImageUrl(
            WatermarkTemplatePreviewInput input) {
        return watermarkTemplateRpcService.getPreviewWatermarkImageUrl(input);
    }

    @Override
    public GenerateWatermarkSnapShootOutput generateWatermarkSnapShoot(
            GenerateWatermarkSnapShootInput input) {
        return watermarkTemplateRpcService.generateWatermarkSnapShoot(input);
    }

    @Override
    public GetSaasBizConfigsOutput getBizConfigs(GetSaasBizConfigsInput input) {
        return saasBizConfigRpcService.getBizConfigs(input);
    }

    @Override
    public ChangeSaasBizConfigsOutput changeBizConfigs(ChangeSaasBizConfigsInput input) {
        return saasBizConfigRpcService.changeBizConfigs(input);
    }

    @Override
    public QueryEffectiveAuthRelationByParentTenantGidOutput
            queryEffectiveAuthRelationListByTenantGid(
                    QueryEffectiveAuthRelationByParentTenantGidInput input) {
        if(StringUtils.isEmpty(input.getParentTenantGid())){
            return new QueryEffectiveAuthRelationByParentTenantGidOutput();
        }
        return authRelationRpcService.queryEffectiveAuthRelationListByParentTenantGid(input);
    }

    @Override
    public EffectiveAuthRelationByChildTenantGidOutput effectiveAuthRelationByChildTenantGid(EffectiveAuthRelationByChildTenantGidInput input) {
        return authRelationRpcService.effectiveAuthRelationByChildTenantGid(input);
    }

    @Override
    public boolean hasAuthRelation(String tenantGid) {
        Boolean hasAuthRelation = authRelationRpcService.hasAuthRelation(tenantGid);
        return hasAuthRelation != null && hasAuthRelation;
    }

    @Override
    public GetFunctionIllustrateInfoOutput getFunctionIllustrateInfo(String functionCode) {
        return saasFunctionIllustrateRpcService.getFunctionIllustrateInfo(functionCode);
    }

    @Override
    public ApplyTrialOutput applyTrialFunctionCode(ApplyTrialInput applyTrialInput) {
        return saasTrialRpcService.applyTrial(applyTrialInput);
    }

    @Override
    public QueryApplyLogOutput queryApplyLog(QueryApplyLogInput queryApplyLogInput) {
        return saasTrialRpcService.queryApplyLog(queryApplyLogInput);
    }

    @Override
    public BillIsolateDTO getBillIsolateInfo(String clientId,String gid) {
        GetBillIsolateInfoOutput output=saasBillingRpcService.getBillIsolateInfo(new GetBillIsolateInfoInput(clientId,gid));
        return output==null?null:output.getBillIsolate();
    }

    public List<BillIsolateDTO> getAllBillIsolateInfo(){
        GetAllBillIsolateInfoOutput output=saasBillingRpcService.getAllBillIsolateInfo();
        return output==null?null:output.getBillIsolateList();
    }

    public GetSaasCommoditiesCategoryOutput getSaasCommoditiesCategory(
            String clientId, String tenantId, String showCaseNo) {
        GetSaasCommoditiesCategoryInput input = new GetSaasCommoditiesCategoryInput();
        input.setClientId(clientId);
        input.setTenantId(tenantId);
        input.setShowCaseNo(showCaseNo);
        return saasBillingRpcService.getSaasCommoditiesCategory(input);
    }

    @Override
    public BillIsolateDTO getBillIsolateFromClientId(String clientId,String gid) {
        GetBillIsolateInfoInput input = new GetBillIsolateInfoInput(clientId,gid);
        GetBillIsolateInfoOutput output = saasBillingRpcService.getBillIsolateInfo(input);
        //已配置的不管了，后加的直接给默认
        return output == null ? BillIsolateDTO.generateDefault() : output.getBillIsolate();
    }

    @Override
    public boolean saveOrgTrialFunctionLog(SaveOrgTrialLogInput input) {
        return saasTrialRpcService.saveOrgTrialFunctionLog(input).isSaveSuccess();
    }
}
