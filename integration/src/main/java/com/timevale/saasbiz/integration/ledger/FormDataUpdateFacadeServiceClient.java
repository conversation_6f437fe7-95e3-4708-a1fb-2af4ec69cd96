package com.timevale.saasbiz.integration.ledger;

import com.timevale.contractanalysis.facade.api.ao.UpdateFormDataRequest;
import com.timevale.contractanalysis.facade.api.bo.FormDataUpdateResult;

/**
 * FormDataUpdateFacadeServiceClient
 *
 * <AUTHOR>
 * @since 2023/9/5 3:39 下午
 */
public interface FormDataUpdateFacadeServiceClient {

    /**
     * 更新一行表单数据
     * @param request
     * @return
     */
    FormDataUpdateResult updateFormData(UpdateFormDataRequest request);

}
