package com.timevale.saasbiz.integration.contractanalysis.impl;

import com.timevale.contractanalysis.facade.api.ao.compare.AddCompareRequest;
import com.timevale.contractanalysis.facade.api.ao.compare.CompareIdRequest;
import com.timevale.contractanalysis.facade.api.ao.compare.QueryCompareRequest;
import com.timevale.contractanalysis.facade.api.bo.compare.AddCompareResult;
import com.timevale.contractanalysis.facade.api.bo.compare.CompareFileDownloadUrlResult;
import com.timevale.contractanalysis.facade.api.bo.compare.CompareRecordsResult;
import com.timevale.contractanalysis.facade.api.compare.FileCompareFacadeService;
import com.timevale.saasbiz.integration.contractanalysis.FileCompareClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO 功能说明
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Service
public class FileCompareClientImpl implements FileCompareClient {
    @Autowired FileCompareFacadeService fileCompareFacadeService;

    @Override
    public String addCompare(AddCompareRequest request) {
        AddCompareResult result = fileCompareFacadeService.addCompare(request);
        return result == null ? null : result.getRecordId();
    }

    @Override
    public void retryCompare(String compareId) {
        CompareIdRequest request = new CompareIdRequest();
        request.setRecordId(compareId);
        fileCompareFacadeService.retryCompare(request);
    }

    @Override
    public CompareFileDownloadUrlResult getCompareDownloadUrl(String compareId) {
        return fileCompareFacadeService.getCompareDownloadUrl(new CompareIdRequest(compareId));
    }

    @Override
    public CompareRecordsResult queryCompare(List<String> compareIdList) {
        QueryCompareRequest request = new QueryCompareRequest();
        request.setRecordIds(compareIdList);
        return fileCompareFacadeService.queryCompare(request);
    }
}
