package com.timevale.saasbiz.integration.fulfillment;

import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleQueryListModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleSaveModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleUpdateModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleDetailResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleListResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleTypeListResult;

import java.util.List;

/**
 * ContractFulfillmentClient
 *
 * <AUTHOR>
 * @since 2023/10/13 5:21 下午
 */
public interface ContractFulfillmentClient {

    String saveRule(ContractFulfillmentRuleSaveModel model);

    void updateRule(ContractFulfillmentRuleUpdateModel model);

    void deleteRule(String ruleId, String tenantId, String accountId);

    void updateRuleStatus(String ruleId, String status, String tenantId, String accountId);

    ContractFulfillmentRuleDetailResult getRuleDetail(String ruleId, String tenantId);

    ContractFulfillmentRuleListResult pageRuleList(ContractFulfillmentRuleQueryListModel model);

    void batchUpdateStatus(String tenantId, String accountId, List<String> recordIdList, String status);

    ContractFulfillmentRuleTypeListResult typeList(String tenantId);
}
