package com.timevale.saasbiz.integration.approval.impl;

import com.timevale.account.flow.service.api.RpcFlowDefineService;
import com.timevale.account.flow.service.model.input.FlowModelListInput;
import com.timevale.account.flow.service.model.output.FlowModelListOutput;
import com.timevale.contractapproval.facade.api.ApprovalTemplateRpcService;
import com.timevale.contractapproval.facade.dto.ApprovalTemplateDTO;
import com.timevale.contractapproval.facade.dto.ApprovalTemplateDetailDTO;
import com.timevale.contractapproval.facade.input.*;
import com.timevale.contractapproval.facade.output.*;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.page.Pages;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.integration.approval.ApprovalTemplateClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/3 10:49
 */
@Component
public class ApprovalTemplateClientImpl implements ApprovalTemplateClient {

    @Autowired
    private ApprovalTemplateRpcService approvalTemplateRpcService;

    @Autowired private RpcFlowDefineService rpcFlowDefineService;

    @Override
    public ApprovalTemplateCreateOutput createApprovalTemplate(ApprovalTemplateCreateInput input) {
        return approvalTemplateRpcService.createApprovalTemplate(input);
    }

    @Override
    public ApprovalTemplateUpdateOutput updateApprovalTemplate(ApprovalTemplateUpdateInput input) {
        return approvalTemplateRpcService.updateApprovalTemplate(input);
    }

    @Override
    public ApprovalTemplateCopyOutput copyApprovalTemplate(ApprovalTemplateCopyInput input) {
        return approvalTemplateRpcService.copyApprovalTemplate(input);
    }

    @Override
    public ApprovalTemplateOpenOutput openApprovalTemplate(ApprovalTemplateOpenInput input) {
        return approvalTemplateRpcService.openApprovalTemplate(input);
    }

    @Override
    public ApprovalTemplateCloseOutput closeApprovalTemplate(ApprovalTemplateCloseInput input) {
        return approvalTemplateRpcService.closeApprovalTemplate(input);
    }

    @Override
    public ApprovalTemplateDeleteOutput deleteApprovalTemplate(ApprovalTemplateDeleteInput input) {
        return approvalTemplateRpcService.deleteApprovalTemplate(input);
    }

    @Override
    public ApprovalTemplateDetailDTO getApprovalTemplateDetail(String approvalTemplateCode) {
        GetApprovalTemplateDetailInput input = new GetApprovalTemplateDetailInput();
        input.setApprovalTemplateCode(approvalTemplateCode);
        return approvalTemplateRpcService.getApprovalTemplateDetail(input);
    }

    @Override
    public ApprovalTemplateDTO getApprovalTemplate(String approvalTemplateCode) {
        GetApprovalTemplateInput input = new GetApprovalTemplateInput();
        input.setApprovalTemplateCode(approvalTemplateCode);
        return approvalTemplateRpcService.getApprovalTemplate(input);
    }

    @Override
    public ApprovalTemplateQueryExistConditionOutput queryExistCondition(ApprovalTemplateQueryExistConditionInput input) {
        return approvalTemplateRpcService.queryExistCondition(input);
    }

    @Override
    public ApprovalTemplateDTO getApprovalTemplateByCondition(GetApprovalTemplateByConditionInput input) {
        return approvalTemplateRpcService.getApprovalTemplateByCondition(input);
    }


    @Override
    public List<ApprovalTemplateDTO>
    queryMatchConditionApprovalTemplate(ApprovalTemplateQueryMatchConditionInput input) {
        return approvalTemplateRpcService.queryMatchConditionApprovalTemplate(input);
    }

    @Override
    public void approvalTemplateDeleteCondition(ApprovalTemplateDeleteConditionInput input) {
        approvalTemplateRpcService.approvalTemplateDeleteCondition(input);
    }

    @Override
    public Map<String, ApprovalTemplateDTO> batchGetApprovalTemplate(Set<String> approvalTemplateCodeList) {
        Map<String, ApprovalTemplateDTO> result = new HashMap<>();
        BatchGetApprovalTemplateInput input = new BatchGetApprovalTemplateInput();
        input.setApprovalTemplateCodeList(approvalTemplateCodeList);
        BatchGetApprovalTemplateOutput output =
                approvalTemplateRpcService.batchGetApprovalTemplate(input);
        if (output != null && CollectionUtils.isNotEmpty(output.getApprovalTemplateList())) {
            result =
                    output.getApprovalTemplateList().stream()
                            .sorted(Comparator.comparing(ApprovalTemplateDTO::getDeleted))
                            .collect(
                                    Collectors.toMap(
                                            ApprovalTemplateDTO::getApprovalTemplateCode,
                                            Function.identity(),
                                            (v1, v2) -> v1));
        }
        return result;
    }

    @Override
    public boolean hasOrgApproveTemplate(String orgId) {
        RpcInput<FlowModelListInput> rpcInput = new RpcInput<>();
        FlowModelListInput input = new FlowModelListInput();
        input.setOrganId(orgId);
        Pages pages = new Pages(0, 1);
        input.setPages(pages);
        rpcInput.setInput(input);
        RpcOutput<PagerResult<FlowModelListOutput>> rpcOutput = rpcFlowDefineService.getFlowDefineByOrgan(rpcInput);
        return Optional.of(rpcOutput.getData())
                .map(PagerResult::getItems)
                .map(list -> list.size() > 0)
                .orElse(false);
    }

    @Override
    public ApprovalTemplateListOutput listApprovalTemplate(ApprovalTemplateListInput input) {
        return approvalTemplateRpcService.listApprovalTemplate(input);
    }

    @Override
    public ApprovalTemplateBatchQueryMatchConditionOutput batchQueryMatchConditionApprovalTemplate(ApprovalTemplateBatchQueryMatchConditionInput input) {
        return approvalTemplateRpcService.batchQueryMatchConditionApprovalTemplate(input);
    }
}
