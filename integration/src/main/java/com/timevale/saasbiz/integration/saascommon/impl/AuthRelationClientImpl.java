package com.timevale.saasbiz.integration.saascommon.impl;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.base.util.ExceptionLogUtil;
import com.timevale.saas.common.manage.common.service.api.AuthRelationRpcService;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizSceneEnum;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.*;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.*;
import com.timevale.saasbiz.integration.saascommon.AuthRelationClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * AuthRelationClientImpl
 *
 * <AUTHOR>
 * @since 2023/4/3 11:37 上午
 */
@Slf4j
@Component
public class AuthRelationClientImpl implements AuthRelationClient {


    @Autowired
    private AuthRelationRpcService authRelationRpcService;

    @Override
    public GetAuthRelationShareConfigOutput getAuthRelationShareConfig(String tenantGid, String configKey) {
        GetAuthRelationShareConfigInput input = new GetAuthRelationShareConfigInput();
        input.setChildTenantGid(tenantGid);
        input.setConfigKey(configKey);
        try {
            return authRelationRpcService.getAuthRelationShareConfig(input);
        } catch (Exception e) {
            ExceptionLogUtil.traceLog(log, e, "getAuthRelationShareConfig failed, tenantGid:{}, configKey:{}", tenantGid, configKey);
            return GetAuthRelationShareConfigOutput.unEffective();
        }
    }

    @Override
    public AuthRelationDTO getEffectiveAuthRelationByChildTenantGid(String childTenantGid) {

        GetEffectiveAuthRelationByChildTenantGidInput input = new GetEffectiveAuthRelationByChildTenantGidInput();
        input.setChildTenantGid(childTenantGid);
        input.setBizScene(AuthRelationBizSceneEnum.DEFAULT.getCode());
        return authRelationRpcService.getEffectiveAuthRelationByChildTenantGid(input);
    }

    @Override
    public SearchAuthRelationLastEffectiveTimeOutput searchAuthRelationLastEffectiveTime(SearchAuthRelationLastEffectiveTimeInput input) {
        return authRelationRpcService.searchAuthRelationLastEffectiveTime(input);
    }

    @Override
    public PageAuthRelationLastEffectiveTimeOutput pageAuthRelationLastEffectiveTime(PageAuthRelationLastEffectiveTimeInput input) {
        return authRelationRpcService.pageAuthRelationLastEffectiveTime(input);
    }

    @Override
    public PageEffectiveAuthRelationOutput pageEffectiveAuthRelation(PageEffectiveAuthRelationInput input) {
        return authRelationRpcService.pageEffectiveAuthRelation(input);
    }

    @Override
    public List<AuthRelationDTO> historyOrNowEffectiveAuthRelationByChildTenantGid(String childTenantGid) {
        if (StringUtils.isBlank(childTenantGid)) {
            return new ArrayList<>();
        }
        HistoryOrNowEffectiveAuthRelationByChildTenantGidInput input = new HistoryOrNowEffectiveAuthRelationByChildTenantGidInput();
        input.setBizScene(AuthRelationBizSceneEnum.CONTRACT_MANAGE.getCode());
        input.setChildTenantGidList(Lists.newArrayList(childTenantGid));
        List<HistoryEffectiveAuthRelationByChildTenantGidDTO> list =
                authRelationRpcService.historyOrNowEffectiveAuthRelationByChildTenantGid(input).getMixAuthRelationList();
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.get(0).getAuthRelationList();
    }

    @Override
    public List<AuthRelationDTO> getEffectiveAndUnEffectiveAuthRelationByChildTenantGid(String childTenantGid) {
        return authRelationRpcService.getEffectiveAndUnEffectiveAuthRelationByChildTenantGid(childTenantGid);
    }

    @Override
    public Boolean checkTenantWasParent(String tenantGid) {
        PageAuthRelationLastEffectiveTimeInput input = new PageAuthRelationLastEffectiveTimeInput();
        input.setPageIndex(1);
        input.setPageSize(1);
        input.setTenantGid(tenantGid);
        input.setBizScene(AuthRelationBizSceneEnum.CONTRACT_MANAGE.getCode());
        PageAuthRelationLastEffectiveTimeOutput output = authRelationRpcService.pageAuthRelationLastEffectiveTime(input);
        if (output == null || CollectionUtils.isEmpty(output.getList())) {
            return false;
        }
        return true;
    }

    @Override
    public void checkHeadOfficeAndAuthResources(CheckHeadOfficeAndAuthResourcesInput input) {
        authRelationRpcService.checkHeadOfficeAndAuthResources(input);
    }

    @Override
    public List<AuthRelationDTO> queryEffectiveAuthRelationListByParentTenantGid(
            QueryEffectiveAuthRelationByParentTenantGidInput input) {
        if(StringUtils.isEmpty(input.getParentTenantGid())){
            return new ArrayList<>();
        }
        return authRelationRpcService
                .queryEffectiveAuthRelationListByParentTenantGid(input)
                .getAuthRelationList();
    }
}
