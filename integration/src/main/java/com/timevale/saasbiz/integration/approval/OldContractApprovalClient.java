package com.timevale.saasbiz.integration.approval;

import com.timevale.account.flow.service.model.approval.model.ApprovalInfoModel;
import com.timevale.account.flow.service.model.input.FlowModelMemberInput;
import com.timevale.account.flow.service.model.output.FlowModelMemberOutput;
import com.timevale.account.flow.service.model.output.FlowModelOutput;
import com.timevale.flow.facade.service.model.input.adapt.ApprovalAgreeAdaptInput;
import com.timevale.flow.facade.service.model.input.adapt.ApprovalRefuseAdaptInput;
import com.timevale.flow.facade.service.model.input.adapt.ApprovalUrgeAdaptInput;
import com.timevale.flow.facade.service.model.input.adapt.ContractApprovalRestartAdaptInput;
import com.timevale.flow.facade.service.model.output.adapt.ContractApprovalApprovalUrlAdaptOutput;
import com.timevale.flow.facade.service.model.output.adapt.ContractApprovalFlowAdaptOutput;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/14 16:46
 * 老合同审批
 */
public interface OldContractApprovalClient {

    /**
     * 查询可用审批流模版
     */
    List<FlowModelMemberOutput> availableApprovalTemplate(FlowModelMemberInput flowModelMemberInput);

    /**
     * 查询审批模版定义
     * 用处： 提起查看审批流使用
     */
    FlowModelOutput getFlowDefineDetailById(String organId, String approvalTemplateId);

    /**
     * 查询审批流
     */
    ContractApprovalFlowAdaptOutput approvalFlow(Long approvalId, String orgId, String accountId);

    /**
     * 重新发起
     */
    void restart(ContractApprovalRestartAdaptInput input);

    /**
     * 同意
     */
    void agree(ApprovalAgreeAdaptInput model);

    /**
     * 批量同意
     */
    void approvalGroupAgree(String approvalGroupId, String orgId, String accountId, String appId);

    /**
     * 拒绝
     */
    void refuse(ApprovalRefuseAdaptInput input);

    /**
     * 组拒绝
     */
    void approvalGroupRefuse(
            String approvalGroupId, String orgId, String accountId, String approvalRemark,String appId);


    /**
     * 撤回
     */
    void revoke(Long approvalId, String orgId, String accountId, String approvalRemark, String appId);


    /**
     * 审批链接
     * "客户端类型，PC|H5"
     */
    ContractApprovalApprovalUrlAdaptOutput getApprovalUrl(
            Long approvalId,
            String orgId,
            String accountId,
            String token,
            String clientType,
            Boolean isNeedShare);

    /**
     * 催办
     */
    void urge(ApprovalUrgeAdaptInput input);

    /**
     * 查询流程详情
     */
    ApprovalInfoModel getApprovalInfo(Long approvalId);

}
