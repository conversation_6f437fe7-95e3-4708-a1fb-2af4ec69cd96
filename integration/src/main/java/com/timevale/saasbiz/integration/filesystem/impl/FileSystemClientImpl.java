package com.timevale.saasbiz.integration.filesystem.impl;

import com.timevale.filesystem.common.service.api.FileSystemService;
import com.timevale.filesystem.common.service.query.BaseInput;
import com.timevale.filesystem.common.service.query.GetDownloadUrlInput;
import com.timevale.filesystem.common.service.query.GetSignUrlInput;
import com.timevale.filesystem.common.service.result.GetDownloadUrlResult;
import com.timevale.filesystem.common.service.result.GetFileInfoResult;
import com.timevale.filesystem.common.service.result.GetSignUrlResult;
import com.timevale.saasbiz.integration.filesystem.FileSystemClient;
import com.timevale.saasbiz.model.enums.AppConfigEnum;
import com.timevale.saasbiz.model.utils.AppConfigUtil;
import com.timevale.saasbiz.model.utils.RemoteHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * FileSystemClientImpl
 *
 * <AUTHOR>
 * @since 2023/4/3 2:06 下午
 */
@Slf4j
@Service
public class FileSystemClientImpl implements FileSystemClient {
    /** 下载地址过期时长 */
    private static final int downloadUrlExpire = 24 * 60 * 60 * 1000;
    private static final String STREAM_OCTET = "application/octet-stream";

    @Autowired private FileSystemService fileSystemService;

    @Override
    public String getDownloadUrl(String fileKey, boolean internal, long expireTime) {
        GetDownloadUrlInput getDownloadUrlInput = new GetDownloadUrlInput();
        getDownloadUrlInput.setFileKey(fileKey);
        getDownloadUrlInput.setInternal(internal);
        getDownloadUrlInput.setExpire(expireTime);
        return getDownloadUrl(getDownloadUrlInput);
    }

    @Override
    public String getDownloadUrl(String fileKey, boolean internal) {
        return getDownloadUrl(fileKey, internal, downloadUrlExpire);
    }

    @Override
    public String getDownloadUrl(GetDownloadUrlInput getDownloadUrlInput) {
        if (getDownloadUrlInput.getExpire() <= 0) {
            getDownloadUrlInput.setExpire(downloadUrlExpire);
        }
        GetDownloadUrlResult downloadUrl = fileSystemService.getDownloadUrl(getDownloadUrlInput);
        return downloadUrl.getUrl();
    }

    @Override
    public byte[] downloadFile(String fileKey) {
        // 内部使用url
        String downloadUrl = getDownloadUrl(fileKey, true);
        return RemoteHttpUtil.executeGetMethodAsBytes(downloadUrl);
    }

    @Override
    public GetFileInfoResult getFileInfo(String fileKey) {
        BaseInput input = new BaseInput();
        input.setFileKey(fileKey);

        return fileSystemService.getFileInfo(input);
    }

    @Override
    public String uploadFileAndGetDownloadUrl(byte[] data, String fileName) {

        long startTime = System.currentTimeMillis();
        GetSignUrlResult result = getUploadUrl(fileName, true);
        RemoteHttpUtil.sendBytesToUrl(result.getUrl(), data, STREAM_OCTET, null);
        long endTime = System.currentTimeMillis();
        log.info(" 上传pdf花费时间：{} ms", (endTime - startTime));

        return getDownloadUrl(result.getFileKey(), false);
    }

    @Override
    public String uploadFileReturnFileKey(byte[] data, String fileName) {

        long startTime = System.currentTimeMillis();
        GetSignUrlResult result = getUploadUrl(fileName, true);
        RemoteHttpUtil.sendBytesToUrl(result.getUrl(), data, STREAM_OCTET, null);
        long endTime = System.currentTimeMillis();
        log.info(" 上传pdf花费时间：{} ms", (endTime - startTime));

        return result.getFileKey();
    }

    @Override
    public GetSignUrlResult getUploadUrl(String fileName, boolean internal) {
        GetSignUrlInput signUrlInput = new GetSignUrlInput();
        signUrlInput.setExpire(downloadUrlExpire);
        signUrlInput.setContentType(STREAM_OCTET);
        signUrlInput.setFileName(fileName);
        signUrlInput.setProjectId(AppConfigUtil.getString(AppConfigEnum.SAAS_PROJECT_ID));
        return fileSystemService.getSignUrl(signUrlInput);
    }
}
