package com.timevale.saasbiz.integration.fulfillment.impl;

import com.timevale.saasbiz.integration.fulfillment.RpcContractFulfillmentSearchServiceClient;
import com.timevale.signflow.search.service.api.v2.RpcContractFulfillmentSearchService;
import com.timevale.signflow.search.service.model.v2.contractfulfillment.ContractFulfillmentQueryModel;
import com.timevale.signflow.search.service.result.v2.contractfulfillment.ContractFulfillmentQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * RpcContractFulfillmentSearchServiceClientImpl
 *
 * <AUTHOR>
 * @since 2023/10/26 10:54 上午
 */
@Service
public class RpcContractFulfillmentSearchServiceClientImpl implements RpcContractFulfillmentSearchServiceClient {

    @Autowired
    private RpcContractFulfillmentSearchService fulfillmentSearchService;

    @Override
    public ContractFulfillmentQueryResult pageQuery(ContractFulfillmentQueryModel model) {
        return fulfillmentSearchService.pageQueryFulfillment(model);
    }
}
