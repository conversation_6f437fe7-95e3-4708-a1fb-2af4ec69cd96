package com.timevale.saasbiz.integration.contractanalysis;

import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcContractAuditRecordPageQueryInput;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcContractAuditRecordPageQueryOutput;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateAuditResultInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateAuditResultOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateContractAuditWebInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateContractAuditWebOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditListsOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditResultRuleInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditResultRuleOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditRuleTreeInputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditRuleTreeOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.bean.RpcContractAuditRecordDTO;

/**
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
public interface ContractAuditClient {

    /**
     * 创建合同审查
     * @param input
     * @return
     */
    RpcCreateContractAuditWebOutputDTO createContractAudit(RpcCreateContractAuditWebInputDTO input);

    /**
     * 分页查询合同审查记录
     * @param input
     * @return
     */
    RpcContractAuditRecordPageQueryOutput contractAuditPageRecord(RpcContractAuditRecordPageQueryInput input);

    /**
     * 获取合同审查 web URL
     * @param recordId 审查记录 ID
     * @return
     */
    String getContractAuditWebUrl(String recordId, String operatorGid, String tenantGid);

    /**
     * 删除合同审查记录
     * @param recordId
     * @param gid
     */
    void deleteContractAuditRecord(String recordId, String gid, String tenantGid);

    RpcQueryAuditListsOutputDTO queryAuditRuleLists(String operatorGid, String tenantGid);

    RpcContractAuditRecordDTO queryEmbedContractAuditRecord(String processId, String fileId, String operatorGid, String tenantGid);

    RpcCreateAuditResultOutputDTO createAuditResult(RpcCreateAuditResultInputDTO inputDTO);

    RpcQueryAuditRuleTreeOutputDTO queryAuditRuleTree(RpcQueryAuditRuleTreeInputDTO input);

    RpcQueryAuditResultRuleOutputDTO queryAuditRuleResult(RpcQueryAuditResultRuleInputDTO inputDTO);
}
