package com.timevale.saasbiz.integration.bizconfig.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.property.core.facade.api.PropertyTransferFacade;
import com.timevale.property.core.facade.bean.base.Response;
import com.timevale.property.core.facade.bean.request.transfer.PropertyTransferRequest;
import com.timevale.property.core.facade.bean.response.function.PropertyValueResponse;
import com.timevale.saasbiz.integration.bizconfig.BizConfigClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/2/27 19:49
 */
@Slf4j
@Component
public class BizConfigClientImpl implements BizConfigClient {

    @Autowired
    private PropertyTransferFacade propertyTransferFacade;

    @Override
    public List<PropertyValueResponse> getPropertiesByRouting(PropertyTransferRequest request) {
        Response<List<PropertyValueResponse>> response = propertyTransferFacade.getPropertiesByRouting(request);
        if (!response.isSuccess()) {
            log.error("getPropertiesByRouting req : {} res : {}", JSON.toJSONString(request), JSON.toJSONString(response));
            throw new BizContractManagerException(BizContractManagerResultCodeEnum.SERVICE_BIZ_ERROR.getCode(), response.getMessage());
        }
        return Optional.ofNullable(response.getData()).orElse(new ArrayList<>());
    }

    @Override
    public Map<String, String> getPropertiesMapByRouting(PropertyTransferRequest request) {
        List<PropertyValueResponse> keyValueList = getPropertiesByRouting(request);
        Map<String, String> keyValueMap = new HashMap<>();
        for (PropertyValueResponse response : keyValueList) {
            if (StringUtils.isBlank(response.getPropertyKey()) || StringUtils.isBlank(response.getPropertyValue())) {
                continue;
            }
            keyValueMap.put(response.getPropertyKey(), response.getPropertyValue());
        }
        return keyValueMap;
    }

}
