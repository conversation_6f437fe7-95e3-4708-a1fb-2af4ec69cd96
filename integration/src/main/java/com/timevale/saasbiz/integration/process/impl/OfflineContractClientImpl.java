package com.timevale.saasbiz.integration.process.impl;

import com.timevale.contractmanager.common.service.api.RpcOfflineContractService;
import com.timevale.contractmanager.common.service.model.offlinecontract.*;
import com.timevale.contractmanager.common.service.result.offlinecontract.BatchQueryOfflineContractRecordInfoResult;
import com.timevale.contractmanager.common.service.result.offlinecontract.QueryExistsCanRetryImportProcessResult;
import com.timevale.contractmanager.common.service.result.offlinecontract.QueryOfflineContractRecordContractsResult;
import com.timevale.contractmanager.common.service.result.offlinecontract.QueryOfflineContractRecordInfoResult;
import com.timevale.contractmanager.common.service.result.offlinecontract.QueryOfflineContractRecordsResult;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.saasbiz.integration.process.OfflineContractClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 线下合同
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Component
public class OfflineContractClientImpl implements OfflineContractClient {

    @Autowired RpcOfflineContractService rpcOfflineContractService;

    @Override
    public String importOfflineContractRecord(SaveOfflineContractRecordModel model) {
        return rpcOfflineContractService.saveOfflineContractRecord(model).getRecordId();
    }

    @Override
    public QueryOfflineContractRecordsResult queryOfflineContractRecords(
            QueryOfflineContractRecordsModel model) {
        return rpcOfflineContractService.queryOfflineContractRecords(model);
    }

    @Override
    public QueryOfflineContractRecordInfoResult queryOfflineContractRecordInfo(
            QueryOfflineContractRecordInfoModel model) {
        return rpcOfflineContractService.queryOfflineContractRecordInfo(model);
    }

    @Override
    public BatchQueryOfflineContractRecordInfoResult batchQueryOfflineContractRecordInfo(
            BatchQueryOfflineContractRecordInfoModel model) {
        return rpcOfflineContractService.batchQueryOfflineContractRecordInfo(model);
    }

    @Override
    public QueryOfflineContractRecordContractsResult queryOfflineContractRecordContracts(
            QueryOfflineContractRecordContractsModel model) {
        return rpcOfflineContractService.queryOfflineContractRecordContracts(model);
    }

    @Override
    public void deleteOfflineContractRecord(DeleteOfflineContractRecordsModel model) {
         rpcOfflineContractService.deleteOfflineContractRecord(model);
    }

    @Override
    public void stopImportOfflineContractRecord(StopImportOfflineContractModel model) {
        rpcOfflineContractService.stopImportOfflineContractRecord(model);
    }

    @Override
    public void recoverImportOfflineContractRecord(RecoverImportOfflineContractModel model) {
        rpcOfflineContractService.recoverImportOfflineContractRecord(model);
    }

    @Override
    public void restartImportFailedOfflineContract(RestartImportFailedOfflineContractModel model) {
        rpcOfflineContractService.restartImportFailedOfflineContract(model);
    }

    @Override
    public boolean queryExistsArrearsImportProcess(String recordId, String tenantGid) {
        QueryExistsArrearsImportProcessModel model = new QueryExistsArrearsImportProcessModel();
        model.setRecordId(recordId);
        model.setTenantGid(tenantGid);
        QueryExistsCanRetryImportProcessResult res = rpcOfflineContractService.queryExistsArrearsImportProcess(model);
        return BooleanUtils.isTrue(res.getExists());
    }
}
