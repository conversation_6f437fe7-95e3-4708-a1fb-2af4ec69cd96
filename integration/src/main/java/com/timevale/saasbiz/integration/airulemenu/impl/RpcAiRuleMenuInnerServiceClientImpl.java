package com.timevale.saasbiz.integration.airulemenu.impl;

import com.timevale.contractmanager.common.service.api.RpcAiRuleMenuInnerService;
import com.timevale.contractmanager.common.service.model.airulemenu.AiRuleMenuQueryModel;
import com.timevale.contractmanager.common.service.result.autobind.AiRuleMenuResult;
import com.timevale.saasbiz.integration.airulemenu.RpcAiRuleMenuInnerServiceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RpcAiRuleMenuInnerServiceClientImpl implements RpcAiRuleMenuInnerServiceClient {

    @Autowired
    private RpcAiRuleMenuInnerService rpcAiRuleMenuInnerService;

    @Override
    public AiRuleMenuResult getByMenuId(String menuId, String tenantId) {
        AiRuleMenuQueryModel model = new AiRuleMenuQueryModel();
        model.setMenuId(menuId);
        model.setTenantOid(tenantId);
        return rpcAiRuleMenuInnerService.getByMenuId(model);
    }
}
