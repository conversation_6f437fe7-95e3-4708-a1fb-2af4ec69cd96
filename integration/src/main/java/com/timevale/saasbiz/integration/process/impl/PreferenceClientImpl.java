package com.timevale.saasbiz.integration.process.impl;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.api.RpcPreferenceService;
import com.timevale.contractmanager.common.service.bean.PreferenceModel;
import com.timevale.contractmanager.common.service.model.QueryProcessPreferenceModel;
import com.timevale.contractmanager.common.service.result.QueryProcessPreferenceResult;
import com.timevale.saasbiz.integration.process.PreferenceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同偏好
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
@Service
public class PreferenceClientImpl implements PreferenceClient {

    @Autowired private RpcPreferenceService rpcPreferenceService;

    @Override
    public List<PreferenceModel> queryPreference(List<String> orgGids, List<String> preferenceKeys) {
        QueryProcessPreferenceModel model = new QueryProcessPreferenceModel();
        model.setOrgGids(orgGids);
        model.setPreferenceKeys(preferenceKeys);
        QueryProcessPreferenceResult result = rpcPreferenceService.query(model);
        return result == null ? Lists.newArrayList() : result.getProcessPreference();
    }

}
