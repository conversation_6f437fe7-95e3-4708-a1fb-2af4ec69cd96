package com.timevale.saasbiz.integration.seal;

import com.timevale.footstone.seal.facade.saas.input.*;
import com.timevale.footstone.seal.facade.saas.input.visible.DeleteSealVisibleInput;
import com.timevale.footstone.seal.facade.saas.input.visible.QuerySealVisibleCountInput;
import com.timevale.footstone.seal.facade.saas.input.visible.QuerySealVisibleInput;
import com.timevale.footstone.seal.facade.saas.input.visible.SaveSealVisibleInput;
import com.timevale.footstone.seal.facade.saas.output.*;
import com.timevale.footstone.seal.facade.saas.output.visible.QuerySealVisibleCountOutput;
import com.timevale.footstone.seal.facade.saas.output.visible.SealVisibleAccountInfo;
import com.timevale.footstone.seal.facade.saas.page.ApiPageResult;

/**
 * 企业印章管理
 *
 * <AUTHOR>
 * @since 2023-05-26 10:41
 */
public interface SealOrganizationClient {

    /** 个人根据印章id查询印章 */
    SealCommonInfoOutput queryBySealIds(QuerySealPersonalBySealsInput input);

    /** 设置企业默认章 */
    void setDefaultSealV2(SetDefaultSealInput input);

    /** 查询印章详情 v2版本 */
    OrgSealDetailV2Output getOrgSealDetail(SealDetailV2Input input);

    /** 查询印章可见范围 */
    ApiPageResult<SealVisibleAccountInfo> pageSealVisible(QuerySealVisibleInput input);

    /** 设置印章可见范围 */
    void saveSealVisible(SaveSealVisibleInput input);

    /** 删除印章可见范围 */
    void deleteSealVisible(DeleteSealVisibleInput input);

    /** 查询印章可见数量 */
    QuerySealVisibleCountOutput querySealVisibleCount(QuerySealVisibleCountInput input);


    /**
     * 查询企业印章列表--从ES
     * @param input
     * @return
     */
    PageSealESOutput pageQuerySeals(PageSealESInput input);

    /**
     * 查询被授权印章列表
     * @param input
     * @return
     */
    SealWithGrantPageOutput queryGrantedSealsFromES(PageGrantedSealsESInput input);


    /**
     * 查询被授权印章的归属企业
     * @param input
     * @return
     */
    QueryGrantedSealOwnersOutput queryGrantedSealOwnersFromES(QueryGrantedSealOwnersInput input);

    /**
     * 查询被授权企业列表
     *
     * @param input
     * @return
     */
    GrantedSealsOutput queryGrantedSealsList(QueryGrantedSealsInput input);

    /**
     * 催办印章
     */
    void imageUrgeForAudit(String sealId);


    /**
     * 创建企业印章预检查
     *
     * @param input
     * @return
     */
    CreateOfficialSealPreCheckOutput createOfficialSealPreCheck(CreateOfficialSealPreCheckInput input);
}
