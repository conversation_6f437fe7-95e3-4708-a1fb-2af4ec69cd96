package com.timevale.saasbiz.integration.saascommon.impl;

import com.netflix.discovery.converters.Auto;
import com.timevale.saas.common.manage.common.service.api.AuditLogSubscriptionConfigRpcService;
import com.timevale.saas.common.manage.common.service.model.input.AuditLogSubscriptionConfigDetailInput;
import com.timevale.saas.common.manage.common.service.model.input.AuditLogSubscriptionConfigQueryInput;
import com.timevale.saas.common.manage.common.service.model.input.AuditLogSubscriptionConfigUpdateInput;
import com.timevale.saas.common.manage.common.service.model.output.AuditLogSubscriptionConfigOutput;
import com.timevale.saas.common.manage.common.service.model.output.AuditLogSubscriptionConfigUpdateOutput;
import com.timevale.saas.common.manage.common.service.model.output.bean.AuditLogSubscriptionConfigDTO;
import com.timevale.saasbiz.integration.saascommon.AuditLogSubscriptionConfigClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * AuditLogSubscriptionConfigClientImpl
 *
 * <AUTHOR>
 * @since 2025/2/20 下午5:19
 */
@Slf4j
@Component
public class AuditLogSubscriptionConfigClientImpl implements AuditLogSubscriptionConfigClient {

    @Autowired
    private AuditLogSubscriptionConfigRpcService configRpcService;

    @Override
    public AuditLogSubscriptionConfigOutput queryConfigList(AuditLogSubscriptionConfigQueryInput input) {
        return configRpcService.queryConfigList(input);
    }

    @Override
    public AuditLogSubscriptionConfigDTO getDetail(AuditLogSubscriptionConfigDetailInput input) {
        return configRpcService.getDetail(input);
    }

    @Override
    public AuditLogSubscriptionConfigUpdateOutput changeStatus(AuditLogSubscriptionConfigUpdateInput input) {
        return configRpcService.changeStatus(input);
    }

    @Override
    public AuditLogSubscriptionConfigUpdateOutput changeAccount(AuditLogSubscriptionConfigUpdateInput input) {
        return configRpcService.changeAccount(input);
    }
}
