package com.timevale.saasbiz.integration.contractanalysis;

import com.timevale.contractanalysis.facade.api.dto.compare.IntelligentCompareInputDTO;
import com.timevale.contractanalysis.facade.api.dto.compare.IntelligentCompareQueryStatusOutputDTO;

/**
 * 智能比对
 * 
 * <AUTHOR>
 * @date 2024-04-03
 */
public interface IntelligentCompareClient {

    /**
     * 创建比对
     * @param inputDTO
     * @return
     */
    String createCompare(IntelligentCompareInputDTO inputDTO);

    /**
     * 查询比对状态
     * @param compareId
     * @param operatorGid
     * @param tenantGid
     * @return
     */
    IntelligentCompareQueryStatusOutputDTO queryCompareStatus(String compareId, String operatorGid, String tenantGid);
}
