package com.timevale.saasbiz.integration.witness;

import com.timevale.signflow.search.service.model.v2.witness.QueryKeywordWitnessParam;
import com.timevale.signflow.search.service.model.v2.witness.QueryWitnessEvidenceParam;
import com.timevale.signflow.search.service.model.v2.witness.QueryWitnessParam;
import com.timevale.signflow.search.service.model.v2.witness.SaveWitnessInfoParam;
import com.timevale.signflow.search.service.result.v2.witness.WitnessQueryResult;

/**
 * <AUTHOR> on 2019/7/30
 */
public interface WitnessSearchClient {

    /**
     * 条件搜索, 企业合同管理页调用
     * @param param
     * @return
     */
    WitnessQueryResult query(QueryWitnessParam param);

    /**
     * 关键字搜索， 经办合同管理页调用
     * @param param
     * @return
     */
    WitnessQueryResult queryKeyword(QueryKeywordWitnessParam param);

    /**
     * 天印出证列表搜索
     * @param param
     * @return
     */
    WitnessQueryResult queryEvidence(QueryWitnessEvidenceParam param);

    /**
     * 保存索引
     * @param param
     * @return
     */
    void save(SaveWitnessInfoParam param);
}
