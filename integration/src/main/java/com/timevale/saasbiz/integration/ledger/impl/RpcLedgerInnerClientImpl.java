package com.timevale.saasbiz.integration.ledger.impl;

import com.timevale.contractanalysis.facade.api.ao.QueryNextFormDataIdRequest;
import com.timevale.contractanalysis.facade.api.bo.QueryNextFormDataIdResult;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecCloseTrialDoneConfirmInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecDetailOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecDetailQueryInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecLastTrialDoneInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecLastTrialDoneOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecUpdateResultInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExistsInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExistsOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFieldBatchSaveInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFieldDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormDataResultDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormListInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormListResultDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormUpdateAiFieldInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerTemplateStructMatchInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerTemplateStructMatchResultDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerViewFormDataInputDTO;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecResultStatusEnum;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecRunStatusEnum;
import com.timevale.contractanalysis.facade.api.form.FormDataReadFacadeService;
import com.timevale.contractanalysis.facade.api.ledger.LedgerInnerFacadeService;
import com.timevale.contractmanager.common.service.api.RpcLedgerInnerService;
import com.timevale.contractmanager.common.service.enums.AiLimitType;
import com.timevale.contractmanager.common.service.model.ledger.AiExtractLimitQueryModel;
import com.timevale.contractmanager.common.service.model.ledger.LedgerFormSaveModel;
import com.timevale.contractmanager.common.service.model.ledger.LedgerFormUpdateModel;
import com.timevale.contractmanager.common.service.result.ledger.AiExtractLimitResult;
import com.timevale.contractmanager.common.service.result.ledger.LedgerFormDetailResult;
import com.timevale.saasbiz.integration.ledger.RpcLedgerInnerClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * RpcLedgerInnerClientImpl
 *
 * <AUTHOR>
 * @since 2023/8/25 5:02 下午
 */
@Service
public class RpcLedgerInnerClientImpl implements RpcLedgerInnerClient {

    @Autowired
    private RpcLedgerInnerService rpcLedgerInnerService;

    @Autowired
    private LedgerInnerFacadeService ledgerInnerFacadeService;

    @Autowired
    private FormDataReadFacadeService formDataReadFacadeService;

    @Override
    public String saveForm(LedgerFormSaveModel input) {
       return rpcLedgerInnerService.saveForm(input).getFormId();
    }

    @Override
    public void updateForm(LedgerFormUpdateModel input) {
        rpcLedgerInnerService.updateForm(input);
    }

    @Override
    public void updateFormAiField(LedgerFormUpdateAiFieldInputDTO input) {
        ledgerInnerFacadeService.updateFormAiField(input);
    }

    @Override
    public void updateStatusNoCheck(String formId, String tenantId, String accountId, Integer status) {
        ledgerInnerFacadeService.updateStatusNoCheck(formId, tenantId, accountId, status);
    }

    @Override
    public void delete(String formId, String tenantId, String accountId) {
        rpcLedgerInnerService.delete(formId, tenantId, accountId);
    }

    @Override
    public LedgerFormDetailResult detail(String formId, String tenantId) {
        return rpcLedgerInnerService.detail(formId, tenantId);
    }

    @Override
    public boolean queryFormExists(String formId, String tenantGid) {
        LedgerExistsInputDTO inputDTO = new LedgerExistsInputDTO();
        inputDTO.setFormId(formId);
        inputDTO.setTenantGid(tenantGid);
        LedgerExistsOutputDTO output = ledgerInnerFacadeService.queryFormExists(inputDTO);
        return output.isExists();
    }

    @Override
    public LedgerFormListResultDTO pageFormList(LedgerFormListInputDTO input) {
        return ledgerInnerFacadeService.pageFormList(input);
    }

    @Override
    public LedgerTemplateStructMatchResultDTO matchTemplateStruct(String tenantId, List<String> templateIds) {
        LedgerTemplateStructMatchInputDTO ledgerTemplateStructMatchInputDTO = new LedgerTemplateStructMatchInputDTO();
        ledgerTemplateStructMatchInputDTO.setTemplateIds(templateIds);
        ledgerTemplateStructMatchInputDTO.setTenantId(tenantId);
        return ledgerInnerFacadeService.matchTemplateStruct(ledgerTemplateStructMatchInputDTO);
    }

    @Override
    public List<LedgerFieldDTO> batchSaveField(LedgerFieldBatchSaveInputDTO inputDTO) {
        return ledgerInnerFacadeService.batchSaveField(inputDTO);
    }

    @Override
    public void updateFormStatus(String tenantId, String accountId, String formId, Boolean switchOn) {
        ledgerInnerFacadeService.updateFormStatus(tenantId, accountId, formId, switchOn);
    }

    @Override
    public LedgerFormDataResultDTO getFormDataResult(LedgerViewFormDataInputDTO inputDTO) {
        return ledgerInnerFacadeService.getFormDataResult(inputDTO);
    }

    @Override
    public AiExtractLimitResult queryExtractLimit(String tenantId) {
        AiExtractLimitQueryModel queryModel = new AiExtractLimitQueryModel();
        queryModel.setTenantOid(tenantId);
        queryModel.setType(AiLimitType.LEDGER.getType());
        return rpcLedgerInnerService.queryExtractLimit(queryModel);
    }

    @Override
    public LedgerExecDetailOutputDTO queryExecDetail(String formId) {
        LedgerExecDetailQueryInputDTO inputDTO = new LedgerExecDetailQueryInputDTO();
        inputDTO.setFormId(formId);
        return ledgerInnerFacadeService.queryLedgerHistoryExecDetail(inputDTO);
    }

    @Override
    public LedgerExecLastTrialDoneOutputDTO queryExecLastTrialDone(String tenantGid) {
        LedgerExecLastTrialDoneInputDTO inputDTO = new LedgerExecLastTrialDoneInputDTO();
        inputDTO.setTenantGid(tenantGid);
        return ledgerInnerFacadeService.queryLastTrialDone(inputDTO);
    }

    @Override
    public void closeLedgerTrialDoneConfirm(String formId) {
        LedgerExecCloseTrialDoneConfirmInputDTO inputDTO = new LedgerExecCloseTrialDoneConfirmInputDTO();
        inputDTO.setFormId(formId);
        ledgerInnerFacadeService.closeFormTrialDoneConfirm(inputDTO);
    }

    @Override
    public String queryNextFormProcessId(String formId, String processId) {
        QueryNextFormDataIdRequest request = new QueryNextFormDataIdRequest();
        request.setFormId(formId);
        request.setProcessId(processId);
        QueryNextFormDataIdResult result = formDataReadFacadeService.getNextFormDataId(request);
        return result.getRowDataId();
    }

    @Override
    public void updateExecResult(String formId, String tenantId, String accountId, LedgerExecResultStatusEnum resultStatus) {
        LedgerExecUpdateResultInputDTO inputDTO = new LedgerExecUpdateResultInputDTO();
        inputDTO.setFormId(formId);
        inputDTO.setTenantId(tenantId);
        inputDTO.setAccountId(accountId);
        inputDTO.setRusultStatus(resultStatus == null ? null : resultStatus.getCode());
        ledgerInnerFacadeService.updateResult(inputDTO);
    }
}
