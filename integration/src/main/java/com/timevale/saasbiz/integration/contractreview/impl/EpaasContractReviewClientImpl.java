package com.timevale.saasbiz.integration.contractreview.impl;

import com.timevale.saasbiz.integration.contractreview.EpaasContractReviewClient;
import com.timevale.saasbiz.integration.aop.MethodException;
import com.timevale.sterna.contract.review.client.ReviewRecordManagementService;
import com.timevale.sterna.contract.review.client.ReviewPreCensorManagementService;
import com.timevale.sterna.contract.review.client.ReviewRuleGroupManagementService;
import com.timevale.sterna.contract.review.client.ReviewRuleInventoryManagementService;
import com.timevale.sterna.contract.review.client.ReviewRuleManagementService;
import com.timevale.sterna.contract.review.client.dto.req.ReviewPreCensorReq;
import com.timevale.sterna.contract.review.client.dto.req.ReviewRuleGroupReq;
import com.timevale.sterna.contract.review.client.dto.req.ReviewRuleReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewHistoryPageReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewRecordIdReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewTaskStopReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewToolsTriggerReq;
import com.timevale.sterna.contract.review.client.dto.req.rule.*;
import com.timevale.sterna.contract.review.client.dto.req.ruleInventory.*;
import com.timevale.sterna.contract.review.client.dto.req.rulegroup.RuleGroupDelReq;
import com.timevale.sterna.contract.review.client.dto.req.rulegroup.RuleGroupListReq;
import com.timevale.sterna.contract.review.client.dto.res.*;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewHistoryPageResp;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewInfoResp;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewResultResp;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewResultRuleResp;
import com.timevale.sterna.contract.review.client.dto.res.rule.*;
import com.timevale.sterna.contract.review.client.dto.res.rulegroup.ReviewRuleGroupSaveResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.timevale.saasbiz.model.exception.SaasBizResultCode.CONTRACT_REVIEW_COMMON_ERROR;

@Component
public class EpaasContractReviewClientImpl implements EpaasContractReviewClient {

    @Autowired
    private ReviewRuleInventoryManagementService reviewRuleInventoryManagementService;

    @Autowired
    private ReviewRuleGroupManagementService reviewRuleGroupManagementService;

    @Autowired
    private ReviewRuleManagementService reviewRuleManagementService;

    @Autowired
    private ReviewRecordManagementService reviewRecordManagementService;

    @Autowired
    private ReviewPreCensorManagementService reviewPreCensorManagementService;

    /*================================================================================================================*/
    /*ruleInventory*/

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则库保存失败")
    @Override
    public ReviewRuleInventorySaveResp ruleInventorySave(RuleInventorySaveReq req) {
        return reviewRuleInventoryManagementService.ruleInventorySave(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则库列表获取失败")
    @Override
    public List<ReviewRuleInventoryListResp> ruleInventoryList(RuleInventoryListReq req) {
        return reviewRuleInventoryManagementService.ruleInventoryList(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则库分页查询失败")
    @Override
    public ReviewRuleInventoryPageResp ruleInventoryPage(RuleInventoryPageReq req) {
        return reviewRuleInventoryManagementService.ruleInventoryPage(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则库删除失败")
    @Override
    public void ruleInventoryDel(RuleInventoryDelReq req) {
        reviewRuleInventoryManagementService.ruleInventoryDel(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则库详情获取失败")
    @Override
    public ReviewRuleInventoryResp ruleInventoryDetail(RuleInventoryDetailReq req) {
        return reviewRuleInventoryManagementService.ruleInventoryDetail(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则库状态更新失败")
    @Override
    public void ruleInventoryStatus(RuleInventoryStatusReq req) {
        reviewRuleInventoryManagementService.ruleInventoryStatus(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "合同类型下拉失败")
    @Override
    public ReviewContractTypeResp contractTypePullDown() {
        ReviewContractTypeResp resp = reviewRuleInventoryManagementService.contractTypePullDown();
        return resp;
    }

    /*================================================================================================================*/
    /*ruleGroup*/

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则组保存失败")
    @Override
    public ReviewRuleGroupSaveResp ruleGroupSave(ReviewRuleGroupReq req) {
        return reviewRuleGroupManagementService.ruleGroupSave(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则组列表获取失败")
    @Override
    public List<ReviewRuleGroupResp> getRuleGroupList(RuleGroupListReq req) {
        return reviewRuleGroupManagementService.getRuleGroupList(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则组删除失败")
    @Override
    public void ruleGroupDel(RuleGroupDelReq req) {
        reviewRuleGroupManagementService.ruleGroupDel(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则组删除确认失败")
    @Override
    public RuleDelResp ruleGroupDelConfirm(RuleGroupDelReq req) {
        return reviewRuleGroupManagementService.ruleGroupDelConfirm(req);
    }

    /*
     * =============================================================================
     * ===================================
     */
    /* rule */

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则分析失败")
    @Override
    public ReviewRuleIdResp ruleAnalysis(RuleAnalysisReq req) {
        return reviewRuleManagementService.ruleAnalysis(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则详情获取失败")
    @Override
    public ReviewRuleResp ruleDetail(RuleIdReq req) {
        return reviewRuleManagementService.ruleDetail(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则保存失败")
    @Override
    public ReviewRuleIdResp ruleSave(ReviewRuleReq req) {
        return reviewRuleManagementService.ruleSave(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则列表获取失败")
    @Override
    public List<ReviewRuleListResp> ruleList(RuleListReq req) {
        return reviewRuleManagementService.ruleList(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则删除失败")
    @Override
    public void ruleDel(RuleIdDelReq req) {
        reviewRuleManagementService.ruleDel(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则删除确认失败")
    @Override
    public RuleDelResp ruleDelConfirm(RuleIdDelReq req) {
        return reviewRuleManagementService.ruleDelConfirm(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则缓存删除失败")
    @Override
    public void ruleCacheDel(RuleIdReq req) {
        reviewRuleManagementService.ruleCacheDel(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则示例获取失败")
    @Override
    public List<String> ruleExample() {
        return reviewRuleManagementService.ruleExample();
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则复制失败")
    public ReviewRuleIdResp ruleCopy(RuleCopyReq req) {
        return reviewRuleManagementService.ruleCopy(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则文件分析失败")
    @Override
    public ReviewRuleIdResp ruleFileAnalysis(RuleFileAnalysisReq req) {
        return reviewRuleManagementService.ruleFileAnalysis(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则文件分析结果获取失败"
    )
    @Override
    public RuleFileAnalysisResp ruleFileAnalysisResult(RuleIdReq req) {
        return reviewRuleManagementService.ruleFileAnalysisResult(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "预审工具初始化失败")
    @Override
    public ReviewPreCensorInitiateResp toolsPreReviewInitiate(ReviewPreCensorReq req) {
        return reviewPreCensorManagementService.toolsPreReviewInitiate(req);
    }

//    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则验证失败")
//    @Override
//    public ReviewRuleIdResp ruleVerify(RuleEffectVerifyReq req) {
//        return reviewRuleManagementService.ruleVerify(req);
//    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则验证结果获取失败")
    @Override
    public ReviewResultResp ruleVerifyResult(RuleIdReq req) {
        return reviewRuleManagementService.ruleVerifyResult(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "规则批量保存失败")
    @Override
    public void ruleBatchSave(ReviewRuleBatchSaveReq req) {
        reviewRuleManagementService.ruleBatchSave(req);
    }

    /*================================================================================================================*/
    /*record*/
    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "审核历史分页查询失败")
    @Override
    public ReviewHistoryPageResp reviewHistoryPage(ReviewHistoryPageReq req) {
        return reviewRecordManagementService.reviewHistoryPage(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "审核历史删除失败")
    @Override
    public void reviewHistoryDel(ReviewToolsTriggerReq req) {
        reviewRecordManagementService.reviewHistoryDel(req);
    }

    @MethodException(code = CONTRACT_REVIEW_COMMON_ERROR, message = "审核信息获取失败")
    @Override
    public ReviewInfoResp reviewInfo(ReviewRecordIdReq req) {
        return reviewRecordManagementService.reviewInfo(req);
    }

    @Override
    public void taskStop(ReviewTaskStopReq req) {
        reviewRecordManagementService.reviewTaskStop(req);
    }

}
