package com.timevale.saasbiz.integration.process;

import com.timevale.contractmanager.common.service.model.ProcessDetailModel;
import com.timevale.contractmanager.common.service.model.StartProcessModel;
import com.timevale.contractmanager.common.service.result.ProcessDetailResult;
import com.timevale.contractmanager.common.service.result.StartProcessResult;

/**
 * ProcessStartClient
 *
 * <AUTHOR>
 * @since 2023/4/7 3:02 下午
 */
public interface ProcessStartClient {

    /**
     * 创建流程并开启流程
     *
     * @param model
     * @return
     */
    StartProcessResult startProcess(StartProcessModel model);

    /**
     * 获取主流程详情
     * @param model
     * @return
     */
    ProcessDetailResult detail(ProcessDetailModel model);
}
