package com.timevale.saasbiz.integration.fulfillment;

import com.timevale.signflow.search.service.model.v2.contractfulfillment.ContractFulfillmentQueryModel;
import com.timevale.signflow.search.service.result.v2.contractfulfillment.ContractFulfillmentQueryResult;

/**
 * RpcContractFulfillmentSearchServiceClient
 *
 * <AUTHOR>
 * @since 2023/10/26 10:54 上午
 */
public interface RpcContractFulfillmentSearchServiceClient {

    ContractFulfillmentQueryResult pageQuery(ContractFulfillmentQueryModel model);
}
