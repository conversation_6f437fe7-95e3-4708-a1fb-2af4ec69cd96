package com.timevale.saasbiz.integration.oauth;

import com.timevale.oauth.facade.model.authorize.base.PageList;
import com.timevale.oauth.facade.model.authorize.query.OauthMappingLogQueryInput;
import com.timevale.oauth.facade.model.authorize.query.OauthMappingQueryInput;
import com.timevale.oauth.facade.model.authorize.request.AuthCancelInput;
import com.timevale.oauth.facade.model.authorize.response.OauthMappingLogManagePageListOutput;
import com.timevale.oauth.facade.model.authorize.response.OauthMappingManageListOutput;

/**
 * 认证授权服务
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
public interface OAuthClient {

    /**
     * 分页查询应用Scope授权关系
     *
     * @param input 查询参数
     * @return 授权关系列表
     */
    PageList<OauthMappingManageListOutput> queryScopeAuthMappings(OauthMappingQueryInput input);

    /**
     * 分页查询应用Scope授权日志
     *
     * @param input 查询参数
     * @return 授权日志列表
     */
    OauthMappingLogManagePageListOutput queryScopeAuthLogs(OauthMappingLogQueryInput input);

    /**
     * 取消应用Scope授权
     *
     * @param input 请求参数
     * @return 成功 or 失败
     */
    boolean cancelScopeAuth(AuthCancelInput input);
}
