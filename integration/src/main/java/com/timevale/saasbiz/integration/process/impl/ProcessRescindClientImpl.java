package com.timevale.saasbiz.integration.process.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.timevale.contractmanager.common.service.api.RpcProcessRescindService;
import com.timevale.contractmanager.common.service.model.rescind.ProcessRescindCheckModel;
import com.timevale.contractmanager.common.service.model.rescind.ProcessRescindCheckResult;
import com.timevale.saasbiz.integration.process.ProcessRescindClient;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ProcessRescindClientImpl implements ProcessRescindClient {

    @Autowired
    private RpcProcessRescindService rpcProcessRescindService;

    @Override
    public ProcessRescindCheckResult checkProcessRescind(ProcessRescindCheckModel request) {
        return rpcProcessRescindService.checkProcessRescind(request);
    }
    
}
