package com.timevale.saasbiz.integration.oauth;

import com.timevale.token.service.model.input.LatestUserAgreementQueryInput;
import com.timevale.token.service.model.input.UserAgreementSaveInput;
import com.timevale.token.service.model.output.LatestUserAgreementQueryOutput;

/**
 * token-manager服务底层接口
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
public interface TokenManagerClient {

    /**
     * 保存用户同意协议
     *
     * @param input
     */
    void saveUserAgreement(UserAgreementSaveInput input);

    /**
     * 查询用户最近一次同意协议记录
     *
     * @param input
     * @return
     */
    LatestUserAgreementQueryOutput queryLatestUserAgreement(LatestUserAgreementQueryInput input);
}
