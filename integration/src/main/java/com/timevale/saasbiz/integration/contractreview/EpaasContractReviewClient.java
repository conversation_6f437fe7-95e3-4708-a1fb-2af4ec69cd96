package com.timevale.saasbiz.integration.contractreview;

import com.timevale.sterna.contract.review.client.dto.req.ReviewPreCensorReq;
import com.timevale.sterna.contract.review.client.dto.req.ReviewRuleGroupReq;
import com.timevale.sterna.contract.review.client.dto.req.ReviewRuleReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewHistoryPageReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewRecordIdReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewTaskStopReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewToolsTriggerReq;
import com.timevale.sterna.contract.review.client.dto.req.rule.*;
import com.timevale.sterna.contract.review.client.dto.req.ruleInventory.*;
import com.timevale.sterna.contract.review.client.dto.req.rulegroup.RuleGroupDelReq;
import com.timevale.sterna.contract.review.client.dto.req.rulegroup.RuleGroupListReq;
import com.timevale.sterna.contract.review.client.dto.res.*;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewHistoryPageResp;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewInfoResp;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewResultResp;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewResultRuleResp;
import com.timevale.sterna.contract.review.client.dto.res.rule.*;
import com.timevale.sterna.contract.review.client.dto.res.rulegroup.ReviewRuleGroupSaveResp;

import java.util.List;

public interface EpaasContractReviewClient {
    ReviewRuleInventorySaveResp ruleInventorySave(RuleInventorySaveReq req);

    List<ReviewRuleInventoryListResp> ruleInventoryList(RuleInventoryListReq req);

    ReviewRuleInventoryPageResp ruleInventoryPage(RuleInventoryPageReq req);

    void ruleInventoryDel(RuleInventoryDelReq req);

    ReviewRuleInventoryResp ruleInventoryDetail(RuleInventoryDetailReq req);

    void ruleInventoryStatus(RuleInventoryStatusReq req);

    ReviewContractTypeResp contractTypePullDown();

    ReviewRuleGroupSaveResp ruleGroupSave(ReviewRuleGroupReq req);

    List<ReviewRuleGroupResp> getRuleGroupList(RuleGroupListReq req);

    void ruleGroupDel(RuleGroupDelReq req);

    RuleDelResp ruleGroupDelConfirm(RuleGroupDelReq req);

    ReviewRuleIdResp ruleAnalysis(RuleAnalysisReq req);

    ReviewRuleResp ruleDetail(RuleIdReq req);

    ReviewRuleIdResp ruleSave(ReviewRuleReq req);

    List<ReviewRuleListResp> ruleList(RuleListReq req);

    void ruleDel(RuleIdDelReq req);

    RuleDelResp ruleDelConfirm(RuleIdDelReq req);

    void ruleCacheDel(RuleIdReq req);

    List<String> ruleExample();

    ReviewRuleIdResp ruleCopy(RuleCopyReq req);

    ReviewRuleIdResp ruleFileAnalysis(RuleFileAnalysisReq req);

    RuleFileAnalysisResp ruleFileAnalysisResult(RuleIdReq req);

    ReviewHistoryPageResp reviewHistoryPage(ReviewHistoryPageReq req);

    void reviewHistoryDel(ReviewToolsTriggerReq req);

    ReviewPreCensorInitiateResp toolsPreReviewInitiate(ReviewPreCensorReq req);

    ReviewInfoResp reviewInfo(ReviewRecordIdReq req);

//    ReviewRuleIdResp ruleVerify(RuleEffectVerifyReq req);

    ReviewResultResp ruleVerifyResult(RuleIdReq req);

    void ruleBatchSave(ReviewRuleBatchSaveReq req);

    void taskStop(ReviewTaskStopReq req);

}
