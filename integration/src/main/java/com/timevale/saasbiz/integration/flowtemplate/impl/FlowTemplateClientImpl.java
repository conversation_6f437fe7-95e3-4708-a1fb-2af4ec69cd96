package com.timevale.saasbiz.integration.flowtemplate.impl;

import com.timevale.contractmanager.common.service.bean.FlowTemplateParticipantRelationDataSourceRuleDTO;
import com.timevale.contractmanager.common.service.bean.FlowTemplateRelationDataSourceRuleDTO;
import com.timevale.contractmanager.common.service.model.ParticipantRelationDataSourceRuleModel;
import com.timevale.contractmanager.common.service.model.RelationDataSourceRuleModel;
import com.timevale.doccooperation.service.api.*;
import com.timevale.doccooperation.service.exception.DocCooperationBizException;
import com.timevale.doccooperation.service.exception.DocCooperationErrorEnum;
import com.timevale.doccooperation.service.exception.DocCooperationException;
import com.timevale.doccooperation.service.input.*;
import com.timevale.doccooperation.service.input.api.*;
import com.timevale.doccooperation.service.input.config.QueryTemplateAuthConfigInput;
import com.timevale.doccooperation.service.input.config.SaveTemplateAuthConfigInput;
import com.timevale.doccooperation.service.input.flowtemplate.*;
import com.timevale.doccooperation.service.model.BaseFlowTemplate;
import com.timevale.doccooperation.service.model.FlowTemplateAuthInfo;
import com.timevale.doccooperation.service.model.FlowTemplateAuthObject;
import com.timevale.doccooperation.service.model.contractpublictemplate.FlowTemplateCategory;
import com.timevale.doccooperation.service.model.datasource.DataSourceFieldsDTO;
import com.timevale.doccooperation.service.result.*;
import com.timevale.doccooperation.service.result.api.ListCategoryResult;
import com.timevale.doccooperation.service.result.api.SaveCategoryResult;
import com.timevale.doccooperation.service.result.config.SaveTemplateAuthConfigResult;
import com.timevale.doccooperation.service.result.config.TemplateAuthConfigDetailResult;
import com.timevale.doccooperation.service.result.flowtemplate.CheckCooperationerSealAssignableResult;
import com.timevale.doccooperation.service.result.flowtemplate.CreateDocTemplateResult;
import com.timevale.doccooperation.service.result.flowtemplate.FlowTemplatePreviewResult;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.BaseResult;
import com.timevale.saasbiz.integration.flowtemplate.FlowTemplateClient;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-03-08 11:14
 */
@Component
public class FlowTemplateClientImpl implements FlowTemplateClient {
    private static final int MAX_COUNT = 50;

    @Autowired private RpcFlowTemplateAuthService rpcFlowTemplateAuthService;

    @Autowired private FlowTemplateManagerService flowTemplateManagerService;

    @Autowired private FlowTemplatePlusService flowTemplatePlusService;

    @Autowired private RpcFlowTemplateService flowTemplateService;

    @Autowired private RpcFlowTemplateCategoryService flowTemplateCategoryService;

    @Autowired private RpcTemplateAuthConfigService templateAuthConfigService;
    @Autowired
    private com.timevale.contractmanager.common.service.api.RpcFlowTemplateService cmFlowTemplateService;
    @Autowired
    private FlowTemplateDataSourceRpcService flowTemplateDataSourceRpcService;

    @Override
    public String saveCategory(FlowTemplateCategory flowTemplateCategory) {
        SaveCategoryResult result = flowTemplateCategoryService.saveCategory(flowTemplateCategory);
        if (Objects.isNull(result)) {
            return null;
        }
        return result.getCategoryId();
    }

    @Override
    public void deleteCategory(String oid, String categoryId) {
        DeleteCategoryInput input = new DeleteCategoryInput();
        input.setOid(oid);
        input.setCategoryId(categoryId);

        flowTemplateCategoryService.deleteCategory(input);
    }

    @Override
    public List<FlowTemplateCategory> listCategory(String oid) {
        ListCategoryInput input = new ListCategoryInput();
        input.setOid(oid);

        ListCategoryResult result = flowTemplateCategoryService.listCategory(input);

        return Optional.ofNullable(result)
                .map(ListCategoryResult::getList)
                .orElse(Collections.emptyList());
    }

    @Override
    public void addToCategory(AddToCategoryInput input) {
        flowTemplateCategoryService.addToCategory(input);
    }

    @Override
    public void removeFromCategory(RemoveFromCategoryInput input) {
        flowTemplateCategoryService.removeFromCategory(input);
    }

    @Override
    public void batchToCategory(BatchToCategoryInput input) {
        flowTemplateCategoryService.batchToCategory(input);
    }

    @Override
    public void addCategoriesForFlowTemplate(AddCategoriesForFlowTemplateInput input) {
        flowTemplateCategoryService.addCategoriesForFlowTemplate(input);
    }

    @Override
    public boolean checkFlowTemplatePrivilege(CheckFlowTemplatePrivilegeInput input) {
        try{
            return rpcFlowTemplateAuthService.checkFlowTemplatePrivilege(input);
        } catch (BaseRuntimeException e) {
            catchException(e);
        }
        return false;
    }

    @Override
    public void topFlowTemplate(TopFlowTemplateInput input) {
        flowTemplateManagerService.topFlowTemplate(input);
    }

    @Override
    public QueryDocTemplatesResult queryDocTemplatesByFlowTemplateId(GetFlowTemplateInput input) {
        return flowTemplateManagerService.queryDocTemplatesByFlowTemplateId(input);
    }

    @Override
    public void changeFlowTemplateStatus(ChangeFlowTemplateStatusInput input) {
        try {
            flowTemplateManagerService.changeFlowTemplateStatus(input);
        } catch (BaseRuntimeException e) {
            catchException(e);
        }
    }

    @Override
    public void confirm(BaseFlowTemplateInput input) {
        try {
            flowTemplateManagerService.confirm(input);
        } catch (DocCooperationException | DocCooperationBizException e) {
            throw new SaasBizException(
                    SaasBizResultCode.FLOW_TEMPLATE_ENABLE_ERROR, e.getMessage());
        }
    }

    @Override
    public PageResult<BaseFlowTemplate> listFlowTemplate(ListFlowTemplateInput input) {
        return flowTemplateManagerService.listFlowTemplate(input);
    }

    @Override
    public BaseResult batchCheckAuthFlowTemplate(BatchAuthFlowtemplateInput input) {
        return flowTemplateManagerService.batchAuthFlowTemplate(input);
    }

    @Override
    public PageResult<FlowTemplateAuthInfo> listFlowTemplateByAuthList(ListFlowTemplateByAuthInput input) {
        return flowTemplateManagerService.listFlowTemplateByAuthList(input);
    }

    @Override
    public List<FlowTemplateAuthObject> singleFlowTemplateAuthList(SingleTemplateAuthListInput input) {
        return flowTemplateManagerService.singleTemplateAuthList(input);
    }

    @Override
    public BaseFlowTemplate checkFlowTemplateOwner(GetFlowTemplateInput input) {
        try {
            return flowTemplateManagerService.getFlowTemplateBaseInfo(input);
        } catch (BaseRuntimeException e) {
            catchException(e);
        }
        return null;
    }

    @Override
    public FlowTemplateRoleResult getFlowTemplateRoleList(FlowTemplateRoleListInput input) {
        return flowTemplateManagerService.getFlowTemplateRoleList(input);
    }

    @Override
    public GetFlowTemplateResult getFlowTemplateDetail(GetFlowTemplateInput input) {
        try {
            return flowTemplateManagerService.getFlowTemplateByFlowTemplateId(input);
        } catch (BaseRuntimeException e) {
            catchException(e);
            return null;
        }
    }


    @Override
    public GetFlowTemplateResult getFlowTemplateDetail(String subjectOid, String flowTemplateId) {
        GetFlowTemplateInput getFlowTemplateInput = new GetFlowTemplateInput();
        getFlowTemplateInput.setOid(subjectOid);
        getFlowTemplateInput.setFlowTemplateId(flowTemplateId);
        return getFlowTemplateDetail(getFlowTemplateInput);
    }

    @Override
    public BatchQueryTemplateFileBaseInfoResult batchQueryFlowTemplateFileBaseInfos(
            BatchGetFlowTemplateInput input) {
        return flowTemplateManagerService.batchQueryFlowTemplateFileBaseInfos(input);
    }

    @Override
    public List<BaseFlowTemplate> batchGetFlowTemplateBaseInfo(
            List<String> flowTemplateIds) {
        batchGetFlowTemplateBaseInfoInput input = new batchGetFlowTemplateBaseInfoInput();
        input.setFlowTemplateIds(flowTemplateIds);
        return flowTemplateManagerService.batchGetFlowTemplateBaseInfo(input);
    }

    @Override
    public Optional<BaseFlowTemplate> getFlowTemplateBaseInfo(String flowTemplateId) {
        if (StringUtils.isBlank(flowTemplateId)) {
            return Optional.empty();
        }
        List<BaseFlowTemplate> list = batchGetFlowTemplateBaseInfo(Arrays.asList(flowTemplateId));
        return CollectionUtils.isEmpty(list) ? Optional.empty() : Optional.ofNullable(list.get(0));
    }

    @Override
    public BaseResult batchChangeStatusAsync(BatchChangeFlowTemplateStatusInput input) {
        return flowTemplatePlusService.batchChangeStatusAsync(input);
    }

    @Override
    public BaseResult batchAuthFlowTemplateAsync(BatchAuthFlowTemplateInput input) {
        return flowTemplatePlusService.batchAuthFlowTemplateAsync(input);
    }

    @Override
    public SaveTemplateAuthConfigResult saveAuthConfig(SaveTemplateAuthConfigInput input) {
        return templateAuthConfigService.saveAuthConfig(input);
    }

    @Override
    public TemplateAuthConfigDetailResult getAuthConfig(String authId) {
        QueryTemplateAuthConfigInput input = new QueryTemplateAuthConfigInput();
        input.setAuthId(authId);
        return templateAuthConfigService.getAuthConfig(input);
    }

    @Override
    public List<TemplateAuthConfigDetailResult> listAuthConfig(String tenantGid) {
        QueryTemplateAuthConfigInput input = new QueryTemplateAuthConfigInput();
        input.setTenantGid(tenantGid);
        return templateAuthConfigService.listAuthConfig(input);
    }

    @Override
    public void updateAuthConfig(SaveTemplateAuthConfigInput input) {
        templateAuthConfigService.updateAuthConfig(input);
    }

    @Override
    public boolean checkHasFlowTemplate(FlowTemplateHasTypesInput input) {
        FlowTemplateHasTypesResult result = flowTemplatePlusService.checkHasFlowTemplate(input);
        return result.getHas();
    }

    @Override
    public boolean checkTemplateHasAuthList(String tenantOid, String templateId, boolean ignoreAll) {
        TemplateHasAuthInput input = new TemplateHasAuthInput();
        input.setTemplateId(templateId);
        input.setTenantOid(tenantOid);
        input.setIgnoreAll(ignoreAll);
        TemplateHasAuthResult result = flowTemplatePlusService.checkTemplateHasAuthList(input);
        return result.getHas();
    }

    @Override
    public String getFlowTemplatePreviewUrl(FlowTemplatePreviewInput input) {
        try {
            FlowTemplatePreviewResult flowTemplatePreviewUrl = flowTemplateService.getFlowTemplatePreviewUrl(input);
            return flowTemplatePreviewUrl.getPreviewUrl();
        } catch (BaseRuntimeException e) {
            catchException(e);
        }
        return null;
    }

    @Override
    public CreateDocTemplateResult batchCreateEpaasDocTemplate(CreateDocTemplateInput input) {
        return flowTemplateService.batchCreateEpaasDocTemplate(input);
    }
    /**
     * 捕捉一下doc-cooperation的异常，转一下错误码
     *
     * @param e exception
     */
    private void catchException(BaseRuntimeException e) {
        // 如果捕捉到的异常不是doc-cooperation定义的异常， 直接抛出
        if (!(e instanceof DocCooperationException) && !(e instanceof DocCooperationBizException)) {
            throw e;
        }
        String code = e.getCode();
        if (DocCooperationErrorEnum.COOPERATION_FLOW_NOT_EXIST.getCode().equals(e.getCode())) {
            throw new SaasBizException(SaasBizResultCode.COOPERATION_FLOW_NOT_EXIST);
        } else if (DocCooperationErrorEnum.TEMPLATE_NO_FLOW_TEMPLATE_INFO.getCode().equals(code)) {
            throw new SaasBizException(SaasBizResultCode.FLOW_TEMPLATE_NOT_EXIST);
        } else if (DocCooperationErrorEnum.TEMPLATE_NOT_OWNER.getCode().equals(code)) {
            throw new SaasBizException(SaasBizResultCode.FLOW_TEMPLATE_OWNER_FAIL);
        } else if (DocCooperationErrorEnum.FLOW_TEMPLATE_MISS_STRUCTS.getCode().equals(code)
                || DocCooperationErrorEnum.FLOW_TEMPLATE_MISS_INPUT_STRUCTS.getCode().equals(code)
                || DocCooperationErrorEnum.FLOW_TEMPLATE_MISS_SIGNPOS_STRUCTS
                        .getCode()
                        .equals(code)) {
            throw new SaasBizException(SaasBizResultCode.FLOW_TEMPLATE_CHECK_ERROR, e.getMessage());
        } else if (DocCooperationErrorEnum.FLOW_TEMPLATE_STATUS_ERROR.getCode().equals(code)) {
            throw new SaasBizException(SaasBizResultCode.FLOW_TEMPLATE_NOT_ENABLE);
        } else if (DocCooperationErrorEnum.COOPERATION_FLOW_NOT_COOPERATING
                .getCode()
                .equals(code)) {
            throw new SaasBizException(
                    SaasBizResultCode.PROCESS_COOPERATION_NOT_COOPERATING, e.getMessage());
        } else if (!DocCooperationErrorEnum.SYSTEM_ERROR.getCode().equals(code)) {
            throw new SaasBizException(SaasBizResultCode.COOPERATION_FAILED, e.getMessage());
        }
        throw e;
    }

    @Override
    public FlowTemplateParticipantRelationDataSourceRuleDTO participantRelationDataSourceRule(String subjectOid, List<String> dataSourceIds) {
        ParticipantRelationDataSourceRuleModel participantRelationDataSourceRuleModel = new ParticipantRelationDataSourceRuleModel();
        participantRelationDataSourceRuleModel.setDataSourceIds(dataSourceIds);
        participantRelationDataSourceRuleModel.setSubjectOid(subjectOid);
        return cmFlowTemplateService.participantRelationDataSourceRule(participantRelationDataSourceRuleModel);
    }

    @Override
    public FlowTemplateRelationDataSourceRuleDTO relationDataSourceRule(
            List<String> dataSourceIds) {
        RelationDataSourceRuleModel relationDataSourceRuleModel = new RelationDataSourceRuleModel();
        relationDataSourceRuleModel.setDataSourceIds(dataSourceIds);
        return cmFlowTemplateService.relationDataSourceRule(relationDataSourceRuleModel);
    }

    @Override
    public List<DataSourceFieldsDTO> dataSourceField(List<String> dataSourceIds) {
        DataSourceFieldQueryInput input = new DataSourceFieldQueryInput();
        input.setDataSourceIds(dataSourceIds);
        return flowTemplateDataSourceRpcService.dataSourceField(input).getDataSourceFieldsList();
    }

    @Override
    public void scheduledStartFlowTemplate(ScheduledStartFlowTemplateInput input) {
        flowTemplatePlusService.scheduledStartFlowTemplate(input);
    }

    @Override
    public CheckCooperationerSealAssignableResult checkCooperationerSealAssignable(String operatorId, String subjectId, String flowTemplateId, List<String> participantIds) {
        CheckCooperationerSealAssignableInput input = new CheckCooperationerSealAssignableInput();
        input.setOperatorId(operatorId);
        input.setSubjectId(subjectId);
        input.setFlowTemplateId(flowTemplateId);
        input.setCooperationerIds(participantIds);
        return flowTemplatePlusService.checkCooperationerSealAssignable(input);
    }

    @Override
    public EpaasStructChoiceListConfigResult epaasStructChoiceListConfig() {
        EpaasStructChoiceListConfigInput input = new EpaasStructChoiceListConfigInput();
        return flowTemplateManagerService.epaasStructChoiceListConfig(input);
    }
}
