package com.timevale.saasbiz.integration.autoarchive.impl;

import com.timevale.contractmanager.common.service.api.RpcAutoArchiveInnerService;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveChangeRuleMenuModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveQueryListModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveUpdateRuleModel;
import com.timevale.contractmanager.common.service.model.autoarchive.ProcessStatusQueryListModel;
import com.timevale.contractmanager.common.service.result.autoarchive.*;
import com.timevale.saasbiz.integration.autoarchive.RpcAutoArchiveInnerServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * RpcAutoArchiveInnerServiceClientImpl
 *
 * <AUTHOR>
 * @since 2023/8/16 3:37 下午
 */
@Slf4j
@Service
public class RpcAutoArchiveInnerServiceClientImpl implements RpcAutoArchiveInnerServiceClient {

    @Autowired
    private RpcAutoArchiveInnerService rpcAutoArchiveInnerService;

    @Override
    public AutoArchiveListResult pageListRule(AutoArchiveQueryListModel request) {
        return rpcAutoArchiveInnerService.pageListRule(request);
    }

    @Override
    public void topRule(String ruleId, String tenantId, String operatorId) {
        rpcAutoArchiveInnerService.topRule(ruleId, tenantId, operatorId);
    }

    @Override
    public void updateRule(AutoArchiveUpdateRuleModel model) {
        rpcAutoArchiveInnerService.updateRule(model);
    }

    @Override
    public void unbindRuleMenu(String menuId, String tenantId, String operatorId) {
        rpcAutoArchiveInnerService.unbindRuleMenu(menuId, tenantId, operatorId);
    }

    @Override
    public void rerunRule(String ruleId, String tenantId) {
        rpcAutoArchiveInnerService.rerunRule(ruleId, tenantId);
    }

    @Override
    public void updateRuleStatus(String ruleId, String tenantId, String operatorId, Integer status) {
        rpcAutoArchiveInnerService.updateRuleStatus(ruleId, tenantId, operatorId, status);
    }

    @Override
    public void updateRuleStatusNoCheck(String ruleId, Integer status) {
        rpcAutoArchiveInnerService.updateRuleStatusNoCheck(ruleId, status);
    }

    @Override
    public AutoArchiveRuleResult getRuleDetail(String ruleId, String tenantId) {
        return rpcAutoArchiveInnerService.getRuleDetail(ruleId, tenantId);
    }

    @Override
    public AutoArchiveRuleResult getRuleDetailByMenuId(String menuId, String tenantId) {
        return rpcAutoArchiveInnerService.getRuleDetailByMenuId(menuId, tenantId);
    }

    @Override
    public void bindRuleMenu(AutoArchiveChangeRuleMenuModel request) {
        rpcAutoArchiveInnerService.bindRuleMenu(request);
    }

    @Override
    public AutoArchiveFormMenuMappingResult getMappingByMenuId(String menuId, String tenantId) {
        return rpcAutoArchiveInnerService.getMappingByMenuId(menuId, tenantId);
    }

    @Override
    public ProcessStatusResult getOptionalStatusList(String tenantId) {
        ProcessStatusQueryListModel request = new ProcessStatusQueryListModel();
        request.setTenantId(tenantId);
        return rpcAutoArchiveInnerService.getOptionalStatusList(request);
    }
}
