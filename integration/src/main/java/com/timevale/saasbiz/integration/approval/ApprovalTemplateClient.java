package com.timevale.saasbiz.integration.approval;

import com.timevale.contractapproval.facade.dto.ApprovalTemplateDTO;
import com.timevale.contractapproval.facade.dto.ApprovalTemplateDetailDTO;
import com.timevale.contractapproval.facade.input.*;
import com.timevale.contractapproval.facade.output.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/4/3 10:49
 */
public interface ApprovalTemplateClient {

    /**
     * 创建审批流模版
     */
    ApprovalTemplateCreateOutput createApprovalTemplate(ApprovalTemplateCreateInput input);

    /**
     * 更新审批流模版
     */
    ApprovalTemplateUpdateOutput updateApprovalTemplate(ApprovalTemplateUpdateInput input);

    /**
     * 复制审批模版
     */
    ApprovalTemplateCopyOutput copyApprovalTemplate(ApprovalTemplateCopyInput input);

    /**
     * open
     */
    ApprovalTemplateOpenOutput openApprovalTemplate(ApprovalTemplateOpenInput input);

    /**
     * close
     */
    ApprovalTemplateCloseOutput closeApprovalTemplate(ApprovalTemplateCloseInput input);

    /**
     * delete
     */
    ApprovalTemplateDeleteOutput deleteApprovalTemplate(ApprovalTemplateDeleteInput input);

    /**
     * 获取详情
     */
    ApprovalTemplateDetailDTO getApprovalTemplateDetail(String approvalTemplateCode);

    ApprovalTemplateDTO getApprovalTemplate(String approvalTemplateCode);

    /**
     * 审批事件，是否已存在
     */
    ApprovalTemplateQueryExistConditionOutput queryExistCondition(ApprovalTemplateQueryExistConditionInput input);

    /**
     * 审批事件查询审批模版
     */
    ApprovalTemplateDTO getApprovalTemplateByCondition(GetApprovalTemplateByConditionInput input);

    /**
     * 发起获取可用的审批模版
     */
    List<ApprovalTemplateDTO> queryMatchConditionApprovalTemplate(ApprovalTemplateQueryMatchConditionInput input);


    void approvalTemplateDeleteCondition(ApprovalTemplateDeleteConditionInput input);

    /**
     * 批量获取模版名称
     */
    Map<String, ApprovalTemplateDTO> batchGetApprovalTemplate(Set<String> approvalTemplateCodeList);

    /**
     * 判断企业是否有合同审批模板
     * @param orgId
     * @return
     */
    boolean hasOrgApproveTemplate(String orgId);

    /**
     * 查询审批模板列表
     * @param input
     * @return
     */
    ApprovalTemplateListOutput listApprovalTemplate(ApprovalTemplateListInput input);

    /**
     * 批量获取事件条件符合的审批模板列表
     * @param input 请求参数
     * @return 事件条件及对应的审批模板列表
     */
    ApprovalTemplateBatchQueryMatchConditionOutput batchQueryMatchConditionApprovalTemplate(ApprovalTemplateBatchQueryMatchConditionInput input);
}
