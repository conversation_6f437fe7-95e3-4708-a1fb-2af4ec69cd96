package com.timevale.saasbiz.integration.contractanalysis.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.timevale.contractanalysis.facade.api.dto.process.QueryProcessAiInfoRequestDTO;
import com.timevale.contractanalysis.facade.api.dto.process.QueryProcessAiInfoResultDTO;
import com.timevale.contractanalysis.facade.api.process.ProcessAiInfoFacadeService;
import com.timevale.saasbiz.integration.contractanalysis.ProcessAiInfoClient;

@Component
public class ProcessAiInfoClientImpl implements ProcessAiInfoClient {

    @Autowired
    private ProcessAiInfoFacadeService processAiInfoFacadeService;

    @Override
    public QueryProcessAiInfoResultDTO getProcessAiInfo(String processId) {
        QueryProcessAiInfoRequestDTO input = new QueryProcessAiInfoRequestDTO();
        input.setProcessId(processId);
        QueryProcessAiInfoResultDTO output = processAiInfoFacadeService.queryProcessAiInfo(input);
        return output;
    }
}
