package com.timevale.saasbiz.integration.ledger;

import com.timevale.contractanalysis.facade.api.bo.FormListResult;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecDetailOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecLastTrialDoneOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFieldBatchSaveInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFieldDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormDataResultDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormListInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormListResultDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFormUpdateAiFieldInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerTemplateStructMatchInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerTemplateStructMatchResultDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerViewFormDataInputDTO;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecResultStatusEnum;
import com.timevale.contractmanager.common.service.model.ledger.AiExtractLimitQueryModel;
import com.timevale.contractmanager.common.service.model.ledger.LedgerFormSaveModel;
import com.timevale.contractmanager.common.service.model.ledger.LedgerFormUpdateModel;
import com.timevale.contractmanager.common.service.result.ledger.AiExtractLimitResult;
import com.timevale.contractmanager.common.service.result.ledger.LedgerFormDetailResult;

import java.util.List;

/**
 * RpcLedgerInnerClient
 *
 * <AUTHOR>
 * @since 2023/8/25 5:02 下午
 */
public interface RpcLedgerInnerClient {

    String saveForm(LedgerFormSaveModel input);

    void updateForm(LedgerFormUpdateModel input);
    
    void updateFormAiField(LedgerFormUpdateAiFieldInputDTO input);

    void updateStatusNoCheck(String formId, String tenantId, String accountId, Integer status);

    void delete(String formId, String tenantId, String accountId);

    boolean queryFormExists(String formId, String tenantGid);

    LedgerFormDetailResult detail(String formId, String tenantId);

    LedgerFormListResultDTO pageFormList(LedgerFormListInputDTO input);

    LedgerTemplateStructMatchResultDTO matchTemplateStruct(String tenantId, List<String> templateIds);

    List<LedgerFieldDTO> batchSaveField(LedgerFieldBatchSaveInputDTO inputDTO);

    void updateFormStatus(String tenantId, String accountId, String formId, Boolean switchOn);

    LedgerFormDataResultDTO getFormDataResult(LedgerViewFormDataInputDTO inputDTO);

    AiExtractLimitResult queryExtractLimit(String tenantId);

    /**
     * 查询执行详情
     * @param formId
     * @return
     */
    LedgerExecDetailOutputDTO queryExecDetail(String formId);

    /**
     * 查询最后一次体验完成台账信息
     * @param tenantGid
     * @return
     */
    LedgerExecLastTrialDoneOutputDTO queryExecLastTrialDone(String tenantGid);

    /**
     * 关闭体验完成确认
     * @param formId
     */
    void closeLedgerTrialDoneConfirm(String formId);

    String queryNextFormProcessId(String formId, String processId);

    /**
     * 更新执行结果
     * @param formId
     * @param tenantId
     * @param accountId
     * @param resultStatus
     */
    void updateExecResult(String formId, String tenantId, String accountId, LedgerExecResultStatusEnum resultStatus);
}
