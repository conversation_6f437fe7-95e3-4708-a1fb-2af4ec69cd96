package com.timevale.saasbiz.integration.contractdfrating.impl;

import com.timevale.saasbiz.integration.contractdfrating.EpaasContractDraftingClient;
import com.timevale.sterna.contract.drafting.client.ContractDraftingService;
import com.timevale.sterna.contract.drafting.client.WebOfficeFileManagementService;
import com.timevale.sterna.contract.drafting.client.dto.draft.ContractDraftingDTO;
import com.timevale.sterna.contract.drafting.client.dto.draft.ContractDraftingUrlDTO;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.req.FileQueryByIdReq;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.req.UploadFileReq;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.resp.GetFileInfoResp;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.resp.UploadFileResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EpaasContractDraftingClientImpl implements EpaasContractDraftingClient {

    @Autowired ContractDraftingService contractDraftingService;
    @Autowired WebOfficeFileManagementService webOfficeFileManagementService;

    @Override
    public ContractDraftingUrlDTO createContractDrafting(ContractDraftingDTO input) {
        return contractDraftingService.createContractDrafting(input);
    }

    @Override
    public UploadFileResp uploadDraftingFile(UploadFileReq input) {
        return webOfficeFileManagementService.uploadFile(input);
    }

    @Override
    public GetFileInfoResp queryDraftingFile(FileQueryByIdReq input) {
        return webOfficeFileManagementService.getFileInfo(input);
    }

}
