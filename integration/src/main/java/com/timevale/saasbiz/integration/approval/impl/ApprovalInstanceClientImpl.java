package com.timevale.saasbiz.integration.approval.impl;

import com.timevale.account.flow.service.api.RpcApprovalGroupService;
import com.timevale.account.flow.service.model.approval.model.ApprovalGroupModel;
import com.timevale.account.flow.service.model.input.ApprovalGroupListInput;
import com.timevale.contractapproval.facade.api.ApprovalAdaptRpcService;
import com.timevale.contractapproval.facade.api.ApprovalInstanceRpcService;
import com.timevale.contractapproval.facade.dto.*;
import com.timevale.contractapproval.facade.input.*;
import com.timevale.contractapproval.facade.input.ApprovalAgreeInput;
import com.timevale.contractapproval.facade.input.ApprovalBatchGetInfoInput;
import com.timevale.contractapproval.facade.input.ApprovalGetInfoInput;
import com.timevale.contractapproval.facade.input.ApprovalPageInput;
import com.timevale.contractapproval.facade.input.ApprovalRefuseInput;
import com.timevale.contractapproval.facade.input.ApprovalRestartInput;
import com.timevale.contractapproval.facade.input.ApprovalRevokeInput;
import com.timevale.contractapproval.facade.input.BatchGetApprovalGroupInput;
import com.timevale.contractapproval.facade.input.QueryUserApprovalTaskTypeInput;
import com.timevale.contractapproval.facade.input.VirtualStartApprovalInput;
import com.timevale.contractapproval.facade.output.*;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saasbiz.integration.approval.ProcessApprovalClient;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.Arrays;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-03-30 14:46
 */
@Slf4j
@Component
public class ApprovalInstanceClientImpl implements ProcessApprovalClient {
    @Autowired private ApprovalInstanceRpcService approvalInstanceRpcService;
    @Autowired private RpcApprovalGroupService rpcApprovalGroupService;
    @Autowired private ApprovalAdaptRpcService adaptRpcService;

    @Override
    public PageApprovalFlowOutput listApproval(ApprovalPageInput input) {
        return approvalInstanceRpcService.listApproval(input);
    }

    @Override
    public List<ApprovalDTO> batchGetApproval(ApprovalBatchGetInfoInput input) {
        return approvalInstanceRpcService.batchGetApproval(input);
    }

    @Override
    public Map<String, ApprovalDTO> mapApproval(List<String> approvalIds) {
        ApprovalBatchGetInfoInput input = new ApprovalBatchGetInfoInput();
        input.setApprovalCodeList(approvalIds);

        List<ApprovalDTO> approvalDTOS = batchGetApproval(input);
        if (CollectionUtils.isEmpty(approvalDTOS)) {
            return Collections.emptyMap();
        }

        return approvalDTOS.stream()
                .collect(
                        Collectors.toMap(
                                ApprovalDTO::getApprovalCode, Function.identity(), (a, b) -> b));
    }

    @Override
    public Map<String, List<String>> mapUserApprovalTaskType(
            String subjectGid, String operatorGid, List<String> approvalCodes) {
        QueryUserApprovalTaskTypeInput input = new QueryUserApprovalTaskTypeInput();
        input.setSubjectGid(subjectGid);
        input.setOperatorGid(operatorGid);
        input.setApprovalCodes(approvalCodes);
        QueryUserApprovalTaskTypeOutput output =
                approvalInstanceRpcService.queryUserApprovalTaskType(input);
        if (Objects.isNull(output) || CollectionUtils.isEmpty(output.getResultList())) {
            return Collections.emptyMap();
        }

        return output.getResultList().stream()
                .collect(
                        Collectors.toMap(
                                QueryUserApprovalTaskTypeOutput.Result::getApprovalCode,
                                QueryUserApprovalTaskTypeOutput.Result::getTypes,
                                (a, b) -> b));
    }

    @Override
    public List<ApprovalGroupDTO> batchGetProcessApprovalGroup(List<String> bizGroupIds) {
        BatchGetApprovalGroupInput input = new BatchGetApprovalGroupInput();
        input.setBizGroupIds(bizGroupIds);
        return approvalInstanceRpcService.batchGetApprovalGroup(input).getGroupList();
    }

    @Override
    public ApprovalGroupDTO getGroupByBizGroupId(String bizGroupIds) {
        if (StringUtils.isBlank(bizGroupIds)) {
            return null;
        }
        List<ApprovalGroupDTO> approvalGroupDTOS = batchGetProcessApprovalGroup(Arrays.asList(bizGroupIds));
        return CollectionUtils.isNotEmpty(approvalGroupDTOS) ? approvalGroupDTOS.get(0) : null;
    }

    @Override
    public Map<String, ApprovalGroupDTO> mapGetApprovalGroup(List<String> bizGroupIds) {
        List<ApprovalGroupDTO> list = batchGetProcessApprovalGroup(bizGroupIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream()
                .collect(Collectors.toMap(ApprovalGroupDTO::getBizGroupId, Function.identity()));
    }

    @Override
    public PageApprovalGroupOutput listApprovalGroup(ApprovalPageInput input) {
        return approvalInstanceRpcService.listApprovalGroup(input);
    }

    @Override
    public List<ApprovalAggregateDTO> countApprovalBySubject(
            String accountGid, String approvalType) {
        SubjectApprovalCountInput input = new SubjectApprovalCountInput();
        input.setAccountGid(accountGid);
        input.setApprovalType(approvalType);

        SubjectApprovalCountOutput output =
                approvalInstanceRpcService.countApprovalBySubject(input);
        if (Objects.isNull(output) || CollectionUtils.isEmpty(output.getAggregates())) {
            return Collections.emptyList();
        }

        return output.getAggregates();
    }

    @Override
    public List<ApprovalGroupModel> listOldApprovalGroup(List<String> bizGroupIds) {
        ApprovalGroupListInput input = new ApprovalGroupListInput();
        input.setApprovalUuids(bizGroupIds);
        RpcOutput<List<ApprovalGroupModel>> output =
                rpcApprovalGroupService.getApprovalGroupByApprovalUuids(new RpcInput<>(input));

        return Optional.ofNullable(output).map(RpcOutput::getData).orElse(Collections.emptyList());
    }

    @Override
    public Map<String, ApprovalGroupModel> mapGetOldApprovalGroup(List<String> bizGroupIds) {
        return listOldApprovalGroup(bizGroupIds).stream()
                .collect(
                        Collectors.toMap(
                                ApprovalGroupModel::getApprovalUuid,
                                Function.identity(),
                                (a, b) -> b));
    }

    @Override
    public ApprovalDTO getApprovalByCode(String approvalCode) {
        ApprovalGetInfoInput getInfoInput = new ApprovalGetInfoInput();
        getInfoInput.setApprovalCode(approvalCode);
        return approvalInstanceRpcService.getApproval(getInfoInput);
    }

    @Override
    public ApprovalDetailDTO getApprovalDetailByCode(String approvalCode) {
        ApprovalGetDetailInput getInfoInput = new ApprovalGetDetailInput();
        getInfoInput.setApprovalCode(approvalCode);
        return approvalInstanceRpcService.getApprovalDetail(getInfoInput);
    }

    @Override
    public ApprovalDetailWithLogDTO getApprovalDetailWithLog(String approvalCode) {
        GetApprovalDetailWithLogInput input = new GetApprovalDetailWithLogInput();
        input.setApprovalCode(approvalCode);
        input.setQueryReadTime(Boolean.TRUE);
        return approvalInstanceRpcService.getApprovalDetailWithLog(input).getApprovalDetailWithLog();
    }

    @Override
    public void sendApprovalTaskReadMsg(String approvalCode, String operatorOid, String operatorGid) {
        ApprovalReadInput input = new ApprovalReadInput();
        input.setApprovalCode(approvalCode);
        input.setOperatorOid(operatorOid);
        input.setOperatorGid(operatorGid);
        try {
            approvalInstanceRpcService.doSendApprovalTaskReadMsg(input);
        }catch (Exception e) {
            log.warn("send approval read msg error:",e);
        }
    }

    @Override
    public VirtualStartApprovalOutput virtualStartApprovalDetail(VirtualStartApprovalInput input) {
        return approvalInstanceRpcService.virtualStartApproval(input);
    }

    @Override
    public void approvalRevoke(ApprovalRevokeInput input) {
        approvalInstanceRpcService.approvalRevoke(input);
    }

    @Override
    public String approvalRestart(ApprovalRestartInput input) {
       return approvalInstanceRpcService.approvalRestart(input).getNewApprovalCode();
    }

    @Override
    public void approvalAgree(ApprovalAgreeInput input) {
        approvalInstanceRpcService.approvalAgree(input);
    }

    @Override
    public void approvalRefuse(ApprovalRefuseInput input) {
        approvalInstanceRpcService.approvalRefuse(input);
    }

    @Override
    public void approvalHidden(ApprovalHiddenInput input) {
        approvalInstanceRpcService.approvalHidden(input);
    }

    @Override
    public ApprovalDTO getByAdaptApprovalId(Long adaptApprovalId) {
        return adaptRpcService.getByApprovalId(adaptApprovalId);
    }

    @Override
    public ApprovalDetailDTO getApprovalDetailByBizInfo(String approvalType, String bizId) {
        GetApprovalDetailByBizInfoInput input = new GetApprovalDetailByBizInfoInput();
        input.setBizId(bizId);
        input.setApprovalType(approvalType);
        return approvalInstanceRpcService.getApprovalDetailByBizInfo(input).getApprovalDetail();
    }

    @Override
    public ApprovalDetailWithLogDTO getApprovalDetailWithLogByBizInfo(String approvalType, String bizId) {
        GetApprovalDetailWithLogByBizInfoInput input = new GetApprovalDetailWithLogByBizInfoInput();
        input.setBizId(bizId);
        input.setApprovalType(approvalType);
        return approvalInstanceRpcService.getApprovalDetailWithLogByBizInfo(input).getApprovalDetailWithLog();
    }

    @Override
    public ApprovalUrlOutput approvalUrl(ApprovalUrlInput input) {
        return approvalInstanceRpcService.approvalUrl(input);
    }

    @Override
    public ApprovalUrlOutput approvalListUrl(ApprovalListUrlInput input) {
        return approvalInstanceRpcService.approvalListUrl(input);
    }

    @Override
    public void approvalUrge(ApprovalUrgeInput input) {
        approvalInstanceRpcService.approvalUrge(input);
    }

    @Override
    public String getApprovalBizData(GetApprovalBizDataInput input) {
        return approvalInstanceRpcService.getApprovalBizData(input).getBizData();
    }

    @Override
    public List<ApprovalFlowDTO> queryApprovalInBatch(QueryApprovalInBatchInput input) {
        return approvalInstanceRpcService.queryApprovalInBatch(input).getApprovals();
    }

    @Override
    public QueryApprovalWillAuthUrlOutput queryApprovalWillAuthUrl(ApprovalWillAuthUrlInput input) {
        return approvalInstanceRpcService.queryApprovalWillAuthUrl(input);
    }

    @Override
    public QueryApprovalPermissionOutput queryApprovalPermission(QueryApprovalPermissionInput input) {
        return approvalInstanceRpcService.queryApprovalPermission(input);
    }

    @Override
    public ApprovalAddOutput addApproval(ApprovalAddInput input) {
        return approvalInstanceRpcService.addApproval(input);
    }

    @Override
    public ApprovalReduceOutput reduceApproval(ApprovalReduceInput input) {
        return approvalInstanceRpcService.reduceApproval(input);
    }

    @Override
    public ApprovalAddCommentOutput addComment(ApprovalAddCommentInput input) {
        return approvalInstanceRpcService.addComment(input);
    }

    @Override
    public boolean isApproverByBizInfo(CheckAccountIsApproverByBizIdInput input) {
        CheckAccountIsApproverByBizIdOutput output = approvalInstanceRpcService.checkAccountIsApproverByBizId(input);
        return output.isApprover();
    }

}
