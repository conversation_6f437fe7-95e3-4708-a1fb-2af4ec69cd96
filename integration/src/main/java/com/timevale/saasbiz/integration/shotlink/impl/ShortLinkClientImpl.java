package com.timevale.saasbiz.integration.shotlink.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.saasbiz.integration.shotlink.ShortLinkClient;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.shortlink.common.service.api.ShortLinkRpcService;
import com.timevale.shortlink.common.service.request.ShortenCodeRequest;
import com.timevale.shortlink.common.service.request.ShortenRequest;
import com.timevale.shortlink.common.service.result.ShortCodeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024-02-27
 */
@Slf4j
@Component
public class ShortLinkClientImpl implements ShortLinkClient {

    @Autowired
    private ShortLinkRpcService shortLinkRpcService;

    @Override
    public String getShortCode(String longCode) {
        ShortenCodeRequest scq = new ShortenCodeRequest();
        scq.setLongCode(longCode);
        ShortCodeResult shortCodeResult = shortLinkRpcService.getShortCode(scq);
        if (!shortCodeResult.isSuccess()) {
            log.warn("get shortlink failed {}", JSON.toJSONString(shortCodeResult));
            throw new SaasBizException(shortCodeResult.getErrCode(), shortCodeResult.getMsg());
        }
        return shortCodeResult.getShortCode();
    }

    @Override
    public String convertShortUrl(String longUrl, long expire) {
        ShortenRequest request = new ShortenRequest();
        request.setUrl(longUrl);
        request.setExpire(expire);
        return shortLinkRpcService.getShortLink(request).getHttpsShortlink();
    }

}
