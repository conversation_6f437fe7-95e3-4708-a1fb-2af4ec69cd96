package com.timevale.saasbiz.integration.realname.impl;

import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.footstone.identity.common.service.api.common.QueryService;
import com.timevale.footstone.identity.common.service.response.IdentityAuthDetailResponse;
import com.timevale.saasbiz.integration.realname.FootstoneIdentityClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-05-17
 */
@Slf4j
@Service
public class FootstoneIdentityClientImpl implements FootstoneIdentityClient {

    @Autowired QueryService queryService;

    @Override
    public IdentityAuthDetailResponse queryDetail(String flowId) {
        BaseResult<IdentityAuthDetailResponse> result = queryService.queryDetail(flowId);
        if (result.ifSuccess() && result.getData() != null) {
            return result.getData();
        }
        return null;
    }
}
