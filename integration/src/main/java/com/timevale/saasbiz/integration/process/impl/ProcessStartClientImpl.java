package com.timevale.saasbiz.integration.process.impl;

import com.timevale.contractmanager.common.service.api.RpcProcessService;
import com.timevale.contractmanager.common.service.api.RpcProcessStartService;
import com.timevale.contractmanager.common.service.model.ProcessDetailModel;
import com.timevale.contractmanager.common.service.model.StartProcessModel;
import com.timevale.contractmanager.common.service.result.ProcessDetailResult;
import com.timevale.contractmanager.common.service.result.StartProcessResult;
import com.timevale.saasbiz.integration.process.ProcessStartClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ProcessStartClientImpl
 *
 * <AUTHOR>
 * @since 2023/4/7 3:08 下午
 */
@Service
public class ProcessStartClientImpl implements ProcessStartClient {

    @Autowired
    private RpcProcessStartService rpcProcessStartService;

    @Autowired
    private RpcProcessService rpcProcessService;

    @Override
    public StartProcessResult startProcess(StartProcessModel model) {
        return rpcProcessStartService.startProcess(model);
    }

    @Override
    public ProcessDetailResult detail(ProcessDetailModel model) {
        return rpcProcessService.detail(model);
    }
}
