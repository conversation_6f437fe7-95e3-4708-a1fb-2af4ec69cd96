package com.timevale.saasbiz.integration.contractanalysis.impl;

import com.timevale.contractanalysis.facade.api.ao.HasFieldRequest;
import com.timevale.contractanalysis.facade.api.field.FieldFacadeService;
import com.timevale.saasbiz.integration.contractanalysis.FieldFacadeClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-12-25 15:51
 */
@Service
public class FieldFacadeClientImpl implements FieldFacadeClient {

    @Autowired private FieldFacadeService fieldFacadeService;

    @Override
    public boolean hasField(String tenantGid) {
        HasFieldRequest request = new HasFieldRequest();
        request.setTenantGid(tenantGid);
        return fieldFacadeService.checkTenantHasField(request).isHas();
    }
}
