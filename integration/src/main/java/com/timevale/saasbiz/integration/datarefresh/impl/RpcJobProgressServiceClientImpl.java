package com.timevale.saasbiz.integration.datarefresh.impl;

import com.timevale.datarefresh.facade.api.RpcJobProgressService;
import com.timevale.datarefresh.facade.bean.JobProgressDTO;
import com.timevale.datarefresh.facade.bean.JobStopResultDTO;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.saasbiz.integration.datarefresh.RpcJobProgressServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * RpcJobProgressServiceClientImpl
 *
 * <AUTHOR>
 * @since 2023/8/17 4:33 下午
 */
@Slf4j
@Service
public class RpcJobProgressServiceClientImpl implements RpcJobProgressServiceClient {

    @Autowired
    private RpcJobProgressService rpcJobProgressService;

    @Override
    public JobProgressDTO getJobProgress(String jobId) {
        return rpcJobProgressService.getJobProgress(jobId);
    }

    @Override
    public boolean stopJob(String jobId) {
        JobStopResultDTO result = rpcJobProgressService.stopJobV2(jobId);
        return BooleanUtils.isTrue(result.getNormalStop());
    }
}
