package com.timevale.saasbiz.integration.flowtemplate;

import com.timevale.contractmanager.common.service.bean.FlowTemplateParticipantRelationDataSourceRuleDTO;
import com.timevale.contractmanager.common.service.bean.FlowTemplateRelationDataSourceRuleDTO;
import com.timevale.doccooperation.service.input.*;
import com.timevale.doccooperation.service.input.api.AddCategoriesForFlowTemplateInput;
import com.timevale.doccooperation.service.input.api.AddToCategoryInput;
import com.timevale.doccooperation.service.input.api.BatchToCategoryInput;
import com.timevale.doccooperation.service.input.api.RemoveFromCategoryInput;
import com.timevale.doccooperation.service.input.config.SaveTemplateAuthConfigInput;
import com.timevale.doccooperation.service.input.flowtemplate.*;
import com.timevale.doccooperation.service.model.BaseFlowTemplate;
import com.timevale.doccooperation.service.model.FlowTemplateAuthInfo;
import com.timevale.doccooperation.service.model.FlowTemplateAuthObject;
import com.timevale.doccooperation.service.model.contractpublictemplate.FlowTemplateCategory;
import com.timevale.doccooperation.service.model.datasource.DataSourceFieldsDTO;
import com.timevale.doccooperation.service.result.*;
import com.timevale.doccooperation.service.result.config.SaveTemplateAuthConfigResult;
import com.timevale.doccooperation.service.result.config.TemplateAuthConfigDetailResult;
import com.timevale.doccooperation.service.result.flowtemplate  .CheckCooperationerSealAssignableResult;
import com.timevale.doccooperation.service.result.flowtemplate.CreateDocTemplateResult;
import com.timevale.mandarin.common.result.BaseResult;

import java.util.List;
import java.util.Optional;

/**
 * 流程模板rpc客户端
 *
 * <AUTHOR>
 * @since 2023-03-08 11:12
 */
public interface FlowTemplateClient {
    /**
     * 保存分类
     *
     * @param flowTemplateCategory
     * @return
     */
    String saveCategory(FlowTemplateCategory flowTemplateCategory);

    /**
     * 删除分类
     *
     * @param oid
     * @param categoryId
     */
    void deleteCategory(String oid, String categoryId);

    /**
     * 获取分类列表
     *
     * @param oid
     * @return
     */
    List<FlowTemplateCategory> listCategory(String oid);

    /**
     * 添加模板到分类
     *
     * @param input
     */
    void addToCategory(AddToCategoryInput input);

    /**
     * 从模板下删除分类
     *
     * @param input
     */
    void removeFromCategory(RemoveFromCategoryInput input);

    /**
     * 批量添加模板到分类
     *
     * @param input
     */
    void batchToCategory(BatchToCategoryInput input);

    /**
     * 为模板添加多个分类
     *
     * @param input
     */
    void addCategoriesForFlowTemplate(AddCategoriesForFlowTemplateInput input);

    /**
     * 校验用户是否有流程模板指定操作权限
     *
     * @param input
     * @return
     */
    boolean checkFlowTemplatePrivilege(CheckFlowTemplatePrivilegeInput input);

    /**
     * 置顶流程模板
     *
     * @param input
     */
    void topFlowTemplate(TopFlowTemplateInput input);

    /**
     * 获取流程模板对应的文件模板列表
     *
     * @param input 入参传flowTemplateId
     * @return 获取流程模板文件列表
     */
    QueryDocTemplatesResult queryDocTemplatesByFlowTemplateId(GetFlowTemplateInput input);

    /**
     * 修改流程模板状态
     *
     * @param input 入参
     */
    void changeFlowTemplateStatus(ChangeFlowTemplateStatusInput input);

    /**
     * 确认流程模板，其实就是校验流程模板数据是否合法
     */
    void confirm(BaseFlowTemplateInput input);

    /**
     * 获取流程模板列表
     *
     * @param input
     * @return
     */
    PageResult<BaseFlowTemplate> listFlowTemplate(ListFlowTemplateInput input);

    /**
     * 流程模板批量授权-删除原先流程模板授权记录
     *
     * @param input
     */
    BaseResult batchCheckAuthFlowTemplate(BatchAuthFlowtemplateInput input);

    /**
     * 依据权限列表获取流程模板列表
     *
     * @param input
     * @return
     */
    PageResult<FlowTemplateAuthInfo> listFlowTemplateByAuthList(ListFlowTemplateByAuthInput input);

    /**
     * 获取单个流程模板权限对象列表
     *
     * @param input
     * @return
     */
    List<FlowTemplateAuthObject> singleFlowTemplateAuthList(SingleTemplateAuthListInput input);

    /**
     * 校验流程模板归属方是否为当前用户
     *
     * @param input
     */
    BaseFlowTemplate checkFlowTemplateOwner(GetFlowTemplateInput input);

    /**
     * 获取流程模板的角色列表
     *
     * @param input
     * @return
     */
    FlowTemplateRoleResult getFlowTemplateRoleList(FlowTemplateRoleListInput input);

    /**
     * 获取流程模板基本详情
     *
     * @param inputDTO
     * @return
     */
    GetFlowTemplateResult getFlowTemplateDetail(GetFlowTemplateInput inputDTO);

    /**
     * 获取流程模版详情
     */
    GetFlowTemplateResult getFlowTemplateDetail(String subjectOid, String flowTemplateId);


    /**
     * 批量获取流程模板文件基本信息列表
     * @param input
     * @return
     */
    BatchQueryTemplateFileBaseInfoResult batchQueryFlowTemplateFileBaseInfos(
            BatchGetFlowTemplateInput input);

    /**
     * 批量查询流程模板基本信息
     *
     * @param flowTemplateIds
     * @return
     */
    List<BaseFlowTemplate> batchGetFlowTemplateBaseInfo(List<String> flowTemplateIds);

    /**
     * 单个获取详情
     */
    Optional<BaseFlowTemplate> getFlowTemplateBaseInfo(String flowTemplateId);

    /**
     * 批量异步修改模板状态
     *
     * @param input 入参
     * @return 结果
     */
    BaseResult batchChangeStatusAsync(BatchChangeFlowTemplateStatusInput input);

    /**
     * 批量异步授权模板
     *
     * @param input 入参
     * @return 结果
     */
    BaseResult batchAuthFlowTemplateAsync(BatchAuthFlowTemplateInput input);

    /**
     * 保存授权配置
     * @param input
     * @return
     */
    SaveTemplateAuthConfigResult saveAuthConfig(SaveTemplateAuthConfigInput input);

    /**
     * 查询授权配置
     * @param authId
     * @return
     */
    TemplateAuthConfigDetailResult getAuthConfig(String authId);

    /**
     *
     * @param tenantGid
     * @return
     */
    List<TemplateAuthConfigDetailResult> listAuthConfig(String tenantGid);

    /**
     * 修改授权配置
     */
    void updateAuthConfig(SaveTemplateAuthConfigInput input);


    /**
     * 查询企业是否指定类型的流程模板
     *
     * @param input
     * @return
     */
    boolean checkHasFlowTemplate(FlowTemplateHasTypesInput input);

    /**
     * 查询指定模板是否有设置授权关系
     * @param tenantOid
     * @param templateId
     * @param ignoreAll 忽略默认的ALL，没有ALL也算设置过授权关系
     * @return
     */
    boolean checkTemplateHasAuthList(String tenantOid, String templateId, boolean ignoreAll);


    /**
     * 通过流程模板ID和获取《流程模板》预览地址
     *
     * @param input
     * @return
     */
    String getFlowTemplatePreviewUrl(FlowTemplatePreviewInput input);

    /**
     * 基于文件批量创建epaas文件模板
     * @param input
     * @return
     */
    CreateDocTemplateResult batchCreateEpaasDocTemplate(CreateDocTemplateInput input);

    /**
     * 参与方关联规则
     */
    FlowTemplateParticipantRelationDataSourceRuleDTO participantRelationDataSourceRule(String subjectOid, List<String> dataSourceIds);

    /** 关联规则 */
    FlowTemplateRelationDataSourceRuleDTO relationDataSourceRule(List<String> dataSourceIds);

    /**
     * 查询应用模版数据源字段
     */
    List<DataSourceFieldsDTO> dataSourceField(List<String> dataSourceIds);

    /**
     * 提交定时发起
     * @param input
     */
    void scheduledStartFlowTemplate(ScheduledStartFlowTemplateInput input);

    /**
     * 校验参与方是否可指定印章
     * @param subjectId
     * @param flowTemplateId
     * @param participantIds
     * @return
     */
    CheckCooperationerSealAssignableResult checkCooperationerSealAssignable(String operatorId, String subjectId, String flowTemplateId, List<String> participantIds);

    /**
     * 获取epaas组建下拉配置
     * @param input
     * @return
     */
    EpaasStructChoiceListConfigResult epaasStructChoiceListConfig();
}
