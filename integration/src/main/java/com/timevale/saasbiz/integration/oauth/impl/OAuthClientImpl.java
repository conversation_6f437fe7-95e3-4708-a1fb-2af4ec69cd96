package com.timevale.saasbiz.integration.oauth.impl;

import com.timevale.oauth.facade.api.RpcOauthManageService;
import com.timevale.oauth.facade.model.authorize.base.PageList;
import com.timevale.oauth.facade.model.authorize.query.OauthMappingLogQueryInput;
import com.timevale.oauth.facade.model.authorize.query.OauthMappingQueryInput;
import com.timevale.oauth.facade.model.authorize.request.AuthCancelInput;
import com.timevale.oauth.facade.model.authorize.response.OauthMappingLogManagePageListOutput;
import com.timevale.oauth.facade.model.authorize.response.OauthMappingManageListOutput;
import com.timevale.saasbiz.integration.oauth.OAuthClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 认证授权服务
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Component
public class OAuthClientImpl implements OAuthClient {

    @Autowired RpcOauthManageService rpcOauthManageService;

    @Override
    public PageList<OauthMappingManageListOutput> queryScopeAuthMappings(
            OauthMappingQueryInput input) {
        return rpcOauthManageService.mappingList(input);
    }

    @Override
    public OauthMappingLogManagePageListOutput queryScopeAuthLogs(OauthMappingLogQueryInput input) {
        return rpcOauthManageService.mappingsLogList(input);
    }

    @Override
    public boolean cancelScopeAuth(AuthCancelInput input) {
        return rpcOauthManageService.cancelAuth(input);
    }
}
