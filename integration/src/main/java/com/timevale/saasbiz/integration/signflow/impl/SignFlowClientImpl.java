package com.timevale.saasbiz.integration.signflow.impl;

import com.timevale.flowmanager.common.service.api.FlowQueryService;
import com.timevale.footstone.rpc.api.FlowRpcService;
import com.timevale.footstone.rpc.model.flowmodel.QueryFlowDetailInput;
import com.timevale.footstone.rpc.model.flowmodel.QueryFlowInfoInput;
import com.timevale.footstone.rpc.result.baseresult.RpcResult;
import com.timevale.footstone.rpc.result.flowresult.FlowInfoOutput;
import com.timevale.footstone.rpc.result.flowresult.SignerQueryOutput;
import com.timevale.footstone.rpc.result.flowresult.bean.SignFlowDetailOutput;
import com.timevale.saasbiz.integration.signflow.SignFlowClient;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 签署流程相关接口
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Component
public class SignFlowClientImpl implements SignFlowClient {

    @Autowired FlowRpcService flowRpcService;

    @Autowired
    FlowQueryService flowQueryService;

    @Override
    public SignFlowDetailOutput queryFlowDetail(String flowId) {
        QueryFlowDetailInput input = new QueryFlowDetailInput();
        input.setFlowId(flowId);

        RpcResult<SignFlowDetailOutput> result = flowRpcService.queryFlowDetail(input);

        if (result == null || !result.ifSuccess() || result.getData() == null) {
            throw new SaasBizException(SaasBizResultCode.SAAS_SERVICE_BUSY);
        }

        return result.getData();
    }

    @Override
    public FlowInfoOutput queryFlowInfo(String flowId) {
        QueryFlowInfoInput input = new QueryFlowInfoInput();
        input.setFlowId(flowId);

        RpcResult<FlowInfoOutput> result = flowRpcService.queryFlowInfo(input);

        if (result == null || !result.ifSuccess() || result.getData() == null) {
            throw new SaasBizException(SaasBizResultCode.SAAS_SERVICE_BUSY);
        }

        return result.getData();
    }

    @Override
    public SignerQueryOutput querySigners(String flowId) {
        RpcResult<SignerQueryOutput> result = flowRpcService.querySigners(flowId);

        if (result == null || !result.ifSuccess() || result.getData() == null) {
            throw new SaasBizException(SaasBizResultCode.SAAS_SERVICE_BUSY);
        }

        return result.getData();
    }
}
