package com.timevale.saasbiz.integration.signflow;

import com.timevale.footstone.rpc.result.flowresult.FlowInfoOutput;
import com.timevale.footstone.rpc.result.flowresult.SignerQueryOutput;
import com.timevale.footstone.rpc.result.flowresult.bean.SignFlowDetailOutput;

/**
 * 签署流程相关接口
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
public interface SignFlowClient {

    /**
     * 查询签署流程详情信息
     *
     * @param flowId 签署流程id
     * @return
     */
    SignFlowDetailOutput queryFlowDetail(String flowId);

    /** 查询签署流程基本信息 */
    FlowInfoOutput queryFlowInfo(String flowId);

    /** 获取签署人列表 */
    SignerQueryOutput querySigners(String flowId);
}
