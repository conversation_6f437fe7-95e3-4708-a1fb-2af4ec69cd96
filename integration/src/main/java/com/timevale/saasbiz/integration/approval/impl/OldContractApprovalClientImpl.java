package com.timevale.saasbiz.integration.approval.impl;

import com.timevale.account.flow.service.api.RpcAccountApprovalService;
import com.timevale.account.flow.service.api.RpcFlowDefineService;
import com.timevale.account.flow.service.model.approval.input.ApprovalInfoInput;
import com.timevale.account.flow.service.model.approval.model.ApprovalInfoModel;
import com.timevale.account.flow.service.model.approval.output.ApprovalInfoOutput;
import com.timevale.account.flow.service.model.input.FlowModelInput;
import com.timevale.account.flow.service.model.input.FlowModelMemberInput;
import com.timevale.account.flow.service.model.output.FlowModelMemberOutput;
import com.timevale.account.flow.service.model.output.FlowModelOutput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.flow.facade.service.api.ContractApprovalAdaptRpcService;
import com.timevale.flow.facade.service.model.input.adapt.ApprovalAgreeAdaptInput;
import com.timevale.flow.facade.service.model.input.adapt.ApprovalRefuseAdaptInput;
import com.timevale.flow.facade.service.model.input.adapt.ApprovalUrgeAdaptInput;
import com.timevale.flow.facade.service.model.input.adapt.ContractApprovalRestartAdaptInput;
import com.timevale.flow.facade.service.model.output.adapt.ContractApprovalApprovalUrlAdaptOutput;
import com.timevale.flow.facade.service.model.output.adapt.ContractApprovalFlowAdaptOutput;
import com.timevale.saasbiz.integration.approval.OldContractApprovalClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/4/14 16:46
 */
@Component
public class OldContractApprovalClientImpl implements OldContractApprovalClient {

    @Autowired
    private RpcFlowDefineService rpcFlowDefineService;
    @Autowired
    private ContractApprovalAdaptRpcService contractApprovalAdaptRpcService;
    @Autowired
    private RpcAccountApprovalService rpcAccountApprovalService;

    @Override
    public FlowModelOutput getFlowDefineDetailById(String organId, String approvalTemplateId) {
        FlowModelInput flowModelInput = new FlowModelInput();
        flowModelInput.setModelId(approvalTemplateId);
        flowModelInput.setOrganId(organId);
        RpcInput<FlowModelInput> input = new RpcInput<>();
        input.setInput(flowModelInput);
        return rpcFlowDefineService.getFlowDefineDetailById(input).getData();
    }


    @Override
    public List<FlowModelMemberOutput> availableApprovalTemplate(FlowModelMemberInput flowModelMemberInput) {
        RpcInput<FlowModelMemberInput> input = new RpcInput<>();
        input.setInput(flowModelMemberInput);
        return rpcFlowDefineService.getFlowDefineByMember(input).getData();
    }


    @Override
    public ContractApprovalFlowAdaptOutput approvalFlow(Long approvalId, String orgId, String accountId) {
        return contractApprovalAdaptRpcService.approvalFlow(approvalId,orgId, accountId);
    }

    @Override
    public void restart(ContractApprovalRestartAdaptInput input) {
         contractApprovalAdaptRpcService.restart(input);
    }

    @Override
    public void agree(ApprovalAgreeAdaptInput model) {
        contractApprovalAdaptRpcService.agree(model);
    }

    @Override
    public void approvalGroupAgree(String approvalGroupId, String orgId, String accountId, String appId) {
        contractApprovalAdaptRpcService.approvalGroupAgree(approvalGroupId, orgId, accountId, appId);
    }

    @Override
    public void refuse(ApprovalRefuseAdaptInput input) {
        contractApprovalAdaptRpcService.refuse(input);
    }

    @Override
    public void approvalGroupRefuse(String approvalGroupId, String orgId, String accountId, String approvalRemark, String appId) {
        contractApprovalAdaptRpcService.approvalGroupRefuse(approvalGroupId, orgId, accountId, approvalRemark, appId);
    }

    @Override
    public void revoke(Long approvalId, String orgId, String accountId, String approvalRemark, String appId) {
        contractApprovalAdaptRpcService.revoke(approvalId, orgId, accountId, approvalRemark, appId);
    }

    @Override
    public ContractApprovalApprovalUrlAdaptOutput getApprovalUrl(Long approvalId, String orgId, String accountId, String token, String clientType, Boolean isNeedShare) {
        return contractApprovalAdaptRpcService.getApprovalUrl(approvalId, orgId, accountId, token, clientType, isNeedShare);
    }

    @Override
    public void urge(ApprovalUrgeAdaptInput input) {
        contractApprovalAdaptRpcService.urge(input);
    }

    @Override
    public ApprovalInfoModel getApprovalInfo(Long approvalId) {
        RpcInput<ApprovalInfoInput> input = new RpcInput<>();
        ApprovalInfoInput approvalInfoInput = new ApprovalInfoInput();
        approvalInfoInput.setApprovalId(approvalId);
        input.setInput(approvalInfoInput);
        return Optional.ofNullable(rpcAccountApprovalService.getApprovalInfo(input).getData())
                .map(ApprovalInfoOutput::getApprovalInfo).orElse(null);
    }

}
