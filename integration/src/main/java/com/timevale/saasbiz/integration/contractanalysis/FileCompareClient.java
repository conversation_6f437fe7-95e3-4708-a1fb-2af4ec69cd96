package com.timevale.saasbiz.integration.contractanalysis;

import com.timevale.contractanalysis.facade.api.ao.compare.AddCompareRequest;
import com.timevale.contractanalysis.facade.api.ao.compare.CompareIdRequest;
import com.timevale.contractanalysis.facade.api.ao.compare.QueryCompareRequest;
import com.timevale.contractanalysis.facade.api.bo.compare.AddCompareResult;
import com.timevale.contractanalysis.facade.api.bo.compare.CompareFileDownloadUrlResult;
import com.timevale.contractanalysis.facade.api.bo.compare.CompareRecordsResult;
import com.timevale.mandarin.common.result.BaseResult;

import java.util.List;

/**
 * TODO 功能说明
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
public interface FileCompareClient {
    String addCompare(AddCompareRequest request);

    void retryCompare(String compareId);

    /**
     * 获取合同比对文件下载地址
     * @param compareId
     * @return
     */
    CompareFileDownloadUrlResult getCompareDownloadUrl(String compareId);


    CompareRecordsResult queryCompare(List<String> compareIdList);


}
