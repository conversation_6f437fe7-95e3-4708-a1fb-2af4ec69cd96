package com.timevale.saasbiz.integration.saascommon;

import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationLogStatusEnum;
import com.timevale.saas.common.manage.common.service.model.base.Page;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationBuildBeforeCheckInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationCreateLogInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationDeleteInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationOperatorLogCreateInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationOrderQueryInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationQueryShareConfigInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationSignFailureInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationSignSuccessInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationUpdateLogInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationUpdateShareConfigInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.CreateAuthRelationStartContractSuccessInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.DirectRescindAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.QueryPageAuthRelationListInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.QueryPageAuthRelationLogListInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.SignRescindAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.*;

import java.util.List;
import java.util.Map;

/**
 * Created by tianlei on 2022/2/17
 */
public interface AuthRelationInnerClient {

    AuthRelationCreateLogOutput createAuthRelationLog(AuthRelationCreateLogInput input);

    AuthRelationUpdateLogOutput updateAuthRelationLog(AuthRelationUpdateLogInput input);

    AuthRelationUpdateLogOutput updateAuthRelationLogStatusById(Long authRelationLogId,
                                                                AuthRelationLogStatusEnum logStatusEnum);

    AuthRelationStartContractSuccessOutput startContractSuccess(CreateAuthRelationStartContractSuccessInput input);

    AuthRelationSignSuccessOutput signSuccess(AuthRelationSignSuccessInput input);

    AuthRelationSignFailureOutput signFailure(AuthRelationSignFailureInput input);


    AuthRelationBuildBeforeCheckOutput startBeforeCheck(AuthRelationBuildBeforeCheckInput input);

    Page<AuthRelationBackendDTO> queryPageAuthRelation(QueryPageAuthRelationListInput input);

    Page<AuthRelationLogBackendDTO> queryPageAuthRelationLog(QueryPageAuthRelationLogListInput input);

    AuthRelationLogDTO getAuthRelationLogByProcessId(String processId);

    AuthRelationDetailDTO getAuthRelationDetailByAuthRelationId(Long authRelationId);

    Map<Long, AuthRelationDetailDTO> queryAuthRelationDetailMap(List<Long> authRelationIds);

    AuthRelationGetAuthRelationLastProcessOutput getAuthRelationLastProcess(Long authRelationId);

    AuthRelationLogDetailOutput getAuthRelationDetailByLogId(Long authRelationLogId);

    List<AuthRelationLogDTO> getAuthRelationLogByAuthRelationIds(List<Long> authRelationIds);

    AuthRelationLogDTO getAuthRelationLogByFlowId(String flowId);

    Boolean checkOneLevelMaxCount(String parentTenantGid, Integer addCount);

    AuthRelationQueryShareConfigOutput queryShareConfig(AuthRelationQueryShareConfigInput input);

    Map<Long, List<AuthRelationShareConfigDTO>> queryShareConfigMap(List<Long> authRelationIds);

    void updateShareConfig(AuthRelationUpdateShareConfigInput input);

    void createAuthRelationOrder(Long logId, String orderId);

    void unLockAuthRelationOrder(Long logId);

    List<AuthRelationOrderDTO> queryAuthRelationLockOrderList(AuthRelationOrderQueryInput input);

    void directRescindAuthRelation(DirectRescindAuthRelationInput input);

    void signRescindAuthRelation(SignRescindAuthRelationInput input);

    AuthRelationDeleteAuthRelationOutput deleteAuthRelation(AuthRelationDeleteInput input);

    void createOperatorLog(AuthRelationOperatorLogCreateInput input);
}
