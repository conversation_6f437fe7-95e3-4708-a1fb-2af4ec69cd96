package com.timevale.saasbiz.integration.contractanalysis;

import com.timevale.contractanalysis.facade.api.dto.contractcategory.*;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.bean.ContractCategoryDTO;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.bean.FlowTemplateContractCategoryDTO;
import com.timevale.contractanalysis.facade.api.request.contractcategory.*;

import java.util.List;

/**
 * 合同类型底层接口对接
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
public interface ContractCategoryClient {
    /**
     * 查询系统推荐的合同类型列表
     *
     * @param request
     * @return
     */
    QuerySysContractCategoriesResultDTO querySysContractCategories(
            QuerySysContractCategoriesRequest request);

    /**
     * 查询系统推荐的合同类型详情
     * @param request
     * @return
     */
    QuerySysContractCategoryResultDTO querySysContractCategoryDetail(
            QuerySysContractCategoryDetailRequest request);

    /**
     * 保存合同类型
     *
     * @param request
     * @return
     */
    SaveContractCategoryResultDTO saveContractCategory(SaveContractCategoryRequest request);

    /**
     * 分页查询合同类型列表
     *
     * @param request
     * @return
     */
    PageQueryContractCategoriesResultDTO pageQueryContractCategories(
            PageQueryContractCategoriesRequest request);

    /**
     * 查询可用合同类型列表
     *
     * @param request
     * @return
     */
    SimpleQueryUsableContractCategoriesResultDTO simpleQueryUsableContractCategories(
            QueryUsableContractCategoriesRequest request);

    /**
     * 查询合同类型详情
     *
     * @param request
     * @return
     */
    GetContractCategoryDetailResultDTO getContractCategoryDetail(
            GetContractCategoryDetailRequest request);

    /**
     * 校验合同类型名称是否已存在
     *
     * @param request
     * @return
     */
    CheckContractCategoryNameExistedResultDTO checkContractCategoryNameExisted(
            CheckContractCategoryNameExistedRequest request);

    /**
     * 批量更新合同类型状态
     *
     * @param request
     */
    void batchUpdateContractCategoryStatus(BatchUpdateContractCategoryStatusRequest request);

    /**
     * 修改合同类型的关联模板列表
     *
     * @param request
     */
    void relateFlowTemplateByCategoryId(RelateFlowTemplateByCategoryIdRequest request);

    /**
     * 清空合同类型的关联模板列表
     *
     * @param request
     */
    void clearFlowTemplateByCategoryId(ClearFlowTemplateByCategoryIdRequest request);

    /**
     * 查询合同类型的关联模板列表
     *
     * @param categoryId
     * @return
     */
    QueryRelateMappingByCategoryIdResultDTO queryRelatedMappingByCategoryId(String categoryId);

    /**
     * 查询流程模板的文件合同类型列表
     *
     * @param flowTemplateId
     * @return
     */
    QueryRelateMappingByFlowTemplateIdResultDTO queryRelatedMappingByFlowTemplateId(
            String flowTemplateId);

    /**
     * 批量查询流程模板的文件合同类型列表
     *
     * @param flowTemplateIds
     * @return
     */
    List<FlowTemplateContractCategoryDTO> queryRelatedMappingByFlowTemplateIds(
            List<String> flowTemplateIds);

    /**
     * 批量查询合同类型列表
     *
     * @param request
     * @return
     */
    List<GetContractCategoryDetailResultDTO> batchQueryContractCategories(
            BatchQueryContractCategoriesRequest request);
}
