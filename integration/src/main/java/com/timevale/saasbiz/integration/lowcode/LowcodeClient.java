package com.timevale.saasbiz.integration.lowcode;

import com.timevale.besp.lowcode.integration.request.FormPageQueryRequest;
import com.timevale.besp.lowcode.integration.response.FormFieldListResponse;
import com.timevale.besp.lowcode.integration.response.FormResponse;
import com.timevale.besp.lowcode.integration.response.PageInfo;
import com.timevale.besp.lowcode.integration.response.SkuPageResponse;
import com.timevale.besp.saas.saassupportmembershippackages.configurationrpcfacade.response.ConfigurationQueryResponse;
import com.timevale.saas.common.manage.common.service.enums.VipCodeEnum;
import com.timevale.saasbiz.integration.lowcode.impl.LowcodeClientImpl;
import com.timevale.saasbiz.model.bean.lowcode.DataSourceChannelDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/12 10:41
 */
public interface LowcodeClient {

    /** 数据源渠道列表 */
    List<DataSourceChannelDTO> subjectDatasourceChannelList(String subjectOid);

    /** 数据源分页列表 */
    PageInfo<FormResponse> dataSourcePage(FormPageQueryRequest formPageQueryRequest);

    /** 表单列表 */
    List<FormResponse> formList(String subjectOid, List<String> formIds);


    FormFieldListResponse formSync(String channel, String subjectOid, String formId);

    /**
     * 根据客户端 ID 和钉钉企业 ID 获取订单信息。
     *
     * <p>该方法用于根据传入的客户端 ID 和钉钉企业 ID 从低代码平台获取订单的分页信息， 返回一个包含订单分页信息的 SkuPageResponse 对象。这里的
     * SkuPageResponse 虽然名称带有 SkuPage，但实际代表订单信息。
     *
     * @param clientId 客户端的唯一标识符，用于标识不同的客户端
     * @param corpId 钉钉企业的唯一标识符，用于标识不同的钉钉企业
     * @return 包含订单分页信息的 SkuPageResponse 对象
     */
    SkuPageResponse getSkuPage(String clientId, String corpId);

    /**
     * 获取版本功能描述信息
     *
     * @param vipCodeEnum
     * @return
     */
    List<ConfigurationQueryResponse> fetchVersionFunctionDesc(VipCodeEnum vipCodeEnum);

    /**
     * 获取版本功能图标信息
     *
     * @param vipCodeEnum
     * @return
     */
    List<LowcodeClientImpl.VersionFunctionIcon> fetchVersionFunctionIcon(VipCodeEnum vipCodeEnum);

    /**
     * 获取会员产品图标信息
     *
     * @return
     */
    List<ConfigurationQueryResponse> fetchVipProductIcons(VipCodeEnum vipCodeEnum);

    /**
     * 通过目标获取会员产品图标信息
     *
     * @return
     */
    List<ConfigurationQueryResponse> fetchVipProductIconByTarget(
            VipCodeEnum vipCodeEnum, String configureTarget);

    /**
     * 获取会员版本下默认的功能图标信息
     *
     * @return
     */
    ConfigurationQueryResponse getDefaultFunctionIcon(VipCodeEnum vipCodeEnum);
}
