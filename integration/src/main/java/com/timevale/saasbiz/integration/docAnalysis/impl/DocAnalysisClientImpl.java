package com.timevale.saasbiz.integration.docAnalysis.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.docanalysis.facade.api.RpcDocAnalysisService;
import com.timevale.docanalysis.facade.api.dto.input.RpcDocAnalysisInputDTO;
import com.timevale.docanalysis.facade.api.dto.output.RpcDocAnalysisOutputDTO;
import com.timevale.saasbiz.integration.aop.MethodException;
import com.timevale.saasbiz.integration.docAnalysis.DocAnalysisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.timevale.saasbiz.model.exception.SaasBizResultCode.DOC_ANALYSIS_COMMON_ERROR;

@Component
@Slf4j
public class DocAnalysisClientImpl implements DocAnalysisClient {

    @Autowired
    private RpcDocAnalysisService rpcDocAnalysisService;

    @MethodException(code = DOC_ANALYSIS_COMMON_ERROR, message = "文件解析失败")
    @Override
    public RpcDocAnalysisOutputDTO docAnalysis(RpcDocAnalysisInputDTO dto) {
        log.info("DocAnalysisClientImpl docAnalysis param:{}", JSON.toJSONString(dto));
        RpcDocAnalysisOutputDTO rpcDocAnalysisOutputDTO = rpcDocAnalysisService.docAnalysis(dto);
        log.info("DocAnalysisClientImpl docAnalysis result:{}", JSON.toJSONString(rpcDocAnalysisOutputDTO));
        return rpcDocAnalysisOutputDTO;
    }

}
