package com.timevale.saasbiz.integration.process.impl;

import com.timevale.contractmanager.common.service.api.RpcProcessPermissionService;
import com.timevale.contractmanager.common.service.api.RpcProcessService;
import com.timevale.contractmanager.common.service.api.RpcProcessStartService;
import com.timevale.contractmanager.common.service.api.grouping.RpcPermissionService;
import com.timevale.contractmanager.common.service.bean.permission.GetAllMenuPermissionsModel;
import com.timevale.contractmanager.common.service.model.*;
import com.timevale.contractmanager.common.service.model.processpermission.CheckUserProcessViewableModel;
import com.timevale.contractmanager.common.service.model.processpermission.GetProcessAccountRoleModel;
import com.timevale.contractmanager.common.service.result.*;
import com.timevale.contractmanager.common.service.result.processpermission.CheckUserProcessViewableResult;
import com.timevale.contractmanager.common.service.result.processpermission.GetProcessAccountRoleResult;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.saasbiz.integration.process.ProcessClient;

import com.timevale.signflow.search.service.api.contractprocess.ContractProcessQueryRpcService;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-04-04 11:39
 */
@Component
public class ProcessClientImpl implements ProcessClient {
    @Autowired private RpcProcessService rpcProcessService;
    @Autowired private RpcProcessStartService rpcProcessStartService;
    @Autowired private RpcProcessPermissionService rpcProcessPermissionService;
    @Autowired private RpcPermissionService rpcPermissionService;
    @Autowired private ContractProcessQueryRpcService contractProcessQueryRpcService;

    @Override
    public StartProcessResult startProcess(StartBizProcessModel model) {
        return rpcProcessStartService.startBizProcess(model);
    }

    @Override
    public CheckUserProcessViewableResult checkUserProcessViewable(
            CheckUserProcessViewableModel model) {
        return rpcProcessPermissionService.checkUserProcessViewable(model);
    }

    @Override
    public CheckUserProcessViewableResult checkUserProcessViewable(String processId, String subjectId, String accountId, String menuId) {
        CheckUserProcessViewableModel model = new CheckUserProcessViewableModel();
        model.setProcessId(processId);
        model.setSubjectId(subjectId);
        model.setAccountId(accountId);
        model.setMenuId(menuId);
        return rpcProcessPermissionService.checkUserProcessViewable(model);
    }

    @Override
    public QueryProcessDetailResult queryProcessDetail(String processId) {
        return rpcProcessService.queryProcessDetail(processId);
    }

    @Override
    public ProcessBaseResult queryProcessBase(String processId) {
        QueryProcessBaseModel model = new QueryProcessBaseModel();
        model.setProcessId(processId);
        return rpcProcessService.base(model);
    }

    @Override
    public boolean hasSubProcess(String processId, int subProcessType) {
        QueryHasSubProcessModel model = new QueryHasSubProcessModel();
        model.setProcessId(processId);
        model.setSubProcessType(subProcessType);
        return rpcProcessService.hasSubProcess(model);
    }

    @Override
    public boolean checkProcessRelevant(String processId, String accountId, String subjectId) {
        return checkProcessRelevant(processId, accountId, subjectId, true);
    }

    @Override
    public boolean checkProcessRelevant(String processId, String accountId, String subjectId, boolean involveCC) {
        CheckProcessRelevantModel model = new CheckProcessRelevantModel();
        model.setProcessId(processId);
        model.setAccountId(accountId);
        model.setSubjectId(subjectId);
        model.setInvolveCC(involveCC);
        return BooleanUtils.isTrue(rpcProcessService.checkProcessRelevant(model));
    }

    @Override
    public boolean checkProcessSubjectRelevant(String processId, String subjectId) {
        CheckProcessSubjectRelevantModel model = new CheckProcessSubjectRelevantModel();
        model.setProcessId(processId);
        model.setSubjectId(subjectId);
        return BooleanUtils.isTrue(rpcProcessService.checkProcessSubjectRelevant(model));
    }

    @Override
    public boolean checkInitiatorSubject(String processId, String subjectId) {
        CheckProcessSubjectRelevantModel model = new CheckProcessSubjectRelevantModel();
        model.setProcessId(processId);
        model.setSubjectId(subjectId);
        return BooleanUtils.isTrue(rpcProcessService.checkInitiatorSubject(model));
    }

    @Override
    public Set<String> getMenuOperationPermissions(GetAllMenuPermissionsModel model) {
        return rpcPermissionService.getAllMenuPermissions(model);
    }

    @Override
    public ProcessConfigResult queryProcessConfig(String processId) {
        return rpcProcessService.queryProcessConfig(processId);
    }

    @Override
    public List<ProcessConfigResult> batchQueryProcessConfig(List<String> processIds) {
        BatchQueryProcessConfigModel model = new BatchQueryProcessConfigModel();
        model.setProcessIds(processIds);
        return rpcProcessService.batchQueryProcessConfig(model).getProcessConfigs();
    }

    @Override
    public ContractProcessDTO getByProcessId(String processId) {
        return contractProcessQueryRpcService.getByProcessId(processId);
    }

    @Override
    public List<ContractProcessDTO> listByProcessIds(List<String> processIds) {
        return contractProcessQueryRpcService.listByProcessIds(processIds);
    }

    @Override
    public Set<String> getAllMenuPermissions(GetAllMenuPermissionsModel model) {
        return rpcPermissionService.getAllMenuPermissions(model);
    }

    @Override
    public List<String> getProcessAccountRoles(GetProcessAccountRoleModel model) {
        GetProcessAccountRoleResult result = rpcProcessPermissionService.getProcessAccountRoles(model);
        return result.getRoles();
    }

    @Override
    public QueryProcessSecretConfigResult queryProcessSecretConfig(QueryProcessSecretConfigModel model) {
        return rpcProcessService.queryProcessSecretConfig(model);
    }

    public List<QueryProcessFilesResult.ProcessFile> getProcessFiles(String processId) {
        QueryProcessFilesResult result = rpcProcessService.queryProcessFiles(processId);
        return result.getProcessFiles();
    }

    @Override
    public ProcessFileAuthResult queryProcessFileAuth(QueryProcessFileAuthModel model) {
        return rpcProcessService.queryProcessFileAuth(model);
    }

    @Override
    public ProcessContractResult listProcessContract(String processId) {
        return rpcProcessService.listProcessContract(processId);
    }

    @Override
    public ProcessStartFlowTemplateResult queryProcessUnStandardInfo(ProcessStartFlowTemplateModel input) {
        return rpcProcessStartService.getStartFlowTemplateInfo(input);
    }

    @Override
    public ProcessUrlResult processUrl(ProcessUrlModel model) {
        return rpcProcessService.processUrl(model);
    }
}
