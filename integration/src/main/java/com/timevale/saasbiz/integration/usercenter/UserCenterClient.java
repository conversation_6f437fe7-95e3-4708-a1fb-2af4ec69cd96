package com.timevale.saasbiz.integration.usercenter;

import com.timevale.account.organization.service.model.service.newbiz.output.BizGetOrganOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizMemberGetOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizFatICUserOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.easun.service.model.account.output.EasunSpaceDeptOutput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdcardInfoOutput;
import com.timevale.easun.service.model.mods.MemberDetail;
import com.timevale.easun.service.model.organization.input.BizEasunOrganInputV2;
import com.timevale.easun.service.model.organization.output.BizDeptBaseInfo;
import com.timevale.easun.service.model.organization.output.BizDeptInfoOutput;
import com.timevale.easun.service.model.organization.output.BizDeptMemberOutput;
import com.timevale.easun.service.model.organization.output.v3.BizDeptListOutputV3;
import com.timevale.easun.service.model.organization.output.v3.BizOrgSummaryOutputV3;

import java.util.List;
import java.util.Map;

/**
 * 用户中心
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface UserCenterClient {

    /**
     * 根据oid获取查询账号基本信息
     *
     * @param oid
     * @return
     */
    BizICUserOutput getICUserBaseInfo(String oid);

    /**
     * 根据oid获取查询账号详情，包括实名状态
     *
     * @param oid
     * @return
     */
    BizAccountRealNameOutput getRealNameByOid(String oid);

    /**
     * 批量根据oid获取查询账号详情，包括实名状态
     *
     * @param oidList
     * @return
     */
    List<BizAccountRealNameOutput> batchGetRealNameByOid(List<String> oidList);

    /**
     * 通过ES批量根据oid获取查询账号详情，包括实名状态
     *
     * @param oidList
     * @return
     */
    List<BizAccountRealNameOutput> batchGetRealNameByOidByES(List<String> oidList);

    /**
     * 批量根据oid获取查询账号详情，包括实名状态
     *
     * @param accountIds
     * @return
     */
    List<BizAccountRealNameOutput> getRealNameByOuidsSimple(List<String> accountIds);

    /**
     * 获取用户直属部门列表
     * @param subjectOid
     * @param userOid
     * @return
     */
    List<BizDeptInfoOutput> userDirectDept(String subjectOid, String userOid);

    /**
     * 校验用户是否在企业里， 即校验是否企业成员
     * @param personOid
     * @param subjectOid
     */
    boolean checkMemberInSubject(String subjectOid, String personOid);

    /** oid 转 organId */
    String subjectOidToOrganId(String subjectOid);

    List<BizMemberGetOutput> batchGetMembersByGuid(String subjectOid, List<String> gids);

    /**
     * 查询用户作为管理员的企业列表
     *
     * @param accountId
     * @return
     */
    List<BizICUserOutput> queryAccountAdminOrgs(String accountId);

    /**
     * 校验账号是否第一次实名
     *
     * @param accountUid
     * @return
     */
    boolean checkIsFirstRealNameByAccountUid(String accountUid);

    /**
     * 根据accountUid获取用户基本账号信息
     *
     * @param accountUid
     * @return
     */
    BizICUserBaseOutput getICUserBaseByAccountUid(String accountUid);

    /**
     * 判断一批部门是否属于一个企业，存在一个部门不属于该企业则返回false
     *
     * @param deptIdList 部门oid列表
     * @param orgOid 企业oid
     * @return
     */
    boolean checkDeptsBelongToTheOrg(List<String> deptIdList, String orgOid);

    /**
     * 判断一批成员是否属于一个企业，存在一个成员不属于该企业则返回false
     *
     * @param memberIdList 成员oid列表
     * @param orgOid 企业oid
     * @return
     */
    boolean checkMembersBelongToTheOrg(List<String> memberIdList, String orgOid);

    /**
     * 根据部门oid查询部门详情
     *
     * @param orgOidList 企业oid列表
     * @return
     */
    BizDeptListOutputV3 batchGetDeptDetailByOid(List<String> orgOidList);
    /**
     * 获取用户详情， 不包含实名状态， 包含注销账号
     *
     * @param userOid 用户oid
     * @return
     */
    BizFatICUserOutput getFatAccountDetailByOid(String userOid);

    /**
     * 获取隔离部门情况
     */
    Map<String, EasunSpaceDeptOutput> checkMultiBizZonePermission(String subjectOid,
                                                                  String operatorOid,
                                                                  List<String> deptIds);

    BizDeptListOutputV3 getBatchDeptDetails(List<String> deptIds);

    BizOrgSummaryOutputV3 getOrgSummary(String subjectOid);
    /**
     * 通过oid获取organ
     * @param oid
     * @return
     */
    BizGetOrganOutput getOrganByOid(String oid);

    /**
     * 获取当前组织下部门组合信息
     *
     * @param input
     * @return
     */
    BizDeptMemberOutput getDeptAndMemberInfoOrgan(BizEasunOrganInputV2 input);

    /**
     * 查询企业下的员工
     * @param oid
     * @return
     */
    List<MemberDetail> getDeptAndMemberInfoByIds(String oid, List<String> memberOidList);


    String queryAccountGidByIdNo(String idNo);

    /**
     * 获取登陆凭证
     *
     * @param accountOid
     * @return
     */
    List<EsAccountIdcardInfoOutput> getAccountIdCardInfoByEs(String accountOid);

    /**
     * 查询成员所有部门信息
     * @param operatorId
     * @param tenantId
     * @return
     */
    List<BizDeptBaseInfo> getMemberAllDept(String operatorId, String tenantId);

    /**
     * 批量统计角色下成员数量
     * @param subjectOid
     * @return
     */
    Map<String, Long> getRoleMemberCountWithList(String subjectOid);

    /**
     * 批量查询企业管理员
     * @param orgIds
     * @return
     */
    Map<String, List<MemberDetail>> batchQueryOrgAdmins(List<String> orgIds);
}
