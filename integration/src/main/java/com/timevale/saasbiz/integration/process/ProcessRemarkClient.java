package com.timevale.saasbiz.integration.process;

import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkAddRequest;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkResult;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkVisibleListRequest;

import java.util.List;

/**
 * 合同备注客户端
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface ProcessRemarkClient {
    /**
     * 添加备注
     *
     */
    void addRemark(ProcessRemarkAddRequest request);

    /**
     * 查询合同备注列表
     * @param processId
     * @return
     */
    List<ProcessRemarkResult> listByProcessId(String processId);

    /**
     * 更具流程id查询可见的合同备注
     * @param request
     * @return
     */
    List<ProcessRemarkResult> visibleListByProcessId(ProcessRemarkVisibleListRequest request);
}
