package com.timevale.saasbiz.integration.contractanalysis.impl;

import com.timevale.contractanalysis.facade.api.contractcategory.ContractCategoryFacadeService;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.*;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.bean.ContractCategoryDTO;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.bean.FlowTemplateContractCategoryDTO;
import com.timevale.contractanalysis.facade.api.request.contractcategory.*;
import com.timevale.saasbiz.integration.contractanalysis.ContractCategoryClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同类型底层接口对接实现
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Service
public class ContractCategoryClientImpl implements ContractCategoryClient {

    @Autowired ContractCategoryFacadeService contractCategoryFacadeService;

    @Override
    public QuerySysContractCategoriesResultDTO querySysContractCategories(
            QuerySysContractCategoriesRequest request) {
        return contractCategoryFacadeService.querySysContractCategories(request);
    }

    @Override
    public QuerySysContractCategoryResultDTO querySysContractCategoryDetail(
            QuerySysContractCategoryDetailRequest request) {
        return contractCategoryFacadeService.querySysContractCategoryDetail(request);
    }

    public SaveContractCategoryResultDTO saveContractCategory(SaveContractCategoryRequest request) {
        return contractCategoryFacadeService.saveContractCategory(request);
    }

    @Override
    public PageQueryContractCategoriesResultDTO pageQueryContractCategories(
            PageQueryContractCategoriesRequest request) {
        return contractCategoryFacadeService.pageQueryContractCategories(request);
    }

    @Override
    public SimpleQueryUsableContractCategoriesResultDTO simpleQueryUsableContractCategories(
            QueryUsableContractCategoriesRequest request) {
        return contractCategoryFacadeService.simpleQueryUsableContractCategories(request);
    }

    @Override
    public GetContractCategoryDetailResultDTO getContractCategoryDetail(
            GetContractCategoryDetailRequest request) {
        return contractCategoryFacadeService.getContractCategoryDetail(request);
    }

    @Override
    public CheckContractCategoryNameExistedResultDTO checkContractCategoryNameExisted(
            CheckContractCategoryNameExistedRequest request) {
        return contractCategoryFacadeService.checkContractCategoryNameExisted(request);
    }

    @Override
    public void batchUpdateContractCategoryStatus(
            BatchUpdateContractCategoryStatusRequest request) {
        contractCategoryFacadeService.batchUpdateContractCategoryStatus(request);
    }

    @Override
    public void relateFlowTemplateByCategoryId(RelateFlowTemplateByCategoryIdRequest request) {
        contractCategoryFacadeService.relateFlowTemplateByCategoryId(request);
    }

    @Override
    public void clearFlowTemplateByCategoryId(ClearFlowTemplateByCategoryIdRequest request) {
        contractCategoryFacadeService.clearFlowTemplateByCategoryId(request);
    }

    @Override
    public QueryRelateMappingByCategoryIdResultDTO queryRelatedMappingByCategoryId(
            String categoryId) {
        return contractCategoryFacadeService.queryRelatedMappingByCategoryId(categoryId);
    }

    @Override
    public QueryRelateMappingByFlowTemplateIdResultDTO queryRelatedMappingByFlowTemplateId(
            String flowTemplateId) {
        return contractCategoryFacadeService.queryRelatedMappingByFlowTemplateId(flowTemplateId);
    }

    @Override
    public List<FlowTemplateContractCategoryDTO> queryRelatedMappingByFlowTemplateIds(
            List<String> flowTemplateIds) {
        BatchQueryFlowTemplateContractCategoriesRequest request =
                new BatchQueryFlowTemplateContractCategoriesRequest();
        request.setFlowTemplateIds(flowTemplateIds);
        return contractCategoryFacadeService
                .batchQueryRelatedMappingByFlowTemplateIds(request)
                .getFlowTemplateContractCategories();
    }

    @Override
    public List<GetContractCategoryDetailResultDTO> batchQueryContractCategories(
            BatchQueryContractCategoriesRequest request) {
        return contractCategoryFacadeService.batchQueryContractCategories(request).getCategories();
    }
}
