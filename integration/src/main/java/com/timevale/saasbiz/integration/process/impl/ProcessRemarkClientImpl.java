package com.timevale.saasbiz.integration.process.impl;

import com.timevale.contractmanager.common.service.api.RpcProcessRemarkService;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkAddRequest;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkListResult;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkResult;
import com.timevale.contractmanager.common.service.bean.process.ProcessRemarkVisibleListRequest;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saasbiz.integration.process.ProcessRemarkClient;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 合同备注接口实现
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Component
@Slf4j
public class ProcessRemarkClientImpl implements ProcessRemarkClient {

    @Autowired
    private RpcProcessRemarkService rpcProcessRemarkService;

    @Override
    public void addRemark(ProcessRemarkAddRequest request) {
        rpcProcessRemarkService.addRemark(request);
    }

    @Override
    public List<ProcessRemarkResult> listByProcessId(String processId) {
        ProcessRemarkListResult listResult = rpcProcessRemarkService.listByProcessId(processId);
        return Optional.ofNullable(listResult)
                .map(ProcessRemarkListResult::getList)
                .orElseGet(Lists::newArrayList);
    }

    @Override
    public List<ProcessRemarkResult> visibleListByProcessId(ProcessRemarkVisibleListRequest request) {
        return rpcProcessRemarkService.visibleListByProcessId(request).getList();
    }
}
