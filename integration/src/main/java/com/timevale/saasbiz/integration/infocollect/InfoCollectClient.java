package com.timevale.saasbiz.integration.infocollect;

import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectCanAuthRoleResultDTO;
import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectDataStatusDetailDTO;
import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectFlowNodeDTO;
import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectResourceAuthDTO;
import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectTemplateFormRelationDTO;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfCollectQueryResourceAuthRequest;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfoCollectBatchEditResourceAuthRequest;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfoCollectEditResourceAuthRequest;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfoCollectFormCopyKeyRequest;
import com.timevale.contractanalysis.facade.api.request.infocollect.InfoCollectFormCopyRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/30 16:10
 */
public interface InfoCollectClient {

    /**
     * 表单复制-获取凭证
     */
    String formCopyKey(InfoCollectFormCopyKeyRequest request);

    /**
     * 表单复制
     */
    String formCopy(InfoCollectFormCopyRequest request);

    /**
     * 获取可授权的角色
     */
    InfoCollectCanAuthRoleResultDTO canAuthRole( String subjectOid);

    /**
     * 编辑资源授权
     */
    void editResourceAuth(InfoCollectEditResourceAuthRequest request);

    /**
     * 批量编辑资源授权
     */
    void batchEditResourceAuth(InfoCollectBatchEditResourceAuthRequest request);

    /**
     * 查询已授权的列表
     */
    List<InfoCollectResourceAuthDTO> queryResourceAuth(InfCollectQueryResourceAuthRequest request);
    
    /**
     * 查询关联关系
     */
    List<InfoCollectTemplateFormRelationDTO> queryTemplateFormRelationByForm(String subjectOid, String formId, String status);

    /**
     * 获取企业是否有表单
     * @param tenantOid
     * @return
     */
    boolean hasForm(String tenantOid, boolean upgrade);

    /**
     * 查询关联关系
     */
    List<InfoCollectTemplateFormRelationDTO> queryTemplateFormRelationByTemplate(String subjectOid, String templateId);

    /**
     * flow
     */
    List<InfoCollectFlowNodeDTO> formFlow(String subjectOid, String formId);
    
    /**
     * 获取任务详情
     */
    InfoCollectDataStatusDetailDTO queryDataStatusDetail(String dataId, String taskId, String taskKey);
}
