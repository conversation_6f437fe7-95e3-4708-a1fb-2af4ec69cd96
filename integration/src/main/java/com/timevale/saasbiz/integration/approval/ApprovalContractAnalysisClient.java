package com.timevale.saasbiz.integration.approval;

import com.timevale.contractapproval.facade.dto.ApprovalContractAnalysisRelationDTO;
import com.timevale.contractapproval.facade.dto.ContractAnalysisGatherDTO;
import com.timevale.contractapproval.facade.input.ApprovalContractAnalysisCreateInput;
import com.timevale.contractapproval.facade.input.ApprovalContractAnalysisPageInput;
import com.timevale.contractapproval.facade.input.ApprovalContractAnalysisQueryInput;
import com.timevale.contractapproval.facade.input.ApprovalContractAnalysisUpdateInput;
import com.timevale.mandarin.common.result.PageQueryResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-03-31 10:44
 */
public interface ApprovalContractAnalysisClient {


    /**
     * 合同比对创建
     *
     * @param input
     */
    void createContractAnalysis(ApprovalContractAnalysisCreateInput input);

    /**
     * 更新合同比对结果
     *
     * @param input
     */
    void updateContractAnalysis(ApprovalContractAnalysisUpdateInput input);

    ApprovalContractAnalysisRelationDTO queryContractAnalysisDetail(String compareId);

    /**
     * 分页查询合同比对记录
     *
     * @param input
     * @return
     */
    PageQueryResult<ApprovalContractAnalysisRelationDTO> queryContractAnalysisPage(
            ApprovalContractAnalysisPageInput input);


    /**
     * 查询合同比对记录
     * @param input
     * @return
     */
    List<ApprovalContractAnalysisRelationDTO> queryContractAnalysisResult(ApprovalContractAnalysisQueryInput input);
}
