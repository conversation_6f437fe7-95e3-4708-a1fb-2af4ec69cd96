package com.timevale.saasbiz.integration.seal;

import com.timevale.footstone.seal.facade.saas.input.CreateSealGrantConfirmUrlInput;
import com.timevale.footstone.seal.facade.saas.input.DoGrantInput;
import com.timevale.footstone.seal.facade.saas.input.QuerySealGrantGroupDetailInput;
import com.timevale.footstone.seal.facade.saas.input.QuerySealGrantResultInput;
import com.timevale.footstone.seal.facade.saas.output.CreateSealGrantConfirmUrlOutput;
import com.timevale.footstone.seal.facade.saas.output.DoGrantOutput;
import com.timevale.footstone.seal.facade.saas.output.QuerySealGrantGroupDetailOutput;
import com.timevale.footstone.seal.facade.saas.output.QuerySealGrantResultOutput;

public interface SealRuleGrantConfirmClient {

    /**
     * 立即授权
     * 会返回：
     * 1、签署链接
     * 2、意愿链接
     * 3、中间页地址
     *
     * @param input
     * @return
     */
    DoGrantOutput doGrant(DoGrantInput input);

    /**
     * 根据分组id查询印章授权详情
     *
     * @param input
     * @return
     */
    QuerySealGrantGroupDetailOutput querySealGrantGroupDetail(QuerySealGrantGroupDetailInput input);

    /**
     * 中间页 扫码或立即签署
     *
     * @param input
     * @return
     */
    CreateSealGrantConfirmUrlOutput createSealGrantConfirmUrl(CreateSealGrantConfirmUrlInput input);

    /**
     * 中间页 轮询查询印章授权结果
     *
     * @param input
     * @return
     */
    QuerySealGrantResultOutput querySealGrantResult(QuerySealGrantResultInput input);
}
