package com.timevale.saasbiz.integration.process;

import com.timevale.contractmanager.common.service.model.rescind.ProcessRescindCheckModel;
import com.timevale.contractmanager.common.service.model.rescind.ProcessRescindCheckResult;

/**
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface ProcessRescindClient {
    
    /**
     * 检查合同是否可以解约
     * @param request
     * @return
     */
    ProcessRescindCheckResult checkProcessRescind(ProcessRescindCheckModel request);
    
}
