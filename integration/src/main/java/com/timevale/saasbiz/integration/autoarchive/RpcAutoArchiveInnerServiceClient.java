package com.timevale.saasbiz.integration.autoarchive;

import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveChangeRuleMenuModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveQueryListModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveSaveRuleModel;
import com.timevale.contractmanager.common.service.model.autoarchive.AutoArchiveUpdateRuleModel;
import com.timevale.contractmanager.common.service.result.autoarchive.*;

import java.util.List;

/**
 * RpcAutoArchiveInnerServiceClient
 *
 * <AUTHOR>
 * @since 2023/8/16 3:37 下午
 */
public interface RpcAutoArchiveInnerServiceClient {

    AutoArchiveListResult pageListRule(AutoArchiveQueryListModel request);

    void topRule(String ruleId, String tenantId, String operatorId);

    void updateRule(AutoArchiveUpdateRuleModel model);

    void unbindRuleMenu(String menuId, String tenantId, String operatorId);

    void rerunRule(String ruleId, String tenantId);

    void updateRuleStatus(String ruleId, String tenantId, String operatorId, Integer status);

    void updateRuleStatusNoCheck(String ruleId, Integer status);

    AutoArchiveRuleResult getRuleDetail(String ruleId, String tenantId);

    AutoArchiveRuleResult getRuleDetailByMenuId(String menuId, String tenantId);

    void bindRuleMenu(AutoArchiveChangeRuleMenuModel request);

    AutoArchiveFormMenuMappingResult getMappingByMenuId(String menuId, String tenantId);

    ProcessStatusResult getOptionalStatusList(String tenantId);

}
