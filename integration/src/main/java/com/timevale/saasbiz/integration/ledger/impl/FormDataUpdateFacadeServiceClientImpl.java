package com.timevale.saasbiz.integration.ledger.impl;

import com.timevale.contractanalysis.facade.api.ao.UpdateFormDataRequest;
import com.timevale.contractanalysis.facade.api.bo.FormDataUpdateResult;
import com.timevale.contractanalysis.facade.api.form.FormDataUpdateFacadeService;
import com.timevale.saasbiz.integration.ledger.FormDataUpdateFacadeServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * FormDataUpdateFacadeServiceClientImpl
 *
 * <AUTHOR>
 * @since 2023/9/5 3:39 下午
 */
@Slf4j
@Service
public class FormDataUpdateFacadeServiceClientImpl implements FormDataUpdateFacadeServiceClient {

    @Autowired
    private FormDataUpdateFacadeService formDataUpdateFacadeService;

    @Override
    public FormDataUpdateResult updateFormData(UpdateFormDataRequest request) {
        return formDataUpdateFacadeService.updateFormData(request);
    }
}
