package com.timevale.saasbiz.integration.bizconfig;

import com.timevale.property.core.facade.bean.request.transfer.PropertyTransferRequest;
import com.timevale.property.core.facade.bean.response.function.PropertyValueResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/2/27 19:48
 */
public interface BizConfigClient {


    List<PropertyValueResponse> getPropertiesByRouting(PropertyTransferRequest request);

    Map<String, String> getPropertiesMapByRouting(PropertyTransferRequest request);
}
