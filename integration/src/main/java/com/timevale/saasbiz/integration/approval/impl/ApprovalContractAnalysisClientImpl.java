package com.timevale.saasbiz.integration.approval.impl;

import com.timevale.contractapproval.facade.api.ApprovalContractAnalysisRpcService;
import com.timevale.contractapproval.facade.dto.ApprovalContractAnalysisRelationDTO;
import com.timevale.contractapproval.facade.input.*;
import com.timevale.contractapproval.facade.output.ApprovalContractAnalysisOutput;
import com.timevale.contractapproval.facade.output.ApprovalContractAnalysisQueryOutput;
import com.timevale.mandarin.common.result.PageQueryResult;
import com.timevale.saasbiz.integration.approval.ApprovalContractAnalysisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-31 10:44
 */
@Component
public class ApprovalContractAnalysisClientImpl implements ApprovalContractAnalysisClient {
    @Autowired private ApprovalContractAnalysisRpcService approvalContractAnalysisRpcService;

    @Override
    public void createContractAnalysis(ApprovalContractAnalysisCreateInput input) {
        approvalContractAnalysisRpcService.createContractAnalysis(input);
    }

    @Override
    public void updateContractAnalysis(ApprovalContractAnalysisUpdateInput input) {
        approvalContractAnalysisRpcService.updateContractAnalysis(input);
    }

    @Override
    public ApprovalContractAnalysisRelationDTO queryContractAnalysisDetail(String compareId) {
        ApprovalContractAnalysisOutput output= approvalContractAnalysisRpcService.queryContractAnalysisDetail(
                new ApprovalContractAnalysisDetailInput(compareId));
        return output==null?null:output.getAnalysisRelationDTO();
    }

    @Override
    public PageQueryResult<ApprovalContractAnalysisRelationDTO> queryContractAnalysisPage(
            ApprovalContractAnalysisPageInput input) {
        return approvalContractAnalysisRpcService.queryContractAnalysisPage(input);
    }

    @Override
    public   List<ApprovalContractAnalysisRelationDTO> queryContractAnalysisResult(ApprovalContractAnalysisQueryInput input) {
        ApprovalContractAnalysisQueryOutput output= approvalContractAnalysisRpcService.queryContractAnalysisResult(input);
        return output==null?null:output.getRelations();
    }
}
