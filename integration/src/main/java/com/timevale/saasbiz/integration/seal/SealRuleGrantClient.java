package com.timevale.saasbiz.integration.seal;

import com.timevale.footstone.seal.facade.saas.input.*;
import com.timevale.footstone.seal.facade.saas.output.*;

/**
 * 印章授权
 *
 * <AUTHOR>
 * @since 2023-05-26 10:39
 */
public interface SealRuleGrantClient {

    /** 新增印章授权 */
    AddRulesOutput addSealGrant(AddRulesInput input);

    /** 授权成员列表 / 授权其他企业列表
     * 默认不需要印章服务鉴权
     * */
    RuleGrantByResourceIdOutput getRuleGrantByResourceId(GrantedDetailInput input);

    /**
     * 获取一批印章中已授权数量最多的印章及授权数量
     * @param input
     * @return
     */
    BatchSealMaxGrantCountOutput getBatchSealMaxGrantNum(BatchSealMaxGrantNumInput input);

    /** 一级授权批量删除 */
    void batchDeleteSealGrant(BatchDeleteSealGrantInput input);

    /**
     * 一级批量授权
     * @param input
     * @return
     */
    BatchSealGrantOutput batchSealGrant(BatchSealFirstGrantInput input);;

    /**
     * 下载线下法人章授权书
     * @param input
     * @return
     */
    DownloadOffLineLegalAuthLetterOutput downloadOffLineLegalAuthLetter(DownloadOffLineLegalAuthLetterInput input);

    /**
     * 集团批量印章授权
     * @param input
     * @return
     */
    BatchGroupSealGrantOutput batchGroupSealGrant(BatchGroupSealFirstGrantInput input);

    /**
     * 设置审批通知
     */
    void changeNotifySetting(ChangeNotifySettingInput input);

    /**
     * 编辑印章授权
     */
    BindRuleOutput updateRuleGrant(BindRuleGrantInput input);

    /**
     * 批量编辑印章授权
     */
    BatchUpdateRuleGrantOutput batchUpdateRuleGrant(BatchUpdateRuleGrantInput input);

    /**
     * 获取印章授权书下载地址
     */
    GrantFlowDownloadUrlOutput getGrantFlowDownloadUrl(GrantFlowDownloadUrlInput input);

    /**
     * 删除印章被授权信息
     */
    void deleteRuleGranted(DeleteByGrantedInput input);
}
