package com.timevale.saasbiz.integration.process.impl;

import com.timevale.contractmanager.common.service.api.RpcProcessStartDataService;
import com.timevale.contractmanager.common.service.bean.ProcessStartDataDTO;
import com.timevale.contractmanager.common.service.model.ProcessStartDataDataIdQueryModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataDeleteModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataOuterDataIdAndTemplateIdQueryModel;
import com.timevale.contractmanager.common.service.model.ProcessStartDataQueryModel;
import com.timevale.contractmanager.common.service.result.ProcessStartDataQueryByDataIdResult;
import com.timevale.contractmanager.common.service.result.ProcessStartDataQueryResult;
import com.timevale.saasbiz.integration.process.ProcessStartDataClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 09:45
 */
@Component
public class ProcessStartDataClientImpl implements ProcessStartDataClient {

    @Autowired
    private RpcProcessStartDataService startDataService;

    @Override
    public void deleteData(List<String> dataIdList) {
        ProcessStartDataDeleteModel deleteModel = new ProcessStartDataDeleteModel();
        deleteModel.setDataIdList(dataIdList);
        startDataService.processStartDataDeleteData(deleteModel);
    }

    @Override
    public List<ProcessStartDataDTO> listByDataIds(List<String> dataIdList) {
        ProcessStartDataDataIdQueryModel dataDataIdQueryModel = new ProcessStartDataDataIdQueryModel();
        dataDataIdQueryModel.setDataIdList(dataIdList);
        return startDataService.processStartDataQueryByDataId(dataDataIdQueryModel).getList();
    }

    @Override
    public ProcessStartDataDTO getByDataId(String dataId) {
        List<ProcessStartDataDTO> list = listByDataIds(Arrays.asList(dataId));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public ProcessStartDataQueryResult listByQuery(ProcessStartDataQueryModel queryRequest) {
        return startDataService.processStartDataListByQuery(queryRequest);
    }

    @Override
    public ProcessStartDataQueryByDataIdResult getByOuterDataIdAndTemplateId(ProcessStartDataOuterDataIdAndTemplateIdQueryModel model) {
        return startDataService.getByOuterDataIdAndTemplateId(model);
    }

}
