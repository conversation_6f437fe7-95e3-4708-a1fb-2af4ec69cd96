package com.timevale.saasbiz.integration.grayconfig.impl;

import com.timevale.gray.config.manage.service.api.ABTestRpcService;
import com.timevale.gray.config.manage.service.model.base.BaseResult;
import com.timevale.gray.config.manage.service.model.request.QueryABTestResultRequest;
import com.timevale.gray.config.manage.service.model.response.ABTestResultResponse;
import com.timevale.saasbiz.integration.grayconfig.GrayConfigManageClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 * @date 2023/4/14
 */
@Component
public class GrayConfigManageClientImpl implements GrayConfigManageClient {
    @Autowired ABTestRpcService abTestRpcService;

    @Override
    public ABTestResultResponse queryABTestResult(QueryABTestResultRequest req) {
        BaseResult<ABTestResultResponse> result = abTestRpcService.queryAbTestResult(req);
        return result != null && result.ifSuccess() ? result.getData() : null;
    }
}
