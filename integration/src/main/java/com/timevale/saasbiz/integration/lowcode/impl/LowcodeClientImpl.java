package com.timevale.saasbiz.integration.lowcode.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.besp.lowcode.integration.common.enums.PlatformEnum;
import com.timevale.besp.lowcode.integration.request.*;
import com.timevale.besp.lowcode.integration.response.*;
import com.timevale.besp.lowcode.integration.third.ThirdFormRpcService;
import com.timevale.besp.lowcode.integration.third.ThirdTenantRpcService;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.besp.lowcode.integration.request.FormListQueryRequest;
import com.timevale.besp.lowcode.integration.request.FormPageQueryRequest;
import com.timevale.besp.lowcode.integration.request.PlatformQueryRequest;
import com.timevale.besp.lowcode.integration.response.FormResponse;
import com.timevale.besp.lowcode.integration.response.PageInfo;
import com.timevale.besp.lowcode.integration.response.PlatformResponse;
import com.timevale.besp.lowcode.integration.third.ThirdFormRpcService;
import com.timevale.besp.saas.saassupportmembershippackages.configurationrpcfacade.enums.ConfigurationModuleEnum;
import com.timevale.besp.saas.saassupportmembershippackages.configurationrpcfacade.enums.ConfigureTargetEnum;
import com.timevale.besp.saas.saassupportmembershippackages.configurationrpcfacade.enums.FunctionStatusEnum;
import com.timevale.besp.saas.saassupportmembershippackages.configurationrpcfacade.request.ConfigurationQueryRequest;
import com.timevale.besp.saas.saassupportmembershippackages.configurationrpcfacade.response.ConfigurationQueryResponse;
import com.timevale.besp.saas.saassupportmembershippackages.saasconfigurationrpcfacade.rpc.ConfigurationRpc;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saas.common.manage.common.service.enums.VipCodeEnum;
import com.timevale.saasbiz.integration.lowcode.LowcodeClient;
import com.timevale.saasbiz.model.bean.lowcode.DataSourceChannelDTO;
import com.timevale.saasbiz.model.utils.AssertX;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/12 10:41
 */
@Slf4j
@Component
public class LowcodeClientImpl implements LowcodeClient {

    @Autowired private ThirdFormRpcService thirdFormRpcService;

    @Autowired private ThirdTenantRpcService thirdTenantRpcService;

    @Resource private ConfigurationRpc configurationRpc;

    @Override
    public List<DataSourceChannelDTO> subjectDatasourceChannelList(String subjectOid) {
        AssertX.isTrue(StringUtils.isNotBlank(subjectOid), "缺少 subjectOid");
        PlatformQueryRequest request = new PlatformQueryRequest();
        request.setOrgOuId(subjectOid);
        List<PlatformResponse> list = thirdFormRpcService.platformList(request).getData();
        return dataSourceChannelConvert(list);
    }

    @Override
    public PageInfo<FormResponse> dataSourcePage(FormPageQueryRequest request) {
        return thirdFormRpcService.formPage(request).getData();
    }

    @Override
    public List<FormResponse> formList(String subjectOid, List<String> formIds) {
        FormListQueryRequest request = new FormListQueryRequest();
        request.setOrgOuId(subjectOid);
        request.setFormKeys(formIds);
        return Optional.ofNullable(thirdFormRpcService.formList(request).getData())
                .orElse(new ArrayList<>());
    }

    @Override
    public FormFieldListResponse formSync(String channel, String subjectOid,String formId){
        FormFieldsQueryRequest request = new FormFieldsQueryRequest();
        request.setFormKey(formId);
        request.setOrgOuId(subjectOid);
        request.setPlatform(channel);
        return thirdFormRpcService.formSync(request);
    }

    @Override
    public SkuPageResponse getSkuPage(String clientId, String corpId) {
        SkuPageRequest request = new SkuPageRequest();
        request.setTenantKey(corpId);
        request.setPlatform(convert2LowCodePlatform(clientId));
        return thirdTenantRpcService.getSkuPage(request);
    }

    private List<DataSourceChannelDTO> dataSourceChannelConvert(List<PlatformResponse> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream()
                .map(
                        elm -> {
                            DataSourceChannelDTO dataSourceChannelDTO = new DataSourceChannelDTO();
                            dataSourceChannelDTO.setDataSourceChannel(elm.getPlatform());
                            dataSourceChannelDTO.setDataSourceChannelName(elm.getPlatformName());
                            return dataSourceChannelDTO;
                        })
                .collect(Collectors.toList());
    }


    /**
     * 获取版本功能描述
     *
     * @param vipCodeEnum
     */
    @Override
    public List<ConfigurationQueryResponse> fetchVersionFunctionDesc(VipCodeEnum vipCodeEnum) {
        ConfigurationQueryRequest request = new ConfigurationQueryRequest();
        request.setVipVersion(vipCodeEnum.getCode());
        request.setConfigurationModule(
                ConfigurationModuleEnum.VERSION_FUNCTION_DESCRIPTION.getModule());
        return configurationRpc.getDataByVipVersion(request);
    }

    @Override
    public List<ConfigurationQueryResponse> fetchVipProductIcons(VipCodeEnum vipCodeEnum) {
        ConfigurationQueryRequest request = new ConfigurationQueryRequest();
        request.setVipVersion(vipCodeEnum.getCode());
        request.setConfigurationModule(ConfigurationModuleEnum.PRODUCT_ICON.getModule());
        return configurationRpc.getDataByVipVersion(request);
    }

    @Override
    public List<ConfigurationQueryResponse> fetchVipProductIconByTarget(
            VipCodeEnum vipCodeEnum, String configureTarget) {
        ConfigurationQueryRequest request = new ConfigurationQueryRequest();
        request.setVipVersion(vipCodeEnum.getCode());
        request.setConfigurationModule(ConfigurationModuleEnum.PRODUCT_ICON.getModule());
        request.setConfigureTarget(configureTarget);
        List<ConfigurationQueryResponse> targetConfig =
                configurationRpc.getDataByVipVersion(request);
        if (CollectionUtils.isNotEmpty(targetConfig)) {
            return targetConfig;
        }
        return null;
    }

    /**
     * 获取版本功能图标
     *
     * @param vipCodeEnum
     * @return
     */
    @Override
    public List<VersionFunctionIcon> fetchVersionFunctionIcon(VipCodeEnum vipCodeEnum) {
        ConfigurationQueryRequest request = new ConfigurationQueryRequest();
        request.setVipVersion(vipCodeEnum.getCode());
        request.setConfigurationModule(ConfigurationModuleEnum.VERSION_FUNCTION_ICON.getModule());
        List<ConfigurationQueryResponse> functionIcons =
                configurationRpc.getDataByVipVersion(request);
        return functionIcons.stream()
                .map(this::getVersionFunctionIcon)
                .collect(Collectors.toList());
    }

    @Override
    public ConfigurationQueryResponse getDefaultFunctionIcon(VipCodeEnum vipCodeEnum) {
        ConfigurationQueryRequest request = new ConfigurationQueryRequest();
        request.setVipVersion(vipCodeEnum.getCode());
        request.setConfigurationModule(ConfigurationModuleEnum.VERSION_FUNCTION_ICON.getModule());
        request.setConfigureTarget(ConfigureTargetEnum.PRODUCT_ICON.getTarget());
        List<ConfigurationQueryResponse> functionIcons =
                configurationRpc.getDataByVipVersion(request);
        if (CollectionUtils.isEmpty(functionIcons)) {
            return null;
        }
        return functionIcons.get(0);
    }

    private VersionFunctionIcon getVersionFunctionIcon(ConfigurationQueryResponse response) {
        VersionFunctionIcon functionIcon = new VersionFunctionIcon();
        functionIcon.setFunctionStatus(functionStatusConvert(response.getConfigureTarget()));
        functionIcon.setIconFileUrl(getDefaultFileUrl(response.getFile()));
        return functionIcon;
    }

    /** 获取图标地址信息 */
    private String getDefaultFileUrl(List<ConfigurationQueryResponse.FileInfo> fileInfos) {
        if (fileInfos == null || fileInfos.isEmpty()) {
            return null;
        }
        return fileInfos.get(0).getUrl();
    }

    /** 版本功能图标 */
    @Data
    public static class VersionFunctionIcon {
        /** enums for: SUPPORT, NON_SUPPORT */
        private String functionStatus;

        /** 图标地址 */
        private String iconFileUrl;
    }

    private String functionStatusConvert(String functionTarget) {
        if (ConfigureTargetEnum.SUPPORT_FUNCTION.getTarget().equals(functionTarget)) {
            return FunctionStatusEnum.SUPPORT.getStatus();
        }
        if (ConfigureTargetEnum.NON_SUPPORT_FUNCTION.getTarget().equals(functionTarget)) {
            return FunctionStatusEnum.NON_SUPPORT.getStatus();
        }
        throw new BaseBizRuntimeException("functionTarget is not supported");
    }

    private String convert2LowCodePlatform(String clientId) {
        if (StringUtils.isBlank(clientId)) {
            return null;
        }
        if (ClientEnum.DING_TALK.getClientId().equals(clientId)) {
            return PlatformEnum.DING_TALK.getPlatform();
        }
        if (ClientEnum.FEI_SHU.getClientId().equals(clientId)) {
            return PlatformEnum.FEI_SHU.getPlatform();
        }
        return PlatformEnum.UNKOWN.getPlatform();
    }
}
