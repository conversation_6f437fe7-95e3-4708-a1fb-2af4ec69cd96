package com.timevale.saasbiz.integration.infocollect.impl;

import com.timevale.contractanalysis.facade.api.dto.infocollect.*;
import com.timevale.contractanalysis.facade.api.infocollect.InfoCollectResourceAuthRpcService;
import com.timevale.contractanalysis.facade.api.infocollect.InfoCollectRpcService;
import com.timevale.contractanalysis.facade.api.request.infocollect.*;
import com.timevale.saasbiz.integration.infocollect.InfoCollectClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/8/30 16:10
 */
@Component
public class InfoCollectClientImpl implements InfoCollectClient {

    @Autowired
    private InfoCollectRpcService infoCollectRpcService;
    @Autowired
    private InfoCollectResourceAuthRpcService resourceAuthRpcService;


    @Override
    public String formCopyKey(InfoCollectFormCopyKeyRequest request) {
        return infoCollectRpcService.infoCollectFormCopyKey(request).getCopyKey();
    }

    @Override
    public String formCopy(InfoCollectFormCopyRequest request) {
        return infoCollectRpcService.infoCollectFormCopy(request).getFormId();
    }


    @Override
    public InfoCollectCanAuthRoleResultDTO canAuthRole(String subjectOid) {

        InfoCollectCanAuthRoleRequest request = new InfoCollectCanAuthRoleRequest();
        request.setSubjectOid(subjectOid);
        return resourceAuthRpcService.infoCollectCanAuthRole(request);
    }

    @Override
    public void editResourceAuth(InfoCollectEditResourceAuthRequest request) {
         resourceAuthRpcService.infCollectEditResourceAuth(request);

    }

    @Override
    public void batchEditResourceAuth(InfoCollectBatchEditResourceAuthRequest request) {
        resourceAuthRpcService.infCollectBatchEditResourceAuth(request);
    }

    @Override
    public List<InfoCollectResourceAuthDTO> queryResourceAuth(InfCollectQueryResourceAuthRequest request) {
        return resourceAuthRpcService.infCollectQueryResourceAuth(request).getResourceAuths();
    }
    
    
    @Override
    public List<InfoCollectTemplateFormRelationDTO> queryTemplateFormRelationByForm(String subjectOid, String formId, String status) {

        InfoCollectQueryRelationByFormRequest request = new InfoCollectQueryRelationByFormRequest();
        request.setFormId(formId);
        request.setTenantOid(subjectOid);
        request.setStatus(status);
        InfoCollectQueryTemplateFormRelationResultDTO resultDTO = infoCollectRpcService.infoCollectQueryTemplateFormRelationByForm(request);
        return Optional.ofNullable(resultDTO)
                .map(InfoCollectQueryTemplateFormRelationResultDTO::getRelationList)
                .orElse(new ArrayList<>());
    }
    
    @Override
    public boolean hasForm(String tenantOid, boolean upgrade) {
        HasFormRequest request = new HasFormRequest();
        request.setTenantOid(tenantOid);
        request.setUpgrade(upgrade);
        HasFormResult result = infoCollectRpcService.hasForm(request);
        return Optional.of(result)
                .map(res -> res.getHas() != null && res.getHas())
                .orElse(false);
    }

    @Override
    public List<InfoCollectTemplateFormRelationDTO> queryTemplateFormRelationByTemplate(String subjectOid, String templateId) {
        InfoCollectQueryRelationByTemplateRequest request = new InfoCollectQueryRelationByTemplateRequest();
        request.setTemplateId(templateId);
        request.setTenantOid(subjectOid);
        InfoCollectQueryTemplateFormRelationResultDTO resultDTO = infoCollectRpcService.infoCollectQueryTemplateFormRelationByTemplate(request);
        return Optional.ofNullable(resultDTO)
                .map(InfoCollectQueryTemplateFormRelationResultDTO::getRelationList)
                .orElse(new ArrayList<>());
    }

    @Override
    public List<InfoCollectFlowNodeDTO> formFlow(String subjectOid, String formId) {
        InfoCollectFormFlowRequest request = new InfoCollectFormFlowRequest();
        request.setFormId(formId);
        request.setSubjectOid(subjectOid);
        return infoCollectRpcService.infoCollectFormFlow(request).getFlowNodes();
    }

    @Override
    public InfoCollectDataStatusDetailDTO queryDataStatusDetail(String dataId, String taskId, String taskKey) {
        InfoCollectDataStatusDetailRequest request = new InfoCollectDataStatusDetailRequest();
        request.setDataId(dataId);
        request.setTaskId(taskId);
        request.setTaskKey(taskKey);
        return infoCollectRpcService.queryDataStatusDetail(request);
    }
}
