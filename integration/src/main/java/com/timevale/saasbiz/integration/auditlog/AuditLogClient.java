package com.timevale.saasbiz.integration.auditlog;

import com.timevale.dayu.config.result.EventDataResult;
import com.timevale.dayu.facade.model.AuditLogVo;
import com.timevale.dayu.facade.request.ConfigQueryEventRequest;
import com.timevale.dayu.facade.request.DownloadStatusRequest;
import com.timevale.dayu.facade.request.NewAuditLogDownloadRequest;
import com.timevale.dayu.facade.request.NewAuditLogQueryRequest;
import com.timevale.dayu.facade.request.SubscribeRecordQueryDetailRequest;
import com.timevale.dayu.facade.request.SubscribeRecordQueryRequest;
import com.timevale.dayu.facade.result.AuditLogDownloadResult;
import com.timevale.dayu.facade.result.AuditLogQueryResult;
import com.timevale.dayu.facade.result.ConfigQueryEventResult;
import com.timevale.dayu.facade.result.DownloadStatusResult;
import com.timevale.dayu.facade.result.SubscribeRecordQueryResult;

/**
 * 审计日志服务
 *
 * <AUTHOR>
 * @since 2023-10-11 17:20
 */
public interface AuditLogClient {
    AuditLogQueryResult queryAuditLogNew(NewAuditLogQueryRequest request);

    AuditLogDownloadResult downloadAuditLogNew(NewAuditLogDownloadRequest request);

    /** 查询事件列表 */
    ConfigQueryEventResult queryEvent(ConfigQueryEventRequest configQueryEventRequest);

    /**
     * 查询审计日志下载结果
     */
    DownloadStatusResult queryDownloadStatus(DownloadStatusRequest request);

    SubscribeRecordQueryResult querySubscribeRecordList(SubscribeRecordQueryRequest request);

    AuditLogVo getRecordDetail(SubscribeRecordQueryDetailRequest request);

    EventDataResult querySubscribeEvent();
}
