package com.timevale.saasbiz.integration.dedicatedcloud.impl;

import com.alibaba.fastjson.JSON;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saas.integration.service.api.RpcDedicatedProjectService;
import com.timevale.saas.integration.service.model.input.CreateDedicatedProjectInput;
import com.timevale.saas.integration.service.model.input.DownloadDedicatedConfigInput;
import com.timevale.saas.integration.service.model.input.QueryDedicatedActivationInput;
import com.timevale.saas.integration.service.model.input.QueryDedicatedProjectInput;
import com.timevale.saas.integration.service.model.input.QueryDedicatedProjectListInput;
import com.timevale.saas.integration.service.model.input.ToggleDedicatedProjectStatusInput;
import com.timevale.saas.integration.service.model.input.UnbindDedicatedProjectAuthInput;
import com.timevale.saas.integration.service.model.input.UpdateDedicatedProjectInput;
import com.timevale.saas.integration.service.model.output.model.CreateDedicatedProjectOutput;
import com.timevale.saas.integration.service.model.output.model.DownloadDedicatedConfigOutput;
import com.timevale.saas.integration.service.model.output.model.QueryDedicatedActivationOutput;
import com.timevale.saas.integration.service.model.output.model.QueryDedicatedProjectListOutput;
import com.timevale.saas.integration.service.model.output.model.QueryDedicatedProjectOutput;
import com.timevale.saasbiz.integration.dedicatedcloud.DedicatedCloudClient;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/2/1 10:47
 */
@Slf4j
@Component
public class DedicatedCloudClientImpl implements DedicatedCloudClient {

    @Autowired
    private RpcDedicatedProjectService dedicatedCloudRpcService;


    @Override
    public String createDedicatedProject(CreateDedicatedProjectInput input) {
        RpcOutput<CreateDedicatedProjectOutput> result = dedicatedCloudRpcService.createDedicatedProject(input);
        if (!result.isSuccess()) {
            log.error("createDedicatedProject req : {} res : {}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), result.getMessage());
        }
        return result.getData().getDedicatedCloudId();
    }

    @Override
    public void updateDedicatedProject(UpdateDedicatedProjectInput input) {

        RpcOutput<Boolean> result = dedicatedCloudRpcService.updateDedicatedProject(input);
        if (!result.isSuccess()) {
            log.error("updateDedicatedProject req : {} res : {}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), result.getMessage());
        }
        if (Boolean.FALSE.equals(result.getData())) {
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), "专属云项目更新失败");
        }
    }

    @Override
    public void updateStatus(ToggleDedicatedProjectStatusInput input) {

        RpcOutput<Boolean> result = dedicatedCloudRpcService.toggleStatus(input);
        if (!result.isSuccess()) {
            log.error("toggleStatus req : {} res : {}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), result.getMessage());
        }
        if (Boolean.FALSE.equals(result.getData())) {
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), "专属云项目状态更新失败");
        }
    }


    @Override
    public QueryDedicatedProjectListOutput listDedicatedProject(QueryDedicatedProjectListInput input) {
        input.setQueryProjectStatus(true);
        RpcOutput<QueryDedicatedProjectListOutput> result = dedicatedCloudRpcService.queryDedicatedProjectList(input);
        if (!result.isSuccess()) {
            log.error("queryDedicatedProjectList req : {} res : {}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), result.getMessage());
        }
        return result.getData();
    }

    @Override
    public QueryDedicatedProjectOutput getDedicatedProjectByGid(String subjectGid) {
        QueryDedicatedProjectListInput listInput = new QueryDedicatedProjectListInput();
        listInput.setGid(subjectGid);
        QueryDedicatedProjectListOutput listOutput = listDedicatedProject(listInput);
        return null != listOutput && CollectionUtils.isNotEmpty(listOutput.getDedicatedProjects()) ? listOutput.getDedicatedProjects().get(0) : null;
    }

    @Override
    public boolean existDedicatedCloud(String subjectGid) {
        QueryDedicatedProjectListInput listInput = new QueryDedicatedProjectListInput();
        listInput.setGid(subjectGid);
        QueryDedicatedProjectListOutput listOutput = listDedicatedProject(listInput);
        return null != listOutput && CollectionUtils.isNotEmpty(listOutput.getDedicatedProjects());
    }

    @Override
    public QueryDedicatedProjectOutput getDedicatedProject(String dedicatedCloudId) {
        QueryDedicatedProjectInput input = new QueryDedicatedProjectInput();
        input.setQueryAuthAppId(true);
        input.setQueryProjectStatus(true);
        input.setDedicatedCloudId(dedicatedCloudId);
        return queryDedicatedProject(input);
    }

    private QueryDedicatedProjectOutput queryDedicatedProject(QueryDedicatedProjectInput input) {
        RpcOutput<QueryDedicatedProjectOutput> result = dedicatedCloudRpcService.queryDedicatedProject(input);
        if (!result.isSuccess()) {
            log.error("queryDedicatedProject req : {} res : {}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), result.getMessage());
        }
        return result.getData();
    }

    @Override
    public String downloadDedicatedConfig(String dedicatedCloudId) {
        DownloadDedicatedConfigInput input = new DownloadDedicatedConfigInput();
        input.setDedicatedCloudId(dedicatedCloudId);
        RpcOutput<DownloadDedicatedConfigOutput> result = dedicatedCloudRpcService.downloadDedicatedConfig(input);
        if (!result.isSuccess()) {
            log.error("downloadDedicatedConfig req : {} res : {}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), result.getMessage());
        }
        return result.getData().getDownloadUrl();
    }

    @Override
    public void unBindDedicatedProjectAuth(UnbindDedicatedProjectAuthInput input) {
        RpcOutput<Boolean> result = dedicatedCloudRpcService.unBindDedicatedProjectAuth(input);
        if (!result.isSuccess()) {
            log.error("unBindDedicatedProjectAuth req : {} res : {}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), result.getMessage());
        }
        if (Boolean.FALSE.equals(result.getData())) {
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), "专属云项目授权解绑失败");
        }
    }

    @Override
    public boolean queryDedicatedActivation(String subjectGid) {
        QueryDedicatedActivationInput input = new QueryDedicatedActivationInput();
        input.setGid(subjectGid);
        RpcOutput<QueryDedicatedActivationOutput> result = dedicatedCloudRpcService.queryDedicatedActivation(input);
        if (!result.isSuccess()) {
            log.error("queryDedicatedActivation req : {} res : {}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), result.getMessage());
        }
        return result.getData().isHasActivatedRecord();
    }
}
