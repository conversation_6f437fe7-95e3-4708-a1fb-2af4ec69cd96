package com.timevale.saasbiz.integration.seal;

import com.timevale.footstone.seal.facade.saas.input.*;
import com.timevale.footstone.seal.facade.saas.output.*;
import com.timevale.footstone.seal.facade.saas.page.ApiPageResult;

/**
 * 印章二级授权
 *
 * <AUTHOR>
 * @since 2023-07-06 10:39
 */
public interface SecondSealGrantClient {
    /** 新增授权 */
    SecondSealGrantBatchAddOutput addGrantOfInternal(SecondSealGrantAddInput input);

    /** 批量删除二级授权 */
    void batchDeleteSecondSealGrant(BatchDeleteSecSealGrantInput input);

    /**
     * 获取一批印章中最大授权数量的授权记录及授权数量
     * @param input
     * @return
     */
    BatchSealMaxSecondGrantCountOutput getBatchSealMaxSecondGrantNum(BatchSealMaxSecondGrantNumInput input);

    /**
     * 批量印章执行二级授权
     * @param input
     * @return
     */
    BatchSealGrantOutput batchSecondSealGrant(BatchSealSecondGrantInput input);

    /**
     * 查询二次授权列表信息 默认不需要印章服务鉴权
     */
    ApiPageResult<SaasSealGrantSecondListOutput> pageSecondSealGrant(SecondSealGrantPageInput input);

    /**
     * 更新二次授权
     */
    SealGrantSecondUpdateOutput updateGrantOfInternal(SecondSealGrantUpdateInput input);

    /**
     * 获取二级授权书下载地址
     */
    SealGrantDownloadOutput getSealGrantDownloadUrl(SealGrantDownloadInput input);

    /**
     * 更新二次授权配置 目前只有消息配置可以更新，所以默认是消息配置的更新
     */
    void updateSealGrantRule(SealGrantRuleUpdateInput input);
}
