package com.timevale.saasbiz.integration.saascommon;

import com.timevale.saas.common.manage.common.service.model.input.AuditLogSubscriptionConfigDetailInput;
import com.timevale.saas.common.manage.common.service.model.input.AuditLogSubscriptionConfigQueryInput;
import com.timevale.saas.common.manage.common.service.model.input.AuditLogSubscriptionConfigUpdateInput;
import com.timevale.saas.common.manage.common.service.model.output.AuditLogSubscriptionConfigOutput;
import com.timevale.saas.common.manage.common.service.model.output.AuditLogSubscriptionConfigUpdateOutput;
import com.timevale.saas.common.manage.common.service.model.output.bean.AuditLogSubscriptionConfigDTO;

/**
 * AuditLogSubscriptionConfigClient
 *
 * <AUTHOR>
 * @since 2025/2/20 下午5:18
 */
public interface AuditLogSubscriptionConfigClient {

    AuditLogSubscriptionConfigOutput queryConfigList(AuditLogSubscriptionConfigQueryInput input);

    AuditLogSubscriptionConfigDTO getDetail(AuditLogSubscriptionConfigDetailInput input);

    AuditLogSubscriptionConfigUpdateOutput changeStatus(AuditLogSubscriptionConfigUpdateInput input);

    AuditLogSubscriptionConfigUpdateOutput changeAccount(AuditLogSubscriptionConfigUpdateInput input);
}
