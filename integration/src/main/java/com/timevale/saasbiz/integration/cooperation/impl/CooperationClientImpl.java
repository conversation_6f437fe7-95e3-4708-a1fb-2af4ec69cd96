package com.timevale.saasbiz.integration.cooperation.impl;

import com.timevale.doccooperation.service.api.RpcFillCooperationFlowService;
import com.timevale.doccooperation.service.input.CooperationPrivilegeInput;
import com.timevale.doccooperation.service.result.CooperationPrivilegeResult;
import com.timevale.saasbiz.integration.cooperation.CooperationClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 填写流程client
 * @date Date : 2024年03月19日 16:52
 */
@Component
public class CooperationClientImpl implements CooperationClient {

    @Autowired
    RpcFillCooperationFlowService rpcFillCooperationFlowService;

    @Override
    public CooperationPrivilegeResult getCooperationPrivilege(CooperationPrivilegeInput input) {
        return rpcFillCooperationFlowService.getCooperationPrivilege(input);
    }
}
