package com.timevale.saasbiz.integration.cooperation;

import com.timevale.doccooperation.service.input.CooperationPrivilegeInput;
import com.timevale.doccooperation.service.result.CooperationPrivilegeResult;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 填写流程客户端
 * @date Date : 2024年03月19日 16:52
 */
public interface CooperationClient {

    /**
     * 获取该人员对填写流程的权限
     * @param input
     * @return
     */
    CooperationPrivilegeResult getCooperationPrivilege(CooperationPrivilegeInput input);
}
