package com.timevale.saasbiz.integration.witness.impl;

import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saasbiz.integration.witness.WitnessSearchClient;
import com.timevale.signflow.search.service.api.v2.RpcWitnessSearchService;
import com.timevale.signflow.search.service.model.v2.witness.QueryKeywordWitnessParam;
import com.timevale.signflow.search.service.model.v2.witness.QueryWitnessEvidenceParam;
import com.timevale.signflow.search.service.model.v2.witness.QueryWitnessParam;
import com.timevale.signflow.search.service.model.v2.witness.SaveWitnessInfoParam;
import com.timevale.signflow.search.service.result.v2.witness.WitnessQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-03-06
 */
@Slf4j
@Component
public class WitnessSearchClientImpl implements WitnessSearchClient {

    @Autowired RpcWitnessSearchService rpcWitnessSearchService;

    @Override
    public WitnessQueryResult query(QueryWitnessParam param) {
        return rpcWitnessSearchService.queryWitnessFlows(param);
    }

    @Override
    public WitnessQueryResult queryKeyword(QueryKeywordWitnessParam param) {
        return rpcWitnessSearchService.queryWitnessFlowsByKeyword(param);
    }

    @Override
    public WitnessQueryResult queryEvidence(QueryWitnessEvidenceParam param) {
        return rpcWitnessSearchService.queryWitnessEvidence(param);
    }

    @Override
    public void save(SaveWitnessInfoParam param) {
        log.info(JsonUtils.obj2json(param));
        rpcWitnessSearchService.saveWitnessInfo(param);
    }
}
