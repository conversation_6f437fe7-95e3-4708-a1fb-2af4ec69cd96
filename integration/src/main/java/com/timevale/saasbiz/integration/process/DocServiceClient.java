package com.timevale.saasbiz.integration.process;

import java.util.List;

import com.timevale.docmanager.service.input.CreateDocByHtmlTemplateInput;
import com.timevale.docmanager.service.input.DocListResult;
import com.timevale.docmanager.service.result.CreateDocByHtmlResult;
import com.timevale.docmanager.service.result.DocInfoResult;

/**
 * DocServiceClient
 *
 * <AUTHOR>
 * @since 2023/4/7 3:35 下午
 */
public interface DocServiceClient {

    /**
     * 根据html动态模板生成文件
     *
     * @param createDocByHtmlTemplateInput 请求对象
     * @return 生成结果
     */
    CreateDocByHtmlResult createDocByHtmlTemplate(CreateDocByHtmlTemplateInput createDocByHtmlTemplateInput);

    /**
     * 查询文档信息
     * @param docId
     * @return
     */
    DocInfoResult queryDocInfo(String docId);

    /**
     * 批量查询文档信息内部接口
     * @param docIds
     * @return
     */
    List<DocListResult> queryInternalDocList(List<String> docIds);
}
