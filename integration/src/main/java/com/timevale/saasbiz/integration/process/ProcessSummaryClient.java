package com.timevale.saasbiz.integration.process;

import com.timevale.contractmanager.common.service.model.processsummary.QueryProcessSummaryDetailModel;
import com.timevale.contractmanager.common.service.model.processsummary.RefreshProcessSummaryModel;
import com.timevale.contractmanager.common.service.model.processsummary.UpdateProcessSummaryModel;
import com.timevale.contractmanager.common.service.result.processsummary.ProcessSummaryDetailResult;

/**
 * 合同摘要
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface ProcessSummaryClient {
    void createProcessSummary(String processId, String fileId, String subjectId, String subjectGid);

    void createProcessAllFileSummary(String processId, String subjectId, String subjectGid);

    void refreshProcessSummary(RefreshProcessSummaryModel model);

    ProcessSummaryDetailResult queryProcessSummaryDetail(QueryProcessSummaryDetailModel model);

    void updateProcessSummary(UpdateProcessSummaryModel model);

    String queryProcessSummaryOverAllStatus(String processId, String subjectGid);
}
