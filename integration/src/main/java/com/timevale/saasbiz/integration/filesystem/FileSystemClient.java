package com.timevale.saasbiz.integration.filesystem;

import com.timevale.filesystem.common.service.query.GetDownloadUrlInput;
import com.timevale.filesystem.common.service.result.GetFileInfoResult;
import com.timevale.filesystem.common.service.result.GetSignUrlResult;

/**
 * FileSystemClient
 *
 * <AUTHOR>
 * @since 2023/4/3 2:05 下午
 */
public interface FileSystemClient {

    /**
     * 获取下载地址
     * @param fileKey
     * @param internal
     * @param expireTime
     * @return
     */
    String getDownloadUrl(String fileKey, boolean internal, long expireTime);

    /**
     * 获取下载地址
     * <AUTHOR>
     * @date 2020/3/4 16:28
     * @param fileKey
     * @param internal 是否内部使用
     * @return java.lang.String
     */
    String getDownloadUrl(String fileKey, boolean internal);

    /**
     * 获取下载地址
     * @param input
     * @return
     */
    String getDownloadUrl(GetDownloadUrlInput input);

    /**
     * 下载文件
     * @param fileKey 文件filekey
     * @return 文件字节数组
     */
    byte[] downloadFile(String fileKey);

    /**
     * 获取文件信息
     * @param fileKey
     * @return
     */
    GetFileInfoResult getFileInfo(String fileKey);

    /**
     * 上传文件并获取外部下载链接
     * @param data
     * @param fileName
     * @return
     */
    String uploadFileAndGetDownloadUrl(byte[] data, String fileName);

    /**
     * 获取文件上传地址
     * @param fileName
     * @param internal
     * @return
     */
    GetSignUrlResult getUploadUrl(String fileName, boolean internal);

    /**
     * 上传文件返回fileKey
     *
     * @param data
     * @param fileName
     * @return
     */
    String uploadFileReturnFileKey(byte[] data, String fileName);

}
