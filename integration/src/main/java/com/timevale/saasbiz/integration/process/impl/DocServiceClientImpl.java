package com.timevale.saasbiz.integration.process.impl;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.contractmanager.common.service.exception.BizContractManagerResultCodeEnum;
import com.timevale.docmanager.service.api.DocService;
import com.timevale.docmanager.service.input.CreateDocByHtmlTemplateInput;
import com.timevale.docmanager.service.input.DocListInternalInput;
import com.timevale.docmanager.service.input.DocListQueryInput;
import com.timevale.docmanager.service.input.DocListResult;
import com.timevale.docmanager.service.result.CreateDocByHtmlResult;
import com.timevale.docmanager.service.result.DocInfoResult;
import com.timevale.docmanager.service.result.SuperResult;
import com.timevale.saasbiz.integration.process.DocServiceClient;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DocServiceClientImpl
 *
 * <AUTHOR>
 * @since 2023/4/7 3:36 下午
 */
@Service
public class DocServiceClientImpl implements DocServiceClient {

    @Autowired
    private DocService docService;

    @Override
    public CreateDocByHtmlResult createDocByHtmlTemplate(CreateDocByHtmlTemplateInput createDocByHtmlTemplateInput){
        CreateDocByHtmlResult docIdResult = docService.createDocByHtmlTemplate(createDocByHtmlTemplateInput);

        if (docIdResult == null || !docIdResult.isSuccess()) {
            throw new BizContractManagerException(
                    BizContractManagerResultCodeEnum.RESCIND_DOC_GENERATE_FAIL);
        }

        return docIdResult;
    }

    @Override
    public DocInfoResult queryDocInfo(String docId) {
        return docService.queryDocInfo(docId);
    }

    @Override
    public List<DocListResult> queryInternalDocList(List<String> docIds) {
        DocListInternalInput input = new DocListInternalInput();
        input.setDocIds(docIds);
        SuperResult<List<DocListResult>> docList = docService.queryInternalDocList(input);
        return Optional.ofNullable(docList)
            .map(SuperResult::getData)
            .orElseGet(Lists::newArrayList);
    }
}
