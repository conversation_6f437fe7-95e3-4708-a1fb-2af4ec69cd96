package com.timevale.saasbiz.integration.saascommon;

import com.timevale.saas.common.manage.common.service.model.bean.bill.BillIsolateDTO;
import com.timevale.saas.common.manage.common.service.model.input.*;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.EffectiveAuthRelationByChildTenantGidInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.QueryEffectiveAuthRelationByParentTenantGidInput;
import com.timevale.saas.common.manage.common.service.model.input.bean.SubTaskAddBean;
import com.timevale.saas.common.manage.common.service.model.input.saasbizconfig.ChangeSaasBizConfigsInput;
import com.timevale.saas.common.manage.common.service.model.input.saasbizconfig.GetSaasBizConfigsInput;
import com.timevale.saas.common.manage.common.service.model.input.watermark.*;
import com.timevale.saas.common.manage.common.service.model.output.*;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.EffectiveAuthRelationByChildTenantGidOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.QueryEffectiveAuthRelationByParentTenantGidOutput;
import com.timevale.saas.common.manage.common.service.model.output.saasbizconfig.ChangeSaasBizConfigsOutput;
import com.timevale.saas.common.manage.common.service.model.output.saasbizconfig.GetSaasBizConfigsOutput;
import com.timevale.saas.common.manage.common.service.model.output.vip.GetSaasCommoditiesCategoryOutput;
import com.timevale.saas.common.manage.common.service.model.output.watermark.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-03-09 10:27
 */
public interface SaasCommonClient {

    /**
     * 查询用户会员信息
     * @param input
     * @return
     */
    AccountVipQueryOutput queryAccountVip(AccountVipQueryInput input);

    /**
     * 查询用户指定会员功能信息
     *
     * @param tenantGid
     * @param clientId
     * @param functionKey
     * @return
     */
    VipFunctionQueryOutput queryVipFunction(String tenantGid, String clientId, String functionKey);

    /**
     * 查询下一个会员版本
     */
    NextVipQueryOutput queryNextVip(String vipCode);
    /**
     * 查询用户指定会员功能信息
     * @param accountOid
     * @param functionKey
     * @return
     */
    VipFunctionQueryOutput queryVipFunctionByOid(String accountOid, String functionKey);

    /**
     * 批量查询指定会员功能列表信息
     *
     * @param tenantId
     * @param clientId
     * @param functionKeys
     * @return
     */
    AccountVipFunctionsOutput queryVipFunctions(
            String tenantId, String clientId, List<String> functionKeys);

    /**
     * 是否支持功能
     *
     * @param tenantId
     * @param clientId
     * @param functionKey
     */
    boolean supportFunction(String tenantId, String clientId, String functionKey);

    /** 添加任务 */
    void addTasks(SaasTaskAddInput input);

    /** 是否存在同类型任务 */
    Boolean existExecutingTaskByAccountIdAndType(String accountId, Integer taskType);

    /** 更新任务 */
    void updateTask(SaasTaskUpdateInput input);

    /** 添加子任务 */
    void addSubTask(SubTaskAddBean subTaskAddBean);

    SaasTaskResultOutput queryResultByBizId(String bizId, Integer taskType, String accountId);

    Map<String, String> taskAddSubTask(SaasTaskBatchAddSubTaskInput batchAddInput);

    /** 查询任务信息 */
    SaasTaskInfoOutput querySaasTask(String bizId, Integer taskType);

    /**
     * 查询水印模板列表
     *
     * @param input 请求入参
     * @return 分页查询结果
     */
    WatermarkTemplatePageOutput listWatermarkTemplates(WatermarkTemplatePageInput input);

    /**
     * 保存水印模板
     *
     * @param input 详情请求入参
     * @return 保存成功的水印模板ID
     */
    String saveWatermarkTemplate(SaveWatermarkTemplateInput input);

    /**
     * 修改水印模板状态(启/停/删)
     *
     * @param input 入参
     * @return 成功无返回，失败报错
     */
    void changeWatermarkTemplateStatus(ChangeWatermarkTemplateStatusInput input);

    /**
     * 查询水印模板详情
     *
     * @param input 详情请求入参
     * @return 水印模板详情
     */
    WatermarkTemplateDetailOutput getWatermarkTemplateDetail(WatermarkTemplateDetailInput input);

    /**
     * 查看水印模板中的图片
     *
     * @param input
     * @return
     */
    WatermarkTemplateViewImageOutput viewWatermarkImage(WatermarkTemplateViewImageInput input);

    /**
     * 获取用于预览水印的图片地址
     *
     * @param input
     * @return
     */
    WatermarkTemplatePreviewOutput getPreviewWatermarkImageUrl(WatermarkTemplatePreviewInput input);

    /**
     * 生成水印模板快照
     *
     * @param input
     * @return
     */
    GenerateWatermarkSnapShootOutput generateWatermarkSnapShoot(
            GenerateWatermarkSnapShootInput input);

    /**
     * 批量查询企业配置信息
     *
     * @param input
     * @return
     */
    GetSaasBizConfigsOutput getBizConfigs(GetSaasBizConfigsInput input);

    /**
     * 批量更新企业配置信息
     *
     * @param input
     * @return
     */
    ChangeSaasBizConfigsOutput changeBizConfigs(ChangeSaasBizConfigsInput input);

    /**
     * 查询主企业下关联的所有子企业（有效的）
     *
     * @param input 入参
     * @return 出参
     */
    QueryEffectiveAuthRelationByParentTenantGidOutput queryEffectiveAuthRelationListByTenantGid(
            QueryEffectiveAuthRelationByParentTenantGidInput input);

    /**
     * 查询子企业的主企业
     * @param input
     * @return
     */
    EffectiveAuthRelationByChildTenantGidOutput effectiveAuthRelationByChildTenantGid(EffectiveAuthRelationByChildTenantGidInput input);

    /**
     * 当前企业是否有过关联企业，子企业和父企业，包含过期的
     * @param tenantGid
     * @return
     */
    boolean hasAuthRelation(String tenantGid);

    /**
     * 获取功能详情
     *
     * @param functionCode 会员功能代码
     * @return 功能详细信息
     */
    GetFunctionIllustrateInfoOutput getFunctionIllustrateInfo(String functionCode);

    /**
     * 申请会员功能
     * @param applyTrialInput
     * @return
     */
    ApplyTrialOutput applyTrialFunctionCode(ApplyTrialInput applyTrialInput);

    /**
     * 回显申请记录
     * @param queryApplyLogInput
     * @return
     */
    QueryApplyLogOutput queryApplyLog(QueryApplyLogInput queryApplyLogInput);

    /**
     * 获取计费隔离信息
     * @param clientId
     * @return
     */
    BillIsolateDTO getBillIsolateInfo(String clientId,String gid);

    /**
     * 获取所有的计费隔离信息
     * @return
     */
    List<BillIsolateDTO> getAllBillIsolateInfo();

    /**
     * 获取saas相关的分类商品信息
     *
     * @param clientId
     * @param tenantId
     * @param showCaseNo
     * @return
     */
    GetSaasCommoditiesCategoryOutput getSaasCommoditiesCategory(
            String clientId, String tenantId, String showCaseNo);

    /**
     * 获取计费隔离信息
     * @param clientId
     * @return
     */
    BillIsolateDTO getBillIsolateFromClientId(String clientId,String gid);

    /**
     * 保存申请记录
     * @param input
     * @return
     */
    boolean saveOrgTrialFunctionLog(SaveOrgTrialLogInput input);
}
