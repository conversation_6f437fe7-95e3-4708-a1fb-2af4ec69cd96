package com.timevale.saasbiz.integration.seal;

import com.timevale.footstone.seal.facade.saas.page.PageResult;
import com.timevale.footstone.seal.facade.saas.statistics.input.QueryPageSealUseInput;
import com.timevale.footstone.seal.facade.saas.statistics.output.QueryPageSealUseInfo;

/**
 * 用印统计
 *
 * <AUTHOR>
 * @since 2023-05-26 14:04
 */
public interface SealStatisticsClient {
    /** 分页查询 */
    PageResult<QueryPageSealUseInfo> pageQuerySealUse(QueryPageSealUseInput input);
}
