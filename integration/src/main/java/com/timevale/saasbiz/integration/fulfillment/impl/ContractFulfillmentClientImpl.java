package com.timevale.saasbiz.integration.fulfillment.impl;

import com.timevale.contractmanager.common.service.api.RpcContractFulfillmentRecordService;
import com.timevale.contractmanager.common.service.api.RpcContractFulfillmentRuleService;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRecordBatchUpdateStatusModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleQueryListModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleSaveModel;
import com.timevale.contractmanager.common.service.model.fulfillment.ContractFulfillmentRuleUpdateModel;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleDetailResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleListResult;
import com.timevale.contractmanager.common.service.result.fulfillment.ContractFulfillmentRuleTypeListResult;
import com.timevale.saasbiz.integration.fulfillment.ContractFulfillmentClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ContractFulfillmentClientImpl
 *
 * <AUTHOR>
 * @since 2023/10/13 5:22 下午
 */
@Service
public class ContractFulfillmentClientImpl implements ContractFulfillmentClient {

    @Autowired
    private RpcContractFulfillmentRuleService ruleService;

    @Autowired
    private RpcContractFulfillmentRecordService recordService;

    @Override
    public String saveRule(ContractFulfillmentRuleSaveModel model) {
        return ruleService.save(model).getRuleId();
    }

    @Override
    public void updateRule(ContractFulfillmentRuleUpdateModel model) {
        ruleService.update(model);
    }

    @Override
    public void deleteRule(String ruleId, String tenantId, String accountId) {
        ruleService.delete(ruleId, tenantId, accountId);
    }

    @Override
    public void updateRuleStatus(String ruleId, String status, String tenantId, String accountId) {
        ruleService.updateStatus(ruleId, status, tenantId, accountId);
    }

    @Override
    public ContractFulfillmentRuleDetailResult getRuleDetail(String ruleId, String tenantId) {
        return ruleService.detail(ruleId, tenantId);
    }

    @Override
    public ContractFulfillmentRuleListResult pageRuleList(ContractFulfillmentRuleQueryListModel model) {
        return ruleService.pageList(model);
    }

    @Override
    public void batchUpdateStatus(String tenantId, String accountId, List<String> recordIdList, String status) {
        ContractFulfillmentRecordBatchUpdateStatusModel model = new ContractFulfillmentRecordBatchUpdateStatusModel();
        model.setTenantId(tenantId);
        model.setAccountId(accountId);
        model.setRecordIdList(recordIdList);
        model.setStatus(status);
        recordService.batchUpdateStatus(model);
    }

    @Override
    public ContractFulfillmentRuleTypeListResult typeList(String tenantId) {
        return ruleService.typeList(tenantId);
    }
}
