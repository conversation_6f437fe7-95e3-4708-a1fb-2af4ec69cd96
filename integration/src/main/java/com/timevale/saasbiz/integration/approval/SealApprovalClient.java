package com.timevale.saasbiz.integration.approval;

import com.timevale.account.flow.service.model.approval.input.BizApprovalFlowRemindInput;
import com.timevale.account.flow.service.model.approval.output.BizApprovalFlowBaseInfoOutput;
import com.timevale.account.flow.service.model.approval.output.task.QueryTaskLogOutput;
import com.timevale.flow.facade.service.model.input.seal.SealApprovalAgreeV2Input;
import com.timevale.flow.facade.service.model.input.seal.SealApprovalRefuseV2Input;
import com.timevale.flow.facade.service.model.input.seal.SealApprovalRevokeV2Input;
import com.timevale.flow.facade.service.model.input.seal.SealApprovalUrlV2Input;
import com.timevale.flow.facade.service.model.output.seal.SealApprovalUrlOutput;

import java.util.Map;
import java.util.Set;

/**
 * 用印审批
 *
 * <AUTHOR>
 * @since 2023-03-31 13:47
 */
public interface SealApprovalClient {

    /**
     * 查询审批人的审批记录类型
     */
    Map<String, Set<String>> queryApprovalType(String accountGid, Set<String> approvalIds);


    void approvalAgree(SealApprovalAgreeV2Input input);

    void approvalRefuse(SealApprovalRefuseV2Input input);

    void approvalRevoke(SealApprovalRevokeV2Input input);

    /**
     * 查询审批信息
     */
    BizApprovalFlowBaseInfoOutput queryApprovalFlowBaseInfo(String approvalId);

    /**
     * 获取审批链接
     */
    SealApprovalUrlOutput queryApprovalUrl(SealApprovalUrlV2Input input);


    QueryTaskLogOutput queryApprovalLog(String approvalId);


    void urge(BizApprovalFlowRemindInput input);

    /**
     * 获取审批意愿认证业务id
     * @param serialId
     * @return
     */
    String queryApprovalWillAuthId(String serialId);
}
