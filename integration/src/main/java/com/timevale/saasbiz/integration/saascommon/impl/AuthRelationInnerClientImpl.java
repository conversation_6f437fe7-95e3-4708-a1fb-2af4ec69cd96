package com.timevale.saasbiz.integration.saascommon.impl;

import com.google.common.collect.Maps;
import com.timevale.saas.common.manage.common.service.api.AuthRelationInnerRpcService;
import com.timevale.saas.common.manage.common.service.api.AuthRelationOperatorLogRpcService;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationLogStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationOrderStatusEnum;
import com.timevale.saas.common.manage.common.service.model.base.Page;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.*;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.*;
import com.timevale.saasbiz.integration.saascommon.AuthRelationInnerClient;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by tianlei on 2022/2/17
 */
@Component
public class AuthRelationInnerClientImpl implements AuthRelationInnerClient {
    @Autowired
    private AuthRelationInnerRpcService authRelationInnerRpcService;
    @Autowired
    private AuthRelationOperatorLogRpcService authRelationOperatorLogRpcService;

    @Override
    public AuthRelationCreateLogOutput createAuthRelationLog(AuthRelationCreateLogInput input) {
        return authRelationInnerRpcService.createAuthRelationLog(input);
    }

    @Override
    public AuthRelationUpdateLogOutput updateAuthRelationLog(AuthRelationUpdateLogInput input) {
        return authRelationInnerRpcService.updateAuthRelationLog(input);
    }

    @Override
    public AuthRelationUpdateLogOutput updateAuthRelationLogStatusById(Long authRelationLogId,
                                                                       AuthRelationLogStatusEnum logStatusEnum) {
        if (null == authRelationLogId || null == logStatusEnum) {
            throw new SaasBizException(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(),
                    "authRelationLogId or logStatusEnum null");
        }
        AuthRelationUpdateLogInput update = new AuthRelationUpdateLogInput();
        update.setAuthRelationLogId(authRelationLogId);
        update.setStatus(logStatusEnum.getCode());
        return updateAuthRelationLog(update);
    }

    @Override
    public AuthRelationStartContractSuccessOutput startContractSuccess(CreateAuthRelationStartContractSuccessInput input) {
        return authRelationInnerRpcService.createAuthRelationAfterContractStartSuccess(input);
    }

    @Override
    public AuthRelationSignSuccessOutput signSuccess(AuthRelationSignSuccessInput input) {
        return authRelationInnerRpcService.signSuccess(input);
    }

    @Override
    public AuthRelationSignFailureOutput signFailure(AuthRelationSignFailureInput input) {
        return authRelationInnerRpcService.signFailure(input);
    }

    @Override
    public AuthRelationBuildBeforeCheckOutput startBeforeCheck(AuthRelationBuildBeforeCheckInput input) {
        return authRelationInnerRpcService.startBeforeCheck(input);
    }

    @Override
    public Page<AuthRelationBackendDTO> queryPageAuthRelation(QueryPageAuthRelationListInput input) {
        return authRelationInnerRpcService.queryPageAuthRelation(input);
    }

    @Override
    public Page<AuthRelationLogBackendDTO> queryPageAuthRelationLog(QueryPageAuthRelationLogListInput input) {
        return authRelationInnerRpcService.queryPageAuthRelationLog(input);
    }

    @Override
    public AuthRelationLogDTO getAuthRelationLogByProcessId(String processId) {
        return authRelationInnerRpcService.getAuthRelationLogByProcessId(processId);
    }

    @Override
    public AuthRelationDetailDTO getAuthRelationDetailByAuthRelationId(Long authRelationId) {
        return authRelationInnerRpcService.getAuthRelationDetailByAuthRelationId(authRelationId);
    }

    @Override
    public Map<Long, AuthRelationDetailDTO> queryAuthRelationDetailMap(List<Long> authRelationIds) {
        AuthRelationQueryByAuthRelationIdsInput input = new AuthRelationQueryByAuthRelationIdsInput();
        input.setAuthRelationIds(authRelationIds);
        List<AuthRelationDetailDTO> authRelationList = authRelationInnerRpcService.getAuthRelationDetailByAuthRelationIds(input);
        if (CollectionUtils.isEmpty(authRelationList)) {
            return Maps.newHashMap();
        }
        return authRelationList.stream().collect(Collectors.toMap(AuthRelationDetailDTO::getAuthRelationId, i -> i));
    }

    @Override
    public AuthRelationGetAuthRelationLastProcessOutput getAuthRelationLastProcess(Long authRelationId) {
        return authRelationInnerRpcService.getAuthRelationLastProcess(authRelationId);
    }

    @Override
    public AuthRelationLogDetailOutput getAuthRelationDetailByLogId(Long authRelationLogId) {
        return authRelationInnerRpcService.getAuthRelationDetailByLogId(authRelationLogId);
    }

    @Override
    public List<AuthRelationLogDTO> getAuthRelationLogByAuthRelationIds(List<Long> authRelationIds) {
        AuthRelationLogQueryByAuthRelationIdsInput input = new AuthRelationLogQueryByAuthRelationIdsInput();
        input.setAuthRelationIds(authRelationIds);
        return authRelationInnerRpcService.getAuthRelationLogByAuthRelationIds(input);
    }

    @Override
    public AuthRelationLogDTO getAuthRelationLogByFlowId(String flowId) {
        return authRelationInnerRpcService.getAuthRelationLogByFlowId(flowId);
    }

    @Override
    public Boolean checkOneLevelMaxCount(String parentTenantGid, Integer addCount) {
        return authRelationInnerRpcService.checkOneLevelMaxCount(parentTenantGid, addCount);
    }

    @Override
    public AuthRelationQueryShareConfigOutput queryShareConfig(AuthRelationQueryShareConfigInput input) {
        return authRelationInnerRpcService.queryShareConfig(input);
    }

    @Override
    public Map<Long, List<AuthRelationShareConfigDTO>> queryShareConfigMap(List<Long> authRelationIds) {
        AuthRelationQueryShareConfigInput queryShareConfigInput = new AuthRelationQueryShareConfigInput();
        queryShareConfigInput.setAuthRelationIds(authRelationIds);
        AuthRelationQueryShareConfigOutput output = queryShareConfig(queryShareConfigInput);
        if (null != output && CollectionUtils.isNotEmpty(output.getShareConfigDTOList())) {
            return output.getShareConfigDTOList().stream()
                    .collect(Collectors.groupingBy(AuthRelationShareConfigDTO::getAuthRelationId));
        }
        return Maps.newHashMap();
    }

    @Override
    public void updateShareConfig(AuthRelationUpdateShareConfigInput input) {
        authRelationInnerRpcService.updateShareConfig(input);
    }

    @Override
    public void createAuthRelationOrder(Long logId, String orderId) {
        AuthRelationOrderInput input = new AuthRelationOrderInput();
        input.setAuthRelationLogId(logId);
        input.setOrderId(orderId);
        authRelationInnerRpcService.createAuthRelationOrder(input);
    }

    @Override
    public void unLockAuthRelationOrder(Long logId) {
        AuthRelationOrderInput input = new AuthRelationOrderInput();
        input.setAuthRelationLogId(logId);
        input.setStatus(AuthRelationOrderStatusEnum.UNLOCK.getStatus());
        authRelationInnerRpcService.updateAuthRelationOrder(input);
    }

    @Override
    public List<AuthRelationOrderDTO> queryAuthRelationLockOrderList(AuthRelationOrderQueryInput input) {
        return authRelationInnerRpcService.queryAuthRelationLockOrderList(input);
    }

    @Override
    public void directRescindAuthRelation(DirectRescindAuthRelationInput input) {
        authRelationInnerRpcService.directRescindAuthRelation(input);
    }

    @Override
    public void signRescindAuthRelation(SignRescindAuthRelationInput input) {
        authRelationInnerRpcService.signRescindAuthRelation(input);
    }

    @Override
    public AuthRelationDeleteAuthRelationOutput deleteAuthRelation(AuthRelationDeleteInput input) {
        return authRelationInnerRpcService.deleteAuthRelation(input);
    }

    @Override
    public void createOperatorLog(AuthRelationOperatorLogCreateInput input) {
        authRelationOperatorLogRpcService.createOperatorLog(input);
    }

}
