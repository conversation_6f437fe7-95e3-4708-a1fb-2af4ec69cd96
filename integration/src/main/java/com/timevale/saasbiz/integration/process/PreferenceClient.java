package com.timevale.saasbiz.integration.process;

import com.timevale.contractmanager.common.service.bean.PreferenceModel;

import java.util.List;

/**
 * 合同偏好
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
public interface PreferenceClient {

    /**
     * 获取合同偏好设置(支持批量)
     *
     * @param orgGids 企业gid
     * @param preferenceKeys 偏好设置key
     * @return 偏好信息
     */
    List<PreferenceModel> queryPreference(List<String> orgGids, List<String> preferenceKeys);

}
