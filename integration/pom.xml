<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.timevale.saasbiz</groupId>
        <artifactId>saasbiz-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>saasbiz-integration</artifactId>
    <name>saasbiz/integration</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>mandarin-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>saasbiz-model</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 用户中心 -->
        <dependency>
            <groupId>com.timevale.easun</groupId>
            <artifactId>easun-facade</artifactId>
            <version>3.4.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>cert-common-service-facade</artifactId>
                    <groupId>com.timevale.cert</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.timevale.account</groupId>
            <artifactId>account-realname-facade</artifactId>
            <version>2.1.2-SNAPSHOT</version>
        </dependency>
        <!-- 认证服务 -->
        <dependency>
            <groupId>com.timevale.oauth</groupId>
            <artifactId>oauth-facade</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>

        <!-- 签署服务 -->
        <dependency>
            <groupId>com.timevale.footstone</groupId>
            <artifactId>footstone-rpc-service-facade</artifactId>
            <version>1.12.23-SNAPSHOT</version>
        </dependency>

        <!-- 通知服务 -->
        <dependency>
            <groupId>com.timevale.notificationmanager</groupId>
            <artifactId>notificationmanager-facade</artifactId>
            <version>3.1.11-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.doc-cooperation</groupId>
            <artifactId>doc-cooperation-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.saas-common-manage</groupId>
            <artifactId>saas-common-manage-common-service-facade</artifactId>
            <version>${saas-common.manager}</version>
        </dependency>

        <!-- 天印流程 -->
        <dependency>
            <groupId>com.timevale.footstone.witness.api</groupId>
            <artifactId>footstone-witness-api-common-service-facade</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>

        <!-- 印章服务 -->
        <dependency>
            <groupId>com.timevale.seal</groupId>
            <artifactId>seal-facade</artifactId>
            <version>1.4.9-SNAPSHOT</version>
        </dependency>

        <!-- 流程搜索服务 -->
        <dependency>
            <groupId>com.timevale.saas</groupId>
            <artifactId>saas-search-facade</artifactId>
            <version>12.4.2-SNAPSHOT</version>
        </dependency>

        <!-- 开放平台服务 -->
        <dependency>
            <groupId>com.timevale.open.platform.service</groupId>
            <artifactId>open-platform-service-facade</artifactId>
            <version>2.4.10</version>
        </dependency>

        <!-- 计费服务 -->
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>base-billing-core-facade</artifactId>
            <version>3.1.11-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.billing.promotion</groupId>
            <artifactId>billing-promotion-common-service-facade</artifactId>
            <version>1.6.5-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.billing.manager</groupId>
            <artifactId>basicbs-billing-manager-facade</artifactId>
            <version>1.4.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.billing.manager</groupId>
            <artifactId>basicbs-billing-manager-sdk</artifactId>
            <version>1.7.61-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>basicbs-product-inventory-facade</artifactId>
            <version>5.0.33-SNAPSHOT</version>
        </dependency>


        <!-- 文件系统 -->
        <dependency>
            <groupId>com.timevale.filesystem</groupId>
            <artifactId>filesystem-facade</artifactId>
            <version>3.3.0</version>
        </dependency>

        <!--合同模板-->
        <dependency>
            <groupId>com.timevale.docmanager</groupId>
            <artifactId>docmanager-facade</artifactId>
        </dependency>

        <!-- AI -->
        <dependency>
            <groupId>com.timevale.athena</groupId>
            <artifactId>ai-athena-facade</artifactId>
        </dependency>

        <!-- es client -->
        <dependency>
            <groupId>com.timevale.thanos</groupId>
            <artifactId>thanos-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>2.6.3</version>
        </dependency>

        <!-- CRM服务-->
        <dependency>
            <groupId>com.timevale.crm.contract.system</groupId>
            <artifactId>facade</artifactId>
            <version>1.0.25-SNAPSHOT</version>
        </dependency>

        <!--三方平台服务-->
        <dependency>
            <groupId>com.timevale.tpi</groupId>
            <artifactId>third-platform-integrate-facade</artifactId>
            <version>1.0.9-SNAPSHOT</version>
        </dependency>

        <!--功能灰度服务-->
        <dependency>
            <groupId>com.timevale.gray-config-manage</groupId>
            <artifactId>gray-config-manage-facade</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>

        <!-- 合同审批 -->
        <dependency>
            <groupId>com.timevale.contractapproval</groupId>
            <artifactId>contractapproval-facade</artifactId>
            <version>1.1.3-SNAPSHOT</version>
        </dependency>

        <!-- 印章审批 -->
        <dependency>
            <groupId>com.timevale.account</groupId>
            <artifactId>account-flow-facade</artifactId>
            <version>1.3.9-SNAPSHOT</version>
        </dependency>

        <!-- 合同流程 -->
        <dependency>
            <groupId>com.timevale.contractmanager</groupId>
            <artifactId>contractmanager-common-service-facade</artifactId>
        </dependency>

        <!-- footstone-flow -->
        <dependency>
            <groupId>com.timevale.flow</groupId>
            <artifactId>flow-facade</artifactId>
            <version>1.1.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.open.platform.service</groupId>
            <artifactId>open-platform-service-facade</artifactId>
            <version>2.4.9</version>
        </dependency>

        <!-- pdf转换,临时使用,后面水印SDK支持移动端原生后下掉 -->
        <dependency>
            <groupId>com.timevale.pdf</groupId>
            <artifactId>pdf-common-service-facade</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>

        <!-- 实名 -->
        <dependency>
            <groupId>com.timevale.footstone</groupId>
            <artifactId>footstone-identity-common-service-facade</artifactId>
            <version>2.3.29-SNAPSHOT</version>
        </dependency>

        <!-- 证书服务 -->
        <dependency>
            <groupId>com.timevale.cert</groupId>
            <artifactId>cert-facade</artifactId>
            <version>2.4.78</version>
        </dependency>
        <!-- 验证码服务 -->
        <dependency>
            <groupId>com.timevale.authcode</groupId>
            <artifactId>authcode-facade</artifactId>
            <version>2.0.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.esign</groupId>
                    <artifactId>component-common-base</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 多语言 -->
        <dependency>
            <groupId>com.timevale.saas-utils</groupId>
            <artifactId>multilingual-translate-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.saas-auth-api</groupId>
            <artifactId>ding-spring-boot-starter</artifactId>
        </dependency>
        <!-- 审计日志 -->
        <dependency>
            <groupId>com.timevale.dayu</groupId>
            <artifactId>dayu-facade</artifactId>
        </dependency>


        <!-- CRM -->
        <dependency>
            <groupId>com.timevale.crm.custom-provider</groupId>
            <artifactId>custom-provider-facade</artifactId>
            <version>2.4.18-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.flowmanager</groupId>
            <artifactId>flowmanager-common-service-facade</artifactId>
            <version>2.1.5-SNAPSHOT</version>
        </dependency>



        <dependency>
            <groupId>com.timevale.datarefresh</groupId>
            <artifactId>datarefresh-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.clmc</groupId>
            <artifactId>clmc-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.token</groupId>
            <artifactId>token-facade</artifactId>
            <version>2.2.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.contractanalysis</groupId>
            <artifactId>contractanalysis-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.sealmanager</groupId>
            <artifactId>sealmanager-common-service-facade</artifactId>
            <version>3.5.23-SNAPSHOT</version>
        </dependency>
        <!-- 短链服务 -->
        <dependency>
            <groupId>com.timevale.shortlink</groupId>
            <artifactId>shortlink-common-service-facade</artifactId>
            <version>2.0.8-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.saas-integration</groupId>
            <artifactId>saas-integration-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.property.core</groupId>
            <artifactId>esign-property-core-facade</artifactId>
            <version>1.0.1-tl-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.middleware</groupId>
                    <artifactId>unified-rpc-copy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 线程本地缓存 -->
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>tlcache-spring-boot-starter</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.besp.lowcode.tripartite</groupId>
            <artifactId>tripartite-rpc-facade</artifactId>
        </dependency>

        <!-- 低代码, 运营支撑，会员配置-->
        <dependency>
            <groupId>com.timevale.besp.saas.saasconfigurationrpcfacade</groupId>
            <artifactId>besp-saas-configuration-rpc-facade</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.esa</groupId>
                    <artifactId>sesp-dependency</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.timevale.open.platform.portal.core</groupId>
            <artifactId>open-platform-portal-core-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.dayu</groupId>
            <artifactId>config-facade</artifactId>
        </dependency>

        <!-- epaas合同审查 -->
        <dependency>
            <groupId>com.timevale.sterna.epaas-contract-review</groupId>
            <artifactId>epaas-contract-review-client</artifactId>
            <version>1.0.3</version>
        </dependency>

        <!-- epaas合同拟定 -->
        <dependency>
            <groupId>com.timevale.sterna.epaas-contract-drafting</groupId>
            <artifactId>epaas-contract-drafting-client</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.sterna.epaas-contract-drafting</groupId>
            <artifactId>epaas-contract-drafting-integrate</artifactId>
            <version>1.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.timevale.docanalysis</groupId>
            <artifactId>docanalysis-facade</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>
