package com.timevale.saasbiz.rest.converter;

import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.saasbiz.model.approval.dto.ApprovalTemplateEsDTO;
import com.timevale.saasbiz.rest.bean.approval.response.ListApprovalTemplateResponse;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-04-06 12:53
 */
public class ApprovalTemplateConverter {
    public static ListApprovalTemplateResponse buildListResponse(
            PagerResult<ApprovalTemplateEsDTO> pageResult) {
        List<ListApprovalTemplateResponse.ApprovalTemplateEsVO> items =
                pageResult.getItems().stream()
                        .map(ApprovalTemplateConverter::buildVO)
                        .collect(Collectors.toList());

        ListApprovalTemplateResponse response = new ListApprovalTemplateResponse();
        response.setItems(items);
        response.setTotal(Long.valueOf(pageResult.getTotal()));

        return response;
    }

    public static ListApprovalTemplateResponse.ApprovalTemplateEsVO buildVO(
            ApprovalTemplateEsDTO esDTO) {
        ListApprovalTemplateResponse.ApprovalTemplateEsVO vo =
                new ListApprovalTemplateResponse.ApprovalTemplateEsVO();
        vo.setApprovalTemplateId(esDTO.getApprovalTemplateId());
        vo.setTenantOid(esDTO.getTenantOid());
        vo.setTenantGid(esDTO.getTenantGid());
        vo.setAppId(esDTO.getAppId());
        vo.setApprovalTemplateName(esDTO.getApprovalTemplateName());
        vo.setStatus(esDTO.getStatus());
        vo.setApprovalTemplateType(esDTO.getApprovalTemplateType());
        vo.setApprovalTemplateConditionType(esDTO.getApprovalTemplateConditionType());
        vo.setApprovalTemplateDescription(esDTO.getApprovalTemplateDescription());
        vo.setCreatorName(esDTO.getCreatorName());
        vo.setUpdaterName(esDTO.getUpdaterName());
        vo.setCreateTime(Optional.ofNullable(esDTO.getCreateTime()).map(Date::new).orElse(null));
        vo.setUpdateTime(Optional.ofNullable(esDTO.getUpdateTime()).map(Date::new).orElse(null));
        vo.setDeleted(esDTO.getDeleted());
        return vo;
    }
}
