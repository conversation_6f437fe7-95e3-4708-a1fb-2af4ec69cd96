package com.timevale.saasbiz.rest.bean.contractcategory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("场景下的合同类型列表信息")
public class SceneContractCategoryVO {

    @ApiModelProperty("场景id")
    private String scene;

    @ApiModelProperty("场景名称")
    private String sceneName;

    @ApiModelProperty("合同类型列表")
    private List<ContractCategoryVO> categories;
}
