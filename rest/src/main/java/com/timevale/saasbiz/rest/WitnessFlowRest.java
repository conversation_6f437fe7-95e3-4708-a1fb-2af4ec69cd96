package com.timevale.saasbiz.rest;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.witness.dto.input.*;
import com.timevale.saasbiz.model.bean.witness.dto.output.*;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.witness.request.*;
import com.timevale.saasbiz.rest.bean.witness.response.*;
import com.timevale.saasbiz.service.witness.WitnessFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.rest.converter.WitnessFlowResponseConverter.*;

/**
 * 天印流程管理rest接口
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@Api(tags = "天印流程管理")
@Validated
@ExternalService
@RestMapping(path = "/v2/witness/flows")
public class WitnessFlowRest {

    @Autowired WitnessFlowService witnessFlowService;

    /**
     * 获取用户参与的天印流程列表
     *
     * @return
     */
    @RestMapping(path = "/user/list", method = RequestMethod.GET)
    @ApiOperation(value = "获取用户参与的天印流程列表", httpMethod = "GET")
    public RestResult<QueryWitnessFlowsResponse> queryUserWitnessFlows(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @URIQueryParam @ModelAttribute QueryUserWitnessFlowRequest request) {
        QueryUserWitnessFlowInputDTO input = new QueryUserWitnessFlowInputDTO();
        input.setPageSize(request.getPageSize());
        input.setPageNum(request.getPageNum());
        input.setStatus(request.getStatus());
        input.setKeyword(request.getKeyword());
        input.setSubjectId(request.getSubjectId());
        input.setAccountId(request.getAccountId());
        input.setTimeRangeType(request.getTimeRangeType());
        input.setBeginTimeInMillSec(request.getBeginTimeInMillSec());
        input.setEndTimeInMillSec(request.getEndTimeInMillSec());
        input.setOperable(request.getOperable());
        input.setTitle(request.getTitle());
        input.setAccount(request.getAccount());
        input.setPersonIdentity(request.getPersonIdentity());
        input.setPersonName(request.getPersonName());
        input.setSubjectName(request.getSubjectName());
        // 如果未指定主体id, 默认获取当前空间主体id
        if (StringUtils.isBlank(input.getSubjectId())) {
            input.setSubjectId(tenantId);
        }
        // 优先获取登录用户账号id, 其次获取参数中指定的账号id
        if (StringUtils.isNotBlank(accountId)) {
            input.setAccountId(accountId);
        }
        QueryWitnessFlowsOutputDTO output = witnessFlowService.queryUserFlows(input);
        return RestResult.success(convertQueryWitnessFlowsResponse(output));
    }

    /**
     * 获取用户可查看的天印流程列表
     *
     * @return
     */
    @RestMapping(path = "/view/list", method = RequestMethod.GET)
    @ApiOperation(value = "获取用户可查看的企业天印流程列表", httpMethod = "GET")
    public RestResult<QueryWitnessFlowsResponse> queryViewWitnessFlows(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @URIQueryParam @ModelAttribute QueryViewableWitnessFlowRequest request) {
        QueryViewableWitnessFlowInputDTO input = new QueryViewableWitnessFlowInputDTO();
        input.setPageSize(request.getPageSize());
        input.setPageNum(request.getPageNum());
        input.setFlowName(request.getFlowName());
        input.setInitiatorKeyword(request.getInitiatorKeyword());
        input.setSignerKeyword(request.getSignerKeyword());
        input.setCreateFrom(request.getCreateFrom());
        input.setCreateEnd(request.getCreateEnd());
        input.setValidFrom(request.getValidFrom());
        input.setValidEnd(request.getValidEnd());
        input.setSignValidFrom(request.getSignValidFrom());
        input.setSignValidEnd(request.getSignValidEnd());
        input.setSubjectId(request.getSubjectId());
        input.setAccountId(request.getAccountId());
        input.setStatus(request.getStatus());
        input.setCompleteTimeFrom(request.getCompleteTimeFrom());
        input.setCompleteTimeEnd(request.getCompleteTimeEnd());
        input.setSortKey(request.getSortKey());
        input.setSortAsc(request.isSortAsc());
        // 如果未指定主体id, 默认获取当前空间主体id
        if (StringUtils.isBlank(input.getSubjectId())) {
            input.setSubjectId(tenantId);
        }
        // 优先获取登录用户账号id, 其次获取参数中指定的账号id
        if (StringUtils.isNotBlank(accountId)) {
            input.setAccountId(accountId);
        }
        QueryWitnessFlowsOutputDTO output = witnessFlowService.queryViewFlows(input);
        return RestResult.success(convertQueryWitnessFlowsResponse(output));
    }

    /**
     * 获取用户可出证的天印流程列表
     *
     * @return
     */
    @RestMapping(path = "/issue/list", method = RequestMethod.GET)
    @ApiOperation(value = "获取用户可出证的天印流程列表", httpMethod = "GET")
    public RestResult<QueryWitnessFlowsResponse> queryIssueWitnessFlows(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @URIQueryParam @ModelAttribute QueryIssueWitnessFlowRequest request) {
        QueryIssueWitnessFlowInputDTO input = new QueryIssueWitnessFlowInputDTO();
        input.setPageSize(request.getPageSize());
        input.setPageNum(request.getPageNum());
        input.setAppId(request.getAppId());
        input.setFlowId(request.getFlowId());
        input.setFlowName(request.getFlowName());
        input.setSignerKeyWord(request.getSignerKeyWord());
        input.setInitiatorKeyWord(request.getInitiatorKeyWord());
        input.setCreateFrom(request.getCreateFrom());
        input.setCreateEnd(request.getCreateEnd());
        input.setSubjectId(request.getSubjectId());
        input.setAccountId(request.getAccountId());
        // 如果未指定主体id, 默认获取当前空间主体id
        if (StringUtils.isBlank(input.getSubjectId())) {
            input.setSubjectId(tenantId);
        }
        // 优先获取登录用户账号id, 其次获取参数中指定的账号id
        if (StringUtils.isNotBlank(accountId)) {
            input.setAccountId(accountId);
        }
        QueryWitnessFlowsOutputDTO output = witnessFlowService.queryIssueFlows(input);
        return RestResult.success(convertQueryWitnessFlowsResponse(output));
    }

    /**
     * 判断用户是否有天印流程
     *
     * @return
     */
    @RestMapping(path = "/user/check", method = RequestMethod.GET)
    @ApiOperation(value = "判断用户是否有天印流程", httpMethod = "GET")
    public RestResult<CheckWitnessFlowResponse> checkHasWitnessFlows(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @URIQueryParam @ModelAttribute CheckHasWitnessFlowRequest request) {

        CheckHasWitnessFlowInputDTO input = new CheckHasWitnessFlowInputDTO();
        input.setType(request.getType());
        input.setSubjectId(request.getSubjectId());
        input.setAccountId(request.getAccountId());
        // 如果未指定主体id, 默认获取当前空间主体id
        if (StringUtils.isBlank(input.getSubjectId())) {
            input.setSubjectId(tenantId);
        }
        // 优先获取登录用户账号id, 其次获取参数中指定的账号id
        if (StringUtils.isNotBlank(accountId)) {
            input.setAccountId(accountId);
        }
        CheckWitnessFlowResponse response =
                new CheckWitnessFlowResponse(witnessFlowService.checkHasFlows(input));
        return RestResult.success(response);
    }

    /**
     * 获取签署任务概要
     *
     * @return
     */
    @RestMapping(path = "/outline", method = RequestMethod.GET)
    @ApiOperation(value = "获取签署任务概要", httpMethod = "GET")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<QueryWitnessFlowOutlineResponse> queryWitnessFlowOutline(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId,
            @URIQueryParam @ModelAttribute QueryWitnessFlowOutlineRequest request) {
        QueryWitnessFlowOutlineInputDTO input = new QueryWitnessFlowOutlineInputDTO();
        input.setFlowId(request.getFlowId());
        input.setAccountId(request.getAccountId());
        input.setSubjectId(RequestContextExtUtils.getTenantId());
        // 优先获取登录用户账号id, 其次获取参数中指定的账号id
        if (StringUtils.isNotBlank(accountId)) {
            input.setAccountId(accountId);
        }
        QueryWitnessFlowOutlineOutputDTO output = witnessFlowService.queryOutline(input);

        return RestResult.success(convertQueryWitnessFlowOutlineResponse(output));
    }

    /**
     * 获取天印流程批量签署列表
     *
     * @return
     */
    @RestMapping(path = "/batchsign/list", method = RequestMethod.POST)
    @ApiOperation(value = "获取天印流程批量签署列表", httpMethod = "POST")
    public RestResult<GetWitnessFlowBatchSignListResponse> queryWitnessBatchSignList(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestBody GetWitnessFlowBatchSignListRequest request) {
        GetWitnessFlowBatchSignListInputDTO input = new GetWitnessFlowBatchSignListInputDTO();
        input.setBatchSerialId(request.getBatchSerialId());
        input.setAccountId(request.getAccountId());
        // 优先获取登录用户账号id, 其次获取参数中指定的账号id
        if (StringUtils.isNotBlank(accountId)) {
            input.setAccountId(accountId);
        }
        input.setSubjectId(tenantId);
        input.setFlowIds(request.getFlowIds());
        GetWitnessFlowBatchSignListOutputDTO output = witnessFlowService.queryBatchSignList(input);
        return RestResult.success(convertBatchSignListResponse(output));
    }

    /**
     * 获取天印流程批量签署地址
     *
     * @return
     */
    @RestMapping(path = "/batchsign/url", method = RequestMethod.POST)
    @ApiOperation(value = "获取天印流程批量签署地址", httpMethod = "POST")
    public RestResult<GetWitnessFlowBatchSignUrlResponse> queryWitnessBatchSignUrl(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestBody GetWitnessFlowBatchSignUrlRequest request) {
        GetWitnessFlowBatchSignUrlInputDTO input = new GetWitnessFlowBatchSignUrlInputDTO();
        input.setAppId(request.getAppId());
        input.setAccountId(request.getAccountId());
        // 优先获取登录用户账号id, 其次获取参数中指定的账号id
        if (StringUtils.isNotBlank(accountId)) {
            input.setAccountId(accountId);
        }
        input.setSubjectId(tenantId);
        input.setFlowIds(request.getFlowIds());
        input.setRedirectUrl(request.getRedirectUrl());

        GetWitnessFlowBatchSignUrlOutputDTO output = witnessFlowService.queryBatchSignUrl(input);
        return RestResult.success(convertBatchSignUrlResponse(output));
    }

    /**
     * 根据签署序列id获取天印流程批量签署流程信息
     *
     * @return
     */
    @RestMapping(path = "/batchsign/{signSerialId}/info", method = RequestMethod.GET)
    @ApiOperation(value = "根据签署序列id获取天印流程批量签署流程信息", httpMethod = "GET")
    public RestResult<GetWitnessFlowBatchSignSerialInfoResponse> queryWitnessSignSerialInfo(
            @PathVariable String signSerialId, @RequestParam("appId") String appId) {
        GetWitnessFlowBatchSignSerialInfoOutputDTO output =
                witnessFlowService.queryBatchSignSerialInfo(signSerialId, appId);
        return RestResult.success(convertBatchSignSerialInfoResponse(output));
    }
}
