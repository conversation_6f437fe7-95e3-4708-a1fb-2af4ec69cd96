package com.timevale.saasbiz.rest.converter;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.auditlog.AuditLogDTO;
import com.timevale.saasbiz.model.bean.auditlog.input.DownloadAuditLogInputDTO;
import com.timevale.saasbiz.model.bean.auditlog.input.ListAuditLogInputDTO;
import com.timevale.saasbiz.model.bean.auditlog.output.AuditLogDownloadOutputDTO;
import com.timevale.saasbiz.model.bean.auditlog.output.AuditLogExportListOutputDTO;
import com.timevale.saasbiz.model.bean.auditlog.output.AuditLogFirstModuleDTO;
import com.timevale.saasbiz.model.bean.auditlog.output.AuditLogListOutputDTO;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogDownloadRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogListRequest;
import com.timevale.saasbiz.rest.bean.auditlog.response.AuditLogDownloadResponse;
import com.timevale.saasbiz.rest.bean.auditlog.response.AuditLogFirstModuleResponse;
import com.timevale.saasbiz.rest.bean.auditlog.response.AuditLogListResponse;
import com.timevale.saasbiz.rest.bean.auditlog.response.QueryAuditLogExportListResponse;
import com.timevale.saasbiz.rest.bean.authrelation.vo.AuditLogVO;

import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-10-11 18:32
 */
public class AuditLogVOConverter {

    public static ListAuditLogInputDTO convert(AuditLogListRequest request) {
        ListAuditLogInputDTO dto = new ListAuditLogInputDTO();
        BeanUtils.copyProperties(request, dto);

        return dto;
    }

    public static AuditLogListResponse convert(AuditLogListOutputDTO dto) {
        if (Objects.isNull(dto)) {
            return new AuditLogListResponse();
        }

        AuditLogListResponse response = new AuditLogListResponse();
        BeanUtils.copyProperties(dto, response);
        response.setList(
                Optional.ofNullable(dto.getList()).orElse(Collections.emptyList()).stream()
                        .map(AuditLogVOConverter::convert)
                        .collect(Collectors.toList()));

        return response;
    }

    public static AuditLogVO convert(AuditLogDTO dto) {
        AuditLogVO vo = new AuditLogVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }

    public static DownloadAuditLogInputDTO convert(AuditLogDownloadRequest request) {
        DownloadAuditLogInputDTO dto = new DownloadAuditLogInputDTO();
        BeanUtils.copyProperties(request, dto);
        return dto;
    }

    public static AuditLogDownloadResponse convert(AuditLogDownloadOutputDTO outputDTO) {
        AuditLogDownloadResponse response = new AuditLogDownloadResponse();
        BeanUtils.copyProperties(outputDTO, response);
        return response;
    }

    public static AuditLogFirstModuleResponse convert(List<AuditLogFirstModuleDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new AuditLogFirstModuleResponse(Collections.emptyList());
        }

        List<AuditLogFirstModuleResponse.FirstModuleVO> voList =
                list.stream()
                        .map(
                                dto -> {
                                    AuditLogFirstModuleResponse.FirstModuleVO vo =
                                            new AuditLogFirstModuleResponse.FirstModuleVO();
                                    BeanUtils.copyProperties(dto, vo);
                                    return vo;
                                })
                        .collect(Collectors.toList());

        return new AuditLogFirstModuleResponse(voList);
    }

    public static QueryAuditLogExportListResponse convert(AuditLogExportListOutputDTO outputDTO) {
        QueryAuditLogExportListResponse response = new QueryAuditLogExportListResponse();
        BeanUtils.copyProperties(outputDTO, response);
        response.setData(
                outputDTO.getData().stream()
                        .map(
                                dto -> {
                                    QueryAuditLogExportListResponse.RecordVO vo =
                                            new QueryAuditLogExportListResponse.RecordVO();
                                    BeanUtils.copyProperties(dto, vo);
                                    return vo;
                                })
                        .collect(Collectors.toList()));
        return response;
    }
}
