package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.cert.dto.input.GetOrgCertInputDTO;
import com.timevale.saasbiz.model.bean.cert.dto.input.GetPersonCertInputDTO;
import com.timevale.saasbiz.model.bean.cert.dto.output.GetCertOutputDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.cert.response.GetCertResponse;
import com.timevale.saasbiz.rest.converter.CertResponseConverter;
import com.timevale.saasbiz.service.cert.CertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.constraints.NotBlank;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.*;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * 证书相关接口
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Api(tags = "证书相关接口")
@Validated
@ExternalService
@RestMapping(path = "/v1/saas-common/certs")
public class CertRest {

    @Autowired private CertService certService;

    @ApiOperation(value = "获取个人证书信息", httpMethod = "GET")
    @RestMapping(path = "/person-cert", method = RequestMethod.GET)
    public RestResult<GetCertResponse> getPersonCert(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId) {
        GetPersonCertInputDTO input = new GetPersonCertInputDTO();
        input.setAccountId(accountId);
        GetCertOutputDTO outputDTO = certService.getPersonLatestCert(input);
        return RestResult.success(CertResponseConverter.convert2GetCertResponse(outputDTO));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取企业证书信息", httpMethod = "GET")
    @RestMapping(path = "/org-cert", method = RequestMethod.GET)
    public RestResult<GetCertResponse> getOrgCert(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId) {
        GetOrgCertInputDTO input = new GetOrgCertInputDTO();
        input.setOrgId(tenantId);
        GetCertOutputDTO outputDTO = certService.getOrgLatestCert(input);
        return RestResult.success(CertResponseConverter.convert2GetCertResponse(outputDTO));
    }
}
