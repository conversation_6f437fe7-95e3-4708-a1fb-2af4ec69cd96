package com.timevale.saasbiz.rest.bean.contractreview.rule.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaveRuleRequest extends ToString {

    @ApiModelProperty("规则ID")
    private String ruleId;

    @ApiModelProperty("规则组ID")
    @NotBlank(message = "规则组ID不能为空")
    private String groupId;

    @NotBlank(message = "规则名称不能为空")
    @ApiModelProperty("规则名称")
    @Length(max = 100, message = "规则名称不能超过100字符")
    private String ruleName;

    @ApiModelProperty("规则描述")
    @Length(max = 1000, message = "规则描述不能超过1000字符")
    private String ruleRemark;

    @ApiModelProperty("规则风险等级")
    @NotBlank(message = "规则风险等级不能为空")
    private String riskLevel;

    @ApiModelProperty("审查点")
    @NotEmpty(message = "审查点不能为空")
    private List<SaveRuleRiskRequest> reviewRuleRisks;
}
