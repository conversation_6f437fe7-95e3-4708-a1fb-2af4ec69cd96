package com.timevale.saasbiz.rest;

import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentRecordStatusEnum;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.fulfillment.dto.input.ContractFulfillmentRecordQueryListDTO;
import com.timevale.saasbiz.model.bean.fulfillment.dto.input.ContractFulfillmentRuleQueryListDTO;
import com.timevale.saasbiz.model.bean.fulfillment.dto.input.ContractFulfillmentRuleSaveDTO;
import com.timevale.saasbiz.model.bean.fulfillment.dto.input.ContractFulfillmentRuleUpdateDTO;
import com.timevale.saasbiz.model.bean.fulfillment.dto.output.ContractFulfillmentRecordListDTO;
import com.timevale.saasbiz.model.bean.fulfillment.dto.output.ContractFulfillmentRuleDetailDTO;
import com.timevale.saasbiz.model.bean.fulfillment.dto.output.ContractFulfillmentRuleListDTO;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRecordIdListRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRecordListRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRuleListRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRuleSaveRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRuleUpdateRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRuleUpdateStatusRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.response.ContractFulfillmentRecordListResponse;
import com.timevale.saasbiz.rest.bean.fulfillment.response.ContractFulfillmentRuleDetailResponse;
import com.timevale.saasbiz.rest.bean.fulfillment.response.ContractFulfillmentRuleListResponse;
import com.timevale.saasbiz.rest.bean.fulfillment.response.ContractFulfillmentRuleTypeResponse;
import com.timevale.saasbiz.service.fulfillment.ContractFulfillmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * ContractFulfillmentRest
 *
 * <AUTHOR>
 * @since 2023/10/13 4:19 下午
 */
@Api(tags = "归档接口")
@Validated
@ExternalService
@RestMapping(path = "/v1/contract-fulfillment")
public class ContractFulfillmentRest {

    @Autowired
    private MapperFactory mapperFactory;

    @Autowired
    private ContractFulfillmentService contractFulfillmentService;

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.FULFILLMENT,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "履约规则列表", httpMethod = "POST")
    @RestMapping(path = "/rule-list", method = RequestMethod.POST)
    public RestResult<ContractFulfillmentRuleListResponse> list(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ContractFulfillmentRuleListRequest request) {

        ContractFulfillmentRuleQueryListDTO queryListDTO = mapperFactory.getMapperFacade().map(request, ContractFulfillmentRuleQueryListDTO.class);
        ContractFulfillmentRuleListDTO ruleListDTO = contractFulfillmentService.pageRuleList(queryListDTO, tenantId, accountId);
        return RestResult.success(mapperFactory.getMapperFacade().map(ruleListDTO, ContractFulfillmentRuleListResponse.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.FULFILLMENT,
            privilegeKey = PrivilegeOperationConstants.ADD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "保存履约规则", httpMethod = "POST")
    @RestMapping(path = "/save-rule", method = RequestMethod.POST)
    public RestResult<String> save(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ContractFulfillmentRuleSaveRequest request) {

        ContractFulfillmentRuleSaveDTO saveDTO = mapperFactory.getMapperFacade().map(request, ContractFulfillmentRuleSaveDTO.class);
        saveDTO.setTenantId(tenantId);
        saveDTO.setAccountId(accountId);
        return RestResult.success(contractFulfillmentService.saveRule(saveDTO));
    }


    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "修改履约规则", httpMethod = "POST")
    @RestMapping(path = "/update-rule", method = RequestMethod.POST)
    public RestResult<Void> update(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ContractFulfillmentRuleUpdateRequest request) {
        ContractFulfillmentRuleUpdateDTO updateDTO = mapperFactory.getMapperFacade().map(request, ContractFulfillmentRuleUpdateDTO.class);
        updateDTO.setTenantId(tenantId);
        updateDTO.setAccountId(accountId);
        contractFulfillmentService.updateRule(updateDTO);
        return RestResult.success();
    }
    
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "删除履约规则", httpMethod = "GET")
    @RestMapping(path = "/delete-rule", method = RequestMethod.GET)
    public RestResult<Void> delete(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam String ruleId) {
        contractFulfillmentService.deleteRule(ruleId, tenantId, accountId);
        return RestResult.success();
    }
    
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "更新履约规则状态", httpMethod = "POST")
    @RestMapping(path = "/update-rule-status", method = RequestMethod.POST)
    public RestResult<Void> updateRuleStatus(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ContractFulfillmentRuleUpdateStatusRequest request) {
        contractFulfillmentService.updateRuleStatus(request.getRuleId(), request.getStatus(), tenantId, accountId);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.FULFILLMENT,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查看履约规则详情", httpMethod = "GET")
    @RestMapping(path = "/get-rule-detail", method = RequestMethod.GET)
    public RestResult<ContractFulfillmentRuleDetailResponse> detail(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam String ruleId) {
        ContractFulfillmentRuleDetailDTO detailDTO = contractFulfillmentService.getRuleDetail(ruleId, tenantId, accountId);
        return RestResult.success(mapperFactory.getMapperFacade().map(detailDTO, ContractFulfillmentRuleDetailResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "履约类型列表", httpMethod = "POST")
    @RestMapping(path = "/type-list", method = RequestMethod.POST)
    public RestResult<ContractFulfillmentRuleTypeResponse> typeList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ContractFulfillmentRecordListRequest request) {

        if(StringUtils.isNotEmpty(request.getSubjectOid())){
            tenantId = request.getSubjectOid();
        }
        List<String> typeNameList = contractFulfillmentService.typeNameList(tenantId);
        ContractFulfillmentRuleTypeResponse response = new ContractFulfillmentRuleTypeResponse();
        response.setList(typeNameList);
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "履约记录列表", httpMethod = "POST")
    @RestMapping(path = "/record-list", method = RequestMethod.POST)
    public RestResult<ContractFulfillmentRecordListResponse> list(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ContractFulfillmentRecordListRequest request) {

        ContractFulfillmentRecordQueryListDTO queryListDTO = mapperFactory.getMapperFacade().map(request, ContractFulfillmentRecordQueryListDTO.class);
        ContractFulfillmentRecordListDTO recordListDTO = contractFulfillmentService.pageRecordList(queryListDTO, tenantId, accountId);
        return RestResult.success(mapperFactory.getMapperFacade().map(recordListDTO, ContractFulfillmentRecordListResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "更新履约规则状态", httpMethod = "POST")
    @RestMapping(path = "/mark-record", method = RequestMethod.POST)
    public RestResult<Void> updateRecordStatus(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ContractFulfillmentRecordIdListRequest request) {
        contractFulfillmentService.batchUpdateRecordStatus(request.getRecordIdList(), FulfillmentRecordStatusEnum.MARKED.getStatus(), tenantId, accountId);
        return RestResult.success();
    }


}
