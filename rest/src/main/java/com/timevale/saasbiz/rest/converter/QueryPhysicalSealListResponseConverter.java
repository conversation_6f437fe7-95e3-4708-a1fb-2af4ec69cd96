package com.timevale.saasbiz.rest.converter;

import com.timevale.saasbiz.model.bean.physicalseal.dto.output.PhysicalSealTaskOutputDTO;
import com.timevale.saasbiz.model.bean.physicalseal.dto.output.QueryPhysicalListOutputDTO;
import com.timevale.saasbiz.rest.bean.physicalseal.response.QueryPhysicalSealListResponse;
import com.timevale.saasbiz.rest.bean.physicalseal.vo.PhysicalSealTaskVO;

import java.util.stream.Collectors;

/**
 * 物理印章查询响应数据转换类
 *
 * <AUTHOR>
 * @since 2023-02-24
 */
public class QueryPhysicalSealListResponseConverter {

    /**
     * 转换对外返回查询物理用印列表的数据
     *
     * @param mappingsDTO
     * @return
     */
    public static QueryPhysicalSealListResponse convertPhysicalSealListResponse(
            QueryPhysicalListOutputDTO mappingsDTO) {
        QueryPhysicalSealListResponse response = new QueryPhysicalSealListResponse();
        response.setTotalCount(mappingsDTO.getTotalCount());
        response.setUsedCount(mappingsDTO.getUsedCount());
        response.setUsingCount(mappingsDTO.getUsingCount());
        response.setPage(mappingsDTO.getPage());

        if (null != mappingsDTO.getTaskList()) {
            response.setTaskList(
                    mappingsDTO.getTaskList().stream()
                            .map(i -> convertPhysicalSealTaskMappingVO(i))
                            .collect(Collectors.toList()));
        }


        return response;
    }

    /**
     * 转换对外返回的物理用印的任务的信息
     *
     * @param mappingBO
     * @return
     */
    public static PhysicalSealTaskVO convertPhysicalSealTaskMappingVO(
            PhysicalSealTaskOutputDTO mappingBO) {
        PhysicalSealTaskVO physicalSealTaskVO =
                new PhysicalSealTaskVO();
        physicalSealTaskVO.setPhySealStatus(mappingBO.getPhySealStatus());
        physicalSealTaskVO.setInitName(mappingBO.getInitName());
        physicalSealTaskVO.setPhySealApprovalTime(mappingBO.getPhySealApprovalTime());
        physicalSealTaskVO.setIsTakeOut(mappingBO.getIsTakeOut());
        physicalSealTaskVO.setTsignUrl(mappingBO.getTsignUrl());
        physicalSealTaskVO.setTsignOrgName(mappingBO.getTsignOrgName());
        physicalSealTaskVO.setTsignProcessTitle(mappingBO.getTsignProcessTitle());
        physicalSealTaskVO.setWitnessId(mappingBO.getWitnessId());
        return physicalSealTaskVO;
    }
}
