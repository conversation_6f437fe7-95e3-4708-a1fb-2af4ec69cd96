package com.timevale.saasbiz.rest;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.tracking.consts.TrackingKeyConstant.AI_PAGE_BILLING_ARREARS_TIP;
import static com.timevale.saasbiz.tracking.consts.TrackingServiceConstant.AI_PAGE_BILLING_ARREARS_TIP_TRACKING;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.enums.ProcessSummaryDataTypeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.ListUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.tracking.annotation.Tracking;
import com.timevale.saas.tracking.constants.TrackingModeConstants;
import com.timevale.saasbiz.model.bean.ai.agent.dto.input.ReadFilesByProcessIdInputDTO;
import com.timevale.saasbiz.model.bean.ai.agent.dto.output.ReadFilesByProcessIdOutputDTO;
import com.timevale.saasbiz.model.bean.pdftool.bo.SearchResultBO;
import com.timevale.saasbiz.model.bean.pdftool.dto.input.ProcessFileTextSearchInputDTO;
import com.timevale.saasbiz.model.bean.process.bo.ProcessSummaryKeyInfoBO;
import com.timevale.saasbiz.model.bean.process.bo.ProcessSummaryKeywordPosBO;
import com.timevale.saasbiz.model.bean.process.bo.ProcessSummaryKeywordPosInfoBO;
import com.timevale.saasbiz.model.bean.process.bo.ProcessSummaryKeywordRectPosBO;
import com.timevale.saasbiz.model.bean.process.dto.input.*;
import com.timevale.saasbiz.model.bean.process.dto.output.AiAgentUseCheckOutputDTO;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessSummaryCreateCheckOutputDTO;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessSummaryKeywordSearchOutputDTO;
import com.timevale.saasbiz.model.bean.process.dto.output.QueryProcessSummaryDetailOutputDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.model.enums.process.ProcessSummaryCheckSceneEnum;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.pdftool.request.ProcessFileKeywordSearchRequest;
import com.timevale.saasbiz.rest.bean.pdftool.response.ProcessFileKeywordSearchResponse;
import com.timevale.saasbiz.rest.bean.process.request.*;
import com.timevale.saasbiz.rest.bean.process.response.AiAgentUseCheckResponse;
import com.timevale.saasbiz.rest.bean.process.response.ProcessSummaryCreateCheckResponse;
import com.timevale.saasbiz.rest.bean.process.response.ProcessSummaryDetailResponse;
import com.timevale.saasbiz.rest.bean.process.response.ProcessSummaryKeywordSearchResponse;
import com.timevale.saasbiz.rest.bean.process.response.ProcessSummaryOverAllStatusResponse;
import com.timevale.saasbiz.rest.bean.process.vo.ProcessSummaryKeyInfoVO;
import com.timevale.saasbiz.rest.bean.process.vo.ProcessSummaryKeywordPosInfoVO;
import com.timevale.saasbiz.rest.bean.process.vo.ProcessSummaryKeywordPosVO;
import com.timevale.saasbiz.rest.bean.process.vo.ProcessSummaryKeywordRectPosVO;
import com.timevale.saasbiz.rest.converter.PdfSearchResultConverter;
import com.timevale.saasbiz.service.ai.agent.AiAgentService;
import com.timevale.saasbiz.service.pdftool.PdfToolService;
import com.timevale.saasbiz.service.process.ProcessSummaryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同摘要
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Api(tags = "合同摘要")
@ExternalService
@RestMapping(path = "/v3/processes")
@Slf4j
public class ProcessSummaryRest {
    @Autowired private ProcessSummaryService processSummaryService;
    @Autowired private PdfToolService pdfToolService;

    @ApiOperation(value = "获取合同提取信息", httpMethod = "GET")
    @RestMapping(path = "/get-summary-info", method = RequestMethod.GET)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.CONTRACT_SUMMARY)
    public RestResult<ProcessSummaryDetailResponse> getSummaryInfo(
            @ApiParam(name = "processId", required = true) @RequestParam(value = "processId")
                    String processId,
            @ApiParam(name = "fileId", required = true) @RequestParam(value = "fileId")
                    String fileId,
            @ApiParam(name = "menuId", required = false)
                    @RequestParam(value = "menuId", required = false)
                    String menuId,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        QueryProcessSummaryDetailInputDTO inputDTO = new QueryProcessSummaryDetailInputDTO();
        inputDTO.setProcessId(processId);
        inputDTO.setMenuId(menuId);
        inputDTO.setFileId(fileId);
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);

        QueryProcessSummaryDetailOutputDTO detailOutputDTO =
                processSummaryService.queryProcessSummaryDetail(inputDTO);
        ProcessSummaryDetailResponse response = new ProcessSummaryDetailResponse();
        response.setStatus(detailOutputDTO.getStatus());
        response.setSummary(detailOutputDTO.getSummary());
        response.setSummaryCount(detailOutputDTO.getSummaryCount());
        response.setKeyInfoCount(detailOutputDTO.getKeyInfoCount());
        response.setSummarySuccess(detailOutputDTO.getSummarySuccess());
        response.setKeyInfoSuccess(detailOutputDTO.getKeyInfoSuccess());
        response.setCanEdit(detailOutputDTO.isCanEdit());
        response.setSubjectMember(detailOutputDTO.isSubjectMember());
        response.setContractCategoryId(detailOutputDTO.getContractCategoryId());
        response.setContractCategoryName(detailOutputDTO.getContractCategoryName());
        response.setRoleList(detailOutputDTO.getRoleList());
        response.setKeyInfo(keyInfoBO2KeyInfoVOList(detailOutputDTO.getKeyInfo()));
        return RestResult.success(response);
    }

    @ApiOperation(value = "修改合同提取信息", httpMethod = "POST")
    @RestMapping(path = "/update-summary-info", method = RequestMethod.POST)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.CONTRACT_SUMMARY)
    public RestResult<Void> updateProcessSummary(
            @RequestBody ProcessSummaryUpdateRequest request,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        UpdateProcessSummaryInputDTO inputDTO = new UpdateProcessSummaryInputDTO();
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setFileId(request.getFileId());
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        inputDTO.setSummary(request.getSummary());
        inputDTO.setMenuId(request.getMenuId());
        inputDTO.setKeyInfo(updateKeyInfoRequest2InputDTOList(request.getKeyInfo()));

        processSummaryService.updateProcessSummary(inputDTO);
        return RestResult.success();
    }

    @ApiOperation(value = "重新生成", httpMethod = "POST")
    @RestMapping(path = "/refresh-summary-info", method = RequestMethod.POST)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.CONTRACT_SUMMARY)
    public RestResult<Void> refreshProcessSummary(
            @RequestBody ProcessSummaryRefreshRequest request,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        RefreshProcessSummaryInputDTO inputDTO = new RefreshProcessSummaryInputDTO();
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setFileId(request.getFileId());
        inputDTO.setDataType(ProcessSummaryDataTypeEnum.from(request.getDataType()));
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        inputDTO.setMenuId(request.getMenuId());
        processSummaryService.refreshProcessSummary(inputDTO);
        return RestResult.success();
    }

    @ApiOperation(value = "创建合同摘要", httpMethod = "POST")
    @RestMapping(path = "/create-summary-info", method = RequestMethod.POST)
    @Tracking(
       trackingKey = AI_PAGE_BILLING_ARREARS_TIP,
       trackingService = AI_PAGE_BILLING_ARREARS_TIP_TRACKING,
       trackingData = "'zhaiyao'",
       mode = TrackingModeConstants.MODE_MQ
    )
    public RestResult<Void> createProcessSummary(
            @RequestBody ProcessSummaryCreateRequest request,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        CreateProcessSummaryInputDTO inputDTO = new CreateProcessSummaryInputDTO();
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setFileId(request.getFileId());
        inputDTO.setMenuId(request.getMenuId());
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        processSummaryService.createProcessSummary(inputDTO);
        return RestResult.success();
    }

    @ApiOperation(value = "是否能创建合同摘要的校验", httpMethod = "POST")
    @RestMapping(path = "/check-create-summary", method = RequestMethod.POST)
    public RestResult<ProcessSummaryCreateCheckResponse> processSummaryCreateCheck(
            @RequestBody ProcessSummaryCreateCheckRequest checkRequest,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        ProcessSummaryCreateCheckInputDTO inputDTO = new ProcessSummaryCreateCheckInputDTO();
        inputDTO.setProcessId(checkRequest.getProcessId());
        inputDTO.setMenuId(checkRequest.getMenuId());
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        ProcessSummaryCreateCheckOutputDTO outputDTO =
                processSummaryService.checkProcessSummaryCreate(inputDTO);
        ProcessSummaryCreateCheckResponse response = new ProcessSummaryCreateCheckResponse();
        response.setNotCreateReason(
                outputDTO.getException() == null ? null : outputDTO.getException().getMessage());
        response.setCheckScene(
                outputDTO.getCheckSceneEnum() == null
                        ? ProcessSummaryCheckSceneEnum.APPID_CONFIG_NOT_SUPPORT.getCode()
                        : outputDTO.getCheckSceneEnum().getCode());
        response.setTips(outputDTO.getTips());
        return RestResult.success(response);
    }

    @ApiOperation(value = "是否能使用ai-agent的校验", httpMethod = "POST")
    @RestMapping(path = "/check-ai-agent-use", method = RequestMethod.POST)
    public RestResult<AiAgentUseCheckResponse> checkAiAgentUse(
            @RequestBody AiAgentCheckRequest request,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        AiAgentUseCehckInputDTO inputDTO = new AiAgentUseCehckInputDTO();
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        AiAgentUseCheckOutputDTO outputDTO = processSummaryService.checkAiAgentUse(inputDTO);
        AiAgentUseCheckResponse response = new AiAgentUseCheckResponse();
        response.setCheckScene(outputDTO.getCheckSceneEnum().getCode());
        response.setNotCreateReason(outputDTO.getException() == null ? null : outputDTO.getException().getMessage());
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取合同内所有文件摘要是否运行完毕", httpMethod = "GET")
    @RestMapping(path = "/query-summary-status", method = RequestMethod.GET)
    public RestResult<ProcessSummaryOverAllStatusResponse> getProcessSummaryStatus(
            @ApiParam(name = "processId")
                    @RequestParam(value = "processId")
                    @NotBlank(message = "请传入processId")
                    String processId,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        String status =
                processSummaryService.queryProcessSummaryOverAllStatus(processId, accountId, subjectId);
        ProcessSummaryOverAllStatusResponse response = new ProcessSummaryOverAllStatusResponse();
        response.setStatus(status);
        return RestResult.success(response);
    }

    @ApiOperation(value = "关键字搜索", httpMethod = "POST")
    @RestMapping(path = "/extract-keyword-pos", method = RequestMethod.POST)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.CONTRACT_SUMMARY)
    public RestResult<ProcessSummaryKeywordSearchResponse> searchProcessSummaryKeyword(
            @RequestBody ProcessSummaryKeywordSearchRequest request,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        ProcessSummaryKeywordSearchInputDTO inputDTO = new ProcessSummaryKeywordSearchInputDTO();
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setFileId(request.getFileId());
        inputDTO.setMenuId(request.getMenuId());
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        inputDTO.setKeyInfoList(vos2KeyInfoInputDTOList(request.getKeyInfoList()));
        ProcessSummaryKeywordSearchOutputDTO outputDTO =
                processSummaryService.searchKeyword(inputDTO);
        ProcessSummaryKeywordSearchResponse response = new ProcessSummaryKeywordSearchResponse();
        response.setKeywordPositions(posBO2VOs(outputDTO.getKeywordPositions()));
        return RestResult.success(response);
    }

    @ApiOperation(value = "关键词搜索", httpMethod = "POST")
    @RestMapping(path = "/keyword-search", method = RequestMethod.POST)
    public RestResult<ProcessFileKeywordSearchResponse> search(@RequestBody ProcessFileKeywordSearchRequest request,
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String tenantId) {
        ProcessFileTextSearchInputDTO inputDTO = new ProcessFileTextSearchInputDTO();
        inputDTO.setOperatorId(accountId);
        inputDTO.setTenantId(tenantId);
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setFileId(request.getFileId());
        inputDTO.setKeywords(request.getKeywords());
        inputDTO.setAcceptPosFormat(request.getAcceptPosFormat());
        List<SearchResultBO> searchResultBOs = pdfToolService.processFileTextSearch(inputDTO);
        
        ProcessFileKeywordSearchResponse response = new ProcessFileKeywordSearchResponse();
        response.setSearchResults(PdfSearchResultConverter.convert(searchResultBOs));
        return RestResult.success(response);
    }

    private List<ProcessSummaryKeywordPosInfoVO> posBO2VOs(
            List<ProcessSummaryKeywordPosInfoBO> bos) {
        if (ListUtils.isEmpty(bos)) {
            return Lists.newArrayList();
        }
        return bos.stream()
                .map(
                        bo ->
                                new ProcessSummaryKeywordPosInfoVO(
                                        bo.getSeq(), positionBO2VOList(bo.getPositions())))
                .sorted(Comparator.comparing(ProcessSummaryKeywordPosInfoVO::getSeq))
                .collect(Collectors.toList());
    }

    private List<ProcessSummaryKeywordPosVO> positionBO2VOList(
            List<ProcessSummaryKeywordPosBO> bos) {
        if (ListUtils.isEmpty(bos)) {
            return Lists.newArrayList();
        }
        return bos.stream()
                .map(
                        bo ->
                                new ProcessSummaryKeywordPosVO(
                                        bo.getPageNum(), posRectBO2VOList(bo.getRectBlocks())))
                .collect(Collectors.toList());
    }

    private List<ProcessSummaryKeywordRectPosVO> posRectBO2VOList(
            List<ProcessSummaryKeywordRectPosBO> bos) {
        if (ListUtils.isEmpty(bos)) {
            return Lists.newArrayList();
        }
        return bos.stream()
                .map(
                        bo ->
                                new ProcessSummaryKeywordRectPosVO(
                                        bo.getLeft(), bo.getBottom(), bo.getRight(), bo.getTop()))
                .collect(Collectors.toList());
    }

    private List<ProcessSummaryKeywordSearchKeyInfoInputDTO> vos2KeyInfoInputDTOList(
            List<ProcessSummaryKeywordSearchRequest.KeyInfo> vos) {
        if (ListUtils.isEmpty(vos)) {
            return Lists.newArrayList();
        }
        return vos.stream().map(this::vo2KeyInfoInputDTO).collect(Collectors.toList());
    }

    private ProcessSummaryKeywordSearchKeyInfoInputDTO vo2KeyInfoInputDTO(
            ProcessSummaryKeywordSearchRequest.KeyInfo vo) {
        ProcessSummaryKeywordSearchKeyInfoInputDTO processSummaryKeywordSearchKeyInfoInputDTO =
                new ProcessSummaryKeywordSearchKeyInfoInputDTO();
        processSummaryKeywordSearchKeyInfoInputDTO.setSeq(vo.getSeq());
        processSummaryKeywordSearchKeyInfoInputDTO.setAiValue(vo.getAiValue());
        return processSummaryKeywordSearchKeyInfoInputDTO;
    }

    private List<UpdateProcessSummaryKeyInfoInputDTO> updateKeyInfoRequest2InputDTOList(
            List<ProcessSummaryKeyInfoUpdateRequest> requestList) {
        if (ListUtils.isEmpty(requestList)) {
            return Lists.newArrayList();
        }
        return requestList.stream()
                .map(this::updateKeyInfoRequest2InputDTO)
                .collect(Collectors.toList());
    }

    private UpdateProcessSummaryKeyInfoInputDTO updateKeyInfoRequest2InputDTO(
            ProcessSummaryKeyInfoUpdateRequest request) {
        UpdateProcessSummaryKeyInfoInputDTO updateProcessSummaryKeyInfoInputDTO =
                new UpdateProcessSummaryKeyInfoInputDTO();
        updateProcessSummaryKeyInfoInputDTO.setSeq(request.getSeq());
        updateProcessSummaryKeyInfoInputDTO.setName(request.getName());
        updateProcessSummaryKeyInfoInputDTO.setValue(request.getValue());
        return updateProcessSummaryKeyInfoInputDTO;
    }

    private List<ProcessSummaryKeyInfoVO> keyInfoBO2KeyInfoVOList(
            List<ProcessSummaryKeyInfoBO> keyInfoBOS) {
        if (ListUtils.isEmpty(keyInfoBOS)) {
            return Lists.newArrayList();
        }
        return keyInfoBOS.stream().map(this::keyInfoBO2KeyInfoVO)
        .sorted(Comparator.comparing(ProcessSummaryKeyInfoVO::getSeq))
        .collect(Collectors.toList());
    }

    private ProcessSummaryKeyInfoVO keyInfoBO2KeyInfoVO(ProcessSummaryKeyInfoBO bo) {
        ProcessSummaryKeyInfoVO processSummaryKeyInfoVO = new ProcessSummaryKeyInfoVO();
        processSummaryKeyInfoVO.setSeq(bo.getSeq());
        processSummaryKeyInfoVO.setName(bo.getName());
        processSummaryKeyInfoVO.setValue(bo.getValue());
        processSummaryKeyInfoVO.setAiValue(bo.getAiValue());
        return processSummaryKeyInfoVO;
    }
}
