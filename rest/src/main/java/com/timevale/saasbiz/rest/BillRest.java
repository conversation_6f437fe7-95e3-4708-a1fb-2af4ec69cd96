package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.bill.*;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasAnyPrivilegeInputDTO;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.utils.AppConfigUtil;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.bill.request.QueryAccountUnifiedAssetRequest;
import com.timevale.saasbiz.rest.bean.bill.request.QueryCommodityRequest;
import com.timevale.saasbiz.rest.bean.bill.response.*;
import com.timevale.saasbiz.rest.bean.bill.vo.SaasBaseCommodityVO;
import com.timevale.saasbiz.service.bill.BillService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.*;
import static com.timevale.saasbiz.model.enums.AppConfigEnum.SAAS_COMMODITY_PACKAGE_SHOW_INFO;

/**
 * <AUTHOR>
 * @since 2024-08-27
 */
@Api(tags = "计费相关接口")
@Validated
@ExternalService
@RestMapping(path = "/v1/saas-common/bills")
public class BillRest {
    @Resource private UserCenterService userCenterService;
    @Autowired private BillService billService;
    @Autowired private MapperFactory mapperFactory;

    @ApiOperation(value = "获取saas商品信息接口", httpMethod = "GET")
    @RestMapping(path = "/saas-commodities", method = RequestMethod.GET)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<QuerySaasCommoditiesResponse> getSaasCommodities(
            @RequestParam(required = false) @ApiParam(name = "橱窗id，有值时优先取该值") String showcaseNo) {
        SaasCommodityDTO saasCommodityDTO =
                billService.getSaasCommodities(
                        RequestContextExtUtils.getClientId(),
                        RequestContextExtUtils.getTenantId(),
                        showcaseNo);
        QuerySaasCommoditiesResponse response =
                mapperFactory
                        .getMapperFacade()
                        .map(saasCommodityDTO, QuerySaasCommoditiesResponse.class);
        if (response != null && CollectionUtils.isNotEmpty(response.getVipCommodities())) {
            wrapCommoditiesResponseVipShowInfo(response);
        }
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取橱窗下商品信息", httpMethod = "POST")
    @RestMapping(path = "/commodity/packages", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<QueryCommodityResponse> queryCommodities(@RequestBody QueryCommodityRequest request) {
        List<CommodityMappingDTO> commodityMappings = billService.getCommodityMapping(request.getProductId(), request.getProductCode(), request.getCombinationProduct());
        CommodityInfoDTO commodityInfos = billService.getCommodityInfo(RequestContextExtUtils.getTenantId(), commodityMappings, request.getCombinationProduct());
        return RestResult.success(convertCommodityResponse(commodityInfos));
    }

    @ApiOperation(value = "获取账户资产/已购商品卡片信息", httpMethod = "POST")
    @RestMapping(path = "/assets/unified-list", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<QueryAccountUnifiedAssetResponse> queryAccountUnifiedAsset(@RequestBody QueryAccountUnifiedAssetRequest request) {
        String tenantId = RequestContextExtUtils.getTenantId();
        String accountId = RequestContextExtUtils.getOperatorId();
        String clientId = RequestContextExtUtils.getClientId();
        accountPrivilegeCheck(accountId, tenantId);

        AccountAssetDTO accountAssetDTO = billService.queryAccountAsset(tenantId, clientId, RequestContextExtUtils.getOperatorId(), request.getPrimaryOid());
        if (accountAssetDTO == null) {
            return RestResult.success();
        }
        QueryAccountUnifiedAssetResponse response = convertResponse(accountAssetDTO);
        return RestResult.success(response);
    }

    /**
     * 账号权限校验
     * 需要有账户查询权限 或者 套餐查询条件
     */
    private void accountPrivilegeCheck(String accountId, String tenantId) {
        CheckHasAnyPrivilegeInputDTO input = new CheckHasAnyPrivilegeInputDTO();
        input.setAccountId(accountId);
        input.setTenantId(tenantId);
        input.addResourcePrivilege(PrivilegeResourceConstants.ACCOUNT, PrivilegeOperationConstants.QUERY);
        input.addResourcePrivilege(PrivilegeResourceConstants.PACKAGE, PrivilegeOperationConstants.QUERY);
        userCenterService.checkHasAnyPrivilege(input, true);
    }

    private QueryAccountUnifiedAssetResponse convertResponse(AccountAssetDTO accountAssetDTO) {
        QueryAccountUnifiedAssetResponse response = new QueryAccountUnifiedAssetResponse();
        response.setAssets(convertMainAccountAsset(accountAssetDTO.getMainAccountAssets()));
        response.setMoreAssets(convertMoreAccountAssets(accountAssetDTO.getMoreAccountAssets()));
        response.setBuyAssets(convertBuyAccountAssets(accountAssetDTO.getBuyAccountAssets()));
        response.setThirdAssert(
                convertThirdAccountAssert(accountAssetDTO.getThirdAccountAssertDTO()));
        return response;
    }

    private ThirdAccountAssertResponse convertThirdAccountAssert(
            ThirdAccountAssertDTO thirdAccountAssertDTO) {
        if (thirdAccountAssertDTO == null) {
            return null;
        }
        ThirdAccountAssertResponse response = new ThirdAccountAssertResponse();
        response.setOrderUrl(thirdAccountAssertDTO.getOrderUrl());
        return response;
    }

    private List<MainAccountAssetResponse> convertMainAccountAsset(List<MainAccountAssetDTO> mainAccountAssets) {
        if (CollectionUtils.isEmpty(mainAccountAssets)) {
            return Lists.newArrayList();
        }
        List<MainAccountAssetResponse> responses = Lists.newArrayList();
        for (MainAccountAssetDTO dto : mainAccountAssets) {
            MainAccountAssetResponse response = new MainAccountAssetResponse();
            baseConvert(response, dto);
            response.setIconUrl(dto.getIconUrl());
            response.setActions(convertActions(dto.getActions()));
            responses.add(response);
        }
        return responses;
    }

    private List<AccountAssetActionResponse> convertActions(List<AccountAssetActionDTO> actions) {
        if (CollectionUtils.isEmpty(actions)) {
            return Lists.newArrayList();
        }
        List<AccountAssetActionResponse> responses = Lists.newArrayList();
        for (AccountAssetActionDTO dto : actions) {
            AccountAssetActionResponse response = new AccountAssetActionResponse();
            response.setType(dto.getType());
            response.setName(dto.getName());
            responses.add(response);
        }
        return responses;
    }

    private void baseConvert(BaseAccountAsset baseAccountAsset, BaseAccountAssetDTO dto) {
        if (baseAccountAsset == null || dto == null) {
            return;
        }
        baseAccountAsset.setAssetName(dto.getAssetName());
        baseAccountAsset.setAssetDesc(dto.getAssetDesc());
        baseAccountAsset.setType(dto.getType());
        baseAccountAsset.setUnlimitNum(dto.isUnlimitNum());
        baseAccountAsset.setTotalNum(dto.getTotalNum());
        baseAccountAsset.setMargin(dto.getMargin());
        baseAccountAsset.setUnits(dto.getUnits());
        baseAccountAsset.setProductId(dto.getProductId());
        baseAccountAsset.setProductName(dto.getProductName());
        baseAccountAsset.setSaleSchemaId(dto.getSaleSchemaId());
        baseAccountAsset.setSaleSchemaName(dto.getSaleSchemaName());
        baseAccountAsset.setCreateTime(dto.getCreateTime());
        baseAccountAsset.setStartTime(dto.getStartTime());
        baseAccountAsset.setEndTime(dto.getEndTime());
        baseAccountAsset.setStatusSign(dto.getStatusSign());
        baseAccountAsset.setAggregateBalance(dto.getAggregateBalance());
        baseAccountAsset.setBalance(dto.getBalance());
        baseAccountAsset.setFreezeBalance(dto.getFreezeBalance());
    }

    private List<MoreAccountAssetResponse> convertMoreAccountAssets(
            List<MoreAccountAssetDTO> moreAccountAssets) {
        if (CollectionUtils.isEmpty(moreAccountAssets)) {
            return Lists.newArrayList();
        }
        List<MoreAccountAssetResponse> responses = Lists.newArrayList();
        for (MoreAccountAssetDTO dto : moreAccountAssets) {
            MoreAccountAssetResponse response = new MoreAccountAssetResponse();
            baseConvert(response, dto);
            responses.add(response);
        }
        return responses;
    }

    private List<BuyAccountAssetResponse> convertBuyAccountAssets(
            List<BuyAccountAssetDTO> buyAccountAssets) {
        if (CollectionUtils.isEmpty(buyAccountAssets)) {
            return Lists.newArrayList();
        }
        List<BuyAccountAssetResponse> responses = Lists.newArrayList();
        for (BuyAccountAssetDTO dto : buyAccountAssets) {
            BuyAccountAssetResponse response = new BuyAccountAssetResponse();
            baseConvert(response, dto);
            response.setIntroduce(dto.getIntroduce());
            response.setIconUrl(dto.getIconUrl());
            response.setUnitPrice(dto.getUnitPrice());
            response.setTags(convertTags(dto.getTags()));
            response.setActions(convertActions(dto.getActions()));
            responses.add(response);
        }
        return responses;
    }

    private List<AccountAssetTagResponse> convertTags(List<AccountAssetTagDTO> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return Lists.newArrayList();
        }

        List<AccountAssetTagResponse> responses = Lists.newArrayList();
        for (AccountAssetTagDTO dto : tags) {
            AccountAssetTagResponse response = new AccountAssetTagResponse();
            response.setName(dto.getName());
            responses.add(response);
        }
        return responses;
    }

    private QueryCommodityResponse convertCommodityResponse(CommodityInfoDTO commodityDetail) {
        List<CommodityPackageDTO> commodityDetailList = commodityDetail.getCommodityDetailList();
        List<CommodityPackageInfo> infos = Lists.newArrayList();
        for (CommodityPackageDTO dto : commodityDetailList) {
            CommodityPackageInfo info = new CommodityPackageInfo();
            info.setPackageId(dto.getPackageId());
            info.setShowcaseId(dto.getShowcaseId());
            info.setCommodityId(dto.getCommodityId());
            info.setCommodityNo(dto.getCommodityNo());
            info.setPackageName(dto.getPackageName());
            info.setProductId(dto.getProductId());
            info.setEffectivePeriod(dto.getEffectivePeriod());
            info.setOriginalPrice(dto.getOriginalPrice());
            info.setRank(dto.getRank());
            info.setEffectiveDate(dto.getEffectiveDate());
            info.setEffectiveIsUnlimited(dto.getEffectiveIsUnlimited());
            info.setEffectiveUnit(dto.getEffectiveUnit());
            info.setPrice(dto.getPrice());
            info.setTotalNum(dto.getTotalNum());
            info.setUnits(dto.getUnits());
            info.setTotalIsUnlimited(dto.getTotalIsUnlimited());
            info.setIconUrl(dto.getIconUrl());
            infos.add(info);
        }

        QueryCommodityResponse response = new QueryCommodityResponse();
        response.setCommodityDetailList(infos);
        return response;
    }

    /**
     * 包装版本商品的展示信息（目前只有版本信息有展示信息，先简单处理）
     *
     * @param response
     */
    private void wrapCommoditiesResponseVipShowInfo(QuerySaasCommoditiesResponse response) {
        String showInfo = AppConfigUtil.getString(SAAS_COMMODITY_PACKAGE_SHOW_INFO);
        if (StringUtils.isBlank(showInfo)) {
            return;
        }
        List<SaasCommodityPackageShowConfig> config =
                JSON.parseArray(showInfo, SaasCommodityPackageShowConfig.class);
        if (CollectionUtils.isEmpty(config)) {
            return;
        }
        Map<String, SaasCommodityPackageShowConfig> configMap =
                config.stream()
                        .collect(
                                Collectors.toMap(
                                        SaasCommodityPackageShowConfig::getPackageId,
                                        v -> v,
                                        (v1, v2) -> v2));
        wrapPackage(response.getVipCommodities(), configMap);
        wrapPackage(response.getSignCommodities(), configMap);
        wrapPackage(response.getAddedCommodities(), configMap);
    }

    private void wrapPackage(
            List<? extends SaasBaseCommodityVO> saasBaseCommodityVOS,
            Map<String, SaasCommodityPackageShowConfig> configMap) {
        if (CollectionUtils.isEmpty(saasBaseCommodityVOS)) {
            return;
        }

        saasBaseCommodityVOS.forEach(
                comm -> {
                    if (CollectionUtils.isEmpty(comm.getPackageInfoList())) {
                        return;
                    }

                    comm.getPackageInfoList()
                            .forEach(
                                    pac -> {
                                        if(pac.getPackageId()==null){
                                            return;
                                        }
                                        SaasCommodityPackageShowConfig showConfig =
                                                configMap.get(pac.getPackageId().toString());
                                        if (showConfig == null) {
                                            return;
                                        }
                                        pac.setRemark(showConfig.getRemark());
                                    });
                });
    }
}
