package com.timevale.saasbiz.rest.converter.contractreview;

import com.timevale.saasbiz.model.bean.contractreview.rule.dto.input.*;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.SaveRuleInventoryInputDTO;
import com.timevale.saasbiz.rest.bean.contractreview.rule.request.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;


public class RuleInputDTOConverter {
    public static AnalysisRuleInputDTO convertToInputDTO(String accountId, String tenantId, AnalysisRuleRequest request) {
        AnalysisRuleInputDTO dto = new AnalysisRuleInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setRuleRemark(request.getRuleRemark());
        dto.setGroupId(request.getGroupId());
        return dto;
    }

    public static BatchSaveRuleInputDTO convertToInputDTO(String accountId, String tenantId, BatchSaveRuleRequest request) {
        BatchSaveRuleInputDTO dto = new BatchSaveRuleInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);

        if (CollectionUtils.isNotEmpty(request.getRuleList())) {
            List<SaveRuleInputDTO> ruleBOs = request.getRuleList().stream()
                    .map(vo -> {
                        SaveRuleInputDTO d = new SaveRuleInputDTO();
                        BeanUtils.copyProperties(vo,d);
                        return d;
                    })
                    .collect(Collectors.toList());
            dto.setRuleList(ruleBOs);
        }
        return dto;
    }

    public static AnalysisRuleFileInputDTO convertToInputDTO(String accountId, String tenantId, AnalysisRuleFileRequest request) {
        AnalysisRuleFileInputDTO dto = new AnalysisRuleFileInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setFileKey(request.getFileKey());
        dto.setGroupId(request.getGroupId());
        return dto;
    }

    public static RuleIdInputDTO convertToInputDTO(String accountId, String tenantId, RuleIdRequest request) {
        RuleIdInputDTO dto = new RuleIdInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setRuleId(request.getRuleId());
        return dto;
    }

    public static RuleCopyInputDTO convertToInputDTO(String accountId, String tenantId, RuleCopyRequest request) {
        RuleCopyInputDTO dto = new RuleCopyInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setRuleId(request.getRuleId());
        dto.setRuleName(request.getRuleName());
        return dto;
    }

    public static SaveRuleInputDTO convertToInputDTO(String accountId, String tenantId, SaveRuleRequest request) {
        SaveRuleInputDTO dto = new SaveRuleInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setRuleId(request.getRuleId());
        dto.setRiskLevel(request.getRiskLevel());
        dto.setRuleName(request.getRuleName());
        dto.setGroupId(request.getGroupId());
        dto.setRuleRemark(request.getRuleRemark());

        if (CollectionUtils.isNotEmpty(request.getReviewRuleRisks())) {
            List<SaveRuleRiskInputDTO> ruleBOs = request.getReviewRuleRisks().stream()
                    .map(vo -> {
                        SaveRuleRiskInputDTO d = new SaveRuleRiskInputDTO();
                        d.setRuleDescription(vo.getRuleDescription());
                        d.setRuleBasis(vo.getRuleBasis());
                        d.setKeyword(vo.getKeyword());
                        d.setRuleRiskId(vo.getRuleRiskId());
                        d.setSuggestion(vo.getSuggestion());
                        d.setSimilarTerms(vo.getSimilarTerms());
                        return d;
                    })
                    .collect(Collectors.toList());
            dto.setReviewRuleRisks(ruleBOs);
        }
        return dto;
    }

    public static ListRuleInputDTO convertToInputDTO(String accountId, String tenantId, ListRuleRequest request) {
        ListRuleInputDTO dto = new ListRuleInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setInventoryId(request.getInventoryId());
        dto.setGroupId(request.getGroupId());
        return dto;
    }


    public static DelRuleInputDTO convertToInputDTO(String accountId, String tenantId, DelRuleRequest request) {
        DelRuleInputDTO dto = new DelRuleInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setRuleIds(request.getRuleIds());
        return dto;
    }



}
