package com.timevale.saasbiz.rest.converter.contractreview;

import com.google.common.collect.Lists;
import com.timevale.saasbiz.model.bean.contractreview.rule.bo.ListRuleBO;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.DetailRuleListOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.DetailRuleRiskOutputDTO;
import com.timevale.saasbiz.rest.bean.contractreview.rule.response.DetailRuleListResponse;
import com.timevale.saasbiz.rest.bean.contractreview.rule.response.DetailRuleRiskResponse;
import com.timevale.saasbiz.rest.bean.contractreview.rule.vo.ListRuleVO;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class RuleOutputDTOConverter {
    public static DetailRuleListResponse convertToDetailResponse(DetailRuleListOutputDTO dto) {
        DetailRuleListResponse detailRuleResponse = new DetailRuleListResponse();
        detailRuleResponse.setRuleId(dto.getRuleId());
        detailRuleResponse.setRuleRemark(dto.getRuleRemark());
        detailRuleResponse.setConvertedStatus(dto.getConvertedStatus());
        detailRuleResponse.setRuleName(dto.getRuleName());
        detailRuleResponse.setInventoryId(dto.getInventoryId());
        detailRuleResponse.setDefaultFlag(dto.getDefaultFlag());
        detailRuleResponse.setGroupId(dto.getGroupId());
        detailRuleResponse.setIsChecked(dto.getIsChecked());
        detailRuleResponse.setRiskLevel(dto.getRiskLevel());
        detailRuleResponse.setInventoryId(dto.getInventoryId());
        detailRuleResponse.setReviewRuleRisks(convertToRuleRiskResponse(dto.getReviewRuleRisks()));
        return detailRuleResponse;
    }

    private static List<DetailRuleRiskResponse> convertToRuleRiskResponse(List<DetailRuleRiskOutputDTO> ruleBOs) {
        return Optional.ofNullable(ruleBOs).orElse(Lists.newArrayList()).stream()
                .map(RuleOutputDTOConverter::convertToRuleVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static DetailRuleRiskResponse convertToRuleVO(DetailRuleRiskOutputDTO dto) {
        if (null == dto) {
            return null;
        }
        DetailRuleRiskResponse response = new DetailRuleRiskResponse();
        response.setRuleId(dto.getRuleId());
        response.setRuleDescription(dto.getRuleDescription());
        response.setRuleBasis(dto.getRuleBasis());
        response.setRuleRiskId(dto.getRuleRiskId());
        response.setKeyword(dto.getKeyword());
        response.setSuggestion(dto.getSuggestion());
        response.setSimilarTerms(dto.getSimilarTerms());
        return response;
    }

    private static ListRuleVO convertToRuleVO(ListRuleBO dto) {
        if (null == dto) {
            return null;
        }
        ListRuleVO response = new ListRuleVO();
        response.setRuleId(dto.getRuleId());
        response.setDefaultFlag(dto.getDefaultFlag());
        response.setIsChecked(dto.getIsChecked());
        response.setRuleName(dto.getRuleName());
        response.setGroupId(dto.getGroupId());
        response.setRiskLevel(dto.getRiskLevel());
        return response;
    }

    public static List<ListRuleVO> convertToListVO(List<ListRuleBO> list) {
        return Optional.ofNullable(list).orElse(Lists.newArrayList()).stream()
                .map(RuleOutputDTOConverter::convertToRuleVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
