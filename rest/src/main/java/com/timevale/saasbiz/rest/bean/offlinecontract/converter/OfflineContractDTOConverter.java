package com.timevale.saasbiz.rest.bean.offlinecontract.converter;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.*;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.*;

import java.util.stream.Collectors;

/**
 * 线下合同业务层入参转换类
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public class OfflineContractDTOConverter {

    /**
     * 转换线下合同信息
     *
     * @param offlineContractVO
     * @return
     */
    public static OfflineContractDTO convert2OfflineContractDTO(
            OfflineContractVO offlineContractVO) {
        if (null == offlineContractVO) {
            return null;
        }
        OfflineContractDTO offlineContractDTO = new OfflineContractDTO();
        offlineContractDTO.setContractFiles(
                offlineContractVO.getContractFiles().stream()
                        .map(i -> convert2OfflineContractFileDTO(i))
                        .collect(Collectors.toList()));
        offlineContractDTO.setProcessInfo(
                convert2ProcessInfoDTO(offlineContractVO.getProcessInfo()));

        return offlineContractDTO;
    }

    /**
     * 转换线下合同导入提取配置
     *
     * @param configVO
     * @return
     */
    public static OfflineContractExtractConfigDTO convert2ExtractConfigDTO(
            OfflineContractExtractConfigVO configVO) {
        if (null == configVO) {
            return null;
        }
        OfflineContractExtractConfigDTO extractConfigDTO = new OfflineContractExtractConfigDTO();
        extractConfigDTO.setFields(
                configVO.getFields().stream()
                        .map(i -> convert2ExtractFieldDTO(i))
                        .collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(configVO.getSigners())) {
            extractConfigDTO.setSigners(
                    configVO.getSigners().stream()
                            .map(i -> convert2SignerConfigDTO(i))
                            .collect(Collectors.toList()));
        }
        return extractConfigDTO;
    }

    /**
     * 转换线下合同对应的合同信息
     *
     * @param processInfoVO
     * @return
     */
    private static OfflineContractProcessInfoDTO convert2ProcessInfoDTO(
            OfflineContractProcessInfoVO processInfoVO) {
        if (null == processInfoVO) {
            return null;
        }
        OfflineContractProcessInfoDTO processInfoDTO = new OfflineContractProcessInfoDTO();
        processInfoDTO.setTitle(processInfoVO.getTitle());
        if (CollectionUtils.isNotEmpty(processInfoVO.getSigners())) {
            processInfoDTO.setSigners(
                    processInfoVO.getSigners().stream()
                            .map(i -> convert2ContractAccountDTO(i))
                            .collect(Collectors.toList()));
        }
        processInfoDTO.setContractValidity(processInfoVO.getContractValidity());
        return processInfoDTO;
    }

    /**
     * 转换线下流程用户信息
     *
     * @param accountVO
     * @return
     */
    private static OfflineContractAccountDTO convert2ContractAccountDTO(
            OfflineContractAccountVO accountVO) {
        OfflineContractAccountDTO accountDTO = new OfflineContractAccountDTO();
        accountDTO.setAccount(accountVO.getAccount());
        accountDTO.setAccountName(accountVO.getAccountName());
        accountDTO.setSubjectName(accountVO.getSubjectName());
        accountDTO.setSubjectType(accountVO.getSubjectType());
        return accountDTO;
    }

    /**
     * 转换线下合同文件信息
     *
     * @param fileVO
     * @return
     */
    private static OfflineContractFileDTO convert2OfflineContractFileDTO(
            OfflineContractFileVO fileVO) {
        OfflineContractFileDTO offlineContractFileDTO = new OfflineContractFileDTO();
        offlineContractFileDTO.setFileId(fileVO.getFileId());
        offlineContractFileDTO.setFileName(fileVO.getFileName());
        offlineContractFileDTO.setFileType(fileVO.getFileType());
        offlineContractFileDTO.setContractNo(fileVO.getContractNo());
        offlineContractFileDTO.setCategoryId(fileVO.getCategoryId());
        offlineContractFileDTO.setCategoryName(fileVO.getCategoryName());
        return offlineContractFileDTO;
    }

    /**
     * 转换提取字段
     *
     * @param extractFieldVO
     * @return
     */
    private static OfflineExtractFieldDTO convert2ExtractFieldDTO(
            OfflineExtractFieldVO extractFieldVO) {
        OfflineExtractFieldDTO extractFieldDTO = new OfflineExtractFieldDTO();
        extractFieldDTO.setFieldCode(extractFieldVO.getFieldCode());
        extractFieldDTO.setEnable(extractFieldVO.isEnable());
        return extractFieldDTO;
    }

    /**
     * 转换签署方配置
     *
     * @param configVO
     * @return
     */
    public static OfflineContractSignerConfigDTO convert2SignerConfigDTO(
            OfflineContractSignerConfigVO configVO) {
        OfflineContractSignerConfigDTO signerConfigDTO = new OfflineContractSignerConfigDTO();
        signerConfigDTO.setSignerKeyword(configVO.getSignerKeyword());
        signerConfigDTO.setSignerSubjectType(configVO.getSignerSubjectType());
        return signerConfigDTO;
    }
}
