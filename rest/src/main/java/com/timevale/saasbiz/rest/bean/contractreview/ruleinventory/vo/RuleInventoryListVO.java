package com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.vo;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class RuleInventoryListVO extends ToString {
    @ApiModelProperty("审查清单id")
    private String inventoryId;
    
    @ApiModelProperty("审查清单名称")
    private String inventoryName;
    
    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("审查视角")
    private String contractView;
    
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty("规则数量")
    private Integer ruleSize;

    @ApiModelProperty("规则数量上限")
    private Integer ruleMaxSize;

    @ApiModelProperty("创建人")
    private String operatorName;
}
