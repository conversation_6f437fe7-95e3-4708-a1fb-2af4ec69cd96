package com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.vo;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RuleInventoryDetailVO extends ToString {
    private String inventoryId;
    private String inventoryName;
    private String contractType;
    private String contractView;
    private Date createTime;
    private Date modifyTime;
    private List<ReviewRuleVO> rules;
}
