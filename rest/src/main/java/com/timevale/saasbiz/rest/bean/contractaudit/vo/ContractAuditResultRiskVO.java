package com.timevale.saasbiz.rest.bean.contractaudit.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Data
public class ContractAuditResultRiskVO {
    @ApiModelProperty(value = "风险点")
    private String riskBrief;
    @ApiModelProperty(value = "风险点说明")
    private String riskExplain;
    @ApiModelProperty(value = "建议类型")
    private String resultType;
    @ApiModelProperty(value = "合同原文")
    private String originalContent;
    @ApiModelProperty(value = "建议内容")
    private String resultContent;
}
