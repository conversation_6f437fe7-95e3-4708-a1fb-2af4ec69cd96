package com.timevale.saasbiz.rest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.ai.agent.dto.input.ReadFilesByProcessIdInputDTO;
import com.timevale.saasbiz.model.bean.ai.agent.dto.output.ReadFilesByProcessIdOutputDTO;
import com.timevale.saasbiz.model.bean.ai.agent.dto.output.ReadFilesByProcessIdOutputDTO.FileInfo;
import com.timevale.saasbiz.model.bean.process.dto.input.ProcessAgentInitInputDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.model.utils.FileUtil;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.aiagen.request.AiAgentInitRequest;
import com.timevale.saasbiz.rest.bean.aiagen.response.AiAgentInitResult;
import com.timevale.saasbiz.rest.bean.aiagen.vo.FileInfoVO;
import com.timevale.saasbiz.service.ai.rag.ProcessRagService;
import com.timevale.saasbiz.service.ai.agent.AiAgentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;;

@Api(tags = "合同rag")
@ExternalService
@RestMapping(path = "/v2/process")
public class ProcessRagRest {

    @Autowired
    private ProcessRagService processRagService;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.CONTRACT_DETAIL_AGENT)
    @ApiOperation(value = "触发rag初始化", httpMethod = "POST")
    @RestMapping(path = "/agent-init/{processId}", method = RequestMethod.POST)
    public RestResult<AiAgentInitResult> init(
            @PathVariable String processId,
            @RequestBody AiAgentInitRequest request,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId) {
        
        ProcessAgentInitInputDTO input = new ProcessAgentInitInputDTO();
        input.setProcessId(processId);
        input.setOperatorId(operatorId);
        input.setTenantId(subjectId);
        input.setFlowType(request.getFlowType());
        input.setMenuId(request.getMenuId());
        ReadFilesByProcessIdOutputDTO outputDTO = processRagService.init(input);
        List<FileInfoVO> fileInfoVOs = convertfFileInfoVOs(outputDTO.getFileList());
        AiAgentInitResult result = new AiAgentInitResult();
        result.setFileInfos(fileInfoVOs);
        return RestResult.success(result);
    }

    private List<FileInfoVO> convertfFileInfoVOs(List<ReadFilesByProcessIdOutputDTO.FileInfo> fileInfos) {
        List<FileInfoVO> results = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fileInfos)) {
            return results;
        }
        for (ReadFilesByProcessIdOutputDTO.FileInfo fileInfo : fileInfos) {
            FileInfoVO fileInfoVO = new FileInfoVO();
            fileInfoVO.setFileId(fileInfo.getFileId());
            fileInfoVO.setFileName(fileInfo.getFileName());
            fileInfoVO.setFileType(FileUtil.getFileType(fileInfo.getFileName()));
            fileInfoVO.setFileUrl(fileInfo.getFileUrl());
            fileInfoVO.setIsAttachment(fileInfo.getIsAttachment());
            fileInfoVO.setPageSize(fileInfo.getPageSize());
            results.add(fileInfoVO);
        }
        return results;
    }
}
