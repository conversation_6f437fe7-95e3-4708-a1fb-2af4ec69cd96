package com.timevale.saasbiz.rest.bean.contractaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
public class CreateAuditResultRequest {
    @ApiModelProperty(value = "合同id")
    @NotBlank(message = "合同id不能为空")
    private String processId;
    @ApiModelProperty(value = "文件id")
    @NotBlank(message = "文件id不能为空")
    private String fileId;
    @ApiModelProperty(value = "审查清单id")
    @NotBlank(message = "审查清单id不能为空")
    private String ruleListId;
    @ApiModelProperty(value = "审查立场")
    private String position;
}
