package com.timevale.saasbiz.rest.converter.contractreview;

import com.google.common.collect.Lists;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.bo.RuleGroupListBO;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.vo.RuleGroupListVO;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class RuleGroupOutputDTOConverter {

    public static List<RuleGroupListVO> convertToListVOS(List<RuleGroupListBO> boList) {
        return Optional.ofNullable(boList).orElse(Lists.newArrayList()).stream()
                .map(RuleGroupOutputDTOConverter::convertToListVO)
                .collect(Collectors.toList());
    }

    private static RuleGroupListVO convertToListVO(RuleGroupListBO bo) {
        RuleGroupListVO vo = new RuleGroupListVO();
       vo.setGroupId(bo.getGroupId());
       vo.setGroupName(bo.getGroupName());
       vo.setGroupDefaultFlag(bo.getGroupDefaultFlag());
       vo.setCount(bo.getCount());
        return vo;
    }

}
