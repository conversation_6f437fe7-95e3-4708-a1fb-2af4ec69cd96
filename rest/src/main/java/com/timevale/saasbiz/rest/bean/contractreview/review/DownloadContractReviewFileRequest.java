package com.timevale.saasbiz.rest.bean.contractreview.review;

import com.timevale.filesystem.common.service.enums.ContentDispositionEnum;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.EnumCheck;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DownloadContractReviewFileRequest extends ToString {
    @NotBlank(message = "审查记录id不能为空")
    private String recordId;
    /** 是否下载源文件 */
    private boolean downloadOriginFile;
    /** 下载内容配置 */
    @EnumCheck(target = ContentDispositionEnum.class, enumField = "value")
    private String contentDisposition;
}
