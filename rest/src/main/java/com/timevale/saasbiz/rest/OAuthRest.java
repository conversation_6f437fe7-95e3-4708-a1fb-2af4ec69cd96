package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saasbiz.model.bean.oauth.dto.input.CancelAuthInputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasPrivilegeInputDTO;
import com.timevale.saasbiz.model.bean.oauth.dto.input.QueryScopeAuthLogsInputDTO;
import com.timevale.saasbiz.model.bean.oauth.dto.input.QueryScopeAuthMappingsInputDTO;
import com.timevale.saasbiz.model.bean.oauth.dto.output.QueryScopeAuthLogsOutputDTO;
import com.timevale.saasbiz.model.bean.oauth.dto.output.QueryScopeAuthMappingsOutputDTO;
import com.timevale.saasbiz.model.enums.PrivilegeOperationEnum;
import com.timevale.saasbiz.model.enums.PrivilegeResourceEnum;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.oauth.request.CancelAuthRequest;
import com.timevale.saasbiz.rest.bean.oauth.request.QueryScopeAuthLogsRequest;
import com.timevale.saasbiz.rest.bean.oauth.request.QueryScopeAuthMappingsRequest;
import com.timevale.saasbiz.rest.bean.oauth.response.QueryScopeAuthLogsResponse;
import com.timevale.saasbiz.rest.bean.oauth.response.QueryScopeAuthMappingsResponse;
import com.timevale.saasbiz.rest.converter.OAuthResponseConverter;
import com.timevale.saasbiz.service.oauth.OAuthService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.constraints.NotBlank;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * 授权相关接口
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Api(tags = "授权管理")
@Validated
@ExternalService
@RestMapping(path = "/v3/oauth")
public class OAuthRest {

    @Autowired OAuthService oAuthService;
    @Autowired UserCenterService userCenterService;

    @ApiOperation("分页查询应用Scope授权关系列表")
    @RestMapping(path = "/mappings/list", method = RequestMethod.GET)
    public RestResult<QueryScopeAuthMappingsResponse> queryScopeAuthMappings(
            @RequestHeader(value = HEADER_OPERATOR_ID, required = false) @NotBlank(message = "用户oid不能为空") String operatorOid,
            @RequestHeader(value = HEADER_TENANT_ID, required = false) @NotBlank(message = "主体oid不能为空") String authOid,
            @ModelAttribute QueryScopeAuthMappingsRequest request) {
        // 组装校验用户权限参数
        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        operatorOid,
                        authOid,
                        PrivilegeResourceEnum.APP_AUTH_RECORD.getType(),
                        PrivilegeOperationEnum.QUERY.getType());
        // 校验用户是否有授权关系查询权限， 无权限则报错
        userCenterService.checkHasPrivilege(privilegeInput, true);
        // 查询授权关系列表
        QueryScopeAuthMappingsInputDTO inputDTO = new QueryScopeAuthMappingsInputDTO();
        inputDTO.setAuthOid(authOid);
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        QueryScopeAuthMappingsOutputDTO authListDTO = oAuthService.queryScopeAuthMappings(inputDTO);
        return RestResult.success(
                OAuthResponseConverter.convertScopeAuthMappingsResponse(authListDTO));
    }

    @ApiOperation("分页查询应用Scope授权日志列表")
    @RestMapping(path = "/mappings/log-list", method = RequestMethod.GET)
    public RestResult<QueryScopeAuthLogsResponse> queryScopeAuthLogs(
            @RequestHeader(value = HEADER_OPERATOR_ID, required = false) @NotBlank(message = "用户oid不能为空") String operatorOid,
            @RequestHeader(value = HEADER_TENANT_ID, required = false) @NotBlank(message = "主体oid不能为空") String authOid,
            @ModelAttribute QueryScopeAuthLogsRequest request) {
        // 组装校验用户权限参数
        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        operatorOid,
                        authOid,
                        PrivilegeResourceEnum.APP_AUTH_RECORD.getType(),
                        PrivilegeOperationEnum.QUERY_LOG.getType());
        // 校验用户是否有授权关系查询权限， 无权限则报错
        userCenterService.checkHasPrivilege(privilegeInput, true);
        // 查询授权日志列表
        QueryScopeAuthLogsInputDTO inputDTO = new QueryScopeAuthLogsInputDTO();
        inputDTO.setAuthOid(authOid);
        inputDTO.setAppId(request.getAppId());
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        QueryScopeAuthLogsOutputDTO authLogsDTO = oAuthService.queryScopeAuthLogs(inputDTO);
        return RestResult.success(OAuthResponseConverter.convertScopeAuthLogsResponse(authLogsDTO));
    }

    @ApiOperation("取消应用Scope授权")
    @RestMapping(path = "/cancel-auth", method = RequestMethod.POST)
    public RestResult<Boolean> cancelScopeAuth(
            @RequestHeader(value = HEADER_OPERATOR_ID, required = false) @NotBlank(message = "用户oid不能为空") String operatorOid,
            @RequestHeader(value = HEADER_TENANT_ID, required = false) @NotBlank(message = "主体oid不能为空") String authOid,
            @RequestBody CancelAuthRequest request) {
        // 组装校验用户权限参数
        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        operatorOid,
                        authOid,
                        PrivilegeResourceEnum.APP_AUTH_RECORD.getType(),
                        PrivilegeOperationEnum.UPDATE.getType());
        // 校验用户是否有授权关系取消权限， 无权限则报错
        userCenterService.checkHasPrivilege(privilegeInput, true);
        // 取消授权
        CancelAuthInputDTO cancelAuthInputDTO = new CancelAuthInputDTO();
        cancelAuthInputDTO.setAuthOid(authOid);
        cancelAuthInputDTO.setOperatorOid(operatorOid);
        cancelAuthInputDTO.setAppId(request.getAppId());
        return RestResult.success(oAuthService.cancelScopeAuth(cancelAuthInputDTO));
    }
}
