package com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageRuleInventoryRequest extends ToString {

    @ApiModelProperty("审查清单名称")
    private String inventoryName;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @NotNull(message = "每页数量不能为空")
    @ApiModelProperty("每页数量")
    private Integer pageSize = 10;
}