package com.timevale.saasbiz.rest.bean.contractreview.rule.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisRuleResponse extends ToString {

    @ApiModelProperty("规则ID")
    private String ruleId;
}