package com.timevale.saasbiz.rest;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.contractaudit.bo.ContractAuditResultCountBO;
import com.timevale.saasbiz.model.bean.contractaudit.bo.ContractAuditResultRiskBO;
import com.timevale.saasbiz.model.bean.contractaudit.bo.ContractAuditResultRuleBO;
import com.timevale.saasbiz.model.bean.contractaudit.bo.ContractAuditRuleListBO;
import com.timevale.saasbiz.model.bean.contractaudit.bo.ContractAuditRuleTreeBO;
import com.timevale.saasbiz.model.bean.contractaudit.bo.ContractAuditTrailRecordBO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.input.ContractAuditPageQueryInputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.input.CreateContractAuditResultInputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.input.CreateContractAuditWebInputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.input.PdfTextSearchInputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.input.QueryAuditResultRiskResultInputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.input.QueryAuditResultRuleTreeInputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.output.ContractAuditPageQueryOutputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.output.CreateContractAuditWebOutputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.output.QueryAuditResultRiskResultOutputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.output.QueryAuditRuleListsOutputDTO;
import com.timevale.saasbiz.model.bean.contractaudit.dto.output.QueryEmbedAuditRecordExistOutputDTO;
import com.timevale.saasbiz.model.bean.pdftool.bo.SearchResultBO;
import com.timevale.saasbiz.model.enums.contractaudit.ContractAuditCheckSceneEnum;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.contractaudit.request.ContractAuditKeywordSearchRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.ContractAuditPageQueryRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.CreateAuditResultRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.CreateContractAuditWebRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.GetContractAuditResultRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.GetContractAuditRuleTreeRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.QueryAuditRecordExistRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.response.ContractAuditCanCreateResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.ContractAuditKeywordSearchResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.ContractAuditPageQueryResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.ContractAuditTrialResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.CreateAuditResultResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.CreateContractAuditWebResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.GetContractAuditResultResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.GetContractAuditRuleTreeResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.GetWebUrlResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.QueryAuditRecordExistResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.QueryRuleListResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditRecordVO;
import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditResultCountVO;
import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditResultRiskVO;
import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditResultRuleVO;
import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditRuleCategoryVO;
import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditRuleListVO;
import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditRuleTreeVO;
import com.timevale.saasbiz.rest.converter.PdfSearchResultConverter;
import com.timevale.saasbiz.service.contractaudit.ContractAuditService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-08-21
 */
@Api(tags = "合同审查")
@Validated
@ExternalService
@RestMapping(path = "v1/contract-audit")
public class ContractAuditRest {

    @Autowired private ContractAuditService contractAuditService;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "创建合同审查-web", httpMethod = "POST")
    @RestMapping(path = "/create-record", method = RequestMethod.POST)
    public RestResult<CreateContractAuditWebResponse> createContractAuditWeb(
            @RequestBody CreateContractAuditWebRequest request,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        CreateContractAuditWebInputDTO inputDTO = new CreateContractAuditWebInputDTO();
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setTenantOid(tenantId);
        inputDTO.setFileId(request.getFileId());
        CreateContractAuditWebOutputDTO outputDTO =
                contractAuditService.createContractAuditWeb(inputDTO);
        CreateContractAuditWebResponse response = new CreateContractAuditWebResponse();
        response.setRecordId(outputDTO.getRecordId());
        response.setUrl(outputDTO.getWebUrl());
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取审查详情url", httpMethod = "GET")
    @RestMapping(path = "/get-detail-url", method = RequestMethod.GET)
    public RestResult<GetWebUrlResponse> getWebUrl(
            @RequestParam String recordId,
            @RequestParam(required = false) Boolean isTrial,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        String webUrl = contractAuditService.getWebUrl(recordId, operatorId, tenantId, BooleanUtils.isTrue(isTrial));
        GetWebUrlResponse response = new GetWebUrlResponse();
        response.setUrl(webUrl);
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "合同审查记录列表", httpMethod = "POST")
    @RestMapping(path = "/record-list", method = RequestMethod.POST)
    public RestResult<ContractAuditPageQueryResponse> pageQuery(
            @RequestBody ContractAuditPageQueryRequest request,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        ContractAuditPageQueryInputDTO inputDTO = new ContractAuditPageQueryInputDTO();
        inputDTO.setTenantOid(tenantId);
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        inputDTO.setJobStage(request.getJobStage());
        inputDTO.setJobStatus(request.getJobStatus());
        inputDTO.setFileName(request.getFileName());
        ContractAuditPageQueryOutputDTO outputDTO = contractAuditService.pageQuery(inputDTO);
        ContractAuditPageQueryResponse response = new ContractAuditPageQueryResponse();
        response.setTotal(outputDTO.getTotal());
        response.setRecords(
                outputDTO.getRecords().stream()
                        .map(this::convertToVO)
                        .collect(Collectors.toList()));
        return RestResult.success(response);
    }

    @ApiOperation(value = "删除合同审查记录", httpMethod = "POST")
    @RestMapping(path = "/delete-record", method = RequestMethod.POST)
    public RestResult<Void> deleteRecord(
            @RequestParam String recordId,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        contractAuditService.deleteRecord(recordId, operatorId, tenantId);
        return RestResult.success();
    }

    @ApiOperation(value = "合同审查试用文件列表", httpMethod = "GET")
    @RestMapping(path = "/trial-files", method = RequestMethod.GET)
    public RestResult<ContractAuditTrialResponse> trial() {
        List<ContractAuditTrailRecordBO> trailRecordBOS =
                contractAuditService.queryContractAuditTrailFiles();
        ContractAuditTrialResponse response = new ContractAuditTrialResponse();
        response.setFiles(
                trailRecordBOS.stream().map(this::convertToFileInfo).collect(Collectors.toList()));
        return RestResult.success(response);
    }

    @ApiOperation(value = "查询合同审查清单列表", httpMethod = "GET")
    @RestMapping(path = "/rule-list", method = RequestMethod.GET)
    public RestResult<QueryRuleListResponse> queryRuleLists(
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        QueryAuditRuleListsOutputDTO outputDTO =
                contractAuditService.queryAuditLists(operatorId, tenantId);
        QueryRuleListResponse response = new QueryRuleListResponse();
        ContractAuditRuleCategoryVO my = new ContractAuditRuleCategoryVO();
        my.setName("我创建的");
        my.setRuleLists(convertRuleLists(outputDTO.getMyRuleList()));
        ContractAuditRuleCategoryVO others = new ContractAuditRuleCategoryVO();
        others.setName("其他人创建的");
        others.setRuleLists(convertRuleLists(outputDTO.getOtherRuleList()));
        response.setCategory(Lists.newArrayList(my, others));
        return RestResult.success(response);
    }

    @ApiOperation(value = "查询合同文件是否存在审查记录", httpMethod = "POST")
    @RestMapping(path = "/record-exists", method = RequestMethod.POST)
    public RestResult<QueryAuditRecordExistResponse> queryAuditRecordExist(
            @RequestBody QueryAuditRecordExistRequest request,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        QueryEmbedAuditRecordExistOutputDTO outputDTO =
                contractAuditService.auditRecordExist(
                        request.getProcessId(), request.getFileId(), operatorId, tenantId);
        QueryAuditRecordExistResponse response = new QueryAuditRecordExistResponse();
        response.setExists(outputDTO.isExists());
        response.setRecordId(outputDTO.getRecordId());
        return RestResult.success(response);
    }

    @ApiOperation(value = "查询合同审查是否可以创建", httpMethod = "GET")
    @RestMapping(path = "/can-create", method = RequestMethod.GET)
    public RestResult<ContractAuditCanCreateResponse> canCreate(
            @RequestHeader(HEADER_TENANT_ID) String tenantId, @RequestParam String processId) {
        ContractAuditCheckSceneEnum checkCanCreate =
                contractAuditService.checkCanCreate(processId, tenantId);
        ContractAuditCanCreateResponse response = new ContractAuditCanCreateResponse();
        response.setScene(checkCanCreate.getCode());
        return RestResult.success(response);
    }

    @ApiOperation(value = "创建审查结果", httpMethod = "POST")
    @RestMapping(path = "/create-job", method = RequestMethod.POST)
    public RestResult<CreateAuditResultResponse> createAuditResult(
            @RequestBody CreateAuditResultRequest request,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        CreateContractAuditResultInputDTO inputDTO = new CreateContractAuditResultInputDTO();
        inputDTO.setFileId(request.getFileId());
        inputDTO.setPosition(request.getPosition());
        inputDTO.setRuleListId(request.getRuleListId());
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setTenantOid(tenantId);
        String recordId = contractAuditService.createAuditResult(inputDTO);
        CreateAuditResultResponse response = new CreateAuditResultResponse();
        response.setRecordId(recordId);
        return RestResult.success(response);
    }

    @ApiOperation(value = "读取规则树", httpMethod = "POST")
    @RestMapping(path = "/get-rule-tree", method = RequestMethod.POST)
    public RestResult<GetContractAuditRuleTreeResponse> getContractAuditRuleTree(
            @RequestBody GetContractAuditRuleTreeRequest request,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        QueryAuditResultRuleTreeInputDTO serviceInput = new QueryAuditResultRuleTreeInputDTO();
        serviceInput.setRecordId(request.getRecordId());
        serviceInput.setProcessId(request.getProcessId());
        serviceInput.setFileId(request.getFileId());
        serviceInput.setOperatorOid(operatorId);
        serviceInput.setTenantOid(tenantId);

        List<ContractAuditRuleTreeBO> resultRuleTree =
                contractAuditService.queryAuditResultRuleTree(serviceInput);
        GetContractAuditRuleTreeResponse response = new GetContractAuditRuleTreeResponse();
        response.setTree(contractRuleTreeVOs(resultRuleTree));
        return RestResult.success(response);
    }

    @ApiOperation(value = "查询审查结果", httpMethod = "POST")
    @RestMapping(path = "/query-job-result", method = RequestMethod.POST)
    public RestResult<GetContractAuditResultResponse> getContractAuditResult(
            @RequestBody GetContractAuditResultRequest request,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        QueryAuditResultRiskResultInputDTO inputDTO = new QueryAuditResultRiskResultInputDTO();
        inputDTO.setRecordId(request.getRecordId());
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setFileId(request.getFileId());
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setTenantOid(tenantId);
        inputDTO.setSeqList(request.getSeqList());
        QueryAuditResultRiskResultOutputDTO outputDTO =
                contractAuditService.queryAuditResultRiskResult(inputDTO);
        GetContractAuditResultResponse response = new GetContractAuditResultResponse();
        response.setStatus(outputDTO.getStatus());
        response.setCount(convertToCountVO(outputDTO.getCount()));
        response.setResult(convertRuleBOs(outputDTO.getResult()));
        return RestResult.success(response);
    }

    @ApiOperation(value = "搜索pdf关键字位置", httpMethod = "POST")
    @RestMapping(path = "/keyword-search", method = RequestMethod.POST)
    public RestResult<ContractAuditKeywordSearchResponse> keywordSearch(
            @RequestBody ContractAuditKeywordSearchRequest request,
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId) {
        PdfTextSearchInputDTO inputDTO = new PdfTextSearchInputDTO();
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setFileId(request.getFileId());
        inputDTO.setKeywords(request.getKeywords());
        inputDTO.setAcceptPosFormat(request.getAcceptPosFormat());
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setTenantOid(tenantId);
        List<SearchResultBO> searchResultBOS = contractAuditService.pdfTextSearch(inputDTO);
        ContractAuditKeywordSearchResponse response = new ContractAuditKeywordSearchResponse();
        response.setSearchResults(PdfSearchResultConverter.convert(searchResultBOS));
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取审查清单配置页面", httpMethod = "GET")
    @RestMapping(path = "/ruleListUrl", method = RequestMethod.GET)
    public RestResult<String> getRuleListViewUrl(
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId
    ) {
        String faruiRuleListViewUrl = contractAuditService.getRuleListViewUrl(operatorId, tenantId);
        return RestResult.success(faruiRuleListViewUrl);
    }

    private List<ContractAuditResultRuleVO> convertRuleBOs(
            List<ContractAuditResultRuleBO> ruleBOs) {
        if (CollectionUtils.isEmpty(ruleBOs)) {
            return Lists.newArrayList();
        }
        return ruleBOs.stream().map(this::convertResultRuleVO).collect(Collectors.toList());
    }

    private ContractAuditResultRuleVO convertResultRuleVO(ContractAuditResultRuleBO ruleBO) {
        if (ruleBO == null) {
            return null;
        }
        ContractAuditResultRuleVO contractAuditResultRuleVO = new ContractAuditResultRuleVO();
        contractAuditResultRuleVO.setRuleSeq(ruleBO.getRuleSeq());
        contractAuditResultRuleVO.setRuleTitle(ruleBO.getRuleTitle());
        contractAuditResultRuleVO.setRuleLevel(ruleBO.getRiskLevel());
        contractAuditResultRuleVO.setStatus(ruleBO.getStatus());
        contractAuditResultRuleVO.setPass(ruleBO.getPass());
        contractAuditResultRuleVO.setSubRisk(convertRiskBOs(ruleBO.getSubRisk()));
        return contractAuditResultRuleVO;
    }

    private List<ContractAuditResultRiskVO> convertRiskBOs(
            List<ContractAuditResultRiskBO> riskBOs) {
        if (CollectionUtils.isEmpty(riskBOs)) {
            return Lists.newArrayList();
        }
        return riskBOs.stream().map(this::convertResultRiskVO).collect(Collectors.toList());
    }

    private ContractAuditResultRiskVO convertResultRiskVO(ContractAuditResultRiskBO riskBO) {
        if (riskBO == null) {
            return null;
        }
        ContractAuditResultRiskVO contractAuditResultRiskVO = new ContractAuditResultRiskVO();
        contractAuditResultRiskVO.setRiskBrief(riskBO.getRiskBrief());
        contractAuditResultRiskVO.setRiskExplain(riskBO.getRiskExplain());
        contractAuditResultRiskVO.setResultType(riskBO.getResultType());
        contractAuditResultRiskVO.setOriginalContent(riskBO.getOriginalContent());
        contractAuditResultRiskVO.setResultContent(riskBO.getResultContent());
        return contractAuditResultRiskVO;
    }

    private ContractAuditResultCountVO convertToCountVO(ContractAuditResultCountBO countBO) {
        if (countBO == null) {
            return null;
        }
        ContractAuditResultCountVO contractAuditResultCountVO = new ContractAuditResultCountVO();
        contractAuditResultCountVO.setPass(countBO.getPass());
        contractAuditResultCountVO.setHigh(countBO.getHigh());
        contractAuditResultCountVO.setMedium(countBO.getMedium());
        contractAuditResultCountVO.setLow(countBO.getLow());
        contractAuditResultCountVO.setTotal(countBO.getTotal());
        return contractAuditResultCountVO;
    }

    private List<ContractAuditRuleTreeVO> contractRuleTreeVOs(
            List<ContractAuditRuleTreeBO> ruleTrees) {
        if (CollectionUtils.isEmpty(ruleTrees)) {
            return Lists.newArrayList();
        }
        return ruleTrees.stream().map(this::convertRuleTreeVO).collect(Collectors.toList());
    }

    private ContractAuditRuleTreeVO convertRuleTreeVO(ContractAuditRuleTreeBO ruleTree) {
        if (ruleTree == null) {
            return null;
        }
        ContractAuditRuleTreeVO contractAuditRuleTreeVO = new ContractAuditRuleTreeVO();
        contractAuditRuleTreeVO.setRuleSeq(ruleTree.getRuleSeq());
        contractAuditRuleTreeVO.setRuleTitle(ruleTree.getRuleTitle());
        contractAuditRuleTreeVO.setSubRules(contractRuleTreeVOs(ruleTree.getSubRules()));
        return contractAuditRuleTreeVO;
    }

    private List<ContractAuditRuleListVO> convertRuleLists(
            List<ContractAuditRuleListBO> ruleLists) {
        if (ruleLists == null) {
            return null;
        }
        return ruleLists.stream().map(this::convertRuleListVO).collect(Collectors.toList());
    }

    private ContractAuditRuleListVO convertRuleListVO(ContractAuditRuleListBO ruleList) {
        if (ruleList == null) {
            return null;
        }
        ContractAuditRuleListVO ruleListVO = new ContractAuditRuleListVO();
        ruleListVO.setRuleListId(ruleList.getRuleListId());
        ruleListVO.setRuleListName(ruleList.getRuleListName());
        return ruleListVO;
    }

    private ContractAuditTrialResponse.FileInfo convertToFileInfo(
            ContractAuditTrailRecordBO trailFile) {
        ContractAuditTrialResponse.FileInfo fileInfo = new ContractAuditTrialResponse.FileInfo();
        fileInfo.setId(trailFile.getId());
        fileInfo.setFileName(trailFile.getFileName());
        return fileInfo;
    }

    private ContractAuditRecordVO convertToVO(ContractAuditPageQueryOutputDTO.Record record) {
        ContractAuditRecordVO contractAuditRecordVO = new ContractAuditRecordVO();
        contractAuditRecordVO.setRecordId(record.getRecordId());
        contractAuditRecordVO.setFileName(record.getFileName());
        contractAuditRecordVO.setJobStage(record.getJobStage());
        contractAuditRecordVO.setJobStatus(record.getJobStatus());
        contractAuditRecordVO.setFailMessage(record.getFailMessage());
        contractAuditRecordVO.setCreateTime(record.getCreateTime() == null ? null : record.getCreateTime().getTime());
        return contractAuditRecordVO;
    }
}
