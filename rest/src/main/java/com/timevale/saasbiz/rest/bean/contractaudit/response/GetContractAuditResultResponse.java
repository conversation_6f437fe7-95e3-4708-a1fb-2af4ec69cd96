package com.timevale.saasbiz.rest.bean.contractaudit.response;

import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditResultCountVO;
import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditResultRuleVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Data
public class GetContractAuditResultResponse {
    @ApiModelProperty(value = "任务状态 running 审查中 done 审查完成 error 审查失败")
    private String status;
    @ApiModelProperty(value = "统计信息")
    private ContractAuditResultCountVO count;
    @ApiModelProperty(value = "审查结果")
    private List<ContractAuditResultRuleVO> result;
}
