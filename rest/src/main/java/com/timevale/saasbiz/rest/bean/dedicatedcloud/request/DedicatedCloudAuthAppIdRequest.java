package com.timevale.saasbiz.rest.bean.dedicatedcloud.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/2/1 10:32
 */
@Data
public class DedicatedCloudAuthAppIdRequest extends ToString {

    @NotBlank(message = "appId 不能为空")
    private  String appId;

    @NotBlank(message = "subjectGid 不能为空")
    private String subjectGid;

    private String subjectOid;
}
