package com.timevale.saasbiz.rest.bean.contractaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
public class GetContractAuditRuleTreeRequest {
    @ApiModelProperty(value = "合同id")
    private String processId;
    @ApiModelProperty(value = "文件id")
    private String fileId;
    @ApiModelProperty(value = "审查记录id")
    private String recordId;
}
