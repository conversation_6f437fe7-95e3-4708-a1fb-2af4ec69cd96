package com.timevale.saasbiz.rest.bean.offlinecontract.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractRecordListVO;
import lombok.Data;

import java.util.List;

/**
 * 查询线下合同导入记录列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class PageQueryOfflineContractRecordsResponse extends ToString {

    /** 总数 */
    private long total;

    /** 线下合同导入记录列表 */
    private List<OfflineContractRecordListVO> records;
}
