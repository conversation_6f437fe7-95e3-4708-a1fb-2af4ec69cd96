package com.timevale.saasbiz.rest;

import com.timevale.contractanalysis.facade.api.enums.ContractCategoryStatusEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.contractcategory.bo.ExtractFieldBO;
import com.timevale.saasbiz.model.bean.contractcategory.bo.RelateFlowTemplateFileBO;
import com.timevale.saasbiz.model.bean.contractcategory.dto.input.*;
import com.timevale.saasbiz.model.bean.contractcategory.dto.output.*;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.utils.IdsUtil;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.contractcategory.request.*;
import com.timevale.saasbiz.rest.bean.contractcategory.response.*;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.BaseContractCategoryListVO;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.ContractCategoryListVO;
import com.timevale.saasbiz.service.contractcategory.ContractCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.rest.converter.ContractCategoryResponseConverter.*;

/**
 * 合同类型REST接口层
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Api(tags = "合同类型接口")
@Validated
@ExternalService
@RestMapping(path = "/v2/contract-categories")
public class ContractCategoryRest {

    @Autowired ContractCategoryService contractCategoryService;

    @ApiOperation("获取系统推荐的合同类型列表")
    @RestMapping(path = "/system-list", method = RequestMethod.GET)
    public RestResult<QuerySysContractCategoriesResponse> querySysCategories() {
        // 查询系统推荐的合同类型列表
        QuerySysContractCategoriesOutputDTO outputDTO = contractCategoryService.querySysCategories();

        // 组装返回值
        QuerySysContractCategoriesResponse response = new QuerySysContractCategoriesResponse();
        if (CollectionUtils.isNotEmpty(outputDTO.getCategories())) {
            response.setCategories(
                    outputDTO.getCategories().stream()
                            .map(i -> convert2SceneContractCategoryVO(i))
                            .collect(Collectors.toList()));
        }
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.ADD_CONTRACT_CATEGORY)
    @ApiOperation("保存合同类型")
    @RestMapping(path = "/save", method = RequestMethod.POST)
    public RestResult<SaveContractCategoryResponse> saveCategory(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody SaveContractCategoryRequest request) {
        // 组装参数
        SaveContractCategoryInputDTO inputDTO =
                buildSaveContractCategoryInputDTO(accountId, subjectId, request);
        // 保存合同类型
        String categoryId = contractCategoryService.saveCategory(inputDTO);
        // 组装返回数据
        SaveContractCategoryResponse response = new SaveContractCategoryResponse();
        response.setCategoryId(categoryId);
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE_CONTRACT_CATEGORY)
    @ApiOperation("编辑合同类型")
    @RestMapping(path = "/update", method = RequestMethod.POST)
    public RestResult<SaveContractCategoryResponse> updateCategory(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody UpdateContractCategoryRequest request) {
        // 组装参数
        SaveContractCategoryInputDTO inputDTO =
                buildSaveContractCategoryInputDTO(accountId, subjectId, request);
        inputDTO.setCategoryId(request.getCategoryId());
        // 保存合同类型
        String categoryId = contractCategoryService.saveCategory(inputDTO);
        // 组装返回数据
        SaveContractCategoryResponse response = new SaveContractCategoryResponse();
        response.setCategoryId(categoryId);
        return RestResult.success(response);
    }

    /**
     * 保存合同类型
     * @param accountId
     * @param subjectId
     * @param request
     * @return
     */
    private SaveContractCategoryInputDTO buildSaveContractCategoryInputDTO(
            String accountId, String subjectId, SaveContractCategoryRequest request) {
        SaveContractCategoryInputDTO inputDTO = new SaveContractCategoryInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        inputDTO.setCategoryName(request.getCategoryName());
        inputDTO.setRefSysCategoryId(request.getRefSysCategoryId());
        if (CollectionUtils.isNotEmpty(request.getExtractFields())) {
            inputDTO.setExtractFields(request.getExtractFields().stream().map(i -> {
                ExtractFieldBO extractFieldBO = new ExtractFieldBO();
                extractFieldBO.setFieldName(i.getFieldName());
                extractFieldBO.setFieldDesc(i.getFieldDesc());
                extractFieldBO.setFieldType(i.getFieldType());
                return extractFieldBO;
            }).collect(Collectors.toList()));
        }
        return inputDTO;
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_CONTRACT_CATEGORY)
    @ApiOperation("查询合同类型列表")
    @RestMapping(path = "/list", method = RequestMethod.GET)
    public RestResult<PageQueryContractCategoriesResponse> pageQueryCategories(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @URIQueryParam @ModelAttribute PageQueryContractCategoriesRequest request) {

        PageQueryContractCategoriesInputDTO inputDTO = new PageQueryContractCategoriesInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        // 合同类型状态
        if (StringUtils.isNotBlank(request.getCategoryStatus())) {
            inputDTO.setStatusList(IdsUtil.getIdList(request.getCategoryStatus()));
        }
        inputDTO.setCategoryId(request.getCategoryId());
        inputDTO.setCategoryName(request.getCategoryName());
        inputDTO.setFieldId(request.getFieldId());
        inputDTO.setFlowTemplateId(request.getFlowTemplateId());
        inputDTO.setFlowTemplateName(request.getFlowTemplateName());
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        // 查询合同类型列表
        PageQueryContractCategoriesOutputDTO outputDTO = contractCategoryService.pageQueryCategories(inputDTO);

        // 组装返回数据
        PageQueryContractCategoriesResponse response = new PageQueryContractCategoriesResponse();
        response.setTotal(outputDTO.getTotal());
        if (CollectionUtils.isNotEmpty(outputDTO.getCategories())) {
            response.setCategories(outputDTO.getCategories().stream().map(i -> {
                ContractCategoryListVO categoryListVO = new ContractCategoryListVO();
                categoryListVO.setCategoryId(i.getCategoryId());
                categoryListVO.setCategoryName(i.getCategoryName());
                categoryListVO.setExtractFields(i.getExtractFields());
                categoryListVO.setFlowTemplates(i.getFlowTemplates());
                categoryListVO.setContractSize(i.getContractSize());
                categoryListVO.setEnable(ContractCategoryStatusEnum.ENABLE.getStatus().equals(i.getStatus()));
                return categoryListVO;
            }).collect(Collectors.toList()));
        }
        return RestResult.success(response);
    }


    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询可用的合同类型列表")
    @RestMapping(path = "/usable-list", method = RequestMethod.GET)
    public RestResult<QueryUsableContractCategoriesResponse> queryUsableCategories(
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestParam(value = "categoryName", required = false) String categoryName) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        QueryUsableContractCategoriesOutputDTO outputDTO =
                contractCategoryService.queryUsableCategories(resourceTenantId, categoryName);

        QueryUsableContractCategoriesResponse response = new QueryUsableContractCategoriesResponse();
        response.setCategories(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(outputDTO.getCategories())) {
            response.setCategories(outputDTO.getCategories().stream().map(i -> {
                BaseContractCategoryListVO contractCategoryVO = new BaseContractCategoryListVO();
                contractCategoryVO.setCategoryId(i.getCategoryId());
                contractCategoryVO.setCategoryName(i.getCategoryName());
                contractCategoryVO.setExtractFields(i.getExtractFields());
                return contractCategoryVO;
            }).collect(Collectors.toList()));
        }
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询合同类型详情")
    @RestMapping(path = "/detail", method = RequestMethod.GET)
    public RestResult<QueryContractCategoriesDetailResponse> queryCategoryDetail(
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestParam("categoryId") String categoryId) {

        // 查询合同类型详情
        QueryContractCategoryDetailOutputDTO outputDTO =
                contractCategoryService.queryCategoryDetail(subjectId, categoryId);
        // 组装返回数据
        QueryContractCategoriesDetailResponse response =
                new QueryContractCategoriesDetailResponse();
        response.setCategoryId(outputDTO.getCategoryId());
        response.setCategoryName(outputDTO.getCategoryName());
        response.setExtractFields(convert2ExtractFieldVOs(outputDTO.getExtractFields()));
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("批量查询合同类型详情")
    @RestMapping(path = "/batch-detail", method = RequestMethod.POST)
    public RestResult<BatchQueryContractCategoriesDetailResponse> batchQueryCategoryDetail(
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody BatchQueryContractCategoryDetailRequest request) {

        // 查询合同类型详情
        BatchQueryContractCategoryDetailOutputDTO outputDTO =
                contractCategoryService.batchQueryCategoryDetail(
                        subjectId, request.getCategoryIds(), request.isWithFields());
        // 组装返回数据
        BatchQueryContractCategoriesDetailResponse response =
                new BatchQueryContractCategoriesDetailResponse();
        response.setCategories(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(outputDTO.getCategories())) {
            response.getCategories().addAll(
                outputDTO.getCategories().stream()
                        .map(i -> convert2ContractCategoryVO(i))
                        .collect(Collectors.toList()));
        }
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("校验合同类型名称是否已存在")
    @RestMapping(path = "/check-name-existed", method = RequestMethod.GET)
    public RestResult<Boolean> checkNameExisted(
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestParam("categoryName") String categoryName) {
        // 校验合同类型名称是否已存在
        boolean existed = contractCategoryService.checkCategoryNameExisted(subjectId, categoryName);
        // 返回校验结果
        return RestResult.success(existed);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE_CONTRACT_CATEGORY)
    @ApiOperation("更新合同类型状态")
    @RestMapping(path = "/update-status", method = RequestMethod.POST)
    public RestResult updateStatus(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody UpdateContractCategoryStatusRequest request) {

        UpdateContractCategoryStatusInputDTO inputDTO = new UpdateContractCategoryStatusInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        inputDTO.setEnable(request.getEnable());
        inputDTO.setCategoryIds(request.getCategoryIds());
        contractCategoryService.batchUpdateStatus(inputDTO);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.DELETE_CONTRACT_CATEGORY)
    @ApiOperation("删除合同类型")
    @RestMapping(path = "/delete", method = RequestMethod.POST)
    public RestResult batchDelete(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody DeleteContractCategoryRequest request) {

        DeleteContractCategoryInputDTO inputDTO = new DeleteContractCategoryInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        inputDTO.setCategoryIds(request.getCategoryIds());
        contractCategoryService.batchDelete(inputDTO);

        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_TEMPLATE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @ApiOperation("关联流程模板")
    @RestMapping(path = "/relate-flow-templates", method = RequestMethod.POST)
    public RestResult relateFlowTemplates(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody RelateContractCategoryFlowTemplatesRequest request) {
        RelateContractCategoryFlowTemplatesInputDTO inputDTO = new RelateContractCategoryFlowTemplatesInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        inputDTO.setCategoryId(request.getCategoryId());
        if (CollectionUtils.isNotEmpty(request.getRelateFlowTemplates())) {
            inputDTO.setRelateFlowTemplates(request.getRelateFlowTemplates().stream().map(i -> {
                RelateFlowTemplateFileBO fileBO = new RelateFlowTemplateFileBO();
                fileBO.setFlowTemplateId(i.getFlowTemplateId());
                fileBO.setFlowTemplateName(i.getFlowTemplateName());
                fileBO.setRelateFileIds(i.getRelateFileIds());
                return fileBO;
            }).collect(Collectors.toList()));
        }
        contractCategoryService.relateFlowTemplates(inputDTO);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("基于合同类型id查询已关联的流程模板列表")
    @RestMapping(path = "/query-related-flow-templates", method = RequestMethod.GET)
    public RestResult<QueryRelatedFlowTemplatesResponse> queryRelatedFlowTemplates(
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestParam("categoryId") String categoryId) {

        QueryRelatedFlowTemplatesOutputDTO outputDTO =
                contractCategoryService.queryRelatedFlowTemplates(subjectId, categoryId);
        QueryRelatedFlowTemplatesResponse response = new QueryRelatedFlowTemplatesResponse();
        response.setRelateFlowTemplates(
                convert2RelateFlowTemplateFileVOs(outputDTO.getFlowTemplateFiles()));

        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("基于流程模板查询已关联的合同类型列表")
    @RestMapping(path = "/query-related-categories", method = RequestMethod.GET)
    public RestResult<QueryRelatedCategoriesResponse> queryRelateCategories(
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestParam("flowTemplateId") String flowTemplateId) {

        QueryRelatedCategoriesOutputDTO outputDTO =
                contractCategoryService.queryRelatedFileCategories(subjectId, flowTemplateId);

        QueryRelatedCategoriesResponse response = new QueryRelatedCategoriesResponse();
        response.setCategories(convert2FileContractCategoryVOs(outputDTO.getCategoryies()));

        return RestResult.success(response);
    }
}
