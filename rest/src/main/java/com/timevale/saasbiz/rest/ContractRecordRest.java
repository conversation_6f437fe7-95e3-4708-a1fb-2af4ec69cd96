package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.contractreview.record.dto.output.ContractReviewCreateDocStatusOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.record.dto.output.ReviewRecordPageOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.review.dto.input.*;
import com.timevale.saasbiz.model.bean.contractreview.review.dto.output.ContractReviewUrlOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.review.dto.output.DownloadContractReviewFileOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.ReviewRecordIdInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.ReviewRecordPageInputDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.contractreview.record.request.ReviewRecordIdRequest;
import com.timevale.saasbiz.rest.bean.contractreview.record.request.ReviewRecordPageRequest;
import com.timevale.saasbiz.rest.bean.contractreview.record.response.PageReviewRecordResponse;
import com.timevale.saasbiz.rest.bean.contractreview.review.*;
import com.timevale.saasbiz.rest.bean.contractreview.review.response.ContractReviewCreateDocStatusResponse;
import com.timevale.saasbiz.rest.bean.contractreview.review.response.ContractReviewUrlResponse;
import com.timevale.saasbiz.rest.bean.contractreview.review.response.DownloadContractReviewFileResponse;
import com.timevale.saasbiz.rest.converter.contractreview.ReviewRecordInputDTOConverter;
import com.timevale.saasbiz.rest.converter.contractreview.ReviewRecordOutputDTOConverter;
import com.timevale.saasbiz.service.contractreview.ContractReviewRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import static com.timevale.saasbiz.model.constants.PrivilegeOperationConstants.*;
import static com.timevale.saasbiz.model.constants.PrivilegeResourceConstants.PRIVILEGE_RESOURCE_INTELLIGENT_TOOL;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

@Api(tags = "合同审查记录")
@Validated
@ExternalService
@RestMapping(path = "/v1/contract-review/record")
public class ContractRecordRest {

    @Resource
    public ContractReviewRecordService contractReviewRecordService;

    @ApiOperation(value = "审查记录-分页", notes = "无")
    @UserPrivilegeCheck(resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL, privilegeKey = USE)
    @RestMapping(path = "/page", method = RequestMethod.POST)
    public RestResult<PageReviewRecordResponse> recordPage(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid ReviewRecordPageRequest request) {
        ReviewRecordPageInputDTO inputDTO = ReviewRecordInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        ReviewRecordPageOutputDTO outputDTO = contractReviewRecordService.recordPage(inputDTO);

        PageReviewRecordResponse response = new PageReviewRecordResponse();
        response.setTotal(outputDTO.getTotal());
        response.setInventoryList(ReviewRecordOutputDTOConverter.convertToListVOS(outputDTO.getRecordList()));
        return RestResult.success(response);
    }

    @ApiOperation(value = "审查记录-删除", notes = "无")
    @UserPrivilegeCheck(resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL, privilegeKey = USE)
    @RestMapping(path = "/del", method = RequestMethod.POST)
    public RestResult recordDel(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid ReviewRecordIdRequest request) {
        ReviewRecordIdInputDTO inputDTO = ReviewRecordInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        contractReviewRecordService.recordDel(inputDTO);
        return RestResult.success();
    }

    @ApiOperation(value = "上传文件生成合同审查地址", notes = "无")
    @UserPrivilegeCheck(resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL, privilegeKey = USE)
    @RestMapping(path = "/create-url", method = RequestMethod.POST)
    public RestResult<ContractReviewUrlResponse> contractReviewUrl(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid CreateContractReviewUrlRequest request) {

        ContractReviewUrlByFileInputDTO inputDTO = new ContractReviewUrlByFileInputDTO();
        inputDTO.setFileId(request.getFileId());
        inputDTO.setCloseRedirectUrl(request.getCloseRedirectUrl());
        inputDTO.setSaveRedirectUrl(request.getSaveRedirectUrl());
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        ContractReviewUrlOutputDTO outputDTO = contractReviewRecordService.contractReviewUrlByFile(inputDTO);
        ContractReviewUrlResponse response = new ContractReviewUrlResponse();
        response.setReviewUrl(outputDTO.getReviewUrl());
        return RestResult.success(response);
    }

    @ApiOperation(value = "根据审查记录获取合同审查地址", notes = "无")
    @UserPrivilegeCheck(resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL, privilegeKey = USE)
    @RestMapping(path = "/review-url", method = RequestMethod.POST)
    public RestResult<ContractReviewUrlResponse> contractReviewRecordUrl(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid ContractReviewRecordUrlRequest request) {

        ContractReviewUrlByRecordInputDTO inputDTO = new ContractReviewUrlByRecordInputDTO();
        inputDTO.setRecordId(request.getRecordId());
        inputDTO.setCloseRedirectUrl(request.getCloseRedirectUrl());
        inputDTO.setSaveRedirectUrl(request.getSaveRedirectUrl());
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        ContractReviewUrlOutputDTO outputDTO = contractReviewRecordService.contractReviewUrlByRecord(inputDTO);
        ContractReviewUrlResponse response = new ContractReviewUrlResponse();
        response.setReviewUrl(outputDTO.getReviewUrl());
        return RestResult.success(response);
    }

    @ApiOperation(value = "根据审查记录下载文件", notes = "无")
    @UserPrivilegeCheck(resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL, privilegeKey = USE)
    @RestMapping(path = "/download", method = RequestMethod.POST)
    public RestResult<DownloadContractReviewFileResponse> downloadContractReviewFile(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid DownloadContractReviewFileRequest request) {

        DownloadContractReviewFileInputDTO inputDTO = new DownloadContractReviewFileInputDTO();
        inputDTO.setRecordId(request.getRecordId());
        inputDTO.setDownloadOriginFile(request.isDownloadOriginFile());
        inputDTO.setContentDisposition(request.getContentDisposition());
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        DownloadContractReviewFileOutputDTO outputDTO = contractReviewRecordService.downloadContractReviewFile(inputDTO);
        DownloadContractReviewFileResponse response = new DownloadContractReviewFileResponse();
        response.setDownloadUrl(outputDTO.getDownloadUrl());
        return RestResult.success(response);
    }

    @ApiOperation(value = "暂停任务", notes = "无")
    @UserPrivilegeCheck(resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL, privilegeKey = USE)
    @RestMapping(path = "/task/stop", method = RequestMethod.POST)
    public RestResult taskStop(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid TaskStopRequest request) {
        TaskStopInputDTO inputDTO = new TaskStopInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        inputDTO.setTaskId(request.getTaskId());
        inputDTO.setScene(request.getScene());
        contractReviewRecordService.taskStop(inputDTO);
        return RestResult.success();
    }


    @ApiOperation(value = "合同审查创建文档", notes = "无")
    @UserPrivilegeCheck(resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL, privilegeKey = USE)
    @RestMapping(path = "/create-doc", method = RequestMethod.POST)
    public RestResult createDoc(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid ContractReviewCreateDocRequest request) {
        ContractReviewCreateDocInputDTO inputDTO = ReviewRecordInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        contractReviewRecordService.createDoc(inputDTO);
        return RestResult.success();
    }

    @ApiOperation(value = "合同审查创建文档结果", notes = "无")
    @UserPrivilegeCheck(resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL, privilegeKey = USE)
    @RestMapping(path = "/create-doc-status", method = RequestMethod.POST)
    public RestResult<ContractReviewCreateDocStatusResponse> createDocStatus(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid ContractReviewCreateDocRequest request) {
        ContractReviewCreateDocInputDTO inputDTO = ReviewRecordInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        ContractReviewCreateDocStatusOutputDTO dto =  contractReviewRecordService.createDocStatus(inputDTO);
        ContractReviewCreateDocStatusResponse response = new ContractReviewCreateDocStatusResponse();
        response.setStatus(dto.getStatus());
        return RestResult.success(response);
    }
}
