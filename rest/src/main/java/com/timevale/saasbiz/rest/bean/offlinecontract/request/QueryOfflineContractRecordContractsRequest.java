package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 查询线下合同导入记录合同信息列表
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class QueryOfflineContractRecordContractsRequest extends ToString {

    @ApiModelProperty(value = "导入记录id", required = true)
    @NotBlank(message = "导入记录id不能为空")
    private String recordId;

    /** 是否返回提取信息 */
    @ApiModelProperty(value = "是否返回提取信息", example = "false")
    private boolean withExtract;

    @ApiModelProperty(value = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @ApiModelProperty(value = "每页数据大小", required = true, example = "10")
    @NotNull(message = "每页数据大小不能为空")
    private Integer pageSize;
}
