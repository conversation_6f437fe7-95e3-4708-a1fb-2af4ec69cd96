package com.timevale.saasbiz.rest.bean.contractcategory.request;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.RelateFlowTemplateFileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 合同类型关联流程模板请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("合同类型关联流程模板请求参数")
public class RelateContractCategoryFlowTemplatesRequest extends ToString {
    /** 合同类型id */
    @ApiModelProperty("合同类型id")
    @NotBlank(message = "合同类型id不能为空")
    private String categoryId;

    @ApiModelProperty("关联的流程模板文件列表， 如果为空表示清空关联关系")
    private List<RelateFlowTemplateFileVO> relateFlowTemplates;
}
