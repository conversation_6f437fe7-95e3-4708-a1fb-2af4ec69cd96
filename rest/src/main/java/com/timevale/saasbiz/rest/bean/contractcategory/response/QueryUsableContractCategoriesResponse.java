package com.timevale.saasbiz.rest.bean.contractcategory.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.BaseContractCategoryListVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 查询可用的合同类型列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("查询可用的合同类型列表响应数据")
public class QueryUsableContractCategoriesResponse extends ToString {

    private List<BaseContractCategoryListVO> categories;
}
