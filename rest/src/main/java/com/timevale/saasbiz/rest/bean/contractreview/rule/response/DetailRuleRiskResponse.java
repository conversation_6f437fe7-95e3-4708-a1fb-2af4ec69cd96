package com.timevale.saasbiz.rest.bean.contractreview.rule.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DetailRuleRiskResponse extends ToString {
    @ApiModelProperty("审查点ID")
    private String ruleRiskId;

    @ApiModelProperty("规则ID")
    private String ruleId;

    @ApiModelProperty("审查点描述")
    private String ruleDescription;

    @ApiModelProperty("审查点逻辑列表")
    private List<String> ruleBasis;

    @ApiModelProperty("审查建议")
    private String suggestion;

    @ApiModelProperty("审查关键词")
    private List<String> keyword;

    @ApiModelProperty("审查相关条款")
    private String similarTerms;
}
