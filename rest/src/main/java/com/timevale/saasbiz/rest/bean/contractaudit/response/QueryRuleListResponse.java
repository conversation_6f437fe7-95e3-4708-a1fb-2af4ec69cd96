package com.timevale.saasbiz.rest.bean.contractaudit.response;

import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditRuleCategoryVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
public class QueryRuleListResponse {
    @ApiModelProperty(value = "分类列表")
    private List<ContractAuditRuleCategoryVO> category;
}
