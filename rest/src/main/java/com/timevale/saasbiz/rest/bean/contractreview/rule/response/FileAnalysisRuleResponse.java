package com.timevale.saasbiz.rest.bean.contractreview.rule.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

@Data
public class FileAnalysisRuleResponse extends ToString {
    private String groupId;
    private String ruleId;
    private String ruleName;
    private String riskLevel;
    private List<FileAnalysisRuleRiskResponse> reviewRuleRisks;
}
