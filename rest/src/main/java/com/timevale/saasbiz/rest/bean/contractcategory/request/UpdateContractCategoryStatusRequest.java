package com.timevale.saasbiz.rest.bean.contractcategory.request;

import com.timevale.contractanalysis.facade.api.enums.ContractCategoryStatusEnum;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.EnumCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改合同类型状态请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("修改合同类型状态请求参数")
public class UpdateContractCategoryStatusRequest extends ToString {

    @ApiModelProperty("合同类型id列表")
    @NotEmpty(message = "合同类型id列表不能为空")
    private List<String> categoryIds;

    @ApiModelProperty("合同类型是否启用")
    @NotNull(message = "请指定合同类型是否启用")
    private Boolean enable;
}
