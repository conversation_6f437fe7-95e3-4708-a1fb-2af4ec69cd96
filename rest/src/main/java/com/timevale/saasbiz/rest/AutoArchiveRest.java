package com.timevale.saasbiz.rest;

import com.timevale.datarefresh.facade.bean.RuleCheckResultDTO;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.autoarchive.bo.AutoArchiveOperatorBO;
import com.timevale.saasbiz.model.bean.autoarchive.bo.AutoArchiveSysRuleConditionBO;
import com.timevale.saasbiz.model.bean.autoarchive.dto.input.AutoArchiveBindMenuInputDTO;
import com.timevale.saasbiz.model.bean.autoarchive.dto.input.AutoArchiveListInputDTO;
import com.timevale.saasbiz.model.bean.autoarchive.dto.input.AutoArchiveUpdateRuleInputDTO;
import com.timevale.saasbiz.model.bean.autoarchive.dto.output.AutoArchiveListResultDTO;
import com.timevale.saasbiz.model.bean.autoarchive.dto.output.AutoArchiveRuleDetailDTO;
import com.timevale.saasbiz.model.bean.autoarchive.dto.output.ProcessStatusOutputDTO;
import com.timevale.saasbiz.model.bean.common.dto.TaskProgressDTO;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.autoarchive.request.*;
import com.timevale.saasbiz.rest.bean.autoarchive.response.AutoArchiveRuleDetailResponse;
import com.timevale.saasbiz.rest.bean.autoarchive.response.AutoArchiveRuleListResponse;
import com.timevale.saasbiz.rest.bean.autoarchive.response.ExistMenuRuleBindResponse;
import com.timevale.saasbiz.rest.bean.autoarchive.response.ProcessStatusResponse;
import com.timevale.saasbiz.rest.bean.autoarchive.vo.AutoArchiveSysRuleConditionVO;
import com.timevale.saasbiz.rest.bean.autoarchive.vo.RuleCheckResultVO;
import com.timevale.saasbiz.rest.bean.common.response.TaskProgressResponse;
import com.timevale.saasbiz.service.airulemenu.AiRuleMenuService;
import com.timevale.saasbiz.service.autoarchive.AutoArchiveService;
import com.timevale.saasbiz.service.autoarchive.AutoBindService;
import com.timevale.saasbiz.service.common.TaskProgressService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.*;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * AutoArchiveRest
 *
 * <AUTHOR>
 * @since 2023/8/9 11:41 上午
 */
@Api(tags = "归档接口")
@Validated
@ExternalService
@RestMapping(path = "/v2/auto-archive")
public class AutoArchiveRest {

    @Autowired
    private AutoArchiveService autoArchiveService;

    @Autowired
    private TaskProgressService taskProgressService;

    @Autowired
    private MapperFactory mapperFactory;

    @Autowired
    private SaasCommonService saasCommonService;

    @Autowired
    private AutoBindService autoBindService;


    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "归档列表", httpMethod = "POST")
    @RestMapping(path = "/rule-list", method = RequestMethod.POST)
    public RestResult<AutoArchiveRuleListResponse> list(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AutoArchiveRuleListRequest request) {

        AutoArchiveListInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, AutoArchiveListInputDTO.class);
        AutoArchiveListResultDTO resultDTO = autoArchiveService.listRule(inputDTO, tenantId, accountId);
        AutoArchiveRuleListResponse res = mapperFactory.getMapperFacade().map(resultDTO, AutoArchiveRuleListResponse.class);
        return RestResult.success(res);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "归档列表置顶/取消置顶", httpMethod = "GET")
    @RestMapping(path = "/top-rule", method = RequestMethod.GET)
    public RestResult<Void> top(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam String ruleId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        autoArchiveService.topRule(ruleId, tenantId, accountId);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "归档系统条件", httpMethod = "GET")
    @RestMapping(path = "/system-rule-config", method = RequestMethod.GET)
    public RestResult<List<AutoArchiveSysRuleConditionVO>> sysRuleConfig(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId, @RequestParam Integer bizType) {
        List<AutoArchiveSysRuleConditionBO> conditionBOS = autoArchiveService.listSysRuleConditions(tenantId, bizType);
        List<AutoArchiveSysRuleConditionVO> res = mapperFactory.getMapperFacade().mapAsList(conditionBOS, AutoArchiveSysRuleConditionVO.class);
        return RestResult.success(res);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.ADD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "保存归档规则", httpMethod = "POST")
    @RestMapping(path = "/save-rule", method = RequestMethod.POST)
    public RestResult<Void> save(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AutoArchiveSaveRuleRequest request) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        AutoArchiveBindMenuInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, AutoArchiveBindMenuInputDTO.class);
        autoArchiveService.bindRuleMenu(inputDTO, tenantId, accountId);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "修改归档规则", httpMethod = "POST")
    @RestMapping(path = "/update-rule", method = RequestMethod.POST)
    public RestResult<Void> update(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AutoArchiveUpdateRuleRequest request) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        AutoArchiveUpdateRuleInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, AutoArchiveUpdateRuleInputDTO.class);
        autoArchiveService.updateRule(inputDTO, tenantId, accountId);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询归档规则", httpMethod = "GET")
    @RestMapping(path = "/get-rule-detail", method = RequestMethod.GET)
    public RestResult<AutoArchiveRuleDetailResponse> detail(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId, @RequestParam String ruleId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        AutoArchiveRuleDetailDTO detail = autoArchiveService.detail(ruleId, tenantId);
        AutoArchiveRuleDetailResponse res = mapperFactory.getMapperFacade().map(detail, AutoArchiveRuleDetailResponse.class);
        return RestResult.success(res);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "校验菜单是否存在规则", httpMethod = "GET")
    @RestMapping(path = "/exist-menu-rule", method = RequestMethod.GET)
    public RestResult<Boolean> checkMenu(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam String menuId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        AutoArchiveRuleDetailDTO detail = autoArchiveService.detailByMenuId(menuId, tenantId);
        if(detail != null && StringUtils.isNotBlank(detail.getUuid())){
            return RestResult.success(true);
        }
        return RestResult.success(false);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "校验菜单是否存在规则(包含纸质文件和指定分类AI自动创建的关联规则)", httpMethod = "GET")
    @RestMapping(path = "/exist-menu-rule-bind", method = RequestMethod.GET)
    public RestResult<ExistMenuRuleBindResponse> checkMenuAndRule(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
                                                                  @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
                                                                  @RequestParam String menuId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        AutoArchiveRuleDetailDTO detail = autoArchiveService.detailByMenuId(menuId, tenantId);

        ExistMenuRuleBindResponse response = new ExistMenuRuleBindResponse();
        response.setExist(detail != null && StringUtils.isNotBlank(detail.getUuid()));
        response.setAutoBindRuleMenu(autoBindService.checkAiRuleMenuBind(menuId, tenantId));
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.DELETE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "删除归档规则", httpMethod = "GET")
    @RestMapping(path = "/delete-rule", method = RequestMethod.GET)
    public RestResult<Void> delete(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam String menuId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        autoArchiveService.delete(menuId, tenantId, accountId);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "重新运行归档规则", httpMethod = "GET")
    @RestMapping(path = "/rerun-rule", method = RequestMethod.GET)
    public RestResult<Void> rerun(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId, @RequestParam String ruleId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        autoArchiveService.rerun(ruleId, tenantId);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "修改归档状态", httpMethod = "POST")
    @RestMapping(path = "/update-rule-status", method = RequestMethod.POST)
    public RestResult<Void> updateStatus(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AutoArchiveUpdateStatusRequest request) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        autoArchiveService.updateStatus(request.getRuleId(), tenantId, accountId, request.getStatus());
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取归档进度", httpMethod = "GET")
    @RestMapping(path = "/get-rule-progress", method = RequestMethod.GET)
    public RestResult<TaskProgressResponse> getRuleProcess(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId, @RequestParam String ruleId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        TaskProgressDTO taskProgress = taskProgressService.getTaskProgress(ruleId, tenantId);
        TaskProgressResponse res = mapperFactory.getMapperFacade().map(taskProgress, TaskProgressResponse.class);
        return RestResult.success(res);
    }

    @ApiOperation(value = "规则条件校验", httpMethod = "POST")
    @RestMapping(path = "/check-rule-conditions", method = RequestMethod.POST)
    public RestResult<List<RuleCheckResultVO>> checkCondition(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId, @RequestBody AutoArchiveCheckRuleRequest request) {
        List<RuleCheckResultDTO> resultDTOS = autoArchiveService.checkCondition(tenantId, mapperFactory.getMapperFacade().mapAsList(request.getOperators(), AutoArchiveOperatorBO.class));
        return RestResult.success(mapperFactory.getMapperFacade().mapAsList(resultDTOS, RuleCheckResultVO.class));
    }


    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "停止运行归档规则", httpMethod = "GET")
    @RestMapping(path = "/stop-rule", method = RequestMethod.GET)
    public RestResult<Void> stop(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId, @RequestParam String ruleId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_AUTH_ARCHIVE);

        autoArchiveService.stopRule(ruleId, tenantId);
        return RestResult.success();
    }

    /**
     * 返回规则：若【合同偏好设置】已设置按已完成合同归档，在智能归档条件设置时，只能选择“已完成“状态的合同
     * @param subjectId
     * @return
     */
    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.AUTO_ARCHIVE,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/get-optional-status", method = RequestMethod.GET)
    @ApiOperation(value = "可选合同状态", httpMethod = "GET")
    public RestResult<ProcessStatusResponse> getOptionalStatusList(
            @ApiParam(value = "租户id", required = true) @RequestHeader(HEADER_TSIGN_OPEN_TENANT_ID) String tenantId) {
        ProcessStatusOutputDTO optionalStatusList = autoBindService.getOptionalStatusList(tenantId);
        return RestResult.success(mapperFactory.getMapperFacade().map(optionalStatusList,ProcessStatusResponse.class));
    }

}
