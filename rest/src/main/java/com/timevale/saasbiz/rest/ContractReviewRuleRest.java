package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.integration.filesystem.FileSystemClient;
import com.timevale.saasbiz.model.bean.contractreview.RuleDelOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rule.bo.ListRuleBO;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.input.*;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.DetailRuleListOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.DetailRuleOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.FileAnalysisOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.ReviewResultRuleDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.contractreview.RuleDelListOutputResponse;
import com.timevale.saasbiz.rest.bean.contractreview.rule.request.*;
import com.timevale.saasbiz.rest.bean.contractreview.rule.response.*;
import com.timevale.saasbiz.rest.bean.contractreview.rule.vo.ListRuleVO;
import com.timevale.saasbiz.rest.converter.contractreview.RuleInputDTOConverter;
import com.timevale.saasbiz.rest.converter.contractreview.RuleOutputDTOConverter;
import com.timevale.saasbiz.service.contractreview.ContractReviewRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.timevale.saasbiz.model.constants.PrivilegeOperationConstants.*;
import static com.timevale.saasbiz.model.constants.PrivilegeResourceConstants.PRIVILEGE_RESOURCE_INTELLIGENT_TOOL;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;


@Api(tags = "合同审查规则")
@Validated
@ExternalService
@RestMapping(path = "/v1/contract-review/rule")
public class ContractReviewRuleRest {

    @Autowired
    ContractReviewRuleService contractReviewRuleService;

    @ApiOperation(value = "规则-解析", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = ADD_RULE)
    @RestMapping(path = "/analysis", method = RequestMethod.POST)
    public RestResult<AnalysisRuleResponse> ruleAnalysis(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid AnalysisRuleRequest request) {

        AnalysisRuleInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        String ruleId = contractReviewRuleService.ruleAnalysis(inputDTO);
        AnalysisRuleResponse response = new AnalysisRuleResponse();
        response.setRuleId(ruleId);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则-详情", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = {ADD_RULE, DELETE_RULE, USE})
    @RestMapping(path = "/detail", method = RequestMethod.POST)
    public RestResult<DetailRuleResponse> ruleDetail(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid RuleIdRequest request) {
        RuleIdInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        DetailRuleOutputDTO ruleOutputDTO = contractReviewRuleService.ruleDetail(inputDTO);
        List<DetailRuleListOutputDTO> detailRuleOutputDTOResult = ruleOutputDTO.getDataList();
        List<DetailRuleListResponse> list = new ArrayList<>();
        detailRuleOutputDTOResult.stream().forEach(dto -> {
            DetailRuleListResponse response = RuleOutputDTOConverter.convertToDetailResponse(dto);
            list.add(response);
        });

        DetailRuleResponse response = new DetailRuleResponse();
        response.setDataList(list);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则-保存", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = ADD_RULE)
    @RestMapping(path = "/save", method = RequestMethod.POST)
    public RestResult<SaveRuleResponse> ruleSave(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid SaveRuleRequest request) {
        SaveRuleInputDTO saveRuleInputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        String ruleId = contractReviewRuleService.ruleSave(saveRuleInputDTO);
        SaveRuleResponse response = new SaveRuleResponse();
        response.setRuleId(ruleId);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则-批量保存", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = ADD_RULE)
    @RestMapping(path = "/batch/save", method = RequestMethod.POST)
    public RestResult<SaveRuleResponse> ruleBatchSave(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid BatchSaveRuleRequest request) {
        BatchSaveRuleInputDTO saveRuleInputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        contractReviewRuleService.ruleBatchSave(saveRuleInputDTO);
        SaveRuleResponse response = new SaveRuleResponse();
        return RestResult.success();
    }

    @ApiOperation(value = "规则-列表", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = {ADD_RULE, DELETE_RULE, USE})
    @RestMapping(path = "/list", method = RequestMethod.POST)
    public RestResult<ListRuleResponse> ruleList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid ListRuleRequest request) {
        ListRuleInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        List<ListRuleBO> list = contractReviewRuleService.ruleList(inputDTO);
        ListRuleResponse listRuleResponse = new ListRuleResponse();
        List<ListRuleVO> req = RuleOutputDTOConverter.convertToListVO(list);
        listRuleResponse.setList(req);
        return RestResult.success(listRuleResponse);
    }

    @ApiOperation(value = "规则-删除确认", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = DELETE_RULE)
    @RestMapping(path = "/del/confirm", method = RequestMethod.POST)
    public RestResult<RuleDelListOutputResponse> ruleDelConfirm(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid DelRuleRequest request) {
        DelRuleInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        RuleDelOutputDTO outputDTO = contractReviewRuleService.ruleDelConfirm(inputDTO);
        RuleDelListOutputResponse response = JSON.parseObject(JSON.toJSONString(outputDTO), RuleDelListOutputResponse.class);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则-删除", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = DELETE_RULE)
    @RestMapping(path = "/del", method = RequestMethod.POST)
    public RestResult ruleDel(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid DelRuleRequest request) {
        DelRuleInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        contractReviewRuleService.ruleDel(inputDTO);
        return RestResult.success();
    }

    @ApiOperation(value = "规则-删除缓存", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = DELETE_RULE)
    @RestMapping(path = "/del/cache", method = RequestMethod.POST)
    public RestResult ruleDelCache(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid RuleIdRequest request) {
        RuleIdInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        contractReviewRuleService.ruleCacheDel(inputDTO);
        return RestResult.success();
    }

    @ApiOperation(value = "规则-示例", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = USE)
    @RestMapping(path = "example", method = RequestMethod.POST)
    public RestResult<ExampleRuleResponse> ruleExample() {
        List<String> exampleList = contractReviewRuleService.ruleExample();
        ExampleRuleResponse req = new ExampleRuleResponse();
        req.setList(exampleList);
        return RestResult.success(req);
    }

    @ApiOperation(value = "规则-拷贝", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = ADD_RULE)
    @RestMapping(path = "copy", method = RequestMethod.POST)
    public RestResult<SaveRuleResponse> ruleCopy(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid RuleCopyRequest request) {
        RuleCopyInputDTO ruleCopyInputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        String ruleId = contractReviewRuleService.ruleCopy(ruleCopyInputDTO);
        SaveRuleResponse req = new SaveRuleResponse();
        req.setRuleId(ruleId);
        return RestResult.success(req);
    }

    @ApiOperation(value = "规则文件-解析", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = ADD_RULE)
    @RestMapping(path = "/file/analysis", method = RequestMethod.POST)
    public RestResult<AnalysisRuleResponse> ruleFileAnalysis(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid AnalysisRuleFileRequest request) {
        AnalysisRuleFileInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        String ruleId = contractReviewRuleService.ruleFileAnalysis(inputDTO);
        AnalysisRuleResponse response = new AnalysisRuleResponse();
        response.setRuleId(ruleId);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则文件-解析结果", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = ADD_RULE)
    @RestMapping(path = "/file/analysis/result", method = RequestMethod.POST)
    public RestResult<FileAnalysisResponse> ruleFileAnalysisResult(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid RuleIdRequest request) {
        RuleIdInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        FileAnalysisOutputDTO fileAnalysisOutputDTO = contractReviewRuleService.ruleFileAnalysisResult(inputDTO);
        FileAnalysisResponse response = new FileAnalysisResponse();
        BeanUtils.copyProperties(fileAnalysisOutputDTO, response);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则-验证结果", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = USE)
    @RestMapping(path = "/verify/result", method = RequestMethod.POST)
    public RestResult<ReviewResultRuleOuterResponse> ruleVerifyResult(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid RuleIdRequest request) {
        RuleIdInputDTO inputDTO = RuleInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        ReviewResultRuleDTO reviewResultRuleDTO = contractReviewRuleService.ruleVerifyResult(inputDTO);
        ReviewResultRuleOuterResponse reviewResultRuleResponse = JSON.parseObject(JSON.toJSONString(reviewResultRuleDTO), ReviewResultRuleOuterResponse.class);
        return RestResult.success(reviewResultRuleResponse);
    }
}
