package com.timevale.saasbiz.rest.converter.request;

import com.timevale.saasbiz.model.bean.seal.dto.input.*;
import com.timevale.saasbiz.rest.bean.seal.request.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-07-20 16:24
 */
@Slf4j
public class SealInputDTOConverter {
    public static SealPartVisibleDetailInputDTO convertSealPartVisibleDetailInputDTO(String accountId, String tenantId, SealPartVisibleDetailRequest request) {
        SealPartVisibleDetailInputDTO inputDTO = new SealPartVisibleDetailInputDTO();
        if(request != null) {
            BeanUtils.copyProperties(request, inputDTO);
        }
        inputDTO.setOrgOid(tenantId);
        inputDTO.setOperatorOid(accountId);
        return inputDTO;
    }
    public static SetVisibleScopeInputDTO convertSetVisibleScopeInputDTO(String accountId, String tenantId, SetSealVisibleScopeRequest request) {
        SetVisibleScopeInputDTO inputDTO = new SetVisibleScopeInputDTO();
        inputDTO.setOperatorOid(accountId);
        inputDTO.setOrgOid(tenantId);
        if(request != null) {
            inputDTO.setSealId(request.getSealId());
            inputDTO.setVisibleScope(request.getVisibleScope());
            inputDTO.setAssignedDeptIdList(request.getAssignedDeptIdList());
            inputDTO.setAssignedMemberIdList(request.getAssignedMemberIdList());
        }
        return inputDTO;
    }

    public static GetSealVisibleScopeInputDTO convertGetSealVisibleScopeInputDTO(String accountId, String tenantId, GetSealVisibleScopeRequest request) {
        GetSealVisibleScopeInputDTO inputDTO = new GetSealVisibleScopeInputDTO();
        inputDTO.setOperatorOid(accountId);
        inputDTO.setOrgOid(tenantId);
        if (request != null) {
          inputDTO.setSealId(request.getSealId());
        }
        return inputDTO;
    }

    public static DeleteVisibleScopeInputDTO convertDeleteVisibleScopeInputDTO(String accountId, String tenantId, DeleteSealVisibleScopeRequest request) {
        DeleteVisibleScopeInputDTO inputDTO = new DeleteVisibleScopeInputDTO();
        if(request != null){
            BeanUtils.copyProperties(request, inputDTO);
        }
        inputDTO.setOperatorOid(accountId);
        inputDTO.setOrgOid(tenantId);
        return inputDTO;
    }

    public static CreateOfficialSealPreCheckInputDTO convertCreateOfficialSealPreCheckInputDTO(String tenantId, CreateOfficialSealPreCheckRequest request) {
        CreateOfficialSealPreCheckInputDTO inputDTO = new CreateOfficialSealPreCheckInputDTO();
        inputDTO.setSealName(request.getSealName());
        inputDTO.setSealOwnerOid(Optional.ofNullable(request.getSealOwnerOid()).orElse(tenantId));
        return inputDTO;
    }
}
