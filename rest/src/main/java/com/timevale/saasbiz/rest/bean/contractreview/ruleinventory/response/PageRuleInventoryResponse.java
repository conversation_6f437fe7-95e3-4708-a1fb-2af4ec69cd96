package com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.vo.RuleInventoryListVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageRuleInventoryResponse extends ToString {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("审查清单列表")
    private List<RuleInventoryListVO> inventoryList;
}