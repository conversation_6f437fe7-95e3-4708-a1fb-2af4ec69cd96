package com.timevale.saasbiz.rest.bean.contractaudit.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@ApiModel("创建合同审计任务请求参数")
public class CreateContractAuditWebRequest extends ToString {
    @ApiModelProperty(value = "文件id")
    @NotBlank(message = "文件id不能为空")
    private String fileId;
}
