package com.timevale.saasbiz.rest.converter;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.pdftool.bo.BlockPositionBO;
import com.timevale.saasbiz.model.bean.pdftool.bo.MatchItemBO;
import com.timevale.saasbiz.model.bean.pdftool.bo.SearchResultBO;
import com.timevale.saasbiz.rest.bean.pdftool.vo.BlockPagePositionVO;
import com.timevale.saasbiz.rest.bean.pdftool.vo.MatchItemVO;
import com.timevale.saasbiz.rest.bean.pdftool.vo.SearchResultVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public class PdfSearchResultConverter {

    public static List<SearchResultVO> convert(List<SearchResultBO> searchResultBOS) {
        if (CollectionUtils.isEmpty(searchResultBOS)) {
            return Lists.newArrayList();
        }
        return searchResultBOS.stream().map(PdfSearchResultConverter::convertSearchResult).collect(Collectors.toList());
    }

    private static SearchResultVO convertSearchResult(SearchResultBO searchResultBO) {
        if (searchResultBO == null) {
            return null;
        }
        SearchResultVO searchResultVO = new SearchResultVO();
        searchResultVO.setKeyword(searchResultBO.getKeyword());
        searchResultVO.setMatchItems(convertMatchItems(searchResultBO.getMatchItems()));
        return searchResultVO;
    }

    private static List<MatchItemVO> convertMatchItems(List<MatchItemBO> matchItemBOS) {
        if (CollectionUtils.isEmpty(matchItemBOS)) {
            return Lists.newArrayList();
        }
        return matchItemBOS.stream().map(PdfSearchResultConverter::convertMatchItem).collect(Collectors.toList());
    }

    private static MatchItemVO convertMatchItem(MatchItemBO matchItemBO) {
        MatchItemVO matchItemVO = new MatchItemVO();
        matchItemVO.setBlockPosition(convertBlockPositions(matchItemBO.getBlockPositions()));
        return matchItemVO;
    }

    private static List<BlockPagePositionVO> convertBlockPositions(List<BlockPositionBO> blockPositionBOS) {
        if (CollectionUtils.isEmpty(blockPositionBOS)) {
            return Lists.newArrayList();
        }
        return blockPositionBOS.stream().map(PdfSearchResultConverter::convertPagePosition).collect(Collectors.toList());
    }

    private static BlockPagePositionVO convertPagePosition(BlockPositionBO blockPositionBO) {
        if (blockPositionBO == null) {
            return null;
        }
        BlockPagePositionVO blockPagePositionVO = new BlockPagePositionVO();
        blockPagePositionVO.setPage(blockPositionBO.getPage());
        blockPagePositionVO.setBlockPos(blockPositionBO.getBlockPos().showPosition());
        return blockPagePositionVO;
    }
}
