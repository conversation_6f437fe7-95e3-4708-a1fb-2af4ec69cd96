package com.timevale.saasbiz.rest.bean.contractreview.rule.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

@Data
public class ReviewResultRuleResponse extends ToString {
    private String ruleId;

    private String ruleLevel;

    private String ruleName;

    private String ruleStatus;

    private Integer riskCount;

    private Boolean hasRisk;

    private List<ReviewResultRuleRiskResponse> subRisks;
}
