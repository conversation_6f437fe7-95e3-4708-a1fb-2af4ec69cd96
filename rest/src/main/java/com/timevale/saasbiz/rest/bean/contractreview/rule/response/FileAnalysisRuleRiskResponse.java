package com.timevale.saasbiz.rest.bean.contractreview.rule.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

@Data
public class FileAnalysisRuleRiskResponse extends ToString {
    private String bizId;
    private String ruleDescription;
    private List<String> ruleBasis;
    private String suggestion;
    private List<String> keyword;
    private String similarTerms;
}
