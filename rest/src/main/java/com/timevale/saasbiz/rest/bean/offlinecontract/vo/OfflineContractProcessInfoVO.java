package com.timevale.saasbiz.rest.bean.offlinecontract.vo;

import com.timevale.saas.common.validator.StringCheck;
import com.timevale.saas.common.validator.enums.StringCheckType;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/** 线下合同合同信息 */
@Data
public class OfflineContractProcessInfoVO {
    /** 合同名称 */
    @NotBlank(message = "合同名称不能为空")
    @Size(max = 50, message = "合同名称最多支持50个字")
    @StringCheck(types = {StringCheckType.SPECIAL, StringCheckType.EMOJI}, message = "合同名称不支持换行、缩进、表情及特殊字符 < > / \\ | : \" * ?")
    private String title;
    /** 签署方 */
    @Valid
    private List<OfflineContractAccountVO> signers;
    /** 合同到期时间 */
    private Long contractValidity;
}
