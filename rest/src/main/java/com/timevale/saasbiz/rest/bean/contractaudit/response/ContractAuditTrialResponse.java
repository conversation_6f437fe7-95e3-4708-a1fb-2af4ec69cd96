package com.timevale.saasbiz.rest.bean.contractaudit.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
public class ContractAuditTrialResponse extends ToString {
    @ApiModelProperty(value = "试用文件列表")
    private List<FileInfo> files;

    @Data
    public static class FileInfo extends ToString{
        @ApiModelProperty(value = "id")
        private String id;
        @ApiModelProperty(value = "文件名称")
        private String fileName;
    }
}
