package com.timevale.saasbiz.rest.bean.contractcategory.vo;

import com.timevale.saas.common.validator.StringCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import static com.timevale.saas.common.validator.enums.StringCheckType.*;

/**
 * 提取字段信息
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("提取字段信息")
public class ExtractFieldVO {

    @Size(max = 64, message = "字段id长度不能超过64")
    @ApiModelProperty(value = "字段id", hidden = true)
    private String fieldId;

    /** 字段名称 */
    @ApiModelProperty("字段名称")
    @NotBlank(message = "字段名称不能为空")
    @Size(max = 20, message = "字段名称长度不能超过20")
    @StringCheck(
            types = {EMOJI, SPECIAL, UTF8MB4},
            message = "字段名称不能包含表情符、生僻字或特殊字符 < > / \\ | : \" * ? * /")
    private String fieldName;

    /** 字段描述 */
    @ApiModelProperty("字段描述")
    @Size(max = 50, message = "字段描述长度不能超过50")
    private String fieldDesc;

    /** 字段类型， 对应的字段值类型 */
    @ApiModelProperty("字段类型， 对应的字段值类型")
    @NotBlank(message = "字段类型不能为空")
    private String fieldType;
}
