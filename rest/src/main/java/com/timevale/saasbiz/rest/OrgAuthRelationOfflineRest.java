package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saasbiz.model.bean.authrelation.dto.input.AuthRelationListDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.input.AuthRelationOfflineBatchAddDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationConfigDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationEffectiveProductDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationListResultDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationOfflineBatchAddResultDTO;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.authrelation.request.AuthRelationOfflineBatchAddRequest;
import com.timevale.saasbiz.rest.bean.authrelation.response.AuthRelationConfigResponse;
import com.timevale.saasbiz.rest.bean.authrelation.response.AuthRelationEffectiveProductsResponse;
import com.timevale.saasbiz.rest.bean.authrelation.response.AuthRelationListResponse;
import com.timevale.saasbiz.rest.bean.authrelation.response.AuthRelationOfflineBatchAddResponse;
import com.timevale.saasbiz.service.authrelation.AuthRelationBizService;
import com.timevale.saasbiz.service.authrelation.AuthRelationCoreService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import com.timevale.saasbiz.utils.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * 关联企业-运营平台使用
 *
 * <AUTHOR>
 * @since 2023/5/22 5:17 下午
 */
@Api(tags = "关联企业运营平台接口")
@Validated
@ExternalService
@RestMapping(path = "/crm/v2/org-auth-relation")
public class OrgAuthRelationOfflineRest {

    @Autowired
    private AuthRelationCoreService authRelationCoreService;

    @Autowired
    private AuthRelationBizService authRelationBizService;

    @Autowired
    private MapperFactory mapperFactory;

    @Autowired
    private SaasCommonService saasCommonService;

    @ApiOperation(value = "运营后台线下批量添加授权记录", httpMethod = "POST")
    @RestMapping(path = "/add", method = RequestMethod.POST)
    public RestResult<AuthRelationOfflineBatchAddResponse> offlineBatchAdd(@RequestBody AuthRelationOfflineBatchAddRequest request) {
        String alias = UserUtils.getUserAlias();
        if (StringUtils.isEmpty(alias)) {
            throw new SaasBizException(SaasBizResultCode.ALIAS_MISS_ERROR);
        }
        saasCommonService.checkSupportFunction(request.getAuthTenantOid(), null, FunctionCodeConstant.MULTI_ORGANIZATION);

        AuthRelationOfflineBatchAddDTO batchAddDTO = mapperFactory.getMapperFacade().map(request, AuthRelationOfflineBatchAddDTO.class);
        AuthRelationOfflineBatchAddResultDTO resultDTO = authRelationBizService.offlineBatchAdd(batchAddDTO);
        AuthRelationOfflineBatchAddResponse response = mapperFactory.getMapperFacade().map(resultDTO, AuthRelationOfflineBatchAddResponse.class);
        return RestResult.success(response);
    }

    @ApiOperation(value = "添加前获取添加条件", httpMethod = "GET")
    @RestMapping(path = "/config", method = RequestMethod.GET)
    public RestResult<AuthRelationConfigResponse> getConfig(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {
        String alias = UserUtils.getUserAlias();
        if (StringUtils.isEmpty(alias)) {
            throw new SaasBizException(SaasBizResultCode.ALIAS_MISS_ERROR);
        }
        AuthRelationConfigDTO authRelationConfig = authRelationCoreService.getAuthRelationConfig(tenantId);
        AuthRelationConfigResponse res = mapperFactory.getMapperFacade().map(authRelationConfig, AuthRelationConfigResponse.class);
        return RestResult.success(res);
    }

    @ApiOperation(value = "获取订单信息", httpMethod = "GET")
    @RestMapping(path = "/effective-products", method = RequestMethod.GET)
    public RestResult<List<AuthRelationEffectiveProductsResponse>> getEffectiveProducts(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {
        String alias = UserUtils.getUserAlias();
        if (StringUtils.isEmpty(alias)) {
            throw new SaasBizException(SaasBizResultCode.ALIAS_MISS_ERROR);
        }
        List<AuthRelationEffectiveProductDTO> productDTOList = authRelationCoreService.queryEffectiveProducts(tenantId);
        List<AuthRelationEffectiveProductsResponse> res = mapperFactory.getMapperFacade().mapAsList(productDTOList, AuthRelationEffectiveProductsResponse.class);
        return RestResult.success(res);
    }

    @ApiOperation(value = "查询关联企业列表", httpMethod = "GET")
    @RestMapping(path = "/list", method = RequestMethod.GET)
    public RestResult<AuthRelationListResponse> authRelationList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam(required = false) Boolean queryDeleted) {
        String alias = UserUtils.getUserAlias();
        if (StringUtils.isEmpty(alias)) {
            throw new SaasBizException(SaasBizResultCode.ALIAS_MISS_ERROR);
        }
        AuthRelationListDTO request = new AuthRelationListDTO();
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        request.setQueryDeleted(queryDeleted);
        AuthRelationListResultDTO resultDTO = authRelationBizService.authRelationList(request, tenantId);
        AuthRelationListResponse response = mapperFactory.getMapperFacade().map(resultDTO, AuthRelationListResponse.class);
        return RestResult.success(response);
    }

    @ApiOperation(value = "查询关联企业授权记录列表", httpMethod = "GET")
    @RestMapping(path = "/log-list", method = RequestMethod.GET)
    public RestResult<AuthRelationListResponse> authRelationList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @Valid @Max(10000) @RequestParam Integer pageNum,
            @Valid @Max(1000) @RequestParam Integer pageSize,
            @RequestParam Long authRelationId) {

        String alias = UserUtils.getUserAlias();
        if (StringUtils.isEmpty(alias)) {
            throw new SaasBizException(SaasBizResultCode.ALIAS_MISS_ERROR);
        }

        AuthRelationListResultDTO resultDTO = authRelationBizService.authRelationLogList(tenantId, authRelationId, pageNum, pageSize);
        AuthRelationListResponse response = mapperFactory.getMapperFacade().map(resultDTO, AuthRelationListResponse.class);
        return RestResult.success(response);

    }
}
