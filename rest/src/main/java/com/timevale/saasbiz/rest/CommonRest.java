package com.timevale.saasbiz.rest;

import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.common.request.CommonTenantQueryListRequest;
import com.timevale.saasbiz.rest.bean.common.request.FunctionUsedRequest;
import com.timevale.saasbiz.rest.bean.common.response.FunctionUsedResponse;
import com.timevale.saasbiz.rest.bean.common.vo.AccountSimpleVO;
import com.timevale.saasbiz.service.common.CommonConfigService;
import com.timevale.saasbiz.service.common.CommonService;
import com.timevale.saasbiz.service.common.function.DTO.ReadFunctionSwitchDTO;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.constraints.NotBlank;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2023-03-08 14:08
 */
@Api(tags = "通用功能聚合服务")
@Validated
@ExternalService
@RestMapping
public class CommonRest {

    @Autowired private CommonConfigService functionConfigService;

    @Autowired private UserCenterService userCenterService;

    @Autowired private CommonService commonService;

    @Autowired private SaasCommonService saasCommonService;

    @Autowired private MapperFactory mapperFactory;

    @ApiOperation("功能开关")
    @RestMapping(path = "/v2/function/switch", method = RequestMethod.GET)
    public RestResult<Boolean> readFunctionSwitch(String functionKey) {
        ReadFunctionSwitchDTO readFunctionSwitchDTO = new ReadFunctionSwitchDTO();
        readFunctionSwitchDTO.setTenantOid(RequestContextExtUtils.getTenantId());
        readFunctionSwitchDTO.setAppId(RequestContextExtUtils.getAppId());
        readFunctionSwitchDTO.setFunctionKey(functionKey);
        readFunctionSwitchDTO.setDnsAppId(RequestContextExtUtils.getDnsAppId());
        Boolean functionSwitch = functionConfigService.readFunctionSwitch(readFunctionSwitchDTO);

        return RestResult.success(functionSwitch);
    }

    /**
     * 用户登录态保活
     * 注：请勿滥用，使用前要与TL沟通好
     * @return
     */
    @ApiOperation(value = "用户登录态保活", httpMethod = "PUT")
    @RestMapping(path = "/v1/saas-common/user/keep-alive", method = RequestMethod.PUT)
    public RestResult keepLoginAlive() {
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("根据租户id查询租户信息,包含已注销的")
    @RestMapping(path = "/v1/saas-common/tenant-list", method = RequestMethod.POST)
    public RestResult<List<AccountSimpleVO>> tenantList(@RequestBody CommonTenantQueryListRequest request) {
        Map<String, AccountDetailDTO> accountDetailDTOMap = userCenterService.queryFatAccountMapByAccountIds(request.getOidList());
        Collection<AccountDetailDTO> accountDetailDTOS = accountDetailDTOMap.values();
        List<AccountSimpleVO> accountSimpleVOS = mapperFactory.getMapperFacade().mapAsList(accountDetailDTOS, AccountSimpleVO.class);
        return RestResult.success(accountSimpleVOS);
    }

    @RestMapping(path = "/v1/common/function/used", method = RequestMethod.POST)
    @ApiOperation(value = "查询功能在当前企业是否有使用记录")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<FunctionUsedResponse> queryUsed(@RequestBody FunctionUsedRequest request) {
        String tenantId = RequestContextExtUtils.getTenantId();
        AccountInfoDTO subjectAccount = userCenterService.queryAccountInfoByOid(tenantId);
        boolean has = commonService.hasFunctionUsed(request.getFunctionCode(), request.getExtendParam(), subjectAccount);
        return BaseResult.success(new FunctionUsedResponse(has));
    }


    @ApiOperation("查询用户是否支持某项功能")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/v1/function/support", method = RequestMethod.GET)
    public RestResult<Boolean> readFunctionSwitch(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @ApiParam(value = "功能标识", required = true) @PathVariable String functionKey) {
        boolean supportFunction = false;
        //1，先判断是否VIP功能
        if (saasCommonService.hasSupportFunction(tenantId, functionKey)) {
            supportFunction = true;
        }
        //2，再判断是否是灰度功能

        //3，在判断是否是配置中心是否开启该功能
        ReadFunctionSwitchDTO readFunctionSwitchDTO = new ReadFunctionSwitchDTO();
        readFunctionSwitchDTO.setFunctionKey(functionKey);
        if (functionConfigService.readFunctionSwitch(readFunctionSwitchDTO)) {
            supportFunction = true;
        }
        ;

        return RestResult.success(supportFunction);
    }
}
