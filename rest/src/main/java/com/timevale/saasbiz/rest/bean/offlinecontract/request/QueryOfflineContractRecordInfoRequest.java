package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询线下合同导入记录基本信息
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class QueryOfflineContractRecordInfoRequest extends ToString {

    @ApiModelProperty(value = "导入记录id", required = true)
    @NotBlank(message = "导入记录id不能为空")
    private String recordId;

    /** 是否返回菜单路径 */
    @ApiModelProperty(value = "是否返回菜单路径", example = "false")
    private boolean withMenuPath;
}
