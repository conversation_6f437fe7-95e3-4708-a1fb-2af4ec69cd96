package com.timevale.saasbiz.rest;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.account.flow.service.enums.ApprovalBizTypeEnum;
import com.timevale.account.flow.service.enums.ApprovalListQueryType;
import com.timevale.contractapproval.facade.dto.ApprovalFlowDTO;
import com.timevale.contractapproval.facade.dto.ApprovalFlowTaskDTO;
import com.timevale.contractapproval.facade.enums.ApprovalQueryTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum;
import com.timevale.contractapproval.facade.input.ApprovalPageInput;
import com.timevale.contractapproval.facade.output.PageApprovalFlowOutput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.integration.approval.ProcessApprovalClient;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessStartSourceOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.converter.ApprovalVOConverter;
import com.timevale.saasbiz.service.approval.convert.ApprovalDTOConverter;
import com.timevale.saasbiz.service.process.ProcessService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;

import io.swagger.annotations.*;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.stream.Collectors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * FIXME 这个是为了减少微信小程序前端改动写的适配接口，出入参和老接口一样，不要维护这个接口，微信小程序审批重构后直接删掉
 *
 * <AUTHOR>
 * @since 2023-04-21 13:33
 */
@Slf4j
@Deprecated
@Api(tags = "审批流程适配接口")
@Validated
@ExternalService
@RestMapping
public class ApprovalFlowAdaptRest {
    @Autowired private ProcessApprovalClient processApprovalClient;
    @Autowired private UserCenterService userCenterService;
    @Autowired private ProcessService processService;

    /** FIXME 这个是为了减少微信小程序前端改动写的适配接口，出入参和老接口一样，不要维护这个接口，微信小程序审批重构后直接删掉（整个类） */
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询合同审批列表", httpMethod = "POST", notes = "审批列表")
    @RestMapping(path = "/v1/approval/adapt-list", method = RequestMethod.POST)
    @Deprecated
    public RestResult<ApiPageResult<ListApprovalResponse>> listApproval(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ListApprovalRequest request) {
        // 用户信息
        Map<String, AccountDetailDTO> accountMap =
                userCenterService.batchQueryAccountMapByOid(
                        Lists.newArrayList(tenantId, accountId));
        AccountDetailDTO tenant = accountMap.get(tenantId);
        AccountDetailDTO account = accountMap.get(accountId);
        if (Objects.isNull(tenant) || Objects.isNull(account)) {
            return RestResult.success(new ApiPageResult<>(Collections.emptyList(), 0));
        }

        // 查询一页
        PageApprovalFlowOutput output =
                processApprovalClient.listApproval(convertInput(request, tenant, account));
        if (Objects.isNull(output) || CollectionUtils.isEmpty(output.getItems())) {
            return RestResult.success(new ApiPageResult<>(Collections.emptyList(), 0));
        }
        //合同来源信息
        Map<String, ProcessStartSourceOutputDTO> startSourceMap = Maps.newHashMap();
        try {
            Set<String> processIds =
                    output.getItems().stream()
                            .filter(i -> !ApprovalBizTypeEnum.SEAL_APPLY_WITNESS.getType().equals(i.getBizType()))
                            .filter(i -> StringUtils.isNotBlank(i.getProcessId()))
                            .map(i -> i.getProcessId())
                            .collect(Collectors.toSet());
            startSourceMap.putAll(processService.batchQueryStartSource(Lists.newArrayList(processIds)));
        } catch (Exception e) {
            // 避免影响主流程
            log.warn("query process source info list fail", e);
        }
        // 转换成老接口的返回数据
        List<ListApprovalResponse> list =
                output.getItems().stream()
                        .map(item -> buildVO(item, account.getGid(), startSourceMap))
                        .collect(Collectors.toList());
        return RestResult.success(new ApiPageResult<>(list, output.getTotal()));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询合同审批数量", httpMethod = "GET", notes = "审批列表")
    @RestMapping(path = "/v1/approval/adapt-count", method = RequestMethod.GET)
    @Deprecated
    public RestResult<CountApprovalFlowListResponse> countApproval(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId) {
        // 用户信息
        Map<String, AccountDetailDTO> accountMap =
                userCenterService.batchQueryAccountMapByOid(
                        Lists.newArrayList(tenantId, accountId));
        AccountDetailDTO tenant = accountMap.get(tenantId);
        AccountDetailDTO account = accountMap.get(accountId);
        if (Objects.isNull(tenant) || Objects.isNull(account)) {
            return RestResult.success(new CountApprovalFlowListResponse());
        }

        // 通用查询条件
        ListApprovalRequest request = new ListApprovalRequest();
        request.setSize(5);
        ApprovalPageInput input = convertInput(request, tenant, account);

        CountApprovalFlowListResponse response = new CountApprovalFlowListResponse();
        // 我发起的
        input.setQueryType(ApprovalQueryTypeEnum.I_INITIATED.getType());
        response.setInitiateCount(getApprovalCount(input));

        // 我收到的
        input.setQueryType(ApprovalQueryTypeEnum.I_RECEIVE.getType());
        response.setReceiverCount(getApprovalCount(input));

        // 待我审批
        input.setQueryType(ApprovalQueryTypeEnum.I_PENDING.getType());
        response.setApprovingCount(getApprovalCount(input));

        // 我已审批
        input.setQueryType(ApprovalQueryTypeEnum.I_APPROVED.getType());
        response.setApprovedCount(getApprovalCount(input));

        return RestResult.success(response);
    }

    private Integer getApprovalCount(ApprovalPageInput input) {
        // 查询一页
        PageApprovalFlowOutput output = processApprovalClient.listApproval(input);

        return Optional.ofNullable(output).map(PageApprovalFlowOutput::getTotal).orElse(0);
    }

    private ListApprovalResponse buildVO(
            ApprovalFlowDTO flowDTO, String operatorGid, Map<String, ProcessStartSourceOutputDTO> startSourceMap) {
        ApprovalFlowTaskDTO currentTask =
                ApprovalDTOConverter.findCurrentTask(flowDTO.getCurrentTasks(), operatorGid);

        ListApprovalResponse response = new ListApprovalResponse();
        response.setApprovalCode(flowDTO.getApprovalCode());
        response.setApprovalUuid(flowDTO.getApprovalCode());
        response.setName(flowDTO.getApprovalName());
        response.setApplyAccountId(flowDTO.getInitiatorOid());
        response.setApplyAccountGid(flowDTO.getInitiatorGid());
        response.setApplyAccountName(flowDTO.getInitiatorName());
        response.setApplyTime(flowDTO.getCreateTime());
        response.setApprovalStatus(flowDTO.getApprovalStatus());
        response.setApprovalEndTime(flowDTO.getUpdateTime());
        response.setProcessFlag(Objects.nonNull(currentTask));
        response.setCanAgreeRefuseFlag(Objects.nonNull(currentTask));
        response.setShowRefreshFlag(null);
        response.setGroupFlag(null);
        response.setGroupCount(null);
        response.setFinish_count(null);
        // 合同流程发起来源信息
        response.setInitiateFrom(
                ApprovalVOConverter.buildProcessInitiateFrom(
                        flowDTO.getProcessId(), flowDTO.getBizType(), flowDTO.getSource(), startSourceMap));
        return response;
    }

    private ApprovalPageInput convertInput(
            ListApprovalRequest request, AccountDetailDTO tenant, AccountDetailDTO account) {
        ApprovalPageInput input = new ApprovalPageInput();
        input.setPageNum(request.getOffset() / request.getSize() + 1);
        input.setPageSize(request.getSize());
        input.setApprovalStatus(request.getApprovalstatus());
        input.setApprovalName(request.getKeyword());
        input.setQueryType(request.getType());
        input.setOperatorOid(account.getOid());
        input.setOperatorGid(account.getGid());
        input.setOrgOid(tenant.getOid());
        input.setOrgGid(tenant.getGid());
        input.setApprovalType(ApprovalTemplateTypeEnum.CONTRACT.getCode());
        input.setSortType(request.getSortType());

        return input;
    }

    /**
     * <AUTHOR>
     * @since 2021-08-25
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("审批列表统计响应数据")
    public static class CountApprovalFlowListResponse extends ToString {
        /** 我发起的统计 */
        private int initiateCount;
        /** 我收到的统计 */
        private int receiverCount;
        /** 待我审批的统计 */
        private int approvingCount;
        /** 我已审批的统计 */
        private int approvedCount;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ListApprovalResponse extends ToString {

        @ApiModelProperty(value = "审批id")
        private Long approvalId;

        @ApiModelProperty(value = "审批code")
        private String approvalCode;

        @ApiModelProperty(value = "审批表uuid")
        private String approvalUuid;

        @ApiModelProperty(value = "审批名称")
        private String name;

        @ApiModelProperty(value = "发起人oid")
        private String applyAccountId;

        @ApiModelProperty(value = "发起人gid")
        private String applyAccountGid;

        @ApiModelProperty(value = "发起人姓名")
        private String applyAccountName;

        @ApiModelProperty(value = "发起时间")
        private Date applyTime;

        /** 1-审批中 2-审批通过 3-审批拒绝 4-发起人撤回 5-终止审批 */
        @ApiModelProperty(value = "审批状态 1-审批中 2-审批通过 3-审批拒绝 4-发起人撤回 5-终止审批 6-已撤回重新发起")
        private Integer approvalStatus;

        @ApiModelProperty(value = "审批完成时间")
        private Date approvalEndTime;

        @ApiModelProperty(value = "审批模板类型")
        private String modelType;

        @ApiModelProperty(value = "是否可操作同意或驳回 true:可以 false:不可以")
        @Deprecated
        private Boolean processFlag;

        @ApiModelProperty(value = "是否可操作同意或驳回 true:可以 false:不可以")
        private Boolean canAgreeRefuseFlag = false;

        @ApiModelProperty(value = "是否审批组")
        private Boolean groupFlag;

        @ApiModelProperty(value = "是否需要展示刷新按钮")
        private Boolean showRefreshFlag;

        @ApiModelProperty(value = "审批组下有多少个审批单，包含未创建的审批单")
        private Integer groupCount;

        @ApiModelProperty(value = "有多少个审批单处理完结状态（包含通过、拒绝、撤回、终止）")
        private Integer finish_count;

        @ApiModelProperty("合同流程发起来源")
        private String initiateFrom;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ListApprovalRequest extends ToString {

        @ApiModelProperty(value = "偏移量 ", required = true, notes = "举例：一页一行，第一页传0，第二页传10，第三页传20")
        private Integer offset = 0;

        @ApiModelProperty(value = "页面数据行数", required = true)
        private Integer size = 10;

        /** 1 我收到的 2我发起的 3待我审批 4我已审批 {@link ApprovalListQueryType#getType()} */
        @ApiModelProperty(value = "查询类型:1-我收到的 2-我发起的 3-待我审批 4-我已审批", required = true)
        @Min(value = 1, message = "type无效")
        @Max(value = 4, message = "type无效")
        @NotNull(message = "type不能为空")
        private Integer type;

        /** 1 待审批 2审批通过 3 审批驳回 4-已撤回 5-终止 */
        @ApiModelProperty(value = "流程状态 1 待审批 2审批通过 3 审批驳回 4-已撤回 5-终止")
        private List<Integer> approvalstatus;

        @ApiModelProperty(value = "搜索关键字")
        private String keyword;

        /** 1-发起时间降序（默认） 2-发起时间升序 3-审批完成时间降序 4-审批完成时间升序 */
        @ApiModelProperty(value = "排序类型 1-发起时间降序（默认） 2-发起时间升序 3-审批完成时间降序 4-审批完成时间升序")
        private Integer sortType = 1;

        @ApiModelProperty(value = "用户guid")
        private String guid;

        @ApiModelProperty(value = "查询审批列表时，返回的数据是否为审批组，默认为非审批组-false")
        private Boolean withApprovalGroup = false;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApiPageResult<T> extends ToString {

        @ApiModelProperty("分页数据")
        private List<T> result;

        @ApiModelProperty("总量")
        private Integer total = 0;

        public List<T> getResult() {
            return result;
        }

        public void setResult(List<T> result) {
            this.result = result;
        }

        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }
    }
}
