package com.timevale.saasbiz.rest;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.common.request.SaveAccessRecordRequest;
import com.timevale.saasbiz.rest.bean.common.response.GetAccessRecordResponse;
import com.timevale.saasbiz.service.common.AccessRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.constraints.NotBlank;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;

/**
 * 资源访问历史记录服务，以人为维度记录访问历史
 *
 * @author: qianyi
 * @since: 2023-03-10
 */
@Api(tags = "资源访问记录", description = "记录前端资源访问记录")
@Validated
@ExternalService
@RestMapping(path = "/v2/access/record")
public class AccessRecordRest {

    @Autowired AccessRecordService accessRecordService;

    /**
     * 保存访问记录，同一用户只保存最后一次提交的URL地址
     *
     * @param request
     * @return redis失效后返回空字符串，否则返回对应URL地址
     */
    @RestMapping(path = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "保存访问记录", httpMethod = "POST")
    public RestResult<Boolean> saveAccessRecord(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户id不能为空") String accountId,
            @RequestBody SaveAccessRecordRequest request) {

        boolean result = accessRecordService.saveAccessRecord(accountId, request.getUrl());
        return RestResult.success(result);
    }

    /**
     * 获取上一个访问记录URL，方便前端返回时跳转
     *
     * @param accountId
     * @return
     */
    @RestMapping(path = "/before", method = RequestMethod.GET)
    @ApiOperation(value = "获取上一个访问记录URL", httpMethod = "GET")
    public RestResult<GetAccessRecordResponse> getBeforeUrl(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户id不能为空") String accountId) {
        String url = StringUtils.defaultString(accessRecordService.getBeforeUrl(accountId), "");
        GetAccessRecordResponse response = new GetAccessRecordResponse();
        response.setRedirectUrl(url);
        return RestResult.success(response);
    }
}
