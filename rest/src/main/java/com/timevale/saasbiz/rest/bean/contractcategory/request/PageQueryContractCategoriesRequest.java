package com.timevale.saasbiz.rest.bean.contractcategory.request;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.StringCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

import static com.timevale.saas.common.validator.enums.StringCheckType.*;

/**
 * 分页查询合同类型列表请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("分页查询合同类型列表请求参数")
public class PageQueryContractCategoriesRequest extends ToString {

    @ApiModelProperty("合同类型状态, 多个以逗号分隔")
    private String categoryStatus;

    /** 合同类型id */
    @ApiModelProperty("合同类型id")
    private String categoryId;

    /** 合同类型名称 */
    @ApiModelProperty("合同类型名称")
    @StringCheck(
            types = {EMOJI, UTF8MB4},
            message = "合同类型名称不能包含表情符、生僻字")
    private String categoryName;

    /** 字段id */
    @ApiModelProperty("字段id")
    private String fieldId;

    /** 流程模板id */
    @ApiModelProperty("流程模板id")
    private String flowTemplateId;

    /** 流程模板名称 */
    @ApiModelProperty("流程模板名称")
    @StringCheck(
            types = {EMOJI, UTF8MB4},
            message = "流程模板名称不能包含表情符、生僻字")
    private String flowTemplateName;

    /** 页码 */
    @ApiModelProperty(value = "页码", required = true)
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    /** 每页数据大小 */
    @ApiModelProperty(value = "每页数据大小", required = true)
    @NotNull(message = "每页数据大小不能为空")
    private Integer pageSize;
}
