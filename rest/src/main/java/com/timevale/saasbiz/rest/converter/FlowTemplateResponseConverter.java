package com.timevale.saasbiz.rest.converter;
import com.google.common.collect.Lists;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.FlowTemplateAccountOperationDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.FlowTemplateNodeAccountDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.output.FlowTemplateParticipantNodesOutputDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.output.FlowTemplateParticipantSealsOutputDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.output.ListFlowTemplateFileOutputDTO;
import com.timevale.saasbiz.rest.bean.flowtemplate.response.FlowTemplateParticipantNodesResponse;
import com.timevale.saasbiz.rest.bean.flowtemplate.response.FlowTemplateParticipantSealsResponse;
import com.timevale.saasbiz.rest.bean.flowtemplate.vo.FlowTemplateAccountOperationVO;
import com.timevale.saasbiz.rest.bean.flowtemplate.vo.FlowTemplateFileVO;
import com.timevale.saasbiz.rest.bean.flowtemplate.response.ListFlowTemplateFileResponse;
import com.timevale.saasbiz.rest.bean.flowtemplate.vo.FlowTemplateNodeAccountVO;
import com.timevale.saasbiz.rest.bean.flowtemplate.vo.FlowTemplateParticipantSealsVO;
import com.timevale.saasbiz.rest.bean.seal.SealShowDetailVO;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 流程模板响应对象转换器
 *
 * <AUTHOR>
 * @since 2023-08-04 17:17
 */
public class FlowTemplateResponseConverter {
    public static ListFlowTemplateFileResponse convertListFlowTemplateFileResponse(
            ListFlowTemplateFileOutputDTO outputDTO) {
        ListFlowTemplateFileResponse response = new ListFlowTemplateFileResponse();
        // 判空
        if (Objects.isNull(outputDTO) || CollectionUtils.isEmpty(outputDTO.getFiles())) {
            response.setFiles(Lists.newArrayList());
            return response;
        }
        // 转换文件列表 fileDTO -> fileVO
        List<FlowTemplateFileVO> files =
                outputDTO.getFiles().stream()
                        .map(
                                fileDTO -> {
                                    FlowTemplateFileVO fileVO = new FlowTemplateFileVO();
                                    fileVO.setFileId(fileDTO.getFileId());
                                    fileVO.setFileName(fileDTO.getFileName());
                                    fileVO.setContractNoType(fileDTO.getContractNoType());
                                    fileVO.setContractNoRule(fileDTO.getContractNoRule());

                                    return fileVO;
                                })
                        .collect(Collectors.toList());
        // 组合响应对象
        response.setFiles(files);

        return response;
    }

    public static FlowTemplateParticipantSealsResponse convertResponse(FlowTemplateParticipantSealsOutputDTO outputDTO) {
        FlowTemplateParticipantSealsResponse response = new FlowTemplateParticipantSealsResponse();
        if (null == outputDTO || CollectionUtils.isEmpty(outputDTO.getParticipantSeals())) {
            response.setParticipantSeals(Lists.newArrayList());
            return response;
        }
        response.setParticipantSeals(outputDTO.getParticipantSeals().stream().map(i -> {
            FlowTemplateParticipantSealsVO participantSealsVO = new FlowTemplateParticipantSealsVO();
            participantSealsVO.setParticipantId(i.getParticipantId());
            participantSealsVO.setSeals(Lists.newArrayList());
            if (CollectionUtils.isEmpty(i.getSeals())) {
                return participantSealsVO;
            }
            participantSealsVO.setSeals(i.getSeals().stream().map(s -> {
                FlowTemplateParticipantSealsVO.SealsVO sealsVO = new FlowTemplateParticipantSealsVO.SealsVO();
                sealsVO.setSignRequirement(s.getSignRequirement());
                sealsVO.setAssignedSeal(s.isAssignedSeal());
                sealsVO.setAssignedSealType(s.isAssignedSealType());
                sealsVO.setSeals(Lists.newArrayList());
                if (CollectionUtils.isEmpty(s.getSeals())) {
                    return sealsVO;
                }
                sealsVO.setSeals(s.getSeals().stream().map(seal -> {
                    SealShowDetailVO sealShowDetailVO = new SealShowDetailVO();
                    sealShowDetailVO.setSealBizType(seal.getSealBizType());
                    sealShowDetailVO.setSealBizTypeDesc(seal.getSealBizTypeDesc());
                    sealShowDetailVO.setSealId(seal.getSealId());
                    sealShowDetailVO.setSealName(seal.getSealName());
                    sealShowDetailVO.setSealUrl(seal.getSealUrl());
                    sealShowDetailVO.setSealWidth(seal.getSealWidth());
                    sealShowDetailVO.setSealHeight(seal.getSealHeight());
                    sealShowDetailVO.setSealStatus(seal.getSealStatus());
                    return sealShowDetailVO;
                }).collect(Collectors.toList()));
                return sealsVO;
            }).collect(Collectors.toList()));
            return participantSealsVO;
        }).collect(Collectors.toList()));

        return response;
    }

    public static FlowTemplateParticipantNodesResponse convertResponse(FlowTemplateParticipantNodesOutputDTO outputDTO) {
        FlowTemplateParticipantNodesResponse response = new FlowTemplateParticipantNodesResponse();
        response.setCooperatorAccounts(Optional.ofNullable(outputDTO.getCooperatorAccounts()).orElse(Lists.newArrayList()).stream().map(i -> buildVO(i)).collect(Collectors.toList()));
        response.setSignerAccounts(Optional.ofNullable(outputDTO.getSignerAccounts()).orElse(Lists.newArrayList()).stream().map(i -> buildVO(i)).collect(Collectors.toList()));
        response.setNodeMultiAccount(outputDTO.isNodeMultiAccount());
        response.setOrderSign(outputDTO.isOrderSign());
        response.setOperations(Optional.ofNullable(outputDTO.getOperations()).orElse(Lists.newArrayList()).stream().map(i -> buildVO(i)).collect(Collectors.toList()));

        return response;
    }

    public static FlowTemplateNodeAccountVO buildVO(FlowTemplateNodeAccountDTO nodeAccountDTO) {
        FlowTemplateNodeAccountVO nodeAccountVO = new FlowTemplateNodeAccountVO();
        nodeAccountVO.setAccounts(Optional.ofNullable(nodeAccountDTO.getAccounts()).orElse(Lists.newArrayList()).stream().map(i -> buildVO(i)).collect(Collectors.toList()));
        nodeAccountVO.setType(nodeAccountDTO.getType());
        nodeAccountVO.setRole(nodeAccountDTO.getRole());
        nodeAccountVO.setOrder(nodeAccountDTO.getOrder());
        nodeAccountVO.setMultiOr(nodeAccountDTO.isMultiOr());
        nodeAccountVO.setSharable(nodeAccountDTO.isSharable());
        nodeAccountVO.setBatch(nodeAccountDTO.isBatch());
        return nodeAccountVO;
    }

    private static FlowTemplateNodeAccountVO.AccountInfoVO buildVO(FlowTemplateNodeAccountDTO.AccountInfoDTO accountDTO) {
        FlowTemplateNodeAccountVO.AccountInfoVO accountInfoVO = new FlowTemplateNodeAccountVO.AccountInfoVO();
        accountInfoVO.setPerson(buildVO(accountDTO.getPerson()));
        accountInfoVO.setSubject(buildVO(accountDTO.getSubject()));
        return accountInfoVO;
    }

    private static FlowTemplateNodeAccountVO.AccountBeanVO buildVO(FlowTemplateNodeAccountDTO.AccountBeanDTO accountDTO) {
        FlowTemplateNodeAccountVO.AccountBeanVO accountBeanVO = new FlowTemplateNodeAccountVO.AccountBeanVO();
        accountBeanVO.setOid(accountDTO.getOid());
        accountBeanVO.setName(accountDTO.getName());
        accountBeanVO.setNickname(accountDTO.getNickname());
        accountBeanVO.setMobile(accountDTO.getMobile());
        accountBeanVO.setEmail(accountDTO.getEmail());
        accountBeanVO.setOrgan(accountDTO.isOrgan());
        accountBeanVO.setLicense(accountDTO.getLicense());
        accountBeanVO.setLicenseType(accountDTO.getLicenseType());
        return accountBeanVO;
    }

    private static FlowTemplateAccountOperationVO buildVO(FlowTemplateAccountOperationDTO operationDTO) {
        FlowTemplateAccountOperationVO operationVO = new FlowTemplateAccountOperationVO();
        operationVO.setCode(operationDTO.getCode());
        operationVO.setDesc(operationDTO.getDesc());
        return operationVO;
    }
}
