package com.timevale.saasbiz.rest.bean.contractreview.rulegroup.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class SaveRuleGroupRequest extends ToString {
    @ApiModelProperty("规则组id")
    private String groupId;

    @ApiModelProperty("规则组名称")
    @NotBlank(message = "规则组名称不能为空")
    @Length(max = 20, message = "规则组名称不能超过20字符")
    private String groupName;

    @ApiModelProperty("规则组id列表")
    private List<String> groupIds;
}
