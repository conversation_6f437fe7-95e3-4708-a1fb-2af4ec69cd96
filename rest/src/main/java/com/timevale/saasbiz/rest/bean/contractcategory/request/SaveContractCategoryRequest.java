package com.timevale.saasbiz.rest.bean.contractcategory.request;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.StringCheck;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.ExtractFieldVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

import static com.timevale.saas.common.validator.enums.StringCheckType.*;

/**
 * 保存合同类型请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("保存合同类型请求参数")
public class SaveContractCategoryRequest extends ToString {
    /** 合同类型名称 */
    @ApiModelProperty("合同类型名称")
    @NotBlank(message = "合同类型名称不能为空")
    @Size(max = 10, message = "合同类型名称最多支持10个字")
    @StringCheck(
            types = {EMOJI, SPECIAL, UTF8MB4},
            message = "合同类型名称不能包含表情符、生僻字或特殊字符 < > / \\ | : \" * ? * /")
    private String categoryName;
    /** 提取字段列表 */
    private List<ExtractFieldVO> extractFields;
    /** 关联的系统推荐合同类型id */
    private String refSysCategoryId;
}
