package com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeleteRuleInventoryRequest extends ToString {

    @NotBlank(message = "审查清单id不能为空")
    @ApiModelProperty("审查清单id")
    private String inventoryId;
}