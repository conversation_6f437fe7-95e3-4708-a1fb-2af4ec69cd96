package com.timevale.saasbiz.rest.converter;

import com.google.common.collect.Lists;
import com.timevale.saasbiz.model.bean.oauth.bo.AuthScopeBO;
import com.timevale.saasbiz.model.bean.oauth.dto.output.QueryScopeAuthLogsOutputDTO;
import com.timevale.saasbiz.model.bean.oauth.dto.output.QueryScopeAuthMappingsOutputDTO;
import com.timevale.saasbiz.rest.bean.oauth.response.QueryScopeAuthLogsResponse;
import com.timevale.saasbiz.rest.bean.oauth.response.QueryScopeAuthMappingsResponse;
import com.timevale.saasbiz.rest.bean.oauth.response.vo.AuthScopeVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 认证授权服务响应数据转换类
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
public class OAuthResponseConverter {

    /**
     * 转换对外返回的应用Scope授权关系列表
     *
     * @param mappingsDTO
     * @return
     */
    public static QueryScopeAuthMappingsResponse convertScopeAuthMappingsResponse(
            QueryScopeAuthMappingsOutputDTO mappingsDTO) {
        QueryScopeAuthMappingsResponse response = new QueryScopeAuthMappingsResponse();
        response.setCount(mappingsDTO.getCount());
        if (null != mappingsDTO.getAuthList()) {
            response.setAuthList(
                    mappingsDTO.getAuthList().stream()
                            .map(i -> convertScopeAuthMappingVO(i))
                            .collect(Collectors.toList()));
        }
        return response;
    }

    /**
     * 转换对外返回的授权关系信息
     *
     * @param mappingBO
     * @return
     */
    public static QueryScopeAuthMappingsResponse.ScopeAuthMappingVO convertScopeAuthMappingVO(
            QueryScopeAuthMappingsOutputDTO.ScopeAuthMappingBO mappingBO) {
        QueryScopeAuthMappingsResponse.ScopeAuthMappingVO scopeAuthMappingVO =
                new QueryScopeAuthMappingsResponse.ScopeAuthMappingVO();
        scopeAuthMappingVO.setScopes(convertAuthScopeVOs(mappingBO.getScopes()));
        scopeAuthMappingVO.setAppId(mappingBO.getAppId());
        scopeAuthMappingVO.setAppLogo(mappingBO.getAppLogo());
        scopeAuthMappingVO.setAppName(mappingBO.getAppName());
        return scopeAuthMappingVO;
    }

    /**
     * 转换对外返回的应用Scope授权日志列表
     *
     * @param logsDTO
     * @return
     */
    public static QueryScopeAuthLogsResponse convertScopeAuthLogsResponse(
            QueryScopeAuthLogsOutputDTO logsDTO) {
        QueryScopeAuthLogsResponse response = new QueryScopeAuthLogsResponse();
        response.setCount(logsDTO.getCount());
        response.setAppId(logsDTO.getAppId());
        response.setAppName(logsDTO.getAppName());
        response.setAppLogo(logsDTO.getAppLogo());
        response.setAuthOid(logsDTO.getAuthOid());
        response.setAuthName(logsDTO.getAuthName());
        if (null != logsDTO.getAuthLogList()) {
            response.setAuthLogList(
                    logsDTO.getAuthLogList().stream()
                            .map(i -> convertScopeAuthLogVO(i))
                            .collect(Collectors.toList()));
        }

        return response;
    }
    /**
     * 转换对外返回的授权日志信息
     *
     * @param logBO
     * @return
     */
    public static QueryScopeAuthLogsResponse.ScopeAuthLogVO convertScopeAuthLogVO(
            QueryScopeAuthLogsOutputDTO.ScopeAuthLogBO logBO) {
        QueryScopeAuthLogsResponse.ScopeAuthLogVO scopeAuthLogVO =
                new QueryScopeAuthLogsResponse.ScopeAuthLogVO();
        scopeAuthLogVO.setOperatorLoginAccount(logBO.getOperatorLoginAccount());
        scopeAuthLogVO.setOperatorName(logBO.getOperatorName());
        scopeAuthLogVO.setOperatorOid(logBO.getOperatorOid());
        scopeAuthLogVO.setOperateTime(logBO.getOperateTime());
        if (null != logBO.getBeforeScopes()) {
            scopeAuthLogVO.setBeforeScopes(convertAuthScopeVOs(logBO.getBeforeScopes()));
        }
        if (null != logBO.getAfterScopes()) {
            scopeAuthLogVO.setAfterScopes(convertAuthScopeVOs(logBO.getAfterScopes()));
        }
        //操作类型
        scopeAuthLogVO.setOperatorSourceDesc(logBO.getOperatorSourceDesc());
        return scopeAuthLogVO;
    }

    /**
     * 转换AuthScopeBO列表 -> AuthScopeVO列表
     * @param scopeBOS
     * @return
     */
    public static List<AuthScopeVO> convertAuthScopeVOs(List<AuthScopeBO> scopeBOS) {
        if (null != scopeBOS) {
            return scopeBOS.stream().map(i -> convertAuthScopeVO(i)).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 转换AuthScopeBO -> AuthScopeVO
     * @param scopeBO
     * @return
     */
    public static AuthScopeVO convertAuthScopeVO(AuthScopeBO scopeBO) {
        AuthScopeVO authScopeVO = new AuthScopeVO();
        authScopeVO.setScopeCode(scopeBO.getScopeCode());
        authScopeVO.setScopeDesc(scopeBO.getScopeDesc());
        return authScopeVO;
    }
}
