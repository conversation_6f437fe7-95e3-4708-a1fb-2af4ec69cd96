package com.timevale.saasbiz.rest.bean.contractreview.record.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.contractreview.record.vo.PageReviewRecordVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PageReviewRecordResponse extends ToString {
    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("审查清单列表")
    private List<PageReviewRecordVO> inventoryList;
}
