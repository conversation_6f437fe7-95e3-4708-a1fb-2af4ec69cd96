package com.timevale.saasbiz.rest;

import java.util.Collections;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saasbiz.model.bean.ai.agent.tool.bo.FileAiInfoBO;
import com.timevale.saasbiz.model.bean.ai.agent.tool.bo.ProcessFileInfoBO;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.input.ProcessRescindUrlInput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessAiInfoOutput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessApprovalOutput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessFileInfoOutput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessRefuseOutput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessRescindUrlOutput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessRestartUrlOutput;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.aiagen.tool.response.ProcessAiInfoResponse;
import com.timevale.saasbiz.rest.bean.aiagen.tool.response.ProcessApprovalResponse;
import com.timevale.saasbiz.rest.bean.aiagen.tool.response.ProcessFileInfoResponse;
import com.timevale.saasbiz.rest.bean.aiagen.tool.response.ProcessRefuseResponse;
import com.timevale.saasbiz.rest.bean.aiagen.tool.response.ProcessRescindUrlResponse;
import com.timevale.saasbiz.rest.bean.aiagen.tool.response.ProcessRestartResponse;
import com.timevale.saasbiz.rest.bean.aiagen.tool.vo.FileAiInfoVO;
import com.timevale.saasbiz.rest.bean.aiagen.tool.vo.ProcessFileInfoVO;
import com.timevale.saasbiz.service.ai.agent.tool.AiAgentToolService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@Api(tags = "AI Agent工具模块", description = "AI Agent工具模块")
@ExternalService
@RestMapping(path = "/v2/ai-agent-tool")
public class AiAgentToolRest {

    @Autowired
    private AiAgentToolService aiAgentToolService;

    @ApiOperation(value = "获取文件信息", httpMethod = "GET")
    @RestMapping(path = "/get-file-info", method = RequestMethod.GET)
    public RestResult<ProcessFileInfoResponse> getFileInfo(@RequestParam String processId,
            @ApiParam(value = "流程类型") @RequestParam Integer flowType,
            @ApiParam(value = "操作人ID") @RequestParam String operatorId,
            @ApiParam(value = "租户ID") @RequestParam String tenantId) {
        ProcessFileInfoOutput output = aiAgentToolService.getFileInfo(processId, flowType, operatorId, tenantId);
        ProcessFileInfoResponse response = convertToResponse(output);
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取解约地址", httpMethod = "GET")
    @RestMapping(path = "/get-rescind-url", method = RequestMethod.GET)
    public RestResult<ProcessRescindUrlResponse> getRescindUrl(
            @ApiParam(value = "流程ID") @RequestParam String processId,
            @ApiParam(value = "操作人ID") @RequestParam String operatorId,
            @ApiParam(value = "租户ID") @RequestParam String tenantId,
            @ApiParam(value = "菜单ID") @RequestParam(required = false) String menuId) {
        ProcessRescindUrlInput input = new ProcessRescindUrlInput();
        input.setProcessId(processId);
        input.setOperatorId(operatorId);
        input.setTenantId(tenantId);
        input.setMenuId(menuId);
        ProcessRescindUrlOutput output = aiAgentToolService.getRescind(input);
        ProcessRescindUrlResponse response = convertToResponse(output);
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取合同信息", httpMethod = "GET")
    @RestMapping(path = "/get-process-info", method = RequestMethod.GET)
    public RestResult<ProcessAiInfoResponse> getAiInfo(@RequestParam String processId) {
        ProcessAiInfoOutput output = aiAgentToolService.getAiInfo(processId);
        ProcessAiInfoResponse response = convertToResponse(output);
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取重新发起地址", httpMethod = "GET")
    @RestMapping(path = "/get-restart-url", method = RequestMethod.GET)
    public RestResult<ProcessRestartResponse> getRestartUrl(@RequestParam String processId, 
            @ApiParam(value = "操作人ID") @RequestParam String operatorId, 
            @ApiParam(value = "租户ID") @RequestParam String tenantId) {
        ProcessRestartUrlOutput output = aiAgentToolService.getRestart(processId, operatorId, tenantId);
        ProcessRestartResponse response = new ProcessRestartResponse();
        response.setCanRestart(output.getCanRestart());
        response.setMessage(output.getMessage());
        response.setRestartUrl(output.getRestartUrl());
        return RestResult.success(response);
    }

    @ApiOperation(value = "合同拒签校验", httpMethod = "GET")
    @RestMapping(path = "/check-refuse", method = RequestMethod.GET)
    public RestResult<ProcessRefuseResponse> checkRefuse(@ApiParam(value = "流程ID") @RequestParam String processId,
            @ApiParam(value = "操作人ID") @RequestParam String operatorId, @ApiParam(value = "租户ID") @RequestParam String tenantId) {
        ProcessRefuseOutput output = aiAgentToolService.refuse(processId, operatorId, tenantId);
        ProcessRefuseResponse response = convertToResponse(output);
        return RestResult.success(response);
    }

    @ApiOperation(value = "合同审批校验", httpMethod = "GET")
    @RestMapping(path = "/check-approval", method = RequestMethod.GET)
    public RestResult<ProcessApprovalResponse> checkApproval(@ApiParam(value = "流程ID") @RequestParam String processId,
            @ApiParam(value = "操作人ID") @RequestParam String operatorId) {
        ProcessApprovalOutput output = aiAgentToolService.approval(processId, operatorId);
        ProcessApprovalResponse response = convertToResponse(output);
        return RestResult.success(response);
    }

    private ProcessApprovalResponse convertToResponse(ProcessApprovalOutput output) {
        ProcessApprovalResponse response = new ProcessApprovalResponse();
        response.setCanApproval(output.getCanApproval());
        response.setMessage(output.getMessage());
        return response;
    }

    private ProcessRefuseResponse convertToResponse(ProcessRefuseOutput output) {
        ProcessRefuseResponse response = new ProcessRefuseResponse();
        response.setCanRefuse(output.getCanRefuse());
        response.setMessage(output.getMessage());
        return response;
    }
    

    private ProcessAiInfoResponse convertToResponse(ProcessAiInfoOutput output) {
        ProcessAiInfoResponse response = new ProcessAiInfoResponse();
        if (CollectionUtils.isNotEmpty(output.getFiles())) {
            response.setFiles(output.getFiles().stream().map(this::convertBO2VO).collect(Collectors.toList()));
        }
        return response;
    }

    private FileAiInfoVO convertBO2VO(FileAiInfoBO fileAiInfoBO) {
        if (fileAiInfoBO == null) {
            return null;
        }
        FileAiInfoVO fInfoVO = new FileAiInfoVO();
        fInfoVO.setFileId(fileAiInfoBO.getFileId());
        fInfoVO.setContractType(fileAiInfoBO.getContractType());
        fInfoVO.setSignerNames(fileAiInfoBO.getSignerNames());
        return fInfoVO;
    }

    private ProcessRescindUrlResponse convertToResponse(ProcessRescindUrlOutput output) {
        ProcessRescindUrlResponse response = new ProcessRescindUrlResponse();
        response.setCanRescind(output.getCanRescind());
        response.setMessage(output.getMessage());
        response.setRescindUrl(output.getRescindUrl());
        return response;
    }

    private ProcessFileInfoResponse convertToResponse(ProcessFileInfoOutput output) {
        ProcessFileInfoResponse response = new ProcessFileInfoResponse();
        if (output == null || CollectionUtils.isEmpty(output.getFileInfoList())) {
            response.setFileInfoList(Collections.emptyList());
            return response;
        }
        response.setFileInfoList(output.getFileInfoList().stream().map(this::convertToVO).collect(Collectors.toList()));
        return response;
    }

    private ProcessFileInfoVO convertToVO(ProcessFileInfoBO bo) {
        ProcessFileInfoVO vo = new ProcessFileInfoVO();
        vo.setFileId(bo.getFileId());
        vo.setFileName(bo.getFileName());
        vo.setFileType(bo.getFileType());
        return vo;
    }

}
