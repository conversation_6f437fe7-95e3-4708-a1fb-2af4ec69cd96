package com.timevale.saasbiz.rest.converter;

import com.timevale.saasbiz.model.bean.cert.dto.output.GetCertOutputDTO;
import com.timevale.saasbiz.rest.bean.cert.response.GetCertResponse;

/**
 * 证书相关接口响应数据转换类
 *
 * <AUTHOR>
 * @since 2023-05-10
 */
public class CertResponseConverter {

    /**
     * 转换查询证书响应数据
     *
     * @param outputDTO 证书信息
     * @return 查询证书信息结果
     */
    public static GetCertResponse convert2GetCertResponse(GetCertOutputDTO outputDTO) {
        if (null == outputDTO) {
            return null;
        }
        GetCertResponse response = new GetCertResponse();
        response.setCertName(outputDTO.getCertName());
        response.setCaName(outputDTO.getCaName());
        response.setIdNumber(outputDTO.getIdNumber());
        response.setSn(outputDTO.getSn());
        response.setValidStartTime(outputDTO.getValidStartTime());
        response.setValidEndTime(outputDTO.getValidEndTime());
        response.setIssueImageUrls(outputDTO.getIssueImageUrls());
        response.setIssuePdfUrl(outputDTO.getIssuePdfUrl());
        response.setContext(outputDTO.getContext());
        response.setContextFormat(outputDTO.getContextFormat());
        return response;
    }
}
