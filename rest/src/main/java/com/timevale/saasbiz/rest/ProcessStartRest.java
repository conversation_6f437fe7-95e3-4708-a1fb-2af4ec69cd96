package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saasbiz.aop.Idempotent;
import com.timevale.saasbiz.model.bean.itsm.AppInfoDTO;
import com.timevale.saasbiz.model.bean.novice.dto.output.CheckNoviceOperateOutputDTO;
import com.timevale.saasbiz.model.bean.process.dto.input.ProcessStartInputDTO;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessStartOutputDTO;
import com.timevale.saasbiz.model.enums.AppConfigEnum;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.model.utils.AppConfigUtil;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.process.request.ProcessStartRequest;
import com.timevale.saasbiz.rest.bean.process.response.ProcessStartResponse;
import com.timevale.saasbiz.rest.converter.ProcessStartResponseConverter;
import com.timevale.saasbiz.rest.converter.request.ProcessStartInputDTOConverter;
import com.timevale.saasbiz.service.itsm.ItsmService;
import com.timevale.saasbiz.service.novice.NoviceOperateService;
import com.timevale.saasbiz.service.process.ProcessStartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.enums.novice.NoviceOperateTypeEnum.COMPLETE_EXPERIENCE_START;

/**
 * <AUTHOR>
 * @since 2023-05-21
 */
@Api(tags = "合同流程发起")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class ProcessStartRest {

    @Autowired ItsmService itsmService;
    @Autowired NoviceOperateService noviceOperateService;
    @Autowired ProcessStartService processStartService;

    /**
     * 流程体验签署发起
     *
     * @param request 发起入参
     */
    @Idempotent(keyPath = "request.requestNo", operation = "process_start")
    @ApiOperation(value = "流程体验签署发起", httpMethod = "POST")
    @RestMapping(path = "/processes/experience/start", method = RequestMethod.POST)
    public RestResult<ProcessStartResponse> experienceStart(
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody ProcessStartRequest request) {
        // 判断主体的体验签署是否已经完成
        String noviceOperateName = COMPLETE_EXPERIENCE_START.getName();
        CheckNoviceOperateOutputDTO noviceOperate =
                noviceOperateService.checkNoviceOperate(subjectId, noviceOperateName);
        // 如果已经发起过体验签署流程， 则报错提示
        if (noviceOperate.isDone()) {
            throw new SaasBizException(SaasBizResultCode.EXPERIENCE_START_ALREADY_DONE);
        }

        // 发起前置校验并处理请求参数
        request.checkRequest();

        // 组装发起业务请求参数
        ProcessStartInputDTO inputDTO =
                ProcessStartInputDTOConverter.convertProcessStartInputDTO(subjectId, request);

        // 体验签署场景下，默认付费方为标准签开发者账号
        String appId = AppConfigUtil.getString(AppConfigEnum.SAAS_PROJECT_ID);
        AppInfoDTO appInfoDTO = itsmService.checkApp(appId);
        inputDTO.setPayerAccountId(appInfoDTO.getOid());
        // 发起流程
        ProcessStartOutputDTO startResult = processStartService.startProcess(inputDTO);
        // 组装返回结果
        ProcessStartResponse response =
                ProcessStartResponseConverter.buildProcessStartResponse(startResult);
        // 标识体验签署流程已发起
        noviceOperateService.completeNoviceOperate(subjectId, noviceOperateName);
        // 返回发起结果
        return RestResult.success(response);
    }
}
