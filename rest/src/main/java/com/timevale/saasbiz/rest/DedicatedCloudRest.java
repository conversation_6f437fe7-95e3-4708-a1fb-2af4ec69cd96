package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.dedicatedcloud.dto.DedicatedCloudAuthRelationDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.dto.DedicatedCloudConfigDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.dto.DedicatedCloudDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.input.DedicatedCloudCreateInputDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.input.DedicatedCloudUpdateInputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.OpenAppDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.request.DedicatedCloudCreateRequest;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.request.DedicatedCloudListRequest;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.request.DedicatedCloudUpdateRequest;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.request.DedicatedCloudUpdateStatusRequest;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.response.*;
import com.timevale.saasbiz.rest.bean.usercenter.SubjectOpenAppResponse;
import com.timevale.saasbiz.rest.converter.DedicatedCloudResetConverter;
import com.timevale.saasbiz.rest.converter.OpenPlatformConvertor;
import com.timevale.saasbiz.service.dedicatedcloud.DedicatedCloudService;
import com.timevale.saasbiz.service.openplatform.OpenPlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2024/2/1 10:20
 */
@Api(tags = "专属云")
@ExternalService
@RestMapping(path = "/v1/dedicated-cloud")
public class DedicatedCloudRest {

    @Autowired private DedicatedCloudService dedicatedCloudService;
    @Autowired private OpenPlatformService openPlatformService;

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-获取配置")
    @RestMapping(path = "/config", method = RequestMethod.GET)
    public RestResult<DedicatedCloudConfigResponse> config() {
        DedicatedCloudConfigDTO configDTO = dedicatedCloudService.getConfig(RequestContextExtUtils.getTenantId());
        DedicatedCloudConfigResponse response = new DedicatedCloudConfigResponse();
        response.setCreateAppUrl(configDTO.getCreateAppUrl());
        response.setDedicatedCloudId(configDTO.getDedicatedCloudId()); 
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-创建")
    @RestMapping(path = "/create", method = RequestMethod.POST)
    public RestResult<DedicatedCloudResponse> create(@RequestBody DedicatedCloudCreateRequest request) {
        DedicatedCloudCreateInputDTO inputDTO = DedicatedCloudResetConverter.createConvert(request);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        DedicatedCloudResponse response = new DedicatedCloudResponse();
        response.setDedicatedCloudId(dedicatedCloudService.create(inputDTO));
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-更新")
    @RestMapping(path = "/update", method = RequestMethod.POST)
    public RestResult<Void> update(@RequestBody DedicatedCloudUpdateRequest request) {

        DedicatedCloudUpdateInputDTO inputDTO = DedicatedCloudResetConverter.updateConvert(request);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        dedicatedCloudService.update(inputDTO);
        return RestResult.success(null);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-更新状态")
    @RestMapping(path = "/update-status", method = RequestMethod.POST)
    public RestResult<Void> updateStatus(@RequestBody DedicatedCloudUpdateStatusRequest request) {
        dedicatedCloudService.updateStatus(RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                request.getDedicatedCloudId(),
                request.getStatus());
        return RestResult.success(null);
    }

    /**
     * 接口废弃， 统一使用OpenPlatformRest中的/v2/open-platform/org-open-app接口
     * @param subjectOid
     * @return
     */
    @Deprecated
    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-查询企业下的appId")
    @RestMapping(path = "/subject-open-app", method = RequestMethod.GET)
    public RestResult<SubjectOpenAppResponse> subjectOpenApp(@RequestParam(required = false) String subjectOid) {
        List<OpenAppDTO> appDTOList =
                openPlatformService.appList(RequestContextExtUtils.getTenantId(), subjectOid);
        SubjectOpenAppResponse response = new SubjectOpenAppResponse();
        response.setList(OpenPlatformConvertor.openAppConvert(appDTOList));
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-查询授权企业下关联企业")
    @RestMapping(path = "/org-auth-relation-list", method = RequestMethod.GET)
    public RestResult<DedicatedCloudAuthRelationResponse> authRelationList() {
        List<DedicatedCloudAuthRelationDTO> list =
                dedicatedCloudService.authRelationList(RequestContextExtUtils.getTenantId());
        DedicatedCloudAuthRelationResponse response = new DedicatedCloudAuthRelationResponse();
        response.setList(DedicatedCloudResetConverter.authRelationConvert(list));
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-查询企业下专属云列表")
    @RestMapping(path = "/list", method = RequestMethod.POST)
    public RestResult<DedicatedCloudListResponse> list(@RequestBody DedicatedCloudListRequest request) {
        List<DedicatedCloudDTO> list = dedicatedCloudService.list(RequestContextExtUtils.getTenantId());
        DedicatedCloudListResponse response = new DedicatedCloudListResponse();
        response.setList(DedicatedCloudResetConverter.dedicatedCloudConvert(list));
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-查询详情")
    @RestMapping(path = "/detail", method = RequestMethod.GET)
    public RestResult<DedicatedCloudVO> detail(@RequestParam String dedicatedCloudId) {
        DedicatedCloudDTO dedicatedCloudDTO =
                dedicatedCloudService.detail(RequestContextExtUtils.getTenantId(), dedicatedCloudId);
        return RestResult.success(DedicatedCloudResetConverter.dedicatedCloudConvert(dedicatedCloudDTO));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.DEDICATED_CLOUD,
            privilegeKey = PrivilegeOperationConstants.ALL)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.DEDICATED_CLOUD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("专属云-下载配置")
    @RestMapping(path = "/download-config", method = RequestMethod.GET)
    public RestResult<DedicatedCloudDownloadConfigResponse> downloadConfig(@RequestParam String dedicatedCloudId) {
        DedicatedCloudDownloadConfigResponse response = new DedicatedCloudDownloadConfigResponse();
        response.setUrl(dedicatedCloudService.downloadConfig(RequestContextExtUtils.getTenantId(), dedicatedCloudId));
        return RestResult.success(response);
    }

}
