package com.timevale.saasbiz.rest.bean.contractcategory.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.ContractCategoryListVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分页查询合同类型列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("分页查询合同类型列表响应数据")
public class PageQueryContractCategoriesResponse extends ToString {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("合同类型列表显示信息")
    private List<ContractCategoryListVO> categories;
}
