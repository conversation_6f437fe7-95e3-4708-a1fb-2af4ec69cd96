package com.timevale.saasbiz.rest.bean.contractcategory.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量查询合同类型详情请求参数
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@ApiModel("批量查询合同类型详情请求参数")
public class BatchQueryContractCategoryDetailRequest extends ToString {

    @ApiModelProperty("合同类型id列表")
    @NotEmpty(message = "合同类型id列表不能为空")
    private List<String> categoryIds;

    @ApiModelProperty("是否返回提取字段列表")
    private boolean withFields;
}
