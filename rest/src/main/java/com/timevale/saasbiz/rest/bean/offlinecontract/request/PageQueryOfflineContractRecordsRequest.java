package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 分页查询线下合同导入记录
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class PageQueryOfflineContractRecordsRequest extends ToString {

    @ApiModelProperty(value = "页码", required = true, example = "1")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @ApiModelProperty(value = "每页数据大小", required = true, example = "10")
    @NotNull(message = "每页数据大小不能为空")
    private Integer pageSize;
}
