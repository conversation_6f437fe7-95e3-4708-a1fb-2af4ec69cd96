package com.timevale.saasbiz.rest.bean.offlinecontract.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractExcelInfoVO;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractProcessInfoVO;
import lombok.Data;

import java.util.List;

/**
 * 解析线下合同录入合同信息的excel响应数据
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class ParseOfflineContractExcelResponse extends ToString {

    /** 导入成功的合同信息数 */
    private int successCount;

    /** 导入失败的合同信息数 */
    private int failCount;

    /** 失败下载地址 */
    private String failDownloadUrl;

    /** 合同信息列表 */
    private List<OfflineContractExcelInfoVO> processInfos;
}
