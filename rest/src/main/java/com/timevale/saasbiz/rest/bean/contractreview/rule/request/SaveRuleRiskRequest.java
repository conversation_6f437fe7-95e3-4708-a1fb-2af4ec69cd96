package com.timevale.saasbiz.rest.bean.contractreview.rule.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaveRuleRiskRequest extends ToString {
    @ApiModelProperty("审查点ID")
    private String ruleRiskId;

    @ApiModelProperty("规则ID")
    private String ruleId;

    @ApiModelProperty("审查点描述")
    @Length(max = 1000, message = "审查点描述不能超过1000字符")
    private String ruleDescription;

    @ApiModelProperty("审查点逻辑列表")
    private List<String> ruleBasis;

    @ApiModelProperty("审查建议")
    @Length(max = 200, message = "审查建议不能超过200字符")
    private String suggestion;

    @ApiModelProperty("审查关键词")
    private List<String> keyword;

    @ApiModelProperty("审查相关条款")
    private String similarTerms;
}
