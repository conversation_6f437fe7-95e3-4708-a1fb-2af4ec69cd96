package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.RelationAndPrivilegeCheck;
import com.timevale.saasbiz.model.bean.watermark.dto.input.GenerateWatermarkSnapshotInputDTO;
import com.timevale.saasbiz.model.bean.watermark.dto.input.SaveWatermarkTemplateInputDTO;
import com.timevale.saasbiz.model.bean.watermark.dto.input.WatermarkTemplatePageInputDTO;
import com.timevale.saasbiz.model.bean.watermark.dto.output.WatermarkTemplateDetailOutputDTO;
import com.timevale.saasbiz.model.bean.watermark.dto.output.WatermarkTemplatePageOutputDTO;
import com.timevale.saasbiz.model.constants.AuthRelationBizSceneConstants;
import com.timevale.saasbiz.model.constants.BizConstants;
import com.timevale.saasbiz.model.enums.PrivilegeOperationEnum;
import com.timevale.saasbiz.model.enums.PrivilegeResourceEnum;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;

import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.watermark.request.GenerateWatermarkSnapshotRequest;
import com.timevale.saasbiz.rest.bean.watermark.request.SaveWatermarkTemplateRequest;
import com.timevale.saasbiz.rest.bean.watermark.request.WatermarkTemplatePageRequest;
import com.timevale.saasbiz.rest.bean.watermark.response.*;
import com.timevale.saasbiz.rest.converter.WatermarkRestInOutConverter;
import com.timevale.saasbiz.service.watermark.WatermarkTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

import static com.timevale.mandarin.base.enums.BaseResultCodeEnum.NO_OPERATE_PERMISSION;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_RESOURCE_TENANT_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 水印模板管理controller
 * @date Date : 2023年04月13日 20:08
 */
@Api(tags = "水印模板管理")
@Validated
@ExternalService
@RestMapping(path = "/v2/watermark-template")
@Slf4j
public class WatermarkTemplateRest extends BaseRest{

    @Autowired WatermarkTemplateService watermarkTemplateService;



    /**
     * 分页查询当前租户下的水印模板列表
     *
     * @param tenantId
     * @param operatorId
     * @param request
     * @return
     */
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @RestMapping(path = "/page-list", method = RequestMethod.POST)
    @ApiOperation(value = "分页查询当前租户下的水印模板列表", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<WatermarkTemplatePageResponse> listWatermarkTemplates(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId,
            @RequestBody @Validated WatermarkTemplatePageRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 权限校验
        //入参转换
        WatermarkTemplatePageInputDTO pageInputDTO = WatermarkRestInOutConverter.pageRequest2pageInput(request, resourceTenantId, operatorId);
        //业务查询
        WatermarkTemplatePageOutputDTO pageOutputDTO = watermarkTemplateService.pageQueryWatermarkTemplates(pageInputDTO);
        //出参转换
        return RestResult.success(WatermarkRestInOutConverter.pageOutput2PageResponse(pageOutputDTO));
    }


    /**
     * 保存水印模板
     *
     * @param tenantId
     * @param operatorId
     * @param request
     * @return
     */
    @RestMapping(path = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "保存水印模板", httpMethod = "POST")
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.SAAS_WATERMARK)
    public RestResult<SaveWatermarkTemplateResponse> saveWatermarkTemplate(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId,
            @RequestBody @Validated SaveWatermarkTemplateRequest request) {
        // 权限校验
        if(!checkUserPrivilege(tenantId, operatorId, PrivilegeResourceEnum.WATERMARK, PrivilegeOperationEnum.UPDATE, false)){
            //创建或更新权限都没有则报错
            throw new SaasBizException(SaasBizResultCode.HAS_NO_PRIVILEGE);
        }
        //入参转换
        SaveWatermarkTemplateInputDTO input = WatermarkRestInOutConverter.saveRequest2SaveInput(request, tenantId, operatorId);
        // 业务逻辑处理
        String watermarkTemplateId = watermarkTemplateService.saveWatermarkTemplate(input);
        return RestResult.success(SaveWatermarkTemplateResponse.builder().watermarkId(watermarkTemplateId).build());
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @RestMapping(path = "/snapshot", method = RequestMethod.POST)
    @ApiOperation(value = "生成水印模板临时快照", httpMethod = "POST")
    public RestResult<GenerateWatermarkSnapshotResponse> generateWatermarkSnapshot(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId,
            @RequestBody @Validated GenerateWatermarkSnapshotRequest request) {
        //入参转换
        GenerateWatermarkSnapshotInputDTO input = WatermarkRestInOutConverter.saveRequest2SnapshotInput(request, tenantId, operatorId);
        // 业务逻辑处理
        String watermarkSnapshotId = watermarkTemplateService.generateWatermarkSnapShoot(input);
        return RestResult.success(GenerateWatermarkSnapshotResponse.builder().watermarkSnapshotId(watermarkSnapshotId).build());
    }

    /**
     * 修改水印模板状态 (启/停)
     *
     * @param tenantId    当前租户空间OID
     * @param operatorId  当前操作人OID
     * @param watermarkId 水印模板ID
     * @return 响应实体
     */
    @RestMapping(path = "/{watermarkId}/status-change", method = RequestMethod.POST)
    @ApiOperation(value = "修改水印模板状态 (启/停)", httpMethod = "POST")
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.SAAS_WATERMARK)
    public RestResult changeWatermarkTemplateStatus(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId,
            @PathVariable("watermarkId") @NotBlank(message = "水印ID不能为空") String watermarkId) {
        // 权限校验
        checkUserPrivilege(tenantId, operatorId, PrivilegeResourceEnum.WATERMARK, PrivilegeOperationEnum.UPDATE,true);
        // 业务逻辑处理
        watermarkTemplateService.changeWatermarkTemplateStatus(tenantId, operatorId, watermarkId);
        return RestResult.success();
    }

    /**
     * 删除水印模板
     *
     * @param tenantId    当前租户空间OID
     * @param operatorId  当前操作人OID
     * @param watermarkId 水印模板ID
     * @return 响应实体
     */
    @RestMapping(path = "/{watermarkId}/delete", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除水印模板", httpMethod = "DELETE")
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.SAAS_WATERMARK)
    public RestResult deleteWatermarkTemplate(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId,
            @PathVariable("watermarkId") @NotBlank(message = "水印ID不能为空") String watermarkId) {
        // 权限校验
        checkUserPrivilege(tenantId, operatorId, PrivilegeResourceEnum.WATERMARK, PrivilegeOperationEnum.UPDATE,true);
        // 业务逻辑处理，调用
        watermarkTemplateService.deleteWatermarkTemplate(tenantId, operatorId, watermarkId);
        return RestResult.success();
    }


    /**
     * 查看水印中的图片
     *
     * @param tenantId    当前租户空间OID
     * @param operatorId  当前操作人OID
     * @param watermarkId 水印模板ID
     * @return 响应实体
     */
    @RestMapping(path = "/{watermarkId}/view-image", method = RequestMethod.GET)
    @ApiOperation(value = "查看水印中的图片", httpMethod = "GET")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<ViewWatermarkImageResponse> viewWatermarkImage(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId,
            @PathVariable("watermarkId") @NotBlank(message = "水印ID不能为空") String watermarkId,
            @RequestParam("filekey") @NotBlank(message = "filekey不能为空") String filekey) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        ViewWatermarkImageResponse response = new ViewWatermarkImageResponse();
        // 业务逻辑处理
        response.setImageUrl(watermarkTemplateService.viewWatermarkImage(resourceTenantId, operatorId, watermarkId, filekey));
        return RestResult.success(response);
    }


    /**
     * 查询水印模板详情
     * @param tenantId    当前租户空间OID
     * @param operatorId  当前操作人OID
     * @param watermarkId 水印模板ID
     * @return 响应实体
     */
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @RestMapping(path = "/{watermarkId}/detail", method = RequestMethod.GET)
    @ApiOperation(value = "查询水印模板详情", httpMethod = "GET")
    public RestResult<WatermarkTemplateDetailResponse> getWatermarkTemplateDetail(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId,
            @PathVariable("watermarkId") @NotBlank(message = "水印ID不能为空") String watermarkId) {
        // 如果非水印快照，则表示为水印模板， 需要进行成员鉴权
        if (!watermarkId.startsWith(BizConstants.WATERMARK_SNAP_SHOOT_PREFIX)) {
            // 校验当前用户是否企业成员
            boolean member = userCenterService.checkMemberInSubject(tenantId, operatorId);
            // 如果非企业成员，报错
            if (!member) {
                throw new SaasBizException(NO_OPERATE_PERMISSION.getNCode(), "您不是该企业成员，请联系企业管理员加入企业。");
            }
        }
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 业务逻辑处理
        WatermarkTemplateDetailOutputDTO watermarkTemplateDetail = watermarkTemplateService.getWatermarkTemplateDetail(resourceTenantId, operatorId, watermarkId);
        // 响应体转换
        WatermarkTemplateDetailResponse response = WatermarkRestInOutConverter.detailOutput2DetailResponse(watermarkTemplateDetail);
        return RestResult.success(response);
    }

    /**
     * 预览水印模板，实际上就是给一个固定的图片地址
     *
     * @param tenantId    当前租户空间OID
     * @param operatorId  当前操作人OID
     * @param watermarkId 水印模板ID
     * @return 响应实体
     */
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @RestMapping(path = "/{watermarkId}/preview", method = RequestMethod.GET)
    @ApiOperation(value = "预览水印模板", httpMethod = "GET")
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.SAAS_WATERMARK)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<PreviewWatermarkResponse> previewWatermark(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId,
            @RequestHeader(value = HEADER_LANGUAGE,required = false) String language,
            @PathVariable("watermarkId") @NotBlank(message = "水印ID不能为空") String watermarkId) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 业务逻辑处理，调用
        PreviewWatermarkResponse response = new PreviewWatermarkResponse();
        response.setPreviewImageUrl(watermarkTemplateService.previewWatermark(resourceTenantId, operatorId, watermarkId, language));
        return RestResult.success(response);
    }

    /**
     * 生成默认系统水印模板
     * 每个用户最多执行一次
     * @param tenantId
     * @param operatorId
     * @return
     */
    @RestMapping(path = "/sysgen", method = RequestMethod.POST)
    @ApiOperation(value = "生成默认系统水印模板", httpMethod = "POST")
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.SAAS_WATERMARK)
    public RestResult sysgenWatermarkTemplate(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorId) {
        watermarkTemplateService.sysgenWatermarkTemplate(tenantId, operatorId);
        return RestResult.success();
    }

    /**
     * 查询水印是否被配置
     *
     * @param tenantId    当前租户空间OID
     * @param watermarkId 水印模板ID
     * @return 响应实体 true: 被配置  false: 未配置
     */
    @RestMapping(path = "/{watermarkId}/configured", method = RequestMethod.GET)
    @ApiOperation(value = "查询水印是否被配置", httpMethod = "GET")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<ConfiguredWatermarkResponse> configured(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体oid不能为空") String tenantId,
            @PathVariable("watermarkId") @NotBlank(message = "水印ID不能为空") String watermarkId) {
        Boolean configured = watermarkTemplateService.configured(tenantId, watermarkId);
        ConfiguredWatermarkResponse response = new ConfiguredWatermarkResponse();
        response.setConfigured(configured);
        return RestResult.success(response);
    }

}
