package com.timevale.saasbiz.rest.bean.offlinecontract.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractExtractConfigVO;
import lombok.Data;

/**
 * 查询线下合同导入记录基本信息效应数据
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class QueryOfflineContractRecordInfoResponse extends ToString {
    /** 导入记录id */
    private String recordId;
    /** 归档菜单id */
    private String menuId;
    /** 归档菜单名称 */
    private String menuName;
    /** 归档菜单路径 */
    private String menuPath;
    /** 导入方式 */
    private String importWay;
    /** 合同信息提取方式 */
    private String extractWay;
    /** 合同数量 */
    private long contractSize;
    /** 导入成功数量 */
    private long successSize;
    /** 导入失败数量 */
    private long failedSize;
    /** 合同信息提取配置 */
    private OfflineContractExtractConfigVO extractConfig;
    /** 状态 */
    private String status;
}
