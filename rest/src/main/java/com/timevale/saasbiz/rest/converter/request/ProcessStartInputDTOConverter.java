package com.timevale.saasbiz.rest.converter.request;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.common.service.model.StartBizProcessModel;
import com.timevale.contractmanager.common.service.model.StartProcessModel;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.process.dto.input.ProcessStartInputDTO;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.process.request.ProcessStartRequest;
import com.timevale.saasbiz.rest.bean.process.vo.ParticipantInstanceVO;
import com.timevale.saasbiz.rest.bean.process.vo.ParticipantVO;
import com.timevale.saasbiz.rest.bean.process.vo.ProcessFileVO;
import com.timevale.saasbiz.rest.bean.process.vo.UserVO;
import lombok.extern.slf4j.Slf4j;

/**
 * 发起流程参数转换
 *
 * <AUTHOR>
 * @since 2023-05-22
 */
@Slf4j
public class ProcessStartInputDTOConverter {

    /**
     * 组装发起流程参数
     *
     * @param request
     * @return
     */

    public static ProcessStartInputDTO convertProcessStartInputDTO(
            String tenantId, ProcessStartRequest request) {
        // 组装发起业务请求参数
        ProcessStartInputDTO inputDTO = new ProcessStartInputDTO();
        inputDTO.setInitiatorAccountId(request.getInitiatorAccountId());
        inputDTO.setInitiatorSubjectId(tenantId);
        inputDTO.setProcessName(request.getTaskName());
        inputDTO.setProcessComment(request.getTaskComment());
        inputDTO.setNoticeType(request.getNoticeType());
        inputDTO.setSignEndTime(request.getSignEndTime());
        inputDTO.setSignValidityConfig(request.getSignValidityConfig());
        inputDTO.setFileEndTime(request.getFileEndTime());
        inputDTO.setFileValidityConfig(request.getFileValidityConfig());
        inputDTO.setRedirectUrl(request.getRedirectUrl());
        inputDTO.setSignedNoticeUrl(request.getSignedNoticeUrl());
        inputDTO.setProcessNotifyUrl(request.getProcessNotifyUrl());
        inputDTO.setToken(request.getToken());
        inputDTO.setBusinessType(request.getBusinessType());
        inputDTO.setBusinessRemark(request.getBusinessRemark());
        inputDTO.setRefFlowTemplateId(request.getRefFlowTemplateId());
        inputDTO.setOriginFlowTemplateId(request.getOriginFlowTemplateId());
        inputDTO.setApproveTemplateId(request.getApproveTemplateId());
        inputDTO.setOriginProcessId(request.getOriginProcessId());
        inputDTO.setOriginFileIds(request.getOriginFileIds());
        inputDTO.setBatchDropSeal(request.getBatchDropSeal());
        inputDTO.setUseWatermarkTemplateId(request.getUseWatermarkTemplateId());
        inputDTO.setFlowTemplateId(request.getFlowTemplateId());
        inputDTO.setPlatform(request.getPlatform());
        inputDTO.setSkipFill(request.getSkipFill());
        inputDTO.setRelationProcessIds(request.getRelationProcessIds());
        inputDTO.setSkipStartValid(request.getSkipStartValid());
        inputDTO.setBizGroupId(request.getBizGroupId());
        inputDTO.setRemarks(request.getRemarks());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setIsvAppId(RequestContextExtUtils.getIsvAppId());
        inputDTO.setDingCorpId(RequestContextExtUtils.getDingCorpId());
        String clientId = RequestContextExtUtils.getClientId();
        ClientEnum clientEnum = ClientEnum.valueOfType(clientId);
        // 如果未指定产品端， Saas发起场景下默认WEB端
        if (null == clientEnum) {
            log.info("unknown clientId: {}", clientId);
            clientEnum = ClientEnum.WEB;
        }
        inputDTO.setClientId(clientEnum.getClientId());

        inputDTO.setContracts(Lists.newArrayList());
        inputDTO.setAttachments(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(request.getFiles())) {
            for (ProcessFileVO file : request.getFiles()) {
                StartBizProcessModel.FileBean fileBean = new StartBizProcessModel.FileBean();
                fileBean.setFileId(file.getFileId());
                fileBean.setFileName(file.getFileName());
                fileBean.setFileType(file.getFileType());
                fileBean.setFrom(file.getFrom());
                fileBean.setOrder(file.getOrder());
                fileBean.setContractNo(file.getContractNo());
                if (ProcessFileType.isContract(file.getFileType())) {
                    inputDTO.getContracts().add(fileBean);
                    continue;
                }
                inputDTO.getAttachments().add(fileBean);
            }
        }
        inputDTO.setParticipants(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(request.getParticipants())) {
            for (ParticipantVO participantVO : request.getParticipants()) {
                StartProcessModel.Participant participant = new StartProcessModel.Participant();
                participant.setParticipantId(participantVO.getParticipantId());
                participant.setParticipantLabel(participantVO.getParticipantLabel());
                participant.setParticipantSubjectType(participantVO.getParticipantSubjectType());
                participant.setSealType(participantVO.getSealType());
                participant.setType(participantVO.getType());
                participant.setRole(participantVO.getRole());
                participant.setRoleSet(participantVO.getRoleSet());
                participant.setSignRequirements(participantVO.getSignRequirements());
                participant.setSignOrder(participantVO.getSignOrder());
                participant.setFillOrder(participantVO.getFillOrder());
                participant.setInstances(Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(participantVO.getInstances())) {
                    for (ParticipantInstanceVO instanceVO : participantVO.getInstances()) {
                        StartProcessModel.ParticipantInstance instance =
                                new StartProcessModel.ParticipantInstance();
                        instance.setSubTaskName(instanceVO.getSubTaskName());
                        instance.setPreFillValues(instanceVO.getPreFillValues());
                        // 填充参与方用户信息
                        fillUserInfo(instanceVO, instance);
                        // 追加参与方实例
                        participant.getInstances().add(instance);
                    }
                }
                inputDTO.getParticipants().add(participant);
            }
        }
        inputDTO.setCcs(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(request.getCcs())) {
            for (UserVO userVO : request.getCcs()) {
                ProcessStartInputDTO.ParticipantAccount cc =
                        new ProcessStartInputDTO.ParticipantAccount();
                // 填充抄送人用户信息
                fillUserInfo(userVO, cc);
                // 追加抄送人
                inputDTO.getCcs().add(cc);
            }
        }
        ProcessStartInputDTO.ProcessExtraConfig extraConfig =
                new ProcessStartInputDTO.ProcessExtraConfig();
        extraConfig.setStartScene(request.getScene());
        extraConfig.setStartType(request.getStartType());
        extraConfig.setSecretType(request.getSecretType());
        extraConfig.setVisibleAccounts(request.getVisibleAccounts());
        extraConfig.setInitiatorDeptId(request.getInitiatorDeptId());
        inputDTO.setExtraConfig(extraConfig);

        return inputDTO;
    }

    /**
     * 填充用户信息
     *
     * @param userVO
     * @param participantAccount
     */
    private static void fillUserInfo(
            UserVO userVO, ProcessStartInputDTO.ParticipantAccount participantAccount) {
        participantAccount.setAccount(userVO.getAccount());
        participantAccount.setAccountOid(userVO.getAccountOid());
        participantAccount.setAccountName(userVO.getAccountName());
        participantAccount.setAccountNick(userVO.getAccountNick());
        participantAccount.setSubjectId(userVO.getSubjectId());
        participantAccount.setSubjectName(userVO.getSubjectName());
        participantAccount.setSubjectType(userVO.getSubjectType());
    }
}
