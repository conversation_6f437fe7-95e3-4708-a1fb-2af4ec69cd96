package com.timevale.saasbiz.rest.bean.offlinecontract.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 批量查询线下合同导入记录基本信息效应数据
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
public class BatchQueryOfflineContractRecordInfoResponse extends ToString {

    /** 线下合同导入记录列表 */
    private List<QueryOfflineContractRecordInfoResponse> records;
}
