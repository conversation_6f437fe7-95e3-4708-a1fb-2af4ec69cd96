package com.timevale.saasbiz.rest.bean.contractreview.rule.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class BatchSaveRuleRequest extends ToString {
    @NotEmpty(message = "规则列表不能为空")
    @Size(max = 50, message = "规则列表不允许超过50")
    private List<@Valid SaveRuleRequest> ruleList;
}
