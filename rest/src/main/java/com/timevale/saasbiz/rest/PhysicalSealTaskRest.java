package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saasbiz.model.bean.physicalseal.dto.input.QueryPhysicalListInputDTO;
import com.timevale.saasbiz.model.bean.physicalseal.dto.input.QueryPhysicalSealFlagInputDTO;
import com.timevale.saasbiz.model.bean.physicalseal.dto.output.QueryPhysicalListOutputDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.physicalseal.request.QueryPhysicalSealListRequest;
import com.timevale.saasbiz.rest.bean.physicalseal.response.QueryPhysicalSealFlagResponse;
import com.timevale.saasbiz.rest.bean.physicalseal.response.QueryPhysicalSealListResponse;
import com.timevale.saasbiz.rest.converter.QueryPhysicalSealListResponseConverter;
import com.timevale.saasbiz.service.physicalseal.PhysicalSealService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.constraints.NotBlank;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;

/**
 * 物理印章查询Rest
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Api(tags = "物理印章查询")
@Validated
@ExternalService
@RestMapping(path = "/v1/physical-seal")
@Slf4j
public class PhysicalSealTaskRest {

    @Autowired
    private PhysicalSealService physicalSealService;

    @ApiOperation("查询物理印章展示标识")
    @RestMapping(path = "query-physical-seal-flag", method = RequestMethod.GET)
    public RestResult<QueryPhysicalSealFlagResponse> queryPhysicalSealFlag(
            @ApiParam(value = "登录用户oid", required = true) @RequestHeader(value = HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorOid) {
        QueryPhysicalSealFlagInputDTO queryPhysicalSealFlagInputDTO = new QueryPhysicalSealFlagInputDTO();
        queryPhysicalSealFlagInputDTO.setOid(operatorOid);
        Boolean flag = physicalSealService.queryPhysicalSealFlag(queryPhysicalSealFlagInputDTO);
        QueryPhysicalSealFlagResponse response = new QueryPhysicalSealFlagResponse();
        response.setFlag(flag);
        return RestResult.success(response);
    }

    @ApiOperation("查询物理用印列表数据")
    @RestMapping(path = "query-physical-seal-list", method = RequestMethod.POST)
    public RestResult<QueryPhysicalSealListResponse> queryPhysicalSealList(
            @ApiParam(value = "登录用户oid", required = true) @RequestHeader(value = HEADER_OPERATOR_ID) @NotBlank(message = "用户oid不能为空") String operatorOid,
            @ApiParam(value = "查询物理用印的参数", required = true) @RequestBody QueryPhysicalSealListRequest request) {
        QueryPhysicalListInputDTO queryPhysicalListInputDTO = buildQueryPhysicalListInputDTO(request, operatorOid);
        QueryPhysicalListOutputDTO queryPhysicalListOutputDTO = physicalSealService.queryPhysicalSealList(queryPhysicalListInputDTO);
        return RestResult.success(QueryPhysicalSealListResponseConverter.convertPhysicalSealListResponse(queryPhysicalListOutputDTO));
    }

    /**
     * 构建service查询的参数
     *
     * @param request
     * @param operatorOid
     * @return
     */
    private QueryPhysicalListInputDTO buildQueryPhysicalListInputDTO(QueryPhysicalSealListRequest request, String operatorOid) {
        QueryPhysicalListInputDTO queryPhysicalListInputDTO = new QueryPhysicalListInputDTO();
        queryPhysicalListInputDTO.setPage(request.getPage());
        queryPhysicalListInputDTO.setPageSize(request.getPageSize());
        queryPhysicalListInputDTO.setIsTakeOut(request.getIsTakeOut());
        queryPhysicalListInputDTO.setCommonSearch(request.getCommonSearch());
        queryPhysicalListInputDTO.setPhySealApprovalStartTime(request.getPhySealApprovalStartTime());
        queryPhysicalListInputDTO.setPhySealApprovalEndTime(request.getPhySealApprovalEndTime());
        queryPhysicalListInputDTO.setPhySealStatus(request.getPhySealStatus());
        queryPhysicalListInputDTO.setOid(operatorOid);
        return queryPhysicalListInputDTO;
    }


}
