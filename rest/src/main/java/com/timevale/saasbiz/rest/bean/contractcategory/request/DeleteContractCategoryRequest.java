package com.timevale.saasbiz.rest.bean.contractcategory.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 删除合同类型请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("删除合同类型请求参数")
public class DeleteContractCategoryRequest extends ToString {

    @ApiModelProperty("合同类型id列表")
    @NotEmpty(message = "合同类型id列表不能为空")
    private List<String> categoryIds;
}
