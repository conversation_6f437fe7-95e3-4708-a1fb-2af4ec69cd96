package com.timevale.saasbiz.rest.bean.contractcategory.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ContractCategoryListVO extends BaseContractCategoryListVO {

    @ApiModelProperty("关联流程模板名称列表")
    private List<String> flowTemplates;

    @ApiModelProperty("合同数量")
    private Integer contractSize;

    @ApiModelProperty("合同类型是否启用")
    private Boolean enable;
}
