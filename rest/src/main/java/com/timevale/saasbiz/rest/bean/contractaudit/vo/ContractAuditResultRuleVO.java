package com.timevale.saasbiz.rest.bean.contractaudit.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Data
public class ContractAuditResultRuleVO {
    @ApiModelProperty(value = "序号")
    private String ruleSeq;
    @ApiModelProperty(value = "规则标题")
    private String ruleTitle;
    @ApiModelProperty(value = "风险等级 pass 通过 high 高风险 medium 中风险 low 低风险")
    private String ruleLevel;
    @ApiModelProperty(value = "状态, waiting 等待中 fail 失败 done 完成")
    private String status;
    @ApiModelProperty(value = "是否通过")
    private Boolean pass;
    @ApiModelProperty(value = "风险列表")
    private List<ContractAuditResultRiskVO> subRisk;
}
