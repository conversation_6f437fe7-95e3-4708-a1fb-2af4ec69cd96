package com.timevale.saasbiz.rest;

import com.google.common.collect.Lists;
import com.timevale.footstone.seal.facade.enums.SealGrantTypeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizSceneEnum;
import com.timevale.saas.common.manage.common.service.model.base.BaseResult;
import com.timevale.saas.common.privilege.aspect.RelationAndPrivilegeCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import com.timevale.saasbiz.model.bean.seal.dto.input.*;
import com.timevale.saasbiz.model.bean.seal.dto.output.*;
import com.timevale.saasbiz.model.constants.*;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.seal.request.*;
import com.timevale.saasbiz.rest.bean.seal.response.*;
import com.timevale.saasbiz.rest.converter.SealResponseConvertor;
import com.timevale.saasbiz.service.seal.*;
import com.timevale.saasbiz.service.seal.vip.condition.BooleanMembershipCondition;
import com.timevale.saasbiz.service.seal.vip.condition.AuthAutoSignMembershipCondition;
import com.timevale.saasbiz.service.seal.vip.condition.EmptyCollectionMembershipCondition;
import com.timevale.saasbiz.service.vip.verify.ValidateMembershipService;
import com.timevale.saasbiz.service.seal.vip.condition.AuthForeverMembershipCondition;
import com.timevale.saasbiz.service.seal.vip.condition.BatchSealAuthMembershipCondition;
import com.timevale.saasbiz.service.vip.verify.model.BatchGrantSealVerifyModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR>
 * @since 2023-05-31 16:54
 */
@Validated
@Api(tags = "印章授权相关操作")
@ExternalService
@RestMapping(path = "/v1/saas-common/rules-grant/seals")
public class SealRuleGrantRest {
    @Resource
    private SealGrantConfirmService sealGrantConfirmService;
    @Resource
    private ValidateMembershipService validateMembershipService;
    @Resource
    private SealOrganizationService sealOrganizationService;
    @Resource
    private SealRuleGrantService sealRuleGrantService;
    @Resource
    private SecondSealGrantService secondSealGrantService;
    @Resource
    private SealManagerService sealManagerService;

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.QUERY)
    @ApiOperation(value = "授权企业/成员列表", httpMethod = "GET")
    @RestMapping(path = "/rule-grant-list", method = RequestMethod.GET)
    public RestResult<GetRuleGrantListResponse> getRuleGrantList(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @URIQueryParam GetRuleGrantListRequest request) {
        // 查询
        ListRuleGrantInputDTO inputDTO = new ListRuleGrantInputDTO();
        BeanUtils.copyProperties(request, inputDTO);
        inputDTO.setAppId(appId);
        inputDTO.setOrgId(StringUtils.defaultIfBlank(request.getSealOwnerOid(), request.getOrgId()));
        inputDTO.setAccountOid(accountId);
        inputDTO.setOperatorOid(accountId);
        ListRuleGrantOutputDTO outputDTO = sealRuleGrantService.listRuleGrant(inputDTO);
        if (Objects.isNull(outputDTO) || CollectionUtils.isEmpty(outputDTO.getGrantList())) {
            return RestResult.success(
                    new GetRuleGrantListResponse(0, false, Collections.emptyList()));
        }

        // 转换
        GetRuleGrantListResponse response = new GetRuleGrantListResponse();
        BeanUtils.copyProperties(outputDTO, response);
        return RestResult.success(response);
    }

    @ApiOperation(value = "被授权企业列表", httpMethod = "GET")
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.QUERY)
    @MultilingualTranslateMethod
    @RestMapping(path = "/get-rule-granted-list", method = RequestMethod.GET)
    public BaseResult<GetRuleGrantedListResponse> getRuleGrantedList(@URIQueryParam GetRuleGrantedListRequest request) {
        request.setOrgId(StringUtils.defaultIfBlank(request.getSealOwnerOid(), request.getOrgId()));
        GetRuleGrantedListInputDTO input = SealResponseConvertor.convertRuleGrantedListRequest(request);

        GetRuleGrantedListOutputDTO output = sealRuleGrantService.queryGrantedSealsList(input);

        GetRuleGrantedListResponse response = SealResponseConvertor.convertRuleGrantedListResponse(output);
        return BaseResult.success(response);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "一级授权批量删除", httpMethod = "POST")
    @RestMapping(path = "/delete-batch-grant", method = RequestMethod.POST)
    public RestResult<Void> deleteBatchGrant(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody DeleteBatchSealGrantRequest request) {

        DeleteBatchSealGrantInputDTO dto = new DeleteBatchSealGrantInputDTO();
        BeanUtils.copyProperties(request, dto);
        dto.setOrgOid(
                StringUtils.isBlank(request.getSealOwnerOid())
                        ? tenantId
                        : request.getSealOwnerOid());
        dto.setOperatorOid(accountId);
        dto.setTerminal(clientId);
        dto.setAppId(appId);

        validateMembershipService.validate(dto.getRuleGrantIds(), tenantId, FunctionCodeConstants.BATCH_DEL_AUTH, BatchSealAuthMembershipCondition.class);
        sealRuleGrantService.deleteBatchGrant(dto);

        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "二级授权批量删除", httpMethod = "POST")
    @RestMapping(path = "/delete-batch-second-grant", method = RequestMethod.POST)
    public RestResult<Void> deleteBatchSecondGrant(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody DeleteBatchSealSecondGrantRequest request) {

        DeleteBatchSecondSealGrantInputDTO dto = new DeleteBatchSecondSealGrantInputDTO();
        BeanUtils.copyProperties(request, dto);
        dto.setOrgOid(
                StringUtils.isBlank(request.getSealOwnerOid())
                        ? tenantId
                        : request.getSealOwnerOid());
        dto.setOperatorOid(accountId);
        dto.setTerminal(clientId);
        dto.setAppId(appId);

        validateMembershipService.validate(dto.getSecSealGrantBizIds(), tenantId, FunctionCodeConstants.BATCH_DEL_AUTH, BatchSealAuthMembershipCondition.class);
        secondSealGrantService.deleteBatchGrant(dto);

        return RestResult.success();
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "删除印章被授权信息", httpMethod = "DELETE")
    @RestMapping(path = "/do-delete-rule-granted", method = RequestMethod.DELETE)
    public BaseResult<Boolean> deleteRuleGranted(
            @ApiParam(name = "ruleGrantedId", value = "规则授权ID", required = true) @RequestParam String ruleGrantedId,
            @RequestParam(value = "sealOwnerOid", required = false) String sealOwnerOid) {
        sealRuleGrantService.deleteRuleGranted(
                RequestContextExtUtils.getAppId(),
                StringUtils.isBlank(sealOwnerOid)
                        ? RequestContextExtUtils.getTenantId()
                        : sealOwnerOid,
                RequestContextExtUtils.getOperatorId(),
                ruleGrantedId);
        return BaseResult.success(true);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.resourceOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "批量印章查询剩余可用授权数量", httpMethod = "POST")
    @RestMapping(path = "/get-batch-grant-num", method = RequestMethod.POST)
    public RestResult<BatchSealGrantNumResponse> getBatchSealGrantNum(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String operatorOid,
            @RequestBody BatchSealGrantNumRequest request) {
        if (CollectionUtils.isNotEmpty(request.getResourceBizIds())) {
            if (request.getResourceBizIds().stream().anyMatch(s -> StringUtils.contains(s, "\\*"))) {
                throw new SaasBizException(SaasBizResultCode.SAAS_ILLEGAL_PARAM, "包含非法字符：*");
            }
        }
        BatchSealGrantNumInputDto inputDto =
                SealResponseConvertor.convertBatchSealGrantNumInput(request);

        BatchSealGrantNumOutputDto outputDto = sealManagerService.getBatchSealMaxGrantNum(inputDto);

        return RestResult.success(
                SealResponseConvertor.convertBatchSealGrantNumResponse(outputDto));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.resourceOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.ORG_SEAL_GRANT)
    @ApiOperation(value = "批量印章授权（一二级授权通用）", httpMethod = "POST")
    @RestMapping(path = "/batch-grant", method = RequestMethod.POST)
    public RestResult<BatchSealGrantResponse> batchSealGrant(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String operatorOid,
            @RequestBody BatchSealGrantRequest request) {

        BatchSealGrantInputDto inputDto = SealResponseConvertor.convertBatchSealGrantInput(request);
        inputDto.setAppId(appId);
        inputDto.setClientId(clientId);
        inputDto.setOperatorTenantOid(tenantId);
        inputDto.setOperatorOid(operatorOid);

        // 校验会员版本
        verifyBatchGrantSealFunction(inputDto, tenantId);

        BatchSealGrantOutputDto batchSealGrantOutputDto =
                sealManagerService.batchGrantSeal(inputDto);
        return RestResult.success(SealResponseConvertor.convertBatchSealGrantResponse(batchSealGrantOutputDto));
    }

    private void verifyBatchGrantSealFunction(BatchSealGrantInputDto input, String tenantOid) {
        BatchGrantSealVerifyModel verifyModel = new BatchGrantSealVerifyModel();
        verifyModel.setGrantedResourceIds(input.getGrantedResourceIds());
        verifyModel.setGrantedAccountIds(input.getGrantedAccountIds());
        verifyModel.setGrantedRoleIds(input.getGrantedRoleIds());
        verifyModel.setGrantedDepartmentIds(input.getGrantedDepartmentIds());
        verifyModel.setScopeList(input.getScopeList());
        verifyModel.setExpireTime(input.getExpireTime());
        validateMembershipService.validate(verifyModel, tenantOid);

        if (SealGrantTypeEnum.ORG_INTERNAL.getCode().equals(input.getGrantType())) {
            validateMembershipService.validate(input.getFallType(), tenantOid, FunctionCodeConstants.SEAL_AUTH_AUTO_FALL, AuthAutoSignMembershipCondition.class);
            validateMembershipService.validate(input.getScopeAppIds(), tenantOid, FunctionCodeConstants.AUTH_APPID, EmptyCollectionMembershipCondition.class);
            validateMembershipService.validate(input.getScopeApprovalTemplateIds(), tenantOid, FunctionCodeConstants.AUTH_APPROVAL_TEMPLATE, EmptyCollectionMembershipCondition.class);
        } else if (SealGrantTypeEnum.ORG_EXTERNAL.getCode().equals(input.getGrantType())){
            validateMembershipService.validate(input.getScopeAppIds(), tenantOid, FunctionCodeConstants.EXTERIOR_AUTH_APPID, EmptyCollectionMembershipCondition.class);
            // 如果是对外授权且appid为空，则是对外部管理员/法人授权
            validateMembershipService.validate(CollectionUtils.isEmpty(input.getScopeAppIds()), tenantOid, FunctionCodeConstants.EXTERIOR_AUTH_ADMIN, BooleanMembershipCondition.class);
        }
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "集团批量印章授权", httpMethod = "POST")
    @RestMapping(path = "/group-batch-grant", method = RequestMethod.POST)
    public RestResult<BatchSealGrantResponse> groupBatchSealGrant(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String operatorOid,
            @RequestBody GroupBatchSealGrantRequest request) {
        // 校验印章及集团的关系
        sealManagerService.checkGroupSealOwnerAuthRelation(tenantId, request.getGrantedResourceIds());
        // 执行批量印章授权
        GroupBatchSealGrantInputDto inputDto = SealResponseConvertor.convertGroupBatchSealGrantInput(request);
        inputDto.setAppId(appId);
        inputDto.setClientId(clientId);
        inputDto.setOperatorTenantOid(tenantId);
        inputDto.setOperatorOid(operatorOid);
        BatchSealGrantOutputDto outputDto = sealManagerService.groupBatchGrantSeal(inputDto);
        return RestResult.success(SealResponseConvertor.convertBatchSealGrantResponse(outputDto));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "编辑印章授权", httpMethod = "PUT")
    @RestMapping(path = "/do-update-rule-grant", method = RequestMethod.PUT)
    public BaseResult<UpdateRuleGrantResponse> updateRuleGrant(@RequestBody UpdateRuleGrantRequest request) {
        String tenantId = RequestContextExtUtils.getTenantId();

        request.setClientId(RequestContextExtUtils.getClientId());
        request.setOrgId(StringUtils.isBlank(request.getSealOwnerOid()) ? request.getOrgId() : request.getSealOwnerOid());
        validateMembershipService.validate(request.getExpireTime(), tenantId, FunctionCodeConstants.AUTH_FOREVER, AuthForeverMembershipCondition.class);

        UpdateRuleGrantInputDTO input = SealResponseConvertor.convertUpdateRuleGrantRequest(request);
        UpdateRuleGrantOutputDTO output = sealRuleGrantService.updateRuleGrant(
                RequestContextExtUtils.getAppId(),
                RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                input);
        return BaseResult.success(SealResponseConvertor.convertUpdateRuleGrantResponse(output));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "批量编辑印章授权", httpMethod = "PUT")
    @RestMapping(path = "/do-update-rule-grants", method = RequestMethod.PUT)
    public BaseResult<UpdateRuleGrantsResponse> updateRuleGrants(@RequestBody UpdateRuleGrantsRequest request) {
        String currentOid = StringUtils.isBlank(request.getSealOwnerOid()) ? request.getOrgId() : request.getSealOwnerOid();
        String tenantId = RequestContextExtUtils.getTenantId();

        request.setClientId(RequestContextExtUtils.getClientId());
        request.setOrgId(currentOid);
        UpdateRuleGrantsInputDTO input = SealResponseConvertor.convertUpdateRuleGrantsRequest(request);
        List<UpdateRuleGrantsInputDTO.BatchRuleItemDTO> ruleGrantedList = input.getRuleGrantedList();
        validateMembershipService.validate(ruleGrantedList, tenantId, FunctionCodeConstants.AUTH_FOREVER, this::validateBatchUpdateExpire);
        validateMembershipService.validate(ruleGrantedList, tenantId, FunctionCodeConstants.BATCH_UPDATE_AUTH, BatchSealAuthMembershipCondition.class);

        UpdateRuleGrantsOutputDTO response =
                sealRuleGrantService.batchUpdateRuleGrant(
                        RequestContextExtUtils.getAppId(),
                        RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId(),
                        input);
        return BaseResult.success(SealResponseConvertor.convertUpdateRuleGrantsResponse(response));
    }


    private boolean validateBatchUpdateExpire(List<UpdateRuleGrantsInputDTO.BatchRuleItemDTO> field) {
        return field.stream()
                .map(UpdateRuleGrantsInputDTO.BatchRuleItemDTO::getExpireTime)
                .anyMatch(SealConstants.FOREVER_EXPIRE_TIME::equals);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.QUERY})
    @ApiOperation(value = "分页查询印章列表(支持集团和单企业查询)", httpMethod = "POST")
    @RestMapping(path = "/page-seals", method = RequestMethod.POST)
    @MultilingualTranslateMethod
    public RestResult<PageQuerySealsResponse> pageQuerySeals(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String operatorOid,
            @RequestBody PageQuerySealsRequest request) {

        List<String> sealOwnerOids = Lists.newArrayList();
        if (request.isQueryAllGroupSeals()) {
            // 集团印章查询,先查出所有带有印章授权的子企业
            List<String> allGroupRelationOids =
                    sealOrganizationService.getAllGroupRelationOidByHeadquarterAndResource(
                            tenantId,
                            Lists.newArrayList(AuthRelationBizSceneEnum.SEAL_MANAGE.getCode()));
            // 不指定印章所属企业
            sealOwnerOids.add(tenantId);
            sealOwnerOids.addAll(allGroupRelationOids);
        } else {
            // 独立企业查询(分为本企业查询和总部查询单企业)
            if (StringUtils.isBlank(request.getSealOwnerOid())) {
                // 非查集团所有印章时,该字段不能为空
                throw new SaasBizException(SaasBizResultCode.SEAL_OWNER_ID_NULL_ERR);
            }
            if (!StringUtils.equals(tenantId, request.getSealOwnerOid())) {
                // 如果印章归属与操作主体不一致,则认为是总部操作,需要鉴权
                sealOrganizationService.checkGroupHeadquarterResourceManagement(
                        request.getSealOwnerOid(),
                        tenantId,
                        Lists.newArrayList(AuthRelationBizSceneEnum.SEAL_MANAGE.getCode()));
            }
            sealOwnerOids.add(request.getSealOwnerOid());
        }

        PageQuerySealsInputDto inputDto = SealResponseConvertor.convertPageSealsInput(request);
        inputDto.setSealOwnerOids(sealOwnerOids);
        inputDto.setOperatorTenantOid(tenantId);
        inputDto.setOperatorOid(operatorOid);
        //排掉检索中的*, 写在这里时为了提醒维护人员,不要忽略这个问题
        inputDto.setName(StringUtils.replaceAll(request.getName(), "\\*", ""));

        PageQuerySealOutPutDTO pageQuerySeals = sealManagerService.pageQuerySeals(inputDto);
        return RestResult.success(SealResponseConvertor.convertPageSealsResponse(pageQuerySeals));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "获取印章授权书下载地址", httpMethod = "GET")
    @RestMapping(path = "/do-download-rule-grant", method = RequestMethod.GET)
    public BaseResult<DownloadRuleGrantResponse> downloadRuleGrant(
            @ApiParam(name = "ruleGrantedId", value = "规则授权ID", required = true) @RequestParam String ruleGrantedId,
            @RequestParam(value = "sealOwnerOid", required = false) String sealOwnerOid) {
        String currentOid = StringUtils.isNotBlank(sealOwnerOid) ? sealOwnerOid : RequestContextExtUtils.getTenantId();
        RuleGrantDownloadUrlOutputDTO outputDTO = sealRuleGrantService.getRuleGrantDownloadUrl(currentOid, RequestContextExtUtils.getOperatorId(), ruleGrantedId);

        DownloadRuleGrantResponse response = new DownloadRuleGrantResponse();
        response.setDownloadUrl(outputDTO.getDownloadUrl());
        return BaseResult.success(response);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.QUERY})
    @ApiOperation(value = "查看二次授权列表", httpMethod = "GET")
    @RestMapping(path = "/get-second-list", method = RequestMethod.GET)
    public BaseResult<GetSecondRuleGrantListResponse> getSecondList(@URIQueryParam GetSecondRuleGrantListRequest request) {
        GetSecondRuleGrantListInputDTO inputDTO = SealResponseConvertor.convertGetSecondRuleGrantListRequest(request);
        GetSecondRuleGrantListOutputDTO outputDTO = secondSealGrantService.pageSecondSealGrant(
                RequestContextExtUtils.getAppId(),
                StringUtils.isBlank(request.getSealOwnerOid()) ? RequestContextExtUtils.getTenantId() : request.getSealOwnerOid(),
                RequestContextExtUtils.getOperatorId(),
                inputDTO);

        return BaseResult.success(SealResponseConvertor.convertGetSecondRuleGrantListResponse(outputDTO));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.AUTH})
    @ApiOperation(value = "二次授权更新", httpMethod = "PUT")
    @RestMapping(path = "/do-update-second", method = RequestMethod.PUT)
    public BaseResult<SecondSealGrantUpdateResponse> updateSecondGrant(@RequestBody SecondSealGrantUpdateRequest request) {
        String tenantId = RequestContextExtUtils.getTenantId();

        request.setClientId(RequestContextExtUtils.getClientId());
        SecondSealGrantUpdateInputDTO inputDTO = SealResponseConvertor.convertUpdateSecondGrantRequest(request);
        validateMembershipService.validate(inputDTO.getExpireTime(), tenantId, FunctionCodeConstants.AUTH_FOREVER, AuthForeverMembershipCondition.class);

        SecondSealGrantUpdateOutputDTO outputDTO = secondSealGrantService.updateSecondGrant(
                RequestContextExtUtils.getAppId(),
                RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                inputDTO);

        return BaseResult.success(SealResponseConvertor.convertUpdateSecondGrantResponse(outputDTO));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.AUTH})
    @ApiOperation(value = "下载二次授权书", httpMethod = "GET")
    @RestMapping(path = "/do-download-second", method = RequestMethod.GET)
    public BaseResult<DownloadSecondRuleGrantResponse> downloadSecondRuleGrant(
            @ApiParam(name = "secondSealGrantBizId", value = "二级授权业务id", required = true)
            @RequestParam(value = "secondSealGrantBizId") String secondSealGrantBizId,
            @RequestParam(value = "sealOwnerOid", required = false) String sealOwnerOid) {

        DownloadSecondRuleGrantOutputDTO outputDTO = secondSealGrantService.getSecondRuleGrantDownloadUrl(
                RequestContextExtUtils.getAppId(),
                StringUtils.isBlank(sealOwnerOid) ? RequestContextExtUtils.getTenantId() : sealOwnerOid,
                RequestContextExtUtils.getOperatorId(),
                secondSealGrantBizId);

        DownloadSecondRuleGrantResponse response = new DownloadSecondRuleGrantResponse();
        response.setDownloadUrl(outputDTO.getDownloadUrl());

        return BaseResult.success(response);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.AUTH})
    @ApiOperation(value = "更新二级授权审批通知", httpMethod = "PUT")
    @RestMapping(path = "/do-setting-second-notify", method = RequestMethod.PUT)
    public BaseResult<Boolean> setSecondNotify(
            @ApiParam(name = "secondSealGrantBizId", value = "二级授权业务id", required = true)
            @RequestParam(value = "secondSealGrantBizId") String secondSealGrantBizId,
            @ApiParam(name = "isNotify", value = "是否接收审批通知", required = true)
            @RequestParam(value = "isNotify") Boolean isNotify,
            @RequestParam(value = "sealOwnerOid", required = false) String sealOwnerOid) {
        secondSealGrantService.setSecondRuleGrantNotify(
                StringUtils.isBlank(sealOwnerOid) ? RequestContextExtUtils.getTenantId() : sealOwnerOid,
                RequestContextExtUtils.getOperatorId(),
                secondSealGrantBizId,
                isNotify);
        return BaseResult.success(true);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.grantedOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.QUERY})
    @ApiOperation(value = "分页查询被授权印章列表", httpMethod = "POST")
    @RestMapping(path = "/page-granted-seals", method = RequestMethod.POST)
    @MultilingualTranslateMethod
    public RestResult<PageQueryGrantedSealsResponse> pageQueryGrantedSeals(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String operatorOid,
            @RequestBody PageQueryGrantedSealsRequest request) {

        PageQueryGrantedSealsInputDto inputDto =
                SealResponseConvertor.convertPageGrantedSealsInput(request);
        inputDto.setOperatorTenantOid(tenantId);
        inputDto.setOperatorOid(operatorOid);
        PageQueryGrantedSealOutPutDTO grantedSeals =
                sealManagerService.pageQueryGrantedSeals(inputDto);
        return RestResult.success(
                SealResponseConvertor.convertPageGrantedSealsResponse(grantedSeals));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#grantedOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.QUERY})
    @ApiOperation(value = "查询被授权印章的所有归属主体", httpMethod = "GET")
    @RestMapping(path = "/granted-seals-owners", method = RequestMethod.GET)
    public RestResult<GrantedSealOwnerListResponse> getGrantedSealOwners(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String operatorOid,
            @RequestParam @NotBlank String grantedOid) {
        GrantedSealOwnersOutPutDTO grantedSealOwners =
                sealManagerService.getGrantedSealOwners(grantedOid, operatorOid);
        return RestResult.success(
                SealResponseConvertor.convertGrantedSealOwnerResponse(grantedSealOwners));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "下载线下法人章授权书", httpMethod = "POST")
    @RestMapping(path = "/download-offline-legal-auth", method = RequestMethod.POST)
    public RestResult<DownloadOfflineLegalAuthLetterResponse> downloadOfflineLegalAuthLetter(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String operatorOid,
            @RequestBody DownloadOfflineLegalAuthLetterRequest request) {

        DownloadOfflineLegalAuthLetterInputDTO inputDto = SealResponseConvertor.convertDownloadLegalSealGrantInput(request);
        inputDto.setSubjectId(tenantId);
        inputDto.setAccountId(operatorOid);
        DownloadOfflineLegalAuthLetterOutPutDTO outputDTO = sealManagerService.downloadOfflineLegalAuthLetter(inputDto);
        return RestResult.success(SealResponseConvertor.convertDownloadOfflineLegalAuthLetterResponse(outputDTO));
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "印章审核催办", httpMethod = "POST")
    @RestMapping(path = "/do-audit-urge", method = RequestMethod.POST)
    public BaseResult<Void> auditUrge(@RequestBody SealGrantAuditUrgeRequest request) {
        String orgOid = StringUtils.isBlank(request.getSealOwnerOid()) ? RequestContextExtUtils.getTenantId() : request.getSealOwnerOid();
        String userOid = RequestContextExtUtils.getOperatorId();

        SealGrantAuditUrgeInputDTO inputDTO = new SealGrantAuditUrgeInputDTO();
        inputDTO.setSealId(request.getSealId());
        inputDTO.setSealOwnerOid(request.getSealOwnerOid());

        sealRuleGrantService.auditUrge(orgOid, userOid, inputDTO);
        return BaseResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "设置接受审批通知", httpMethod = "PUT")
    @RestMapping(path = "/do-setting-rule-grant-notify", method = RequestMethod.PUT)
    public BaseResult<Boolean> setRuleGrantNotify(@RequestBody SettingRuleGrantNotifyRequest request) {
        request.setOrgId(StringUtils.isBlank(request.getSealOwnerOid()) ? request.getOrgId() : request.getSealOwnerOid());
        SettingRuleGrantNotifyInputDTO input = SealResponseConvertor.convertRuleGrantNotify(request);
        sealRuleGrantService.setChangeSetting(RequestContextExtUtils.getOperatorId(), input);
        return BaseResult.success(true);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = PrivilegeOperationConstants.AUTH)
    @ApiOperation(value = "获取立即授权地址", httpMethod = "POST")
    @RestMapping(path = "/get-grant-url", method = RequestMethod.POST)
    public BaseResult<DoGrantResponse> doGrant(@RequestBody DoGrantRequest request) {
        String orgOid = StringUtils.isNotBlank(request.getSealOwnerOid()) ? request.getSealOwnerOid() : RequestContextExtUtils.getTenantId();

        DoGrantInputDTO inputDTO = new DoGrantInputDTO();
        inputDTO.setRuleGrantId(request.getRuleGrantId());
        inputDTO.setOrgId(orgOid);
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());

        DoGrantOutputDTO outputDTO = sealGrantConfirmService.doGrant(inputDTO);

        DoGrantResponse response = new DoGrantResponse();
        response.setUrl(outputDTO.getUrl());

        return BaseResult.success(response);
    }

    @ApiOperation(value = "根据分组id查询印章授权详情", httpMethod = "POST")
    @RestMapping(path = "/query-seal-grant-group-detail", method = RequestMethod.POST)
    public BaseResult<QuerySealGrantGroupDetailResponse> querySealGrantGroupDetail(@RequestBody QuerySealGrantGroupDetailRequest request) {

        QuerySealGrantGroupDetailInputDTO inputDTO = new QuerySealGrantGroupDetailInputDTO();
        inputDTO.setGroupId(request.getGroupId());
        inputDTO.setOffset(request.getOffset());
        inputDTO.setSize(request.getSize());

        QuerySealGrantGroupDetailOutputDTO outputDTO = sealGrantConfirmService.querySealGrantGroupDetail(inputDTO);

        QuerySealGrantGroupDetailResponse response = SealResponseConvertor.convertQuerySealGrantGroupDetail(outputDTO);
        return BaseResult.success(response);
    }

    @ApiOperation(value = "中间页 扫码或立即签署", httpMethod = "POST")
    @RestMapping(path = "/create-seal-grant-confirm-url", method = RequestMethod.POST)
    public BaseResult<CreateSealGrantConfirmUrlResponse> createSealGrantConfirmUrl(@RequestBody CreateSealGrantConfirmUrlRequest request) {
        CreateSealGrantConfirmUrlInputDTO inputDTO = new CreateSealGrantConfirmUrlInputDTO();
        inputDTO.setGroupId(request.getGroupId());
        inputDTO.setAuthConfirmMethod(request.getAuthConfirmMethod());
        inputDTO.setRedirectUrl(request.getRedirectUrl());

        CreateSealGrantConfirmUrlOutputDTO outputDTO = sealGrantConfirmService.createSealGrantConfirmUrl(inputDTO);

        CreateSealGrantConfirmUrlResponse response = new CreateSealGrantConfirmUrlResponse();
        response.setUrl(outputDTO.getUrl());
        return BaseResult.success(response);
    }

    @ApiOperation(value = "中间页 轮询查询印章授权结果", httpMethod = "POST")
    @RestMapping(path = "/query-seal-grant-result", method = RequestMethod.POST)
    public BaseResult<QuerySealGrantResultResponse> querySealGrantResult(@RequestBody QuerySealGrantResultRequest request) {
        QuerySealGrantResultInputDTO inputDTO = new QuerySealGrantResultInputDTO();
        inputDTO.setGroupId(request.getGroupId());

        QuerySealGrantResultOutputDTO outputDTO = sealGrantConfirmService.querySealGrantResult(inputDTO);

        QuerySealGrantResultResponse response = new QuerySealGrantResultResponse();
        response.setGroupId(outputDTO.getGroupId());
        response.setGroupStatus(outputDTO.getGroupStatus());
        response.setAuthConfirmMethod(outputDTO.getAuthConfirmMethod());

        return BaseResult.success(response);
    }
}
