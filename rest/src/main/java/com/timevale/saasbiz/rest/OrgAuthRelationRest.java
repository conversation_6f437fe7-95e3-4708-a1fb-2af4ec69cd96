package com.timevale.saasbiz.rest;

import com.google.common.collect.Lists;
import com.timevale.billing.manager.sdk.model.order.MultiOrderSearchQuery;
import com.timevale.billing.manager.sdk.model.order.OrderSearchVO;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizSceneEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationStatusEnum;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.integration.bill.BillClient;
import com.timevale.saasbiz.model.bean.authrelation.bo.AuthRelationChildTenantBO;
import com.timevale.saasbiz.model.bean.authrelation.dto.input.*;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.*;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasPrivilegeInputDTO;
import com.timevale.saasbiz.model.enums.PrivilegeOperationEnum;
import com.timevale.saasbiz.model.enums.PrivilegeResourceEnum;
import com.timevale.saasbiz.model.enums.authrelation.AuthRelationQuerySceneEnum;
import com.timevale.saasbiz.model.utils.AppConfigUtil;
import com.timevale.saasbiz.model.utils.AssertX;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.authrelation.request.*;
import com.timevale.saasbiz.rest.bean.authrelation.response.*;
import com.timevale.saasbiz.rest.bean.authrelation.vo.AuthRelationImportTenantVO;
import com.timevale.saasbiz.service.authrelation.AuthRelationBizService;
import com.timevale.saasbiz.service.authrelation.AuthRelationCoreService;
import com.timevale.saasbiz.service.authrelation.AuthRelationTaskService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_APP_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.enums.AppConfigEnum.BATCH_ADD_AUTH_RELATION_NEW_ENABLE;

/**
 * 关联企业
 *
 * <AUTHOR>
 * @since 2023/3/31 2:00 下午
 */
@Api(tags = "关联企业接口")
@Validated
@ExternalService
@RestMapping(path = "v2/org-auth-relation")
public class OrgAuthRelationRest {

    @Autowired private AuthRelationTaskService authRelationTaskService;

    @Autowired private AuthRelationCoreService authRelationCoreService;

    @Autowired private AuthRelationBizService authRelationBizService;

    @Autowired MapperFactory mapperFactory;

    @Autowired private AuthRelationTaskService batchAddTaskService;

    @Autowired private UserCenterService userCenterService;

    @Autowired private BillClient billClient;

    @Autowired private SaasCommonService saasCommonService;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "批量保存授权记录", httpMethod = "POST")
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.MULTI_ORGANIZATION)
    @RestMapping(path = "/add", method = RequestMethod.POST)
    public RestResult<AuthRelationBatchAddResponse> batchAdd(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestBody AuthRelationBatchAddRequest request) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.ADD.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);
        // 校验印章管理权限
        checkSupportResource(tenantId, request.getChildTenantList());
        saasCommonService.checkSupportFunction(
                tenantId, null, FunctionCodeConstant.MULTI_ORGANIZATION);
        AuthRelationBatchAddInputDTO input =
                mapperFactory.getMapperFacade().map(request, AuthRelationBatchAddInputDTO.class);
        input.setAppId(appId);
        input.setOperatorOid(accountId);
        input.setAuthTenantOid(tenantId);
        input.setClientId(RequestContextExtUtils.getClientId());
        // 发布过程中兼容处理， 上线前关闭， 上线后再打开
        if (!Boolean.TRUE.equals(AppConfigUtil.getBoolean(BATCH_ADD_AUTH_RELATION_NEW_ENABLE))) {
            authRelationTaskService.batchAdd(
                    RequestContextExtUtils.getClientId(), input, tenantId, accountId, appId);;
            return RestResult.success(new AuthRelationBatchAddResponse(false));
        }
        AuthRelationBatchAddResultDTO resultDTO = authRelationBizService.batchAdd(input);
        AuthRelationBatchAddResponse response = new AuthRelationBatchAddResponse();
        response.setGoToJobCenter(resultDTO.isGoToJobCenter());
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "批量续签关联企业", httpMethod = "POST")
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.MULTI_ORGANIZATION)
    @RestMapping(path = "/renew", method = RequestMethod.POST)
    public RestResult<AuthRelationBatchAddResponse> batchRenew(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestBody AuthRelationBatchRenewRequest request) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.ADD.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);
        saasCommonService.checkSupportFunction(
                tenantId, null, FunctionCodeConstant.MULTI_ORGANIZATION);
        AuthRelationBatchRenewInputDTO input =
                mapperFactory.getMapperFacade().map(request, AuthRelationBatchRenewInputDTO.class);
        input.setAppId(appId);
        input.setOperatorOid(accountId);
        input.setAuthTenantOid(tenantId);
        input.setClientId(RequestContextExtUtils.getClientId());
        AuthRelationBatchAddResultDTO resultDTO = authRelationBizService.batchRenew(input);
        AuthRelationBatchAddResponse response = new AuthRelationBatchAddResponse();
        response.setGoToJobCenter(resultDTO.isGoToJobCenter());
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取批量任务进度", httpMethod = "GET")
    @RestMapping(path = "/batch-add-task", method = RequestMethod.GET)
    public RestResult<AuthRelationGetBatchAddTaskResponse> queryBatchAddTask(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {
        AuthRelationGetBatchAddTaskDTO authRelationGetBatchAddTaskDTO =
                authRelationBizService.queryBatchAddProgress(tenantId);
        AuthRelationGetBatchAddTaskResponse response =
                mapperFactory
                        .getMapperFacade()
                        .map(
                                authRelationGetBatchAddTaskDTO,
                                AuthRelationGetBatchAddTaskResponse.class);
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "处理删除过完成的任务", httpMethod = "DELETE")
    @RestMapping(path = "/delete-batch-add-task", method = RequestMethod.DELETE)
    public RestResult<Void> deleteCompleteBatchAddTask(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {
        authRelationTaskService.deleteCompleteBatchAddTask(tenantId);
        return RestResult.success(null);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "添加前获取添加条件", httpMethod = "GET")
    @RestMapping(path = "/config", method = RequestMethod.GET)
    public RestResult<AuthRelationConfigResponse> getConfig(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {
        AuthRelationConfigDTO authRelationConfig =
                authRelationCoreService.getAuthRelationConfig(tenantId);
        AuthRelationConfigResponse res =
                mapperFactory
                        .getMapperFacade()
                        .map(authRelationConfig, AuthRelationConfigResponse.class);
        return RestResult.success(res);
    }
    

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "添加关联企业前校验企业是否符合规则", httpMethod = "POST")
    @RestMapping(path = "/check-tenant-when-add", method = RequestMethod.POST)
    public RestResult<List<AuthRelationCheckTenantBeforeAddResultResponse>> checkTenantBeforeAdd(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AuthRelationCheckTenantBeforeAddRequest request) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.ADD.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);
        checkSupportResource(tenantId, request.getChildTenantList());
        List<AuthRelationCheckTenantBeforeAddResultDTO> list =
                authRelationCoreService.checkTenantBeforeAdd(
                        tenantId, request.getParentTenantOid(), request.getChildTenantList());
        List<AuthRelationCheckTenantBeforeAddResultResponse> res =
                mapperFactory
                        .getMapperFacade()
                        .mapAsList(list, AuthRelationCheckTenantBeforeAddResultResponse.class);
        return RestResult.success(res);
    }

    @ApiOperation(value = "查询关联企业列表", httpMethod = "GET")
    @RestMapping(path = "/list", method = RequestMethod.GET)
    public RestResult<AuthRelationListResponse> authRelationList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @Valid @Max(10000) @RequestParam Integer pageNum,
            @Valid @Max(100) @RequestParam Integer pageSize,
            @RequestParam(required = false) String shareKey,
            @RequestParam(required = false) String searchKey,
            @RequestParam(required = false) String authRelationStatusList,
            @RequestParam(required = false) Boolean queryDeleted,
            @RequestParam(required = false) Long minEffectiveStartTime,
            @RequestParam(required = false) Long maxEffectiveStartTime,
            @RequestParam(required = false) Long minEffectiveEndTime,
            @RequestParam(required = false) Long maxEffectiveEndTime,
            @RequestParam(required = false) String queryScene) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.LOOK.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);

        AuthRelationQuerySceneEnum querySceneEnum = AuthRelationQuerySceneEnum.from(queryScene);

        AuthRelationListDTO request = new AuthRelationListDTO();
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        request.setSearchKey(searchKey);
        request.setShareKey(shareKey);
        request.setQueryDeleted(queryDeleted);
        request.setMinEffectiveStartTime(minEffectiveStartTime);
        request.setMaxEffectiveStartTime(maxEffectiveStartTime);
        request.setMinEffectiveEndTime(minEffectiveEndTime);
        request.setMaxEffectiveEndTime(maxEffectiveEndTime);
        request.setSortType(querySceneEnum.getSortType());
        if (AuthRelationQuerySceneEnum.RENEW.equals(querySceneEnum)) {
            request.setAuthRelationStatusList(Lists.newArrayList(AuthRelationStatusEnum.EFFECTIVE.getCode()));
        } else if (StringUtils.isNotBlank(authRelationStatusList)) {
            request.setAuthRelationStatusList(
                    Arrays.stream(authRelationStatusList.split(","))
                            .map(Integer::valueOf)
                            .collect(Collectors.toList()));
        }
        AuthRelationListResultDTO resultDTO =
                authRelationBizService.authRelationList(request, tenantId);
        AuthRelationListResponse response =
                mapperFactory.getMapperFacade().map(resultDTO, AuthRelationListResponse.class);
        return RestResult.success(response);
    }

    @ApiOperation(value = "查询关联企业授权记录列表", httpMethod = "GET")
    @RestMapping(path = "/log-list", method = RequestMethod.GET)
    public RestResult<AuthRelationListResponse> authRelationList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @Valid @Max(10000) @RequestParam Integer pageNum,
            @Valid @Max(100) @RequestParam Integer pageSize,
            @RequestParam Long authRelationId) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.LOOK.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);

        AuthRelationListResultDTO resultDTO =
                authRelationBizService.authRelationLogList(
                        tenantId, authRelationId, pageNum, pageSize);
        AuthRelationListResponse response =
                mapperFactory.getMapperFacade().map(resultDTO, AuthRelationListResponse.class);
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "授权关系修改配置", httpMethod = "PUT")
    @RestMapping(path = "/share-config", method = RequestMethod.PUT)
    public RestResult<Void> changeShareConfig(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AuthRelationChangeShareConfigRequest request) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.ADD.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);

        AuthRelationChangeShareConfigDTO input =
                mapperFactory
                        .getMapperFacade()
                        .map(request, AuthRelationChangeShareConfigDTO.class);
        authRelationBizService.changeShareConfig(input, tenantId, accountId);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "删除授权", httpMethod = "DELETE")
    @RestMapping(path = "/delete", method = RequestMethod.DELETE)
    public RestResult<Void> delete(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam Long authRelationId) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.DELETE.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);

        authRelationBizService.delete(tenantId, accountId, authRelationId);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "主动解除授权", httpMethod = "POST")
    @RestMapping(path = "/rescind", method = RequestMethod.POST)
    public RestResult<Void> rescind(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AuthRelationRescindRequest request) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.DELETE.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);

        authRelationBizService.rescind(
                tenantId, accountId, request.getAuthRelationId(), request.getAuthRelationLogId());
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询授权关系最新的签署信息", httpMethod = "GET")
    @RestMapping(path = "/get-auth-relation-last-process", method = RequestMethod.GET)
    public RestResult<AuthRelationGetAuthRelationLastProcessResponse> getAuthRelationLastProcess(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam Long authRelationId,
            @RequestParam Long authRelationLogId) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.LOOK.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);

        AuthRelationGetAuthRelationLastProcessDTO responseDTO =
                authRelationBizService.getAuthRelationLastProcess(
                        authRelationId, authRelationLogId, tenantId);
        return RestResult.success(
                mapperFactory
                        .getMapperFacade()
                        .map(responseDTO, AuthRelationGetAuthRelationLastProcessResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取授权详情", httpMethod = "GET")
    @RestMapping(path = "/detail", method = RequestMethod.GET)
    public RestResult<AuthRelationGetAuthRelationDetailResponse> getAuthRelationDetail(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestParam Long authRelationId) {
        AuthRelationGetAuthRelationDetailDTO detailDTO =
                authRelationBizService.getAuthRelation(authRelationId, tenantId);
        AuthRelationGetAuthRelationDetailResponse response =
                mapperFactory
                        .getMapperFacade()
                        .map(detailDTO, AuthRelationGetAuthRelationDetailResponse.class);
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "解析批量添加excel", httpMethod = "GET")
    @RestMapping(path = "/analysis-batch-add-excel", method = RequestMethod.GET)
    public RestResult<List<AuthRelationImportTenantVO>> analysisBatchAddExcel(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam String fileKey) {

        CheckHasPrivilegeInputDTO privilegeInput =
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUTH_RELATION.getType(),
                        PrivilegeOperationEnum.ADD.getType());
        userCenterService.checkHasPrivilege(privilegeInput, true);

        List<AuthRelationImportTenantDTO> res =
                authRelationBizService.analysisBatchAddExcel(
                        RequestContextExtUtils.getTenantId(), fileKey);
        return RestResult.success(
                mapperFactory.getMapperFacade().mapAsList(res, AuthRelationImportTenantVO.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "判断是否可作为授权企业", httpMethod = "GET")
    @RestMapping(path = "/check-auth-tenant", method = RequestMethod.GET)
    public RestResult<Boolean> checkAuthTenant(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {
        return RestResult.success(authRelationBizService.existParentAuthRelation(tenantId));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询历史有效关联企业列表", httpMethod = "GET")
    @RestMapping(path = "/search-history-effective-auth-relation", method = RequestMethod.GET)
    public RestResult<SearchHistoryEffectiveAuthRelationResponse>
            searchHistoryEffectiveAuthRelation(
                    @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
                    @RequestParam Integer pageNum,
                    @RequestParam Integer pageSize,
                    @RequestParam(required = false) String searchTenantName,
                    @RequestParam(required = false) String bizScene) {
        SearchHistoryEffectiveAuthRelationDTO effectiveAuthRelationDTO =
                authRelationBizService.searchHistoryEffectiveAuthRelation(
                        pageNum, pageSize, searchTenantName, tenantId, bizScene);
        return RestResult.success(
                mapperFactory
                        .getMapperFacade()
                        .map(
                                effectiveAuthRelationDTO,
                                SearchHistoryEffectiveAuthRelationResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询有效关联企业列表", httpMethod = "GET")
    @RestMapping(path = "/search-effective-auth-relation", method = RequestMethod.GET)
    public RestResult<SearchEffectiveAuthRelationResponse> searchEffectiveAuthRelation(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestParam(required = false) String searchTenantName,
            @RequestParam(required = false) String bizScene,
            @ApiParam(value = "true查询自己 会把当前企业拼在第一页的第一位") @RequestParam(required = false)
                    Boolean querySelf) {
        SearchEffectiveAuthRelationDTO effectiveAuthRelationDTO =
                authRelationBizService.searchEffectiveAuthRelation(
                        pageNum, pageSize, searchTenantName, RequestContextExtUtils.getResourceTenantId(), bizScene, querySelf);
        return RestResult.success(
                mapperFactory
                        .getMapperFacade()
                        .map(effectiveAuthRelationDTO, SearchEffectiveAuthRelationResponse.class));
    }



    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取企业可授权信息", httpMethod = "GET")
    @RestMapping(path = "/can-auth-infos", method = RequestMethod.GET)
    public RestResult<AuthRelationCanAuthInfoResponse> getCaAuthInfos(){
        AuthRelationCanAuthInfoDTO canAuthInfoDTO= authRelationBizService.getAllCanAuthInfo(RequestContextExtUtils.getTenantId());
        return RestResult.success(
                mapperFactory
                        .getMapperFacade()
                        .map(canAuthInfoDTO, AuthRelationCanAuthInfoResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取授权信息", httpMethod = "GET")
    @RestMapping(path = "/get-auth-info", method = RequestMethod.GET)
    //todo 鉴权
    public RestResult<AuthRelationGetAuthInfoResponse> getAuthInfo(
            @RequestParam @NotBlank(message = "被授权账号不能为空") String authGid,
            @RequestParam @NotBlank(message = "授权类型不能为空") String authType) {
        AuthRelationAuthInfoDTO authInfoDTO =
                authRelationBizService.getAuthInfos(
                        RequestContextExtUtils.getTenantId(), authGid, authType);
        return RestResult.success(
                mapperFactory
                        .getMapperFacade()
                        .map(authInfoDTO, AuthRelationGetAuthInfoResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "给授权企业分配授权资源", httpMethod = "POST")
    @RestMapping(path = "/do-auth-info", method = RequestMethod.POST)
    public RestResult<AuthRelationDoAuthInfoResponse> doAuthInfo(
            @RequestBody AuthRelationDoAuthInfoRequest request) {
        // todo 入参获取需要优化下
        authRelationBizService.doAuthInfo(
                RequestContextExtUtils.getTenantId(),
                request.getAuthGid(),
                request.getShareConfigInfo().getConfigValue(),
                request.getShareConfigInfo().getConfigKey(),
                request.getOrderId());
        return RestResult.success(new AuthRelationDoAuthInfoResponse(true));
    }

    // 测试异步执行定时任务
    @RestMapping(path = "/testBatchTask", method = RequestMethod.GET)
    public String testBatchTask(
            @RequestParam(required = false, defaultValue = "1") Integer total,
            @RequestParam(required = false, defaultValue = "0") Integer index) {
        batchAddTaskService.asyncHandleBatchAddTask(total, index);
        return "success";
    }

    @RestMapping(path = "/testOrder", method = RequestMethod.POST)
    public List<OrderSearchVO> test(@RequestBody MultiOrderSearchQuery query) {
        List<OrderSearchVO> result = billClient.searchMultiOrderResult(query);
        return result;
    }

    private void checkSupportResource(String tenantId, List<AuthRelationChildTenantBO> childTenantList){
        AssertX.isTrue(CollectionUtils.isNotEmpty(childTenantList), "关联企业列表不能为空");
        Set<String> functionKeys = new HashSet<>();
        for(AuthRelationChildTenantBO childTenantBO : childTenantList){
            for(String authResource : childTenantBO.getAuthResources()){
                AuthRelationBizSceneEnum sceneEnum = AuthRelationBizSceneEnum.from(Integer.valueOf(authResource));
                AssertX.isTrue(sceneEnum != null, "资源类型不符");
                functionKeys.add(sceneEnum.getFunctionCode());
            }
        }
        saasCommonService.checkSupportFunctions(tenantId, null, Lists.newArrayList(functionKeys));
    }
}
