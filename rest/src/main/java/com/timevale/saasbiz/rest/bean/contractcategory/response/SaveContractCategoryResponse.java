package com.timevale.saasbiz.rest.bean.contractcategory.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 保存合同类型响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("保存合同类型响应数据")
public class SaveContractCategoryResponse extends ToString {

    @ApiModelProperty("合同类型id")
    private String categoryId;
}
