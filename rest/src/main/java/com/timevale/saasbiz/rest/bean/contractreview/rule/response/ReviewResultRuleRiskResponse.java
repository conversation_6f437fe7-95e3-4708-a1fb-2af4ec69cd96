package com.timevale.saasbiz.rest.bean.contractreview.rule.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

@Data
public class ReviewResultRuleRiskResponse extends ToString {
    private String riskId;
    /**
     * 风险说明
     */
    private String riskStatement;

    /**
     * 原文
     */
    private String originText;

    /**
     * 修改建议
     */
    private String suggestionText;

    /**
     * 前端展示
     */
    private String showText;

    /**
     * 修改建议状态
     */
    private String suggestionStatus;

    private String status;

    private Boolean hasRisk;
}
