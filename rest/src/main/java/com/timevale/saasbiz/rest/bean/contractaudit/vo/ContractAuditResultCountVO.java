package com.timevale.saasbiz.rest.bean.contractaudit.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Data
public class ContractAuditResultCountVO {
    @ApiModelProperty(value = "通过数")
    private Integer pass;
    @ApiModelProperty(value = "高风险数")
    private Integer high;
    @ApiModelProperty(value = "中风险数")
    private Integer medium;
    @ApiModelProperty(value = "低风险数")
    private Integer low;
    @ApiModelProperty(value = "总数")
    private Integer total;
}
