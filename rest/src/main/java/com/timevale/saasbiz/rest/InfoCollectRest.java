package com.timevale.saasbiz.rest;

import com.timevale.contractmanager.common.service.constant.FunctionCodeConstants;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saas.tracking.mq.TrackingProducer;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.output.StartDataWillStartOutputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfCollectQueryResourceAuthInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectBuildRelationBeforeInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectTaskBatchEditAuthInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectTaskEditAuthInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectTaskResultInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.output.InfoCollectCanAuthRoleOutputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.output.InfoCollectFlowNodeDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.output.InfoCollectResourceAuthOutputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.output.InfoCollectTaskResultOutputDTO;
import com.timevale.saasbiz.model.bean.process.bo.ProcessStartDataBO;
import com.timevale.saasbiz.model.bean.process.dto.input.ProcessStartDataQueryInputDTO;
import com.timevale.saasbiz.model.constants.InfoCollectConstants;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.flowtemplate.response.InfoCollectBuildRelationBeforeResponse;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectBuildRelationBeforeRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectDeleteStartDataRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormCopyKeyRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormCopyRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskBatchEditAuthRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskEditAuthRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskResultRequest;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfCollectQueryResourceAuthResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectCanAuthRoleResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectFormCopyKeyResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectFormCopyResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectFormEditResultResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectStartDataListResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectStartDataWillStartResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectTaskResultResponse;
import com.timevale.saasbiz.rest.bean.infocollect.vo.InfoCollectFlowNodeVO;
import com.timevale.saasbiz.rest.bean.infocollect.vo.ProcessStartDataVO;
import com.timevale.saasbiz.rest.converter.InfoCollectRestConvert;
import com.timevale.saasbiz.service.infocollect.InfoCollectFormService;
import com.timevale.saasbiz.service.infocollect.InfoCollectTaskService;
import com.timevale.saasbiz.service.process.ProcessStartDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Arrays;
import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2023/8/29 14:46
 */
@Api(tags = "信息采集接口")
@ExternalService
@RestMapping(path = "/v1/info-collect")
public class InfoCollectRest {

    @Autowired
    private InfoCollectFormService formService;
    @Autowired
    private InfoCollectTaskService taskService;
    @Autowired
    private MapperFactory mapperFactory;
    @Autowired
    private TrackingProducer trackingProducer;
    @Autowired
    private ProcessStartDataService processStartDataService;



    @UserPrivilegeCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = InfoCollectConstants.PRIVILEGE_RESOURCE, privilegeKey = InfoCollectConstants.PRIVILEGE_RESOURCE_KEY_COPY)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("表单-获取复制凭证")
    @RestMapping(path = "/form-copy-key", method = RequestMethod.POST)
    public RestResult<InfoCollectFormCopyKeyResponse> formCopyKey(@RequestBody InfoCollectFormCopyKeyRequest request) {
        String copyKey = formService.formCopyKey(RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                request.getFormId());
        InfoCollectFormCopyKeyResponse response = new InfoCollectFormCopyKeyResponse();
        response.setCopyKey(copyKey);
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = InfoCollectConstants.PRIVILEGE_RESOURCE, privilegeKey = InfoCollectConstants.PRIVILEGE_RESOURCE_KEY_COPY)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("表单-复制表单")
    @RestMapping(path = "/form-copy", method = RequestMethod.POST)
    public RestResult<InfoCollectFormCopyResponse> formCopy(@RequestBody InfoCollectFormCopyRequest request) {
        String formId = formService.formCopy(RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                request.getCopyKey());

        InfoCollectFormCopyResponse response = new InfoCollectFormCopyResponse();
        response.setFormId(formId);
        return RestResult.success(response);
    }


    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("采集任务-查询可授权角色")
    @RestMapping(path = "/task-auth-role", method = RequestMethod.GET)
    public RestResult<InfoCollectCanAuthRoleResponse> taskAuthRole() {
        InfoCollectCanAuthRoleOutputDTO outputDTO = taskService.taskAuthRole(RequestContextExtUtils.getTenantId());
        InfoCollectCanAuthRoleResponse response =
                mapperFactory.getMapperFacade().map(outputDTO, InfoCollectCanAuthRoleResponse.class);
        return RestResult.success(response);
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("采集任务-编辑可授权")
    @RestMapping(path = "/task-edit-auth", method = RequestMethod.POST)
    public RestResult<Void> taskEditAuth(@RequestBody InfoCollectTaskEditAuthRequest request) {
        InfoCollectTaskEditAuthInputDTO inputDTO = InfoCollectRestConvert.taskEditAuthConvert(request);
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        taskService.taskEditAuth(inputDTO);
        return RestResult.success(null);
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("采集任务批量编辑授权")
    @RestMapping(path = "/task-batch-edit-auth", method = RequestMethod.POST)
    public RestResult<Void> taskBatchEditAuth(@RequestBody InfoCollectTaskBatchEditAuthRequest request) {
        InfoCollectTaskBatchEditAuthInputDTO inputDTO = InfoCollectRestConvert.taskBothEditAuthConvert(request);
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        taskService.taskBatchEditAuth(inputDTO);
        return RestResult.success(null);
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("采集任务-查询已授权的列表")
    @RestMapping(path = "/task-auth-list", method = RequestMethod.GET)
    public RestResult<InfCollectQueryResourceAuthResponse> taskAuthList(@RequestParam String formId,
                                                                        @RequestParam(required = false) String taskId) {
        InfCollectQueryResourceAuthInputDTO inputDTO = new InfCollectQueryResourceAuthInputDTO();
        inputDTO.setFormId(formId);
        inputDTO.setTaskId(taskId);
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        List<InfoCollectResourceAuthOutputDTO> resourceAuthOutputDTOList = taskService.taskAuthList(inputDTO);
        InfCollectQueryResourceAuthResponse response = new InfCollectQueryResourceAuthResponse();
        response.setAuthData(InfoCollectRestConvert.resourceAuthConvert(resourceAuthOutputDTOList));
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("采集任务-到发起数据列表")
    @RestMapping(path = "/start-data-list", method = RequestMethod.GET)
    public RestResult<InfoCollectStartDataListResponse> statDataList(@RequestParam Integer pageNum,
                                                                     @RequestParam Integer pageSize,
                                                                     @RequestParam(required = false) String dataSourceChannel,
                                                                     @RequestParam(required = false) String status,
                                                                     @RequestParam(required = false) String title,
                                                                     @RequestParam(required = false) String primaryOid) {


        ProcessStartDataQueryInputDTO inputDTO = new ProcessStartDataQueryInputDTO();
        inputDTO.setPageNum(pageNum);
        inputDTO.setPageSize(pageSize);
        if (StringUtils.isNotBlank(primaryOid)) {
            inputDTO.setSubjectOid(primaryOid);
        } else {
            inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        }
        inputDTO.setOwnerOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setDataSourceChannel(dataSourceChannel);
        inputDTO.setStatusList(Arrays.asList(status));
        inputDTO.setProcessTitle(title);

        PagerResult<ProcessStartDataBO> pagerResult = processStartDataService.list(inputDTO);
        InfoCollectStartDataListResponse response = new InfoCollectStartDataListResponse();
        response.setTotal(pagerResult.getTotal());
        response.setList(mapperFactory.getMapperFacade().mapAsList(pagerResult.getItems(), ProcessStartDataVO.class));
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("删除待发起数据")
    @RestMapping(path = "/delete-start-data", method = RequestMethod.POST)
    public RestResult<Void> deleteStartData(@RequestBody InfoCollectDeleteStartDataRequest request) {
        processStartDataService.delete(request.getDataIdList(),
                RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId());
        return RestResult.success(null);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("待发起数据发起前查询必要数据")
    @RestMapping(path = "/start-data-will-start", method = RequestMethod.GET)
    public RestResult<InfoCollectStartDataWillStartResponse> startDataWillStart(@RequestParam String dataId) {
        StartDataWillStartOutputDTO outputDTO = processStartDataService.startDataWillStart(
                RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                dataId);
        return RestResult.success(mapperFactory.getMapperFacade().map(outputDTO, InfoCollectStartDataWillStartResponse.class));
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("表单模版关联企业获取必要数据")
    @RestMapping(path = "/build-template-form-relation-before", method = RequestMethod.POST)
    public RestResult<InfoCollectBuildRelationBeforeResponse> buildFlowTemplateFormRelationBefore(@RequestBody InfoCollectBuildRelationBeforeRequest request) {

        InfoCollectBuildRelationBeforeInputDTO inputDTO = new InfoCollectBuildRelationBeforeInputDTO();
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setFlowTemplateId(request.getFlowTemplateId());
        inputDTO.setFormId(request.getFormId());
        String version = formService.buildFlowTemplateFormRelationBefore(inputDTO);
        InfoCollectBuildRelationBeforeResponse response = new InfoCollectBuildRelationBeforeResponse();
        response.setVersion(version);
        return RestResult.success(response);
    }

    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INFO_COLLECT)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("表单-编辑结果")
    @RestMapping(path = "/form-edit-result", method = RequestMethod.GET)
    public RestResult<InfoCollectFormEditResultResponse> formEditResult(@RequestParam String formId) {
        List<InfoCollectFlowNodeDTO> list = formService.formEditResult(RequestContextExtUtils.getTenantId(), formId);
        InfoCollectFormEditResultResponse response = new InfoCollectFormEditResultResponse();
        response.setFlowNodes(mapperFactory.getMapperFacade().mapAsList(list, InfoCollectFlowNodeVO.class));
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("任务-结果页")
    @RestMapping(path = "/task-result", method = RequestMethod.POST)
    public RestResult<InfoCollectTaskResultResponse> taskResult(@RequestBody InfoCollectTaskResultRequest request) {
        InfoCollectTaskResultInputDTO inputDTO = new InfoCollectTaskResultInputDTO();
        inputDTO.setTaskKey(request.getTaskKey());
        inputDTO.setTaskId(request.getTaskId());
        inputDTO.setDataId(request.getDataId());
        inputDTO.setPlatform(request.getPlatform());
        inputDTO.setToken(request.getToken());
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperationOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setClientId(RequestContextExtUtils.getClientId());
        InfoCollectTaskResultOutputDTO resultDTO = formService.taskResult(inputDTO);
        InfoCollectTaskResultResponse response = new InfoCollectTaskResultResponse();
        response.setBtnList(resultDTO.getBtnList());
        response.setLoopFlag(resultDTO.isLoopFlag());
        response.setTaskDataStatus(resultDTO.getTaskDataStatus());
        response.setDataId(resultDTO.getDataId());
        return RestResult.success(response);
    }

}
