package com.timevale.saasbiz.rest.bean.contractreview.rule.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

@Data
public class RuleEffectVerifyRuleRequest extends ToString {
    private String ruleId;

    private String groupId;

    private String ruleName;

    private String ruleRemark;

    private String riskLevel;

    private List<RuleEffectVerifyRuleRiskRequest> reviewRuleRisks;

}
