package com.timevale.saasbiz.rest;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saasbiz.model.bean.novice.bo.NoviceTaskConfigBO;
import com.timevale.saasbiz.model.bean.novice.bo.NoviceTaskInfoBO;
import com.timevale.saasbiz.model.bean.novice.dto.output.*;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.novice.request.CompleteNoviceOperateRequest;
import com.timevale.saasbiz.rest.bean.novice.response.*;
import com.timevale.saasbiz.rest.bean.novice.vo.NoviceTaskConfigVO;
import com.timevale.saasbiz.rest.bean.novice.vo.NoviceTaskInfoVO;
import com.timevale.saasbiz.service.novice.NoviceOperateService;
import com.timevale.saasbiz.service.novice.NoviceTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2023-05-17
 */
@Api(tags = "新手任务操作")
@ExternalService
@RestMapping(path = "/v1/novices")
public class NoviceRest {

    @Autowired NoviceOperateService noviceOperateService;

    @Autowired NoviceTaskService noviceTaskService;

    @ApiOperation(value = "判断用户是否可以领取/显示新手任务", httpMethod = "GET")
    @RestMapping(path = "/{accountId}/tasks/show", method = RequestMethod.GET)
    public RestResult<CheckNoviceTaskResponse> checkNoviceTask(
            @PathVariable(value = "accountId") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {

        CheckNoviceTaskOutputDTO outputDTO = noviceTaskService.checkNoviceTask(accountId, tenantId);

        CheckNoviceTaskResponse response = new CheckNoviceTaskResponse();
        response.setShowNovice(outputDTO.isShowNovice());
        response.setCanIgnore(outputDTO.isCanIgnore());
        response.setTotalGiftNum(outputDTO.getTotalGiftNum());
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取用户新手任务信息", httpMethod = "GET")
    @RestMapping(path = "/{accountId}/tasks/list", method = RequestMethod.GET)
    public RestResult<QueryNoviceTaskResponse> queryNoviceTask(
            @PathVariable(value = "accountId") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {

        QueryNoviceTaskOutputDTO outputDTO = noviceTaskService.queryNoviceTask(accountId, tenantId);

        QueryNoviceTaskResponse response = new QueryNoviceTaskResponse();
        response.setOrganizeId(outputDTO.getOrganizeId());
        response.setOrganizeName(outputDTO.getOrganizeName());
        response.setTasks(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(outputDTO.getTasks())) {
            for (NoviceTaskInfoBO taskInfoBO : outputDTO.getTasks()) {
                NoviceTaskInfoVO noviceTaskInfoVO = new NoviceTaskInfoVO();
                noviceTaskInfoVO.setTaskId(taskInfoBO.getTaskId());
                noviceTaskInfoVO.setTaskName(taskInfoBO.getTaskName());
                noviceTaskInfoVO.setPicUrl(taskInfoBO.getPicUrl());
                noviceTaskInfoVO.setTaskType(taskInfoBO.getTaskType());
                noviceTaskInfoVO.setTaskStatus(taskInfoBO.getTaskStatus());
                noviceTaskInfoVO.setGiftNum(taskInfoBO.getGiftNum());
                noviceTaskInfoVO.setOrderNum(taskInfoBO.getOrderNum());
                response.getTasks().add(noviceTaskInfoVO);
            }
        }

        return RestResult.success(response);
    }

    @ApiOperation(value = "获取用户新手任务配置", httpMethod = "GET")
    @RestMapping(path = "/{accountId}/tasks/config", method = RequestMethod.GET)
    public RestResult<QueryNoviceTaskConfigResponse> queryNoviceTaskConfig(
            @ApiParam("用户id") @PathVariable(value = "accountId") String accountId,
            @ApiParam("新手任务主体类型，0-个人，1-企业") @RequestParam(value = "subjectType")
                    Integer subjectType) {
        QueryNoviceTaskConfigOutputDTO outputDTO =
                noviceTaskService.queryNoviceTaskConfig(accountId, subjectType);

        QueryNoviceTaskConfigResponse response = new QueryNoviceTaskConfigResponse();
        response.setTasks(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(outputDTO.getTasks())) {
            for (NoviceTaskConfigBO taskConfigBO : outputDTO.getTasks()) {
                NoviceTaskConfigVO noviceTaskConfigVO = new NoviceTaskConfigVO();
                noviceTaskConfigVO.setTaskName(taskConfigBO.getTaskName());
                noviceTaskConfigVO.setPicUrl(taskConfigBO.getPicUrl());
                noviceTaskConfigVO.setTaskType(taskConfigBO.getTaskType());
                noviceTaskConfigVO.setGiftNum(taskConfigBO.getGiftNum());
                noviceTaskConfigVO.setOrderNum(taskConfigBO.getOrderNum());
                response.getTasks().add(noviceTaskConfigVO);
            }
        }
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取任务进度", httpMethod = "GET")
    @RestMapping(path = "/{accountId}/tasks/process", method = RequestMethod.GET)
    public RestResult<QueryNoviceTaskProcessResponse> queryNoviceTaskProcess(
            @PathVariable(value = "accountId") String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {

        QueryNoviceTaskProcessOutputDTO outputDTO =
                noviceTaskService.queryNoviceTaskProcess(accountId, tenantId);

        QueryNoviceTaskProcessResponse response = new QueryNoviceTaskProcessResponse();
        response.setNeedNotify(outputDTO.isNeedNotify());
        response.setTaskName(outputDTO.getTaskName());
        response.setOrganizeName(outputDTO.getOrganizeName());
        response.setOrganizeComplete(outputDTO.isOrganizeComplete());
        response.setTotalGiftNum(outputDTO.getTotalGiftNum());
        response.setRemainGiftNum(outputDTO.getRemainGiftNum());
        response.setHasNextOrgTask(outputDTO.isHasNextOrgTask());
        return RestResult.success(response);
    }

    @ApiOperation(value = "判断用户指定新手操作是否已完成", httpMethod = "GET")
    @RestMapping(path = "/{accountId}/operations/check", method = RequestMethod.GET)
    public RestResult<CheckNoviceOperateResponse> checkNoviceOperate(
            @PathVariable(value = "accountId") String accountId,
            @RequestParam("operateName") String operateName) {
        CheckNoviceOperateOutputDTO outputDTO =
                noviceOperateService.checkNoviceOperate(accountId, operateName);

        CheckNoviceOperateResponse response = new CheckNoviceOperateResponse();
        response.setDone(outputDTO.isDone());
        return RestResult.success(response);
    }

    @ApiOperation(value = "完成用户指定新手操作", httpMethod = "POST")
    @RestMapping(path = "/{accountId}/operations/complete", method = RequestMethod.POST)
    public RestResult completeNoviceOperate(
            @PathVariable(value = "accountId") String accountId,
            @RequestBody CompleteNoviceOperateRequest request) {
        noviceOperateService.completeNoviceOperate(accountId, request.getOperateName());
        return RestResult.success();
    }
}
