package com.timevale.saasbiz.rest.converter;

import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectFormFieldDTO;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectEditResourceAuthInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectFormSaveInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectFormUpdateInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectTaskBatchEditAuthInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.input.InfoCollectTaskEditAuthInputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.output.InfoCollectAuthRoleOutputDTO;
import com.timevale.saasbiz.model.bean.infocollect.dto.output.InfoCollectResourceAuthOutputDTO;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormSaveRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormUpdateRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskBatchEditAuthRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskEditAuthRequest;
import com.timevale.saasbiz.rest.bean.infocollect.vo.InfoCollectAuthRoleVO;
import com.timevale.saasbiz.rest.bean.infocollect.vo.InfoCollectFormFieldVO;
import com.timevale.saasbiz.rest.bean.infocollect.vo.InfoCollectResourceAuthVO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/31 17:18
 */
public class InfoCollectRestConvert {


    public static InfoCollectFormSaveInputDTO saveRequestConvert(InfoCollectFormSaveRequest infoCollectFormSaveRequest) {
        if (null == infoCollectFormSaveRequest) {
            return null;
        }
        InfoCollectFormSaveInputDTO infoCollectFormSaveInputDTO = new InfoCollectFormSaveInputDTO();
        infoCollectFormSaveInputDTO.setName(infoCollectFormSaveRequest.getName());
        infoCollectFormSaveInputDTO.setFormJson(infoCollectFormSaveRequest.getFormJson());
        infoCollectFormSaveInputDTO.setPageConfig(infoCollectFormSaveRequest.getPageConfig());
        return infoCollectFormSaveInputDTO;
    }

    public static InfoCollectFormUpdateInputDTO updateRequestConvert(InfoCollectFormUpdateRequest before) {
        if (null == before) {
            return null;
        }
        InfoCollectFormUpdateInputDTO after = new InfoCollectFormUpdateInputDTO();
        after.setFormId(before.getFormId());
        after.setName(before.getName());
        after.setFormJson(before.getFormJson());
        after.setPageConfig(before.getPageConfig());
        return after;
    }


    public static List<InfoCollectFormFieldVO> fieldConvert(List<InfoCollectFormFieldDTO> before) {
        if (CollectionUtils.isEmpty(before)) {
            return new ArrayList<>();
        }
        List<InfoCollectFormFieldVO> infoCollectFormFieldVOList = new ArrayList();
        for (InfoCollectFormFieldDTO infoCollectFormFieldDTO : before) {
            InfoCollectFormFieldVO infoCollectFormFieldVO = new InfoCollectFormFieldVO();
            infoCollectFormFieldVO.setFieldId(infoCollectFormFieldDTO.getFieldId());
            infoCollectFormFieldVO.setFieldName(infoCollectFormFieldDTO.getFieldName());
            infoCollectFormFieldVO.setFieldType(infoCollectFormFieldDTO.getFieldType());
            if (CollectionUtils.isNotEmpty(infoCollectFormFieldDTO.getOptionals())) {
                infoCollectFormFieldVO.setOptionals(infoCollectFormFieldDTO.getOptionals().stream().map(elm -> {
                    InfoCollectFormFieldVO.Optional optional = new InfoCollectFormFieldVO.Optional();
                    optional.setLabel(elm.getLabel());
                    optional.setValue(elm.getValue());
                    return optional;
                }).collect(Collectors.toList()));
            }
            infoCollectFormFieldVOList.add(infoCollectFormFieldVO);
        }
        return infoCollectFormFieldVOList;
    }


    public static InfoCollectTaskEditAuthInputDTO taskEditAuthConvert(InfoCollectTaskEditAuthRequest before) {
        if (null == before) {
            return null;
        }
        InfoCollectTaskEditAuthInputDTO after = new InfoCollectTaskEditAuthInputDTO();
//        after.setSubjectOid(before.getSubjectOid());
//        after.setOperatorOid(before.getOperatorOid());
        after.setFormId(before.getFormId());
        after.setInfoCollectTaskId(before.getInfoCollectTaskId());
        if (CollectionUtils.isNotEmpty(before.getAuthList())) {
            List<InfoCollectEditResourceAuthInputDTO> authInputDTOS = before.getAuthList().stream().map(elm -> {
                InfoCollectEditResourceAuthInputDTO editResourceAuthInputDTO = new InfoCollectEditResourceAuthInputDTO();
                editResourceAuthInputDTO.setAuthorizedEntityType(elm.getAuthorizedEntityType());
                editResourceAuthInputDTO.setAuthorizedEntityName(elm.getAuthorizedEntityName());
                editResourceAuthInputDTO.setAuthorizedEntityId(elm.getAuthorizedEntityId());
                editResourceAuthInputDTO.setAuthorizedEntityIdAlias(elm.getAuthorizedEntityIdAlias());
                editResourceAuthInputDTO.setRoleId(elm.getRoleId());
                editResourceAuthInputDTO.setOperateType(elm.getOperateType());
                return editResourceAuthInputDTO;
            }).collect(Collectors.toList());
            after.setAuthList(authInputDTOS);
        }
        return after;
    }

    public static InfoCollectTaskBatchEditAuthInputDTO taskBothEditAuthConvert(InfoCollectTaskBatchEditAuthRequest before) {
        if (null == before) {
            return null;
        }
        InfoCollectTaskBatchEditAuthInputDTO after = new InfoCollectTaskBatchEditAuthInputDTO();
        after.setFormTaskRelationMap(before.getFormTaskRelationMap());
        if (CollectionUtils.isNotEmpty(before.getAuthList())) {
            List<InfoCollectEditResourceAuthInputDTO> authInputDTOS = before.getAuthList().stream().map(elm -> {
                InfoCollectEditResourceAuthInputDTO editResourceAuthInputDTO = new InfoCollectEditResourceAuthInputDTO();
                editResourceAuthInputDTO.setAuthorizedEntityType(elm.getAuthorizedEntityType());
                editResourceAuthInputDTO.setAuthorizedEntityName(elm.getAuthorizedEntityName());
                editResourceAuthInputDTO.setAuthorizedEntityId(elm.getAuthorizedEntityId());
                editResourceAuthInputDTO.setAuthorizedEntityIdAlias(elm.getAuthorizedEntityIdAlias());
                editResourceAuthInputDTO.setRoleId(elm.getRoleId());
                editResourceAuthInputDTO.setOperateType(elm.getOperateType());
                return editResourceAuthInputDTO;
            }).collect(Collectors.toList());
            after.setAuthList(authInputDTOS);
        }
        return after;
    }



    public static List<InfoCollectResourceAuthVO> resourceAuthConvert(List<InfoCollectResourceAuthOutputDTO> beforeList) {
        if (CollectionUtils.isEmpty(beforeList)) {
            return new ArrayList<>();
        }
        List<InfoCollectResourceAuthVO> afterList = new ArrayList();
        for (InfoCollectResourceAuthOutputDTO beforeSingle : beforeList) {
            InfoCollectResourceAuthVO afterSingle = new InfoCollectResourceAuthVO();
            afterSingle.setCreateTime(beforeSingle.getCreateTime());
            afterSingle.setUpdateTime(beforeSingle.getUpdateTime());
            afterSingle.setSubjectOid(beforeSingle.getSubjectOid());
            afterSingle.setSubjectGid(beforeSingle.getSubjectGid());
            afterSingle.setResourceType(beforeSingle.getResourceType());
            afterSingle.setResourceId(beforeSingle.getResourceId());
            afterSingle.setResourceName(beforeSingle.getResourceName());
            afterSingle.setAuthorizerOid(beforeSingle.getAuthorizerOid());
            afterSingle.setAuthorizerGid(beforeSingle.getAuthorizerGid());
            afterSingle.setAuthorizedEntityType(beforeSingle.getAuthorizedEntityType());
            afterSingle.setAuthorizedEntityId(beforeSingle.getAuthorizedEntityId());
            afterSingle.setAuthorizedEntityIdAlias(beforeSingle.getAuthorizedEntityIdAlias());
            afterSingle.setAuthorizedEntityName(beforeSingle.getAuthorizedEntityName());
            afterSingle.setRoleId(beforeSingle.getPermission());
            afterSingle.setAvailableRoles(authRoleConvert(beforeSingle.getAvailableRoles()));
            afterSingle.setCanEdit(beforeSingle.getCanEdit());
            afterSingle.setAuthUserDisplayType(beforeSingle.getAuthUserDisplayType());
            afterList.add(afterSingle);
        }
        return afterList;
    }


    public static List<InfoCollectAuthRoleVO> authRoleConvert(List<InfoCollectAuthRoleOutputDTO> beforeList) {
        if (CollectionUtils.isEmpty(beforeList)) {
            return new ArrayList();
        }
        List<InfoCollectAuthRoleVO> afterList = new ArrayList();
        for (InfoCollectAuthRoleOutputDTO before : beforeList) {

            InfoCollectAuthRoleVO after = new InfoCollectAuthRoleVO();
            after.setId(before.getId());
            after.setName(before.getName());
            after.setDesc(before.getDesc());
            after.setRoleKey(before.getRoleKey());
            afterList.add(after);
        }
        return afterList;
    }


}
