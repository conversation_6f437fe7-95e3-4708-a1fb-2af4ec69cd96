package com.timevale.saasbiz.rest;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_RESOURCE_TENANT_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.FunctionCodeConstants.FLOW_TEMPLATE_CATEGORY;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.RelationAndPrivilegeCheck;
import com.timevale.saasbiz.model.bean.usercenter.dto.CategoryDTO;
import com.timevale.saasbiz.model.constants.AuthRelationBizSceneConstants;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.flowtemplate.request.*;
import com.timevale.saasbiz.service.flowtemplate.FlowTemplateCategoryService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023-03-08 10:21
 */
@Api(tags = "流程模板分类接口")
@Validated
@ExternalService
@RestMapping(path = "/v2/flow-templates")
public class FlowTemplateCategoryRest {
    @Autowired private FlowTemplateCategoryService flowTemplateCategoryService;

    @Autowired private SaasCommonService saasCommonService;

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation("保存流程模板分类")
    @RestMapping(path = "/save-category", method = RequestMethod.POST)
    public RestResult<String> saveCategory(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestBody SaveCategoryRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 版本是否支持模板分类
        saasCommonService.checkSupportFunction(resourceTenantId, clientId, FLOW_TEMPLATE_CATEGORY);

        String categoryId =
                flowTemplateCategoryService.saveCategory(
                        tenantId, resourceTenantId, accountId, request.getCategoryId(), request.getCategoryName());

        return RestResult.success(categoryId);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation("删除流程模板分类")
    @RestMapping(path = "/delete-category", method = RequestMethod.POST)
    public RestResult<Void> deleteCategory(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestBody DeleteCategoryRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 版本是否支持模板分类
        saasCommonService.checkSupportFunction(resourceTenantId, clientId, FLOW_TEMPLATE_CATEGORY);

        flowTemplateCategoryService.removeCategory(tenantId, resourceTenantId, accountId, request.getCategoryId());

        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("流程模板分类列表")
    @RestMapping(path = "/category-list", method = RequestMethod.GET)
    public RestResult<List<CategoryDTO>> listCategory(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {

        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        List<CategoryDTO> list = flowTemplateCategoryService.listCategory(resourceTenantId);

        return RestResult.success(list);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation("添加流程模板到分类下")
    @RestMapping(path = "/add-to-category", method = RequestMethod.POST)
    public RestResult<Void> addToCategory(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestBody AddToCategoryRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 版本是否支持模板分类
        saasCommonService.checkSupportFunction(tenantId, clientId, FLOW_TEMPLATE_CATEGORY);

        flowTemplateCategoryService.addToCategory(
                tenantId, resourceTenantId, accountId, request.getCategoryId(), request.getFlowTemplateId());

        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation("批量添加流程模板到分类下")
    @RestMapping(path = "/batch-to-category", method = RequestMethod.POST)
    public RestResult<Void> batchToCategory(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestBody BatchToCategoryRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 版本是否支持模板分类
        saasCommonService.checkSupportFunction(tenantId, clientId, FLOW_TEMPLATE_CATEGORY);

        flowTemplateCategoryService.batchToCategory(
                tenantId, resourceTenantId, accountId, request.getCategoryId(), request.getFlowTemplateIds());

        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation("从分类下删除模板")
    @RestMapping(path = "/remove-from-category", method = RequestMethod.POST)
    public RestResult<Void> removeFromCategory(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestBody RemoveFromCategoryRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 版本是否支持模板分类
        saasCommonService.checkSupportFunction(tenantId, clientId, FLOW_TEMPLATE_CATEGORY);

        flowTemplateCategoryService.removeFromCategory(
                tenantId, resourceTenantId, accountId, request.getCategoryId(), request.getFlowTemplateId());

        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation("添加流程模板到多个分类下")
    @RestMapping(path = "/add-to-categories", method = RequestMethod.POST)
    public RestResult<Void> addToCategories(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestBody AddToCategoriesRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 版本是否支持模板分类
        saasCommonService.checkSupportFunction(tenantId, clientId, FLOW_TEMPLATE_CATEGORY);

        flowTemplateCategoryService.addToCategories(
                tenantId, resourceTenantId, accountId, request.getFlowTemplateId(), request.getCategoryIds());

        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation("流程模板置顶")
    @RestMapping(path = "/top", method = RequestMethod.POST)
    public RestResult<Void> topFlowTemplate(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody BaseFlowTemplateRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        flowTemplateCategoryService.topFlowTemplate(tenantId, resourceTenantId, accountId, request.getFlowTemplateId());

        return RestResult.success();
    }
}
