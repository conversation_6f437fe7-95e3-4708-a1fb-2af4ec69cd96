package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.process.dto.input.ProcessRemarkInputDTO;
import com.timevale.saasbiz.model.bean.process.dto.input.QueryProcessRemarkInputDTO;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessRemarkOutputDTO;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.process.request.ProcessRemarkAddRequest;
import com.timevale.saasbiz.service.process.ProcessRemarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2023-05-23
 */
@Api(tags = "合同备注")
@Validated
@ExternalService
@RestMapping(path = "/v2/processes")
@Slf4j
public class ProcessRemarkRest {

    @Autowired private ProcessRemarkService processRemarkService;

    @ApiOperation("新增合同备注")
    @RestMapping(path = "/add-remark", method = RequestMethod.POST)
    public RestResult<Void> addRemark(@RequestBody ProcessRemarkAddRequest request) {
        ProcessRemarkInputDTO inputDTO = new ProcessRemarkInputDTO();
        inputDTO.setProcessId(request.getProcessId());
        inputDTO.setRemark(request.getRemark());
        inputDTO.setMenuId(request.getMenuId());
        inputDTO.setOperatorId(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectId(RequestContextExtUtils.getTenantId());
        processRemarkService.addRemark(inputDTO);
        return RestResult.success();
    }

    @ApiOperation("查询合同备注")
    @RestMapping(path = "/get-remark", method = RequestMethod.GET)
    public RestResult<List<ProcessRemarkOutputDTO>> queryRemark(
            @ApiParam(value = "合同id", required = true) @RequestParam String processId,
            @ApiParam(value = "菜单id") @RequestParam(required = false) String menuId) {
        QueryProcessRemarkInputDTO inputDTO = new QueryProcessRemarkInputDTO();
        inputDTO.setProcessId(processId);
        inputDTO.setMenuId(menuId);
        inputDTO.setOperatorId(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectId(RequestContextExtUtils.getTenantId());
        List<ProcessRemarkOutputDTO> outputDTOS = processRemarkService.queryByProcessId(inputDTO);
        return RestResult.success(outputDTOS);
    }
}
