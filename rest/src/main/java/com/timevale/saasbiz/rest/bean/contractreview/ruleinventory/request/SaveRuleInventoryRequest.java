package com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaveRuleInventoryRequest extends ToString {

    @ApiModelProperty("审查清单id")
    private String inventoryId;

    @NotBlank(message = "审查清单名称不能为空")
    @ApiModelProperty("审查清单名称")
    @Length(max = 20, message = "审查清单名称不能超过20字符")
    private String inventoryName;

    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("审查视角")
    private String contractView;

    @ApiModelProperty("审查规则列表")
    private List<ReviewRuleVO> rules;

    @Data
    public static class ReviewRuleVO extends ToString {
        private String ruleId;
    }

    private String recordId;
}