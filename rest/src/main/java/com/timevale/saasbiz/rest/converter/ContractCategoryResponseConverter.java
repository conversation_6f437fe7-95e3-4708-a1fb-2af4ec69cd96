package com.timevale.saasbiz.rest.converter;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.contractcategory.bo.*;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同类型响应数据转换类
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
public class ContractCategoryResponseConverter {

    /**
     * 转换场景下的合同类型列表VO对象
     *
     * @param sceneContractCategoryBO
     * @return
     */
    public static SceneContractCategoryVO convert2SceneContractCategoryVO(
            SceneContractCategoryBO sceneContractCategoryBO) {
        SceneContractCategoryVO sceneContractCategoryVO = new SceneContractCategoryVO();
        sceneContractCategoryVO.setScene(sceneContractCategoryBO.getScene());
        sceneContractCategoryVO.setSceneName(sceneContractCategoryBO.getSceneName());
        if (CollectionUtils.isNotEmpty(sceneContractCategoryBO.getCategories())) {
            sceneContractCategoryVO.setCategories(
                    sceneContractCategoryBO.getCategories().stream()
                            .map(j -> convert2ContractCategoryVO(j))
                            .collect(Collectors.toList()));
        }
        return sceneContractCategoryVO;
    }

    /**
     * 转换合同类型VO对象
     *
     * @param categoryBO
     * @return
     */
    public static ContractCategoryVO convert2ContractCategoryVO(ContractCategoryBO categoryBO) {
        ContractCategoryVO contractCategoryVO = new ContractCategoryVO();
        contractCategoryVO.setCategoryId(categoryBO.getCategoryId());
        contractCategoryVO.setCategoryName(categoryBO.getCategoryName());
        contractCategoryVO.setExtractFields(convert2ExtractFieldVOs(categoryBO.getExtractFields()));
        return contractCategoryVO;
    }

    /**
     * 批量转换提取字段VO对象
     *
     * @param extractFieldBOList
     * @return
     */
    public static List<ExtractFieldVO> convert2ExtractFieldVOs(
            List<ExtractFieldBO> extractFieldBOList) {
        if (CollectionUtils.isEmpty(extractFieldBOList)) {
            return Lists.newArrayList();
        }
        return extractFieldBOList.stream()
                .map(i -> convert2ExtractFieldVO(i))
                .collect(Collectors.toList());
    }

    /**
     * 转换提取字段VO对象
     *
     * @param extractFieldBO
     * @return
     */
    private static ExtractFieldVO convert2ExtractFieldVO(ExtractFieldBO extractFieldBO) {
        ExtractFieldVO extractFieldVO = new ExtractFieldVO();
        extractFieldVO.setFieldId(extractFieldBO.getFieldId());
        extractFieldVO.setFieldName(extractFieldBO.getFieldName());
        extractFieldVO.setFieldDesc(extractFieldBO.getFieldDesc());
        extractFieldVO.setFieldType(extractFieldBO.getFieldType());
        return extractFieldVO;
    }

    /**
     * 批量转换关联流程模板文件VO对象
     *
     * @param relateFlowTemplateFileBOS
     * @return
     */
    public static List<RelateFlowTemplateFileVO> convert2RelateFlowTemplateFileVOs(
            List<RelateFlowTemplateFileBO> relateFlowTemplateFileBOS) {
        if (CollectionUtils.isEmpty(relateFlowTemplateFileBOS)) {
            return Lists.newArrayList();
        }
        return relateFlowTemplateFileBOS.stream()
                .map(i -> convert2RelateFlowTemplateFileVO(i))
                .collect(Collectors.toList());
    }

    /**
     * 转换关联流程模板文件VO对象
     *
     * @param relateFlowTemplateFileBO
     * @return
     */
    private static RelateFlowTemplateFileVO convert2RelateFlowTemplateFileVO(
            RelateFlowTemplateFileBO relateFlowTemplateFileBO) {
        RelateFlowTemplateFileVO fileVO = new RelateFlowTemplateFileVO();
        fileVO.setFlowTemplateId(relateFlowTemplateFileBO.getFlowTemplateId());
        fileVO.setFlowTemplateName(relateFlowTemplateFileBO.getFlowTemplateName());
        fileVO.setRelateFileIds(relateFlowTemplateFileBO.getRelateFileIds());
        return fileVO;
    }

    /**
     * 批量转换流程模板对应的文件合同类型VO对象
     *
     * @param fileContractCategoryBOS
     * @return
     */
    public static List<FileContractCategoryVO> convert2FileContractCategoryVOs(
            List<FileContractCategoryBO> fileContractCategoryBOS) {
        if (CollectionUtils.isEmpty(fileContractCategoryBOS)) {
            return Lists.newArrayList();
        }
        return fileContractCategoryBOS.stream()
                .map(i -> convert2FileContractCategoryVO(i))
                .collect(Collectors.toList());
    }

    /**
     * 转换关联流程模板文件VO对象
     *
     * @param fileContractCategoryBO
     * @return
     */
    private static FileContractCategoryVO convert2FileContractCategoryVO(
            FileContractCategoryBO fileContractCategoryBO) {
        FileContractCategoryVO fileContractCategoryVO = new FileContractCategoryVO();
        fileContractCategoryVO.setCategoryId(fileContractCategoryBO.getCategoryId());
        fileContractCategoryVO.setCategoryName(fileContractCategoryBO.getCategoryName());
        fileContractCategoryVO.setFileId(fileContractCategoryBO.getFileId());
        return fileContractCategoryVO;
    }
}
