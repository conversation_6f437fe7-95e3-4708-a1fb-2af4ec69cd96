package com.timevale.saasbiz.rest.bean.contractreview.rule.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RuleCopyRequest extends ToString {
    @NotBlank(message = "规则ID不能为空")
    @ApiModelProperty("规则ID")
    private String ruleId;

    @NotBlank(message = "规则名称不能为空")
    @ApiModelProperty("规则名称")
    @Length(max = 50, message = "规则名称不能超过50字符")
    private String ruleName;
}
