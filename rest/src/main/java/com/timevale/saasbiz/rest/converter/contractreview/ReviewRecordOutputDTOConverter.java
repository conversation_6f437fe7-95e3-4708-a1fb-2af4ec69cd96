package com.timevale.saasbiz.rest.converter.contractreview;

import com.google.common.collect.Lists;
import com.timevale.saasbiz.model.bean.contractreview.record.bo.ReviewRecordPageBO;
import com.timevale.saasbiz.rest.bean.contractreview.record.vo.PageReviewRecordVO;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class ReviewRecordOutputDTOConverter {
    public static List<PageReviewRecordVO> convertToListVOS(List<ReviewRecordPageBO> boList) {
        return Optional.ofNullable(boList).orElse(Lists.newArrayList()).stream()
                .map(ReviewRecordOutputDTOConverter::convertToListVO)
                .collect(Collectors.toList());
    }

    private static PageReviewRecordVO convertToListVO(ReviewRecordPageBO bo) {
        PageReviewRecordVO vo = new PageReviewRecordVO();
        vo.setRecordId(bo.getRecordId());
        vo.setStatus(bo.getStatus());
        vo.setFileKey(bo.getFileKey());
        vo.setDraftId(bo.getDraftId());
        vo.setFileName(bo.getFileName());
        vo.setModifyTime(bo.getModifyTime());
        vo.setModifyTime(bo.getModifyTime());
        return vo;
    }
}
