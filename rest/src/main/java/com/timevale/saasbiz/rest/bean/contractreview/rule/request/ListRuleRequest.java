package com.timevale.saasbiz.rest.bean.contractreview.rule.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListRuleRequest extends ToString {
    @ApiModelProperty("规则清单ID")
    private String inventoryId;

    @NotBlank(message = "规则组ID不能为空")
    @ApiModelProperty("规则组ID")
    private String groupId;
}
