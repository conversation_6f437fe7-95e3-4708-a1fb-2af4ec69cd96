package com.timevale.saasbiz.rest.bean.contractaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Data
public class ContractAuditKeywordSearchRequest {
    @ApiModelProperty(value = "合同id")
    @NotBlank(message = "合同id不能为空")
    private String processId;
    @ApiModelProperty(value = "文件id")
    @NotBlank(message = "文件id不能为空")
    private String fileId;
    @ApiModelProperty(value = "关键词列表")
    @NotEmpty(message = "关键词列表不能为空")
    private List<String> keywords;
    @ApiModelProperty(value = "接收的矩形坐标格式，lbrt 左下角和右上角坐标组成 center 中心点坐标加长宽组成。默认lbrt")
    private String acceptPosFormat;
}
