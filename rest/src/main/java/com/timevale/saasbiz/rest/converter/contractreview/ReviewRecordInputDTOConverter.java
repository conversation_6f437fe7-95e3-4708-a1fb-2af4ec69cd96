package com.timevale.saasbiz.rest.converter.contractreview;

import com.timevale.saasbiz.model.bean.contractreview.review.dto.input.ContractReviewCreateDocInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.ReviewRecordIdInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.ReviewRecordPageInputDTO;
import com.timevale.saasbiz.rest.bean.contractreview.record.request.ReviewRecordIdRequest;
import com.timevale.saasbiz.rest.bean.contractreview.record.request.ReviewRecordPageRequest;
import com.timevale.saasbiz.rest.bean.contractreview.review.ContractReviewCreateDocRequest;

public class ReviewRecordInputDTOConverter {
    public static ReviewRecordPageInputDTO convertToInputDTO(String accountId, String tenantId, ReviewRecordPageRequest request) {
        ReviewRecordPageInputDTO dto = new ReviewRecordPageInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setFileName(request.getFileName());
        dto.setStatus(request.getStatus());
        dto.setPageNum(request.getPageNum());
        dto.setPageSize(request.getPageSize());
        return dto;
    }

    public static ReviewRecordIdInputDTO convertToInputDTO(String accountId, String tenantId, ReviewRecordIdRequest request) {
        ReviewRecordIdInputDTO dto = new ReviewRecordIdInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setRecordId(request.getRecordId());
        return dto;
    }

        public static ContractReviewCreateDocInputDTO convertToInputDTO(String accountId, String tenantId, ContractReviewCreateDocRequest request) {
            ContractReviewCreateDocInputDTO dto = new ContractReviewCreateDocInputDTO();
            dto.setAccountId(accountId);
            dto.setTenantId(tenantId);
            dto.setFileId(request.getFileId());
            return dto;
        }

}
