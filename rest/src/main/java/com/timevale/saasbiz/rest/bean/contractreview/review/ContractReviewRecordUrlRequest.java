package com.timevale.saasbiz.rest.bean.contractreview.review;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ContractReviewRecordUrlRequest extends ToString {
    @NotBlank(message = "审查记录id不能为空")
    private String recordId;
    /** 页面关闭地址 */
    private String closeRedirectUrl;
    /** 页面保存地址 */
    private String saveRedirectUrl;
}
