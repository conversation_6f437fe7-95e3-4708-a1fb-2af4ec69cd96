package com.timevale.saasbiz.rest.bean.contractaudit.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@ApiModel(value = "合同审查查询参数")
public class ContractAuditPageQueryRequest extends ToString {
    @ApiModelProperty(value = "页码")
    private Integer pageNum;
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;
    @ApiModelProperty(value = "文件名")
    private String fileName;
    @ApiModelProperty(value = "审查阶段")
    private String jobStage;
    @ApiModelProperty(value = "审查状态")
    private String jobStatus;
}
