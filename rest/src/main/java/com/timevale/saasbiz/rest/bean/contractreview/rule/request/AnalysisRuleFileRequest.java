package com.timevale.saasbiz.rest.bean.contractreview.rule.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AnalysisRuleFileRequest extends ToString {
    @NotBlank(message = "文件KEY不能为空")
    @ApiModelProperty("文件KEY")
    private String fileKey;

    @NotBlank(message = "规则组ID不能为空")
    @ApiModelProperty("规则组ID")
    private String groupId;
}
