package com.timevale.saasbiz.rest.converter;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.input.biz.ListFlowTemplateBizInputDTO;
import com.timevale.saasbiz.rest.bean.flowtemplate.request.ListFlowTemplateRequest;
import ma.glasnost.orika.MapperFactory;
import org.assertj.core.util.Lists;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程模板业务层输入对象转换器
 *
 * <AUTHOR>
 * @since 2023-08-08 11:32
 */
public class FlowTemplateInputDTOConverter {
    /**
     * 流程模板列表request -> inputDTO
     *
     * @param request 转换对象
     * @param mapperFactory 对象转换工厂
     * @return 目标对象
     */
    public static ListFlowTemplateBizInputDTO converterListFlowTemplateBizInputDTO(
            ListFlowTemplateRequest request, MapperFactory mapperFactory) {
        // 1.基本属性转换
        ListFlowTemplateBizInputDTO inputDTO =
                mapperFactory.getMapperFacade().map(request, ListFlowTemplateBizInputDTO.class);
        // 2.填充漏掉的
        inputDTO.setContainsShared(request.isContainShared());
        // 3.将状态字符串转换为数组
        List<Integer> status = Lists.emptyList();
        if (StringUtils.isNotEmpty(request.getStatus())) {
            String[] split = request.getStatus().split(",");
            status = Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());
        }
        // 4.将要排除的流程模板列表转换为数组
        List<String> excludeLabels = Lists.emptyList();
        if (StringUtils.isNotEmpty(request.getExcludeLabels())) {
            excludeLabels = Arrays.asList(request.getExcludeLabels().split(","));
        }
        // 5.将流程模板分类id列表转换
        List<String> categoryIds = Lists.emptyList();
        if (StringUtils.isNotBlank(request.getCategoryId())) {
            categoryIds = Collections.singletonList(request.getCategoryId());
        }
        inputDTO.setStatusList(status);
        inputDTO.setExcludeLabelList(excludeLabels);
        inputDTO.setCategoryIds(categoryIds);
        inputDTO.setTenantId(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorId(RequestContextExtUtils.getOperatorId());
        inputDTO.setResourceTenantId(RequestContextExtUtils.getResourceTenantId());
        return inputDTO;
    }
}
