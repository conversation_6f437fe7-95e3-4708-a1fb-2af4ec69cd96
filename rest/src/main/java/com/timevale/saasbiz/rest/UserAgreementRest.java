package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.privilege.service.enums.BuiltinRoleType;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserRoleCheck;
import com.timevale.saasbiz.model.bean.oauth.dto.output.QueryLatestUserAgreementOutputDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.oauth.request.SaveUserAgreementRequest;
import com.timevale.saasbiz.service.oauth.UserAgreementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * 用户同意协议管理
 *
 * <AUTHOR>
 * @since 2023-08-26
 */
@Api(tags = "用户同意协议管理")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
@Validated
public class UserAgreementRest {

    @Autowired
    UserAgreementService userAgreementService;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询用户是否已同意协议", httpMethod = "GET")
    @RestMapping(path = "/user-agreements/check-agreed", method = RequestMethod.GET)
    public RestResult<Boolean> checkUserAgreed(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @ApiParam("协议类型， ENABLE_AI-AI功能开通协议")
                    @RequestParam("agreementType")
                    @NotBlank(message = "协议类型不能为空")
                    String agreementType) {
        QueryLatestUserAgreementOutputDTO outputDTO =
                userAgreementService.queryLatestUserAgreement(accountId, subjectId, agreementType);
        return RestResult.success(null != outputDTO.getLatestAgreeTime());
    }

    @UserRoleCheck(roleKey = {BuiltinRoleType.ADMIN, BuiltinRoleType.ORGAN_LEGAL})
    @ApiOperation(value = "同意用户协议", httpMethod = "POST")
    @RestMapping(path = "/user-agreements/agree", method = RequestMethod.POST)
    public RestResult agree(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody SaveUserAgreementRequest request) {
        userAgreementService.saveUserAgreement(accountId, subjectId, request.getAgreementType());
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "通知用户同意协议", httpMethod = "GET")
    @RestMapping(path = "/user-agreements/notice-to-agree", method = RequestMethod.GET)
    public RestResult noticeToAgree(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @ApiParam("协议类型， ENABLE_AI-AI功能开通协议")
                    @RequestParam("agreementType")
                    @NotBlank(message = "协议类型不能为空")
                    String agreementType) {
        userAgreementService.noticeToAgree(accountId, subjectId, agreementType);
        return RestResult.success();
    }
}
