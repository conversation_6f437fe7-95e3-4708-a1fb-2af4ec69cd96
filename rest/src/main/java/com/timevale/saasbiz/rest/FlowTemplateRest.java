package com.timevale.saasbiz.rest;

import com.timevale.docmanager.service.input.epaas.EpaasBatchCreateTemplateByFileIdsInput;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.RelationAndPrivilegeCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.datasource.dto.DataSourceChannelFilterDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.FlowTemplateBaseInfoDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.FlowTemplateSimpleFilesDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.input.*;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.input.base.BaseBatchFlowTemplateOperateInputDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.input.base.BaseFlowTemplateInputDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.input.biz.*;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.output.*;
import com.timevale.saasbiz.model.bean.lowcode.DataSourceChannelDTO;
import com.timevale.saasbiz.model.bean.lowcode.DataSourceDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.ListPermissionInfoDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.output.EpaasStructChoiceListConfigOutputDTO;
import com.timevale.saasbiz.model.constants.AuthRelationBizSceneConstants;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.flowtemplate.request.*;
import com.timevale.saasbiz.rest.bean.flowtemplate.response.*;
import com.timevale.saasbiz.rest.bean.flowtemplate.vo.FlowTemplateSimpleFilesVO;
import com.timevale.saasbiz.rest.bean.usercenter.PermissionInfoResponse;
import com.timevale.saasbiz.rest.converter.FlowTemplateDataSourceConverter;
import com.timevale.saasbiz.rest.converter.FlowTemplateInputDTOConverter;
import com.timevale.saasbiz.rest.converter.FlowTemplateResponseConverter;
import com.timevale.saasbiz.service.flowtemplate.FlowTemplateBizService;
import com.timevale.saasbiz.service.flowtemplate.FlowTemplateDataSourceService;
import com.timevale.saasbiz.service.flowtemplate.factory.FlowTemplateParticipantSealServiceFactory;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_RESOURCE_TENANT_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR>
 * @since 2023-08-04 15:55
 */
@Api(tags = "合同管理流程模板管理")
@ExternalService
@RestMapping(path = "/v2")
@Slf4j
public class FlowTemplateRest {

    @Autowired private MapperFactory mapperFactory;
    @Autowired private UserCenterService userCenterService;
    @Autowired private FlowTemplateBizService flowTemplateBizService;
    @Autowired private FlowTemplateDataSourceService flowTemplateDataSourceService;
    @Autowired private FlowTemplateParticipantSealServiceFactory participantSealServiceFactory;

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation(value = "流程模板的文件列表", httpMethod = "GET")
    @RestMapping(
            path = "/processes/flowTemplates/files/{flowTemplateId}",
            method = RequestMethod.GET)
    public RestResult<ListFlowTemplateFileResponse> listFlowTemplateFile(
            @PathVariable String flowTemplateId) {
        // 请求对象 -> 业务层入参
        BaseFlowTemplateInputDTO inputDTO = new BaseFlowTemplateInputDTO();
        inputDTO.setFlowTemplateId(flowTemplateId);
        inputDTO.setOperatorId(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectId(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        ListFlowTemplateFileOutputDTO outputDTO = flowTemplateBizService.listFile(inputDTO);
        // 业务层出参 -> 响应对象
        ListFlowTemplateFileResponse response =
                FlowTemplateResponseConverter.convertListFlowTemplateFileResponse(outputDTO);
        return RestResult.success(response);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation(value = "删除流程模板", httpMethod = "DELETE")
    @RestMapping(path = "/processes/flowTemplates/{flowTemplateId}", method = RequestMethod.DELETE)
    public RestResult<?> deleteFlowTemplate(@PathVariable String flowTemplateId) {
        ChangeStatusBizInputDTO inputDTO = new ChangeStatusBizInputDTO();
        inputDTO.setFlowTemplateId(flowTemplateId);
        inputDTO.setSubjectId(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setAccountId(RequestContextExtUtils.getOperatorId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());

        flowTemplateBizService.delete(inputDTO);
        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation(value = "启用流程模板状态", httpMethod = "PUT")
    @RestMapping(
            path = "/processes/flowTemplates/{flowTemplateId}/enable",
            method = RequestMethod.PUT)
    public RestResult<?> enableFlowTemplate(@PathVariable String flowTemplateId) {
        ChangeStatusBizInputDTO inputDTO = new ChangeStatusBizInputDTO();
        inputDTO.setFlowTemplateId(flowTemplateId);
        inputDTO.setSubjectId(RequestContextExtUtils.getTenantId());
        inputDTO.setAccountId(RequestContextExtUtils.getOperatorId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());

        flowTemplateBizService.enable(inputDTO);
        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation(value = "禁用流程模板状态", httpMethod = "PUT")
    @RestMapping(
            path = "/processes/flowTemplates/{flowTemplateId}/disable",
            method = RequestMethod.PUT)
    public RestResult<?> disableFlowTemplate(@PathVariable String flowTemplateId) {
        ChangeStatusBizInputDTO inputDTO = new ChangeStatusBizInputDTO();
        inputDTO.setFlowTemplateId(flowTemplateId);
        inputDTO.setSubjectId(RequestContextExtUtils.getTenantId());
        inputDTO.setAccountId(RequestContextExtUtils.getOperatorId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());

        flowTemplateBizService.disable(inputDTO);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.TEMPLATE_INTELLIGENT_ADD)
    @ApiOperation(value = "AI识别流程模板中文件的控件", httpMethod = "GET")
    @RestMapping(
            path = "/flowTemplates/{flowTemplateId}/files/{fileId}/parseStructComponents",
            method = RequestMethod.GET)
    public RestResult<FlowTemplateAiParseResponse> aiParseStructComponent(
            @PathVariable String flowTemplateId, @PathVariable String fileId) {
        // 置放参数
        FlowTemplateAiParseBizInputDTO inputDTO = new FlowTemplateAiParseBizInputDTO();
        inputDTO.setFlowTemplateId(flowTemplateId);
        inputDTO.setFileId(fileId);
        inputDTO.setTenantId(RequestContextExtUtils.getTenantId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        // 调用
        FlowTemplateAiParseOutputDTO outputDTO =
                flowTemplateBizService.aiParseStructComponents(inputDTO);
        // 转换结果对象
        FlowTemplateAiParseResponse response =
                mapperFactory.getMapperFacade().map(outputDTO, FlowTemplateAiParseResponse.class);
        return RestResult.success(response);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation(value = "流程模板列表", httpMethod = "GET")
    @RestMapping(path = "/flowTemplates", method = RequestMethod.GET)
    public RestResult<ListFlowTemplateResponse> listFlowTemplate(
            @URIQueryParam ListFlowTemplateRequest request) {
        ListFlowTemplateBizInputDTO inputDTO =
                FlowTemplateInputDTOConverter.converterListFlowTemplateBizInputDTO(
                        request, mapperFactory);
        ListFlowTemplateBizOutputDTO outputDTO = flowTemplateBizService.list(inputDTO);
        ListFlowTemplateResponse response =
                mapperFactory.getMapperFacade().map(outputDTO, ListFlowTemplateResponse.class);
        return RestResult.success(response);
    }

    @Deprecated
    @FunctionPrivilegeCheck(function = FunctionCodeConstant.TEMPLATE_GRANT)
    @ApiOperation(value = "流程模板批量授权", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/batchAuth", method = RequestMethod.POST)
    public RestResult<?> batchAuthFlowTemplate(@RequestBody FlowTemplateBatchAuthRequest request) {
        FlowTemplateBatchAuthBizInputDTO inputDTO =
                mapperFactory
                        .getMapperFacade()
                        .map(request, FlowTemplateBatchAuthBizInputDTO.class);
        inputDTO.setSubjectId(RequestContextExtUtils.getTenantId());
        inputDTO.setAccountId(RequestContextExtUtils.getOperatorId());
        inputDTO.setResourceTenantId(RequestContextExtUtils.getResourceTenantId());
        flowTemplateBizService.batchAuth(inputDTO);
        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @ApiOperation(value = "流程模板权限列表", httpMethod = "GET")
    @RestMapping(path = "/flowTemplates/getPermissionList", method = RequestMethod.GET)
    public RestResult<List<PermissionInfoResponse>> getPermissionList(
            @ApiParam(value = "流程模板id") @RequestParam(value = "flowTemplateId", required = false)
                    String flowTemplateId) {
        ListFlowTemplatePermissionBizInputDTO inputDTO =
                new ListFlowTemplatePermissionBizInputDTO();
        inputDTO.setFlowTemplateId(flowTemplateId);
        inputDTO.setTenantId(RequestContextExtUtils.getTenantId());
        inputDTO.setResourceTenantId(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setOperatorId(RequestContextExtUtils.getOperatorId());
        ListPermissionInfoDTO outputDTO = flowTemplateBizService.getPermissionList(inputDTO);
        List<PermissionInfoResponse> response =
                outputDTO.getPermissionInfoList().stream()
                        .map(
                                i ->
                                        mapperFactory
                                                .getMapperFacade()
                                                .map(i, PermissionInfoResponse.class))
                        .collect(Collectors.toList());
        return RestResult.success(response);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_TEMPLATE,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @ApiOperation(value = "批量获取流程模板文件基本信息列表", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/batch-query-simple-files", method = RequestMethod.POST)
    public RestResult<BatchFlowTemplateSimpleFileResponse> batchQuerySimpleFiles(
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody BatchQueryFlowTemplateRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        AccountInfoDTO subject = userCenterService.queryAccountInfoByOid(resourceTenantId);
        BatchGetFlowTemplateFilesInputDTO inputDTO = new BatchGetFlowTemplateFilesInputDTO();
        inputDTO.setSubjectOid(subject.getOid());
        inputDTO.setSubjectGid(subject.getGid());
        inputDTO.setFlowTemplateIds(request.getFlowTemplateIds());
        inputDTO.setWithContractCategory(request.isWithContractCategory());
        List<FlowTemplateSimpleFilesDTO> simpleFilesDTOS =
                flowTemplateBizService.batchQueryFlowTemplateSimpleFiles(inputDTO);
        BatchFlowTemplateSimpleFileResponse response = new BatchFlowTemplateSimpleFileResponse();
        response.setFlowTemplateFiles(
                mapperFactory
                        .getMapperFacade()
                        .mapAsList(simpleFilesDTOS, FlowTemplateSimpleFilesVO.class));
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_TEMPLATE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @ApiOperation(value = "流程模板批量授权-异步", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/batch-auth-async", method = RequestMethod.POST)
    public RestResult<?> batchAuthAsync(FlowTemplateBatchAuthAsyncRequest request) {
        FlowTemplateBatchAuthAsyncInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, FlowTemplateBatchAuthAsyncInputDTO.class);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setResourceSubjectOid(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        flowTemplateBizService.batchAuthAsync(inputDTO);
        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_TEMPLATE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @ApiOperation(value = "流程模板批量开启-异步", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/batch-enable-async", method = RequestMethod.POST)
    public RestResult<?> batchEnableAsync(FlowTemplateBatchOperateBaseRequest request) {
        BaseBatchFlowTemplateOperateInputDTO inputDTO =
                mapperFactory
                        .getMapperFacade()
                        .map(request, BaseBatchFlowTemplateOperateInputDTO.class);
        inputDTO.setSubjectOid(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        flowTemplateBizService.batchEnableAsync(inputDTO);
        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_TEMPLATE,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @ApiOperation(value = "流程模板批量禁用-异步", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/batch-disable-async", method = RequestMethod.POST)
    public RestResult<?> batchDisableAsync(FlowTemplateBatchOperateBaseRequest request) {
        BaseBatchFlowTemplateOperateInputDTO inputDTO =
                mapperFactory
                        .getMapperFacade()
                        .map(request, BaseBatchFlowTemplateOperateInputDTO.class);
        inputDTO.setSubjectOid(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        flowTemplateBizService.batchDisableAsync(inputDTO);
        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_TEMPLATE,
            privilegeKey = PrivilegeOperationConstants.DELETE)
    @ApiOperation(value = "流程模板批量删除-异步", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/batch-delete-async", method = RequestMethod.POST)
    public RestResult<?> batchDeleteAsync(FlowTemplateBatchOperateBaseRequest request) {
        BaseBatchFlowTemplateOperateInputDTO inputDTO =
                mapperFactory
                        .getMapperFacade()
                        .map(request, BaseBatchFlowTemplateOperateInputDTO.class);
        inputDTO.setSubjectOid(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        flowTemplateBizService.batchDeleteAsync(inputDTO);
        return RestResult.success();
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "按单个模板查询授权列表", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/single-template-auth-list", method = RequestMethod.POST)
    public RestResult<FlowTemplateAuthDetailResponse> singleFlowTemplateAuthList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            FlowTemplateDetailQueryRequest request) {
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        SingleFlowTemplateAuthListBizInputDTO inputDTO =
                new SingleFlowTemplateAuthListBizInputDTO();
        inputDTO.setFlowTemplateId(request.getFlowTemplateId());
        inputDTO.setAuthType(request.getAuthType());
        inputDTO.setTenantId(resourceTenantId);
        inputDTO.setOperatorId(accountId);
        inputDTO.setQuerySelf(request.isQuerySelf());
        inputDTO.setOwnerTenantId(request.getOwnerTenantId());
        SingleFlowTemplateAuthDetailOutputDTO outputDTO =
                flowTemplateBizService.flowTemplateAuthDetail(inputDTO);

        FlowTemplateAuthDetailResponse response = mapperFactory.getMapperFacade().map(outputDTO, FlowTemplateAuthDetailResponse.class);
        response.setTotal(response.getList().size());
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "保存数据范围", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/save-template-auth-config", method = RequestMethod.POST)
    public RestResult<Void> saveTemplateAuthConfig(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            TemplateAuthConfigSaveRequest request) {

        TemplateAuthConfigSaveInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, TemplateAuthConfigSaveInputDTO.class);
        inputDTO.setTenantId(tenantId);
        inputDTO.setOperatorId(accountId);
        flowTemplateBizService.saveTemplateAuthConfig(inputDTO);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询数据范围", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/get-template-auth-config", method = RequestMethod.POST)
    public RestResult<TemplateAuthConfigResponse> getTemplateAuthConfig(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            TemplateAuthConfigQueryRequest request) {
        TemplateAuthConfigQueryInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, TemplateAuthConfigQueryInputDTO.class);
        inputDTO.setTenantId(tenantId);
        inputDTO.setOperatorId(accountId);
        TemplateAuthConfigOutputDTO outputDTO = flowTemplateBizService.getTemplateAuthConfig(inputDTO);
        TemplateAuthConfigResponse response = mapperFactory.getMapperFacade().map(outputDTO, TemplateAuthConfigResponse.class);
        return RestResult.success(response);
    }


    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取预览地址（多种场景）", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/preview-url", method = RequestMethod.POST)
    public RestResult<FlowTemplatePreviewResponse> getTemplatePreviewUrl(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            FlowTemplatePreviewRequest request) {
        FlowTemplatePreviewInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, FlowTemplatePreviewInputDTO.class);
        inputDTO.setClientId(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setTenantId(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setOperatorId(accountId);
        if (null != request.getDataSource() && StringUtils.isNotBlank(request.getDataSource().getDataId())) {
            FlowTemplatePreviewInputDTO.DataSource dataSource = new FlowTemplatePreviewInputDTO.DataSource();
            dataSource.setDataId(request.getDataSource().getDataId());
            inputDTO.setDataSource(dataSource);
        }
        FlowTemplatePreviewOutputDTO outputDTO = flowTemplateBizService.getTemplatePreviewUrl(inputDTO);
        FlowTemplatePreviewResponse response = mapperFactory.getMapperFacade().map(outputDTO, FlowTemplatePreviewResponse.class);
        return RestResult.success(response);
    }
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "基于文件/模板创建ePaas文档", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/create-doc", method = RequestMethod.POST)
    public RestResult<FlowTemplateCreateTemplateResponse> createDoc(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
                                                                    @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
                                                                    @RequestHeader(HEADER_APP_ID) String appId,
                                                                    FlowTemplateCreateTemplateRequest request) {
        List<FlowTemplateFileIdRequest> resourceList = request.getResourceList();
        List<EpaasBatchCreateTemplateByFileIdsInput.TemplateFileInfo> fileInfos = resourceList.stream().map(this::convert).collect(Collectors.toList());
        List<EpaasDocTemplateAndFileMapping> epaasDocTemplateAndFileMappings = flowTemplateBizService.batchCreateEpaasDocTemplate(
                fileInfos,
                StringUtils.defaultIfBlank(appId, userCenterService.queryAccountInfoByOid(tenantId).getAppId()),
                tenantId,
                accountId,
                request.getResourceTenantId()
        );
        List<FlowTemplateCreateTemplateResponse.TemplateInfo> templateInfoList = epaasDocTemplateAndFileMappings.stream()
                .map(docTemplateAndFileMapping -> JsonUtils.obj2pojo(docTemplateAndFileMapping, FlowTemplateCreateTemplateResponse.TemplateInfo.class))
                .collect(Collectors.toList());
        return RestResult.success(new FlowTemplateCreateTemplateResponse(templateInfoList));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.TEMPLATE_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询流程模板基础信息", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/{flowTemplateId}/base-info", method = RequestMethod.POST)
    public RestResult<FlowTemplateBaseInfoResponse> queryBaseInfo(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
                                                                    @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
                                                                        @PathVariable String flowTemplateId) {
        FlowTemplateBaseInfoDTO baseInfoDTO = flowTemplateBizService.queryFlowTemplateBaseInfo(
                flowTemplateId,
                tenantId,
                accountId
        );
        FlowTemplateBaseInfoResponse response = mapperFactory.getMapperFacade().map(baseInfoDTO, FlowTemplateBaseInfoResponse.class);
        return RestResult.success(response);
    }

    private EpaasBatchCreateTemplateByFileIdsInput.TemplateFileInfo convert(FlowTemplateFileIdRequest request) {
        EpaasBatchCreateTemplateByFileIdsInput.TemplateFileInfo templateFileInfo = new EpaasBatchCreateTemplateByFileIdsInput.TemplateFileInfo();
        templateFileInfo.setFileId(request.getFileId());
        templateFileInfo.setTemplateType(request.getTemplateType());
        templateFileInfo.setTemplateName(request.getTemplateName());
        return templateFileInfo;
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "企业模版数据源渠道", httpMethod = "GET")
    @RestMapping(path = "/flow-templates/data-source-channel", method = RequestMethod.GET)
    public RestResult<FlowTemplateDataSourceChannelResponse> dataSourceChannel() {
        List<DataSourceChannelFilterDTO> channels =
                flowTemplateDataSourceService.dataSourceChannel(
                        RequestContextExtUtils.getClientId(),
                        RequestContextExtUtils.getResourceTenantId());
        return RestResult.success(new FlowTemplateDataSourceChannelResponse(FlowTemplateDataSourceConverter.convert(channels)));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "企业某个渠道的数据源列表", httpMethod = "GET")
    @RestMapping(path = "/flow-templates/channel-data-source-list", method = RequestMethod.POST)
    public RestResult<FlowTemplateChannelDataSourceListResponse> channelDataSourceList(@RequestBody FlowTemplateChannelDataSourceListRequest request) {
        FlowTemplateChannelDataSourceListInputDTO inputDTO = new FlowTemplateChannelDataSourceListInputDTO();
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        inputDTO.setSearchKey(request.getSearchKey());
        inputDTO.setSubjectOid(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setDataSourceChannel(request.getDataSourceChannel());
        PagerResult<DataSourceDTO> pagerResult = flowTemplateDataSourceService.channelDataSourceList(inputDTO);
        FlowTemplateChannelDataSourceListResponse response = new FlowTemplateChannelDataSourceListResponse();
        response.setTotal(pagerResult.getTotal());
        response.setList(FlowTemplateDataSourceConverter.dataSourceConvert(pagerResult.getItems()));
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取参与方数据源字段和关联规则", httpMethod = "GET")
    @RestMapping(path = "/flow-templates/participant-data-source-field", method = RequestMethod.POST)
    @Deprecated
    public RestResult<FlowTemplateParticipantDataSourceFieldResponse>
    participantDataSourceField(@RequestBody FlowTemplateParticipantDataSourceFieldRequest request) {
        // 字段
        ParticipantRelationDataSourceFieldRuleOutputDTO ruleOutputDTO = flowTemplateDataSourceService.participantRelationDataSourceFieldRule(
                RequestContextExtUtils.getResourceTenantId(),
                request.getDataSourceIds());
        //
        FlowTemplateParticipantDataSourceFieldResponse response =
                mapperFactory.getMapperFacade().map(ruleOutputDTO, FlowTemplateParticipantDataSourceFieldResponse.class);

        FlowTemplateParticipantDataSourceFieldResponse.Rule fieldRelationRule =
                new FlowTemplateParticipantDataSourceFieldResponse.Rule();
        fieldRelationRule.setAccountNameRelationFieldTypes(ruleOutputDTO.getAccountNameRelationFieldTypes());
        fieldRelationRule.setAccountRelationFieldTypes(ruleOutputDTO.getAccountRelationFieldTypes());
        fieldRelationRule.setSubjectNameRelationFieldTypes(ruleOutputDTO.getSubjectNameRelationFieldTypes());
        response.setParticipantRelationRule(fieldRelationRule);
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "校验同步数据源表单", httpMethod = "GET")
    @RestMapping(path = "/flow-templates/check-data-source-form", method = RequestMethod.GET)
    public RestResult<CheckAndSyncFormResponse> checkAndSyncForm(
            @ApiParam(value = "渠道") @RequestParam(value = "channel") String channel,
            @ApiParam(value = "表单id") @RequestParam(value = "formId") String formId) {
        CheckAndSyncFormDTO checkAndSyncFormDTO =
                flowTemplateDataSourceService.checkAndSyncForm(
                        channel,
                        RequestContextExtUtils.getTenantId(),
                        formId);
        return checkAndSyncFormDTO == null
                ? RestResult.success()
                : RestResult.success(
                        new CheckAndSyncFormResponse(
                                checkAndSyncFormDTO.getFormId(),
                                checkAndSyncFormDTO.getFormName()));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取数据源字段和关联规则", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/data-source-field", method = RequestMethod.POST)
    public RestResult<FlowTemplateDataSourceFieldResponse> dataSourceField(
            @RequestBody FlowTemplateDataSourceFieldRequest request) {
        // 字段
        RelationDataSourceFieldRuleOutputDTO relationDataSourceFieldRule =
                flowTemplateDataSourceService.relationDataSourceFieldRule(
                        RequestContextExtUtils.getTenantId(), request.getDataSourceIds(),request.getFieldTypes());
        FlowTemplateDataSourceFieldResponse response =
                mapperFactory
                        .getMapperFacade()
                        .map(
                                relationDataSourceFieldRule,
                                FlowTemplateDataSourceFieldResponse.class);
        return RestResult.success(response);
    }


    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取模版关联数据源异常信息", httpMethod = "GET")
    @RestMapping(path = "/flow-templates/data-source-error", method = RequestMethod.GET)
    public RestResult<FlowTemplateDataSourceErrorResponse>
    dataSourceError(@RequestParam String flowTemplateId, @RequestParam String dataSourceId) {
        // 字段
        String message = flowTemplateDataSourceService.dataSourceError(RequestContextExtUtils.getResourceTenantId(),
                flowTemplateId, dataSourceId);
        // 关联规则i
        FlowTemplateDataSourceErrorResponse response = new FlowTemplateDataSourceErrorResponse();
        response.setErrorMessage(message);
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取数据源详情", httpMethod = "GET")
    @RestMapping(path = "/flow-templates/data-source-detail", method = RequestMethod.GET)
    public RestResult<FlowTemplateDataSourceDetailResponse> dataSourceDetail(@RequestParam String dataSourceId) {
        Optional<DataSourceDTO> dataSourceDTOOptional = flowTemplateDataSourceService.dataSourceDetail(RequestContextExtUtils.getResourceTenantId(),
                dataSourceId);
        if (!dataSourceDTOOptional.isPresent()) {
            return RestResult.success(new FlowTemplateDataSourceDetailResponse());
        }
        return RestResult.success(dataSourceDTOOptional
                .map(FlowTemplateDataSourceConverter::dataSourceConvert)
                .orElse(new FlowTemplateDataSourceDetailResponse()));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取流程模板参与方节点", httpMethod = "GET")
    @RestMapping(path = "/flow-templates/participant-nodes", method = RequestMethod.GET)
    public RestResult<FlowTemplateParticipantNodesResponse> participantNodes(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam String flowTemplateId,
            @RequestParam(required = false) Boolean withOperation) {
        FlowTemplateParticipantNodesInputDTO inputDTO = new FlowTemplateParticipantNodesInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(tenantId);
        inputDTO.setFlowTemplateId(flowTemplateId);
        inputDTO.setWithOperation(Boolean.TRUE.equals(withOperation));
        FlowTemplateParticipantNodesOutputDTO outputDTO =
                flowTemplateBizService.queryFlowTemplateParticipantNodes(inputDTO);
        return RestResult.success(FlowTemplateResponseConverter.convertResponse(outputDTO));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "根据流程模板id提交定时发起", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/scheduled-start", method = RequestMethod.POST)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.SCHEDULE_INITIATE)
    public RestResult<FlowTemplateScheduledStartResponse> scheduledStart(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_CLIENT_ID) @NotBlank String clientId,
            @RequestBody FlowTemplateScheduledStartRequest request) {
        FlowTemplateScheduledStartInputDTO inputDTO = new FlowTemplateScheduledStartInputDTO();
        inputDTO.setClientId(clientId);
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        inputDTO.setFlowTemplateId(request.getFlowTemplateId());
        inputDTO.setBatchDropSeal(request.getBatchDropSeal());
        FlowTemplateScheduledStartOutputDTO outputDTO = flowTemplateBizService.scheduledStartFlowTemplate(inputDTO);
        return RestResult.success(new FlowTemplateScheduledStartResponse(outputDTO.isSuccess()));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取流程模板参与方印章列表", httpMethod = "POST")
    @RestMapping(path = "/flow-templates/participant-seals", method = RequestMethod.POST)
    public RestResult<FlowTemplateParticipantSealsResponse> participantSeals(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_CLIENT_ID) @NotBlank String clientId,
            @RequestBody FlowTemplateParticipantSealsRequest request) {
        FlowTemplateParticipantSealsInputDTO inputDTO = new FlowTemplateParticipantSealsInputDTO();
        inputDTO.setClientId(clientId);
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(tenantId);
        inputDTO.setFlowTemplateId(request.getFlowTemplateId());
        inputDTO.setParams(request.getQueryParam());
        FlowTemplateParticipantSealsOutputDTO outputDTO = participantSealServiceFactory.getService(request.getQueryType()).querySeals(inputDTO);
        return RestResult.success(FlowTemplateResponseConverter.convertResponse(outputDTO));
    }

    
    @ApiOperation(value = "epaas组建下拉列表配置", httpMethod = "POST")
    @RestMapping(path = "/flowTemplates/epaas-struct-choice-list-config", method = RequestMethod.POST)
    public BaseResult<EpaasStructChoiceListConfigResponse> epaasStructChoiceListConfig() {
        EpaasStructChoiceListConfigOutputDTO epaasStructChoiceListConfigOutputDTO = flowTemplateBizService.queryDocTemplatesByFlowTemplateId();
        if (Objects.isNull(epaasStructChoiceListConfigOutputDTO)) {
            return BaseResult.success(null);
        }
        return BaseResult.success(mapperFactory.getMapperFacade().map(epaasStructChoiceListConfigOutputDTO, EpaasStructChoiceListConfigResponse.class));
    }
}
