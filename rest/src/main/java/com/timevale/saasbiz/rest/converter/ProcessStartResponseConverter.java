package com.timevale.saasbiz.rest.converter;

import com.timevale.contractmanager.common.service.enums.SubProcessTypeEnum;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessStartOutputDTO;
import com.timevale.saasbiz.rest.bean.process.response.ProcessStartResponse;

/**
 * <AUTHOR>
 * @since 2023-05-21
 */
public class ProcessStartResponseConverter {

    /**
     * 组装发起响应数据
     *
     * @param startResult
     * @return
     */
    public static ProcessStartResponse buildProcessStartResponse(ProcessStartOutputDTO startResult) {
        ProcessStartResponse response = new ProcessStartResponse();
        response.setLongResultUrl(startResult.getLongResultUrl());
        response.setRealProcessId(startResult.getProcessId());
        response.setProcessId(startResult.getProcessId());
        response.setResultUrl(startResult.getResultUrl());
        response.setGroupId(startResult.getGroupId());
        // 填充子流程id
        fillSubProcessFlowId(startResult, response);
        // 返回发起结果
        return response;
    }

    /**
     * 填充子流程id
     * @param startResult
     * @param response
     */
    private static void fillSubProcessFlowId(ProcessStartOutputDTO startResult, ProcessStartResponse response) {
        if (null == startResult.getFlowType()) {
            return;
        }
        // 签署流程
        if (SubProcessTypeEnum.SIGN.getType() == startResult.getFlowType()) {
            response.setFlowId(startResult.getFlowId());
            return;
        }
        // 填写流程
        if (SubProcessTypeEnum.COOPERATION.getType() == startResult.getFlowType()){
            response.setCooperationId(startResult.getFlowId());
            return;
        }
        // 合同审批流程
        if (SubProcessTypeEnum.CONTRACT_APPROVAL.getType() == startResult.getFlowType()) {
            response.setApprovalId(startResult.getFlowId());
            return;
        }
    }
}
