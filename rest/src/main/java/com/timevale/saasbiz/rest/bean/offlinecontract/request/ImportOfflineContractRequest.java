package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractExtractConfigVO;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 导入线下合同请求参数
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class ImportOfflineContractRequest extends ToString {
    /** 归档菜单id */
    @ApiModelProperty(value = "归档菜单id", required = true)
    @NotBlank(message = "归档菜单id不能为空")
    private String menuId;

    /** 导入方式 */
    @ApiModelProperty(value = "导入方式", required = true)
    @NotBlank(message = "导入方式不能为空")
    private String importWay;

    /** 合同信息提取方式 */
    @ApiModelProperty(value = "合同信息提取方式", required = true)
    @NotBlank(message = "合同信息提取方式不能为空")
    private String extractWay;

    /** 线下合同列表 */
    @ApiModelProperty(value = "线下合同列表", required = true)
    @NotEmpty(message = "线下合同列表不能为空")
    private List<OfflineContractVO> contracts;

    /** 合同信息提取配置 */
    @ApiModelProperty(value = "合同信息提取配置")
    private OfflineContractExtractConfigVO extractConfig;

    @ApiModelProperty(value = "关联合同的原合同id")
    private String masterProcessId;

    /** 专属云项目id */
    private String dedicatedCloudId;
}
