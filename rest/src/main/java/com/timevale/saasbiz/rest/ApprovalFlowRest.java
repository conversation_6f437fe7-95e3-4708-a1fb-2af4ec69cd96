package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.account.flow.service.enums.ApprovalBizTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalQueryTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalWillAuthTypeEnum;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.flow.facade.service.exception.FootstoneFlowBizException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.oauth.facade.enums.ClientTypeEnum;
import com.timevale.saas.common.base.util.ExceptionLogUtil;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import com.timevale.saasbiz.integration.approval.SealApprovalClient;
import com.timevale.saasbiz.model.approval.dto.ApprovalAggrDTO;
import com.timevale.saasbiz.model.approval.dto.ApprovalFlowItemDTO;
import com.timevale.saasbiz.model.approval.dto.ApprovalGroupItemDTO;
import com.timevale.saasbiz.model.approval.dto.ApprovalParticipantDTO;
import com.timevale.saasbiz.model.bean.approval.dto.*;
import com.timevale.saasbiz.model.bean.approval.dto.input.*;
import com.timevale.saasbiz.model.bean.approval.dto.task.ApprovalLogBizDTO;
import com.timevale.saasbiz.model.bean.approval.dto.task.ApprovalVirtualStartResultDTO;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessStartFileChangeOutputDTO;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessStartSourceOutputDTO;
import com.timevale.saasbiz.model.bean.saascommon.dto.BizConfigDTO;
import com.timevale.saasbiz.model.bean.saascommon.dto.input.UpdateSaasBizConfigsInputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasAnyPrivilegeInputDTO;
import com.timevale.saasbiz.model.constants.ApprovalConstants;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.enums.approval.ApprovalOperateTypeEnum;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.utils.AssertX;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.approval.request.*;
import com.timevale.saasbiz.rest.bean.approval.response.*;
import com.timevale.saasbiz.rest.bean.approval.vo.ApprovalLogVO;
import com.timevale.saasbiz.rest.bean.approval.vo.ApprovalOperateDetailVO;
import com.timevale.saasbiz.rest.bean.approval.vo.ApprovalParticipantVO;
import com.timevale.saasbiz.rest.bean.approval.vo.ProcessStartFileChangeVO;
import com.timevale.saasbiz.rest.converter.ApprovalRestConverter;
import com.timevale.saasbiz.rest.converter.ApprovalVOConverter;
import com.timevale.saasbiz.service.approval.ApprovalFlowService;
import com.timevale.saasbiz.service.approval.ApprovalStartService;
import com.timevale.saasbiz.service.approval.approvaloperate.ApprovalOperateForwardService;
import com.timevale.saasbiz.service.approval.approvaloperate.operate.ApprovalOperateForwardResult;
import com.timevale.saasbiz.service.approval.approvaloperate.strategy.operate.ApprovalOperatePreCheckService;
import com.timevale.saasbiz.service.approval.approvaloperate.strategy.operate.factory.ApprovalOperatePreCheckServiceFactory;
import com.timevale.saasbiz.service.process.ProcessService;
import com.timevale.saasbiz.service.process.ProcessStartService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.exception.SaasBizResultCode.*;
import static com.timevale.saasbiz.rest.converter.ApprovalVOConverter.buildInputDTO;

/**
 * <AUTHOR>
 * @since 2023-03-28 16:28
 */
@Api(tags = "审批流程接口")
@Validated
@ExternalService
@RestMapping(path = "/v2/approval/flow")
@Slf4j
public class ApprovalFlowRest {
    // 全部主体
    private static final String SUBJECT_ALL_LABEL = "全部";
    private static final String SUBJECT_ALL_ID = "ALL";

    @Autowired private UserCenterService userCenterService;
    @Autowired private ApprovalFlowService approvalFlowService;
    @Autowired private ApprovalStartService approvalStartService;
    @Autowired private ApprovalOperateForwardService operateForwardService;
    @Autowired private ProcessService processService;
    @Autowired private SaasCommonService saasCommonService;
    @Autowired private ProcessStartService processStartService;
    @Autowired private SealApprovalClient sealApprovalClient;
    @Autowired private ApprovalOperatePreCheckServiceFactory operatePreCheckServiceFactory;
    @Autowired private MapperFactory mapperFactory;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("审批列表地址")
    @RestMapping(path = "/list-url", method = RequestMethod.POST)
    public RestResult<ApprovalFlowListUrlResponse> listUrl(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody ListApprovalFlowUrlRequest request) {

        ApprovalFlowListUrlInputDTO inputDTO = new ApprovalFlowListUrlInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(tenantId);
        inputDTO.setQueryType(request.getQueryType());
        inputDTO.setApprovalType(request.getApprovalType());
        inputDTO.setToken(request.getToken());
        inputDTO.setH5Flag(request.isH5Flag());
        inputDTO.setExtraParams(request.getExtra());
        String listUrl = approvalFlowService.queryListUrl(inputDTO);
        return RestResult.success(new ApprovalFlowListUrlResponse(listUrl));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("审批列表")
    @RestMapping(path = "/list", method = RequestMethod.POST)
    public RestResult<ListApprovalFlowResponse> list(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ListApprovalFlowRequest request) {
        ListApprovalFlowInputDTO inputDTO = requireListInputDTO(accountId, request);
        if (Objects.isNull(inputDTO)) {
            return RestResult.success();
        }

        // 查询一页
        PagerResult<ApprovalFlowItemDTO> pagerResult = approvalFlowService.page(inputDTO);
        if (Objects.isNull(pagerResult)) {
            return RestResult.success();
        }
        //合同来源信息
        Map<String, ProcessStartSourceOutputDTO> startSourceMap = Maps.newHashMap();
        try {
            Set<String> processIds =
                    pagerResult.getItems().stream()
                            .filter(i -> !ApprovalBizTypeEnum.SEAL_APPLY_WITNESS.getType().equals(i.getApprovalBizType()))
                            .filter(i -> StringUtils.isNotBlank(i.getProcessId()))
                            .map(i -> i.getProcessId())
                            .collect(Collectors.toSet());
            startSourceMap.putAll(processService.batchQueryStartSource(Lists.newArrayList(processIds)));
        } catch (Exception e) {
            // 避免影响主流程
            log.warn("query process source info list fail", e);
        }
        // 查询审批支持的会员功能列表
        List<String> supportFunctions =
                approvalStartService.queryApprovalSupportFunctions(
                        inputDTO.getSubjectOid(), RequestContextExtUtils.getClientId());
        // 组装审批列表返回数据
        List<ListApprovalFlowResponse.ApprovalFlowVO> list =
                pagerResult.getItems().stream()
                        .map(i -> ApprovalVOConverter.buildVO(i, supportFunctions, startSourceMap))
                        .collect(Collectors.toList());

        // 转换格式 没有获取默认表头
        List<ListApprovalFlowResponse.TableHeadVO> headList = new ArrayList<>();
        if (request.getPageNum() == 1) {
            List<ApprovalTableHeadDTO> approvalTableHeadConfig =
                    approvalFlowService.getTableHead(
                            accountId, StringUtils.isNotBlank(request.getGroupId()));
            headList =
                    approvalTableHeadConfig.stream()
                            .map(ApprovalVOConverter::buildVO)
                            .sorted(
                                    Comparator.comparingInt(
                                            ListApprovalFlowResponse.TableHeadVO::getSort))
                            .collect(Collectors.toList());
        }

        return RestResult.success(
                new ListApprovalFlowResponse(pagerResult.getTotal(), list, headList));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("审批分组列表")
    @RestMapping(path = "/group-list", method = RequestMethod.POST)
    public RestResult<ListApprovalGroupResponse> listGroup(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ListApprovalFlowRequest request) {
        // 用印审批没有分组，直接返回空列表
        if (ApprovalTemplateTypeEnum.SEAL.getCode().equals(request.getApprovalType())) {
            return RestResult.success();
        }

        ListApprovalFlowInputDTO inputDTO = requireListInputDTO(accountId, request);
        if (Objects.isNull(inputDTO)) {
            return RestResult.success();
        }
        // 查询一页
        PagerResult<ApprovalGroupItemDTO> pagerResult = approvalFlowService.pageGroup(inputDTO);
        List<ListApprovalGroupResponse.ApprovalGroupVO> list =
                pagerResult.getItems().stream()
                        .map(item -> ApprovalVOConverter.buildVO(item))
                        .collect(Collectors.toList());

        return RestResult.success(new ListApprovalGroupResponse(pagerResult.getTotal(), list));
    }

    @ApiOperation("审批列表查看流程参与人")
    @RestMapping(path = "/participant", method = RequestMethod.GET)
    public RestResult<ApprovalParticipantResponse> participant(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @URIQueryParam ListParticipantRequest request) {

        /** 根据审批id判断用户是否可查看审批 */
        boolean canView = checkCanViewParticipantByApproval(tenantId, accountId, request);

        ListParticipantInputDTO input = new ListParticipantInputDTO();
        input.setProcessId(request.getProcessId());
        input.setAccountId(accountId);
        input.setSubjectId(tenantId);
        input.setSkipViewCheck(canView);
        // 查询流程参与人
        List<ApprovalParticipantDTO> participants = approvalFlowService.listParticipant(input);
        if (CollectionUtils.isEmpty(participants)) {
            return RestResult.success();
        }

        List<ApprovalParticipantVO> list =
                participants.stream()
                        .map(ApprovalVOConverter::buildVO)
                        .collect(Collectors.toList());

        return RestResult.success(new ApprovalParticipantResponse(list));
    }

    /**
     * 校验是否可查看审批流程的业务参与人信息
     *
     * @param tenantId
     * @param accountId
     * @param request
     * @return
     */
    private boolean checkCanViewParticipantByApproval(
            String tenantId, String accountId, ListParticipantRequest request) {
        if (StringUtils.isAnyBlank(request.getApprovalId(), request.getApprovalType())) {
            return false;
        }
        try {
            ApprovalMixFlowInputDTO inputDTO = new ApprovalMixFlowInputDTO();
            // 前端中台页面，无法通过请求头穿企业信息
            inputDTO.setSubjectOid(tenantId);
            inputDTO.setOperatorOid(accountId);
            inputDTO.setApprovalId(request.getApprovalId());
            inputDTO.setApprovalType(request.getApprovalType());
            ApprovalMixFlowResultDTO resultDTO = approvalStartService.mixFlow(inputDTO);
            // 如果查询审批流程信息为空， 说明当前用户非审批所属企业的成员， 无权查看
            if (CollectionUtils.isEmpty(resultDTO.getList())) {
                return false;
            }
            // 判断当前空间是否审批所在企业空间， 如果不是， 无权查看
            ApprovalDetailWithLogsDTO approvalDetail = resultDTO.getList().get(0);
            String approvalSubjectOid = approvalDetail.getSubjectOid();
            if (StringUtils.equals(tenantId, approvalSubjectOid)) {
                return true;
            }
            if (StringUtils.isNotBlank(approvalSubjectOid)) {
                String tenantGid = userCenterService.queryAccountInfoByOid(tenantId).getGid();
                String subjectGid = userCenterService.queryAccountInfoByOid(approvalSubjectOid).getGid();
                return StringUtils.equals(tenantGid, subjectGid);
            }
            return false;
        } catch (Exception e) {
            ExceptionLogUtil.messageLogIfWarn(log, e, "checkCanViewParticipantByApproval faield.");
            return false;
        }
    }

    @ApiOperation("按照企业统计用户审批数量")
    @RestMapping(path = "/aggr-by-subject", method = RequestMethod.GET)
    public RestResult<ApprovalAggregateResponse> aggrBySubject(
            @ApiParam(value = "审批类型: 1-印章 2-合同") @URIQueryParam String approvalType) {
        AccountInfoDTO account = userCenterService.queryAccountInfoByOid(RequestContextExtUtils.getOperatorId());
        if (Objects.isNull(account) || StringUtils.isBlank(account.getGid())) {
            return RestResult.success();
        }

        List<ApprovalAggrDTO> list =
                approvalFlowService.countApprovalBySubject(account.getGid(), approvalType);
        if (CollectionUtils.isEmpty(list)) {
            return RestResult.success();
        }

        List<ApprovalAggregateResponse.ApprovalAggregateVO> voList =
                list.stream().map(ApprovalVOConverter::buildVO).collect(Collectors.toList());

        return RestResult.success(formatResponse(voList));
    }

    private ApprovalAggregateResponse formatResponse(
            List<ApprovalAggregateResponse.ApprovalAggregateVO> voList) {
        ApprovalAggregateResponse response = new ApprovalAggregateResponse(voList);

        ApprovalAggregateResponse.ApprovalAggregateVO totalCountTab =
                new ApprovalAggregateResponse.ApprovalAggregateVO(
                        SUBJECT_ALL_LABEL, SUBJECT_ALL_ID, response.getTotal());
        response.getSubjects().add(0, totalCountTab);

        return response;
    }

    /** 获取列表请求参数 */
    private ListApprovalFlowInputDTO requireListInputDTO(
            String operatorOid, ListApprovalFlowRequest request) {
        ListApprovalFlowInputDTO inputDTO = buildListInputDTO(operatorOid, request, ListApprovalFlowInputDTO.class);
        if (null != inputDTO) {
            inputDTO.setPageNum(request.getPageNum());
            inputDTO.setPageSize(request.getPageSize());
        }
        return inputDTO;
    }

    /** 获取列表请求参数 */
    private <T extends ListApprovalFlowBaseInputDTO> T buildListInputDTO(
            String operatorOid, ListApprovalFlowBaseRequest request, Class<T> inputClass) {
        // 查询的目标用户
        AccountInfoDTO targetAccount;
        // 判断当前用户是否管理员
        boolean admin = false;
        // 查看全部审批， 需要校验当前操作用户是否企业管理员
        if (ApprovalQueryTypeEnum.ADMIN_ALL.getType().equals(request.getQueryType())) {
            AssertX.isTrue(StringUtils.isNotBlank(request.getSubjectOid()), "仅支持查看指定企业的全部审批");
            admin = userCenterService.checkHasAdminPrivilege(operatorOid, request.getSubjectOid());
            if (!admin) {
                throw new SaasBizException(APPROVAL_LIST_MEMBER_QUERY_LIMIT_ADMIN);
            }
        }
        // 未指定主体/用户场景， 默认查询当前操作人的审批列表
        if (StringUtils.isAnyBlank(request.getSubjectOid(), request.getQueryMemberOid())
                || StringUtils.equalsIgnoreCase(operatorOid, request.getSubjectOid())
                || StringUtils.equalsIgnoreCase(operatorOid, request.getQueryMemberOid())) {
            targetAccount = userCenterService.queryAccountInfoByOid(operatorOid);
        } else {
            // 查看企业成员审批数据， 需校验当前操作用户是否有成员管理的编辑或删除权限
            CheckHasAnyPrivilegeInputDTO inputDTO = new CheckHasAnyPrivilegeInputDTO();
            inputDTO.setAccountId(operatorOid);
            inputDTO.setTenantId(request.getSubjectOid());
            inputDTO.addResourcePrivilege(PrivilegeResourceConstants.PRIVILEGE_RESOURCE_MEMBER, Lists.newArrayList(PrivilegeOperationConstants.UPDATE, PrivilegeOperationConstants.DELETE));
            if (!admin && !userCenterService.checkHasAnyPrivilege(inputDTO, false)) {
                throw new SaasBizException(APPROVAL_LIST_MEMBER_QUERY_LIMIT);
            }
            targetAccount = userCenterService.queryAccountInfoByOid(request.getQueryMemberOid());
        }
        if (Objects.isNull(targetAccount)) {
            throw new SaasBizException(SAAS_SERVICE_ERROR);
        }
        if (StringUtils.isBlank(targetAccount.getGid())) {
            // 无个人gid肯定
            return null;
        }

        // 查询的目标企业
        String targetTenantId = request.getSubjectOid();
        AccountInfoDTO targetTenant = null;
        if (StringUtils.isNotBlank(targetTenantId)) {
            targetTenant = userCenterService.queryAccountInfoByOid(targetTenantId);
            // 要查的企业未实名，直接返回空列表
            if (Objects.isNull(targetTenant) || StringUtils.isBlank(targetTenant.getGid())) {
                return null;
            }
        }

        return buildInputDTO(targetAccount, targetTenant, request, inputClass);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("选择模版时查看审批流")
    @RestMapping(path = "/pre-flow", method = RequestMethod.GET)
    public RestResult<ApprovalPreFlowResponse> preFlow(@RequestParam String approvalTemplateId,
                                                       @RequestParam(required = false) Integer approvalTemplateConditionType,
                                                       @RequestParam(required = false) String approvalTemplateConditionValue,
                                                       @RequestParam(required = false) String deptId,
                                                       @RequestParam(required = false) String approvalId) {
        ApprovalVirtualStartInputDTO inputDTO = new ApprovalVirtualStartInputDTO();
        inputDTO.setApprovalTemplateCode(approvalTemplateId);
        inputDTO.setDeptId(deptId);
        inputDTO.setConditionType(approvalTemplateConditionType);
        inputDTO.setConditionValue(approvalTemplateConditionValue);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setApprovalCode(approvalId);
        ApprovalVirtualStartResultDTO approvalVirtualStartResultDTO = approvalStartService.preFlow(inputDTO);
        ApprovalPreFlowResponse response = new ApprovalPreFlowResponse();
        response.setApprovalLogs(JSON.parseArray
                (JSON.toJSONString(approvalVirtualStartResultDTO.getApprovalNodeTasks()), ApprovalLogVO.class));
        response.setDescription(approvalVirtualStartResultDTO.getDescription());
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("同意审批")
    @RestMapping(path = "/agree", method = RequestMethod.POST)
    public RestResult<ApprovalOperateResponse> agree(@RequestBody ApprovalOperateRequest request) {
        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
        inputDTO.setOperateType(ApprovalOperateTypeEnum.AGREE);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setSerialId(request.getSerialId());
        inputDTO.setClient(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setSecurityCode(request.getSecurityCode());
        inputDTO.setWillAuthId(request.getWillAuthId());
        inputDTO.setWillAuthType(request.getWillAuthType());
        inputDTO.setSupportBatch(true);
        inputDTO.setOperateDatas(ApprovalRestConverter.operateDataConvert(request.getOperateDatas()));
        // 兼容补充意愿信息
        amendsWillAuthIdAndType(inputDTO);
        ApprovalOperateForwardResult forwardResult = operateForwardService.forward(inputDTO);
        return RestResult.success(ApprovalRestConverter.forwardResultConvert(forwardResult));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("拒绝审批")
    @RestMapping(path = "/refuse", method = RequestMethod.POST)
    public RestResult<ApprovalOperateResponse> refuse(@RequestBody ApprovalOperateRequest request) {
        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
        inputDTO.setOperateType(ApprovalOperateTypeEnum.REFUSE);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setClient(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setSecurityCode(request.getSecurityCode());
        inputDTO.setSerialId(request.getSerialId());
        inputDTO.setWillAuthId(request.getWillAuthId());
        inputDTO.setWillAuthType(request.getWillAuthType());
        inputDTO.setOperateDatas(ApprovalRestConverter.operateDataConvert(request.getOperateDatas()));
        inputDTO.setSupportBatch(true);

        // 兼容补充意愿信息
        amendsWillAuthIdAndType(inputDTO);
        ApprovalOperateForwardResult forwardResult = operateForwardService.forward(inputDTO);
        return RestResult.success(ApprovalRestConverter.forwardResultConvert(forwardResult));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("增加审批人")
    @RestMapping(path = "/add-approval", method = RequestMethod.POST)
    public RestResult<ApprovalOperateResponse> addApproval(@RequestBody ApprovalAddRequest request) {
        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
        inputDTO.setOperateType(ApprovalOperateTypeEnum.ADD);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setClient(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setOperateDatas(ApprovalRestConverter.operateDataConvert(request.getOperateDatas()));
        inputDTO.setCandidates(mapperFactory.getMapperFacade().mapAsList(request.getCandidates(), ApprovalCandidateDTO.class));
        inputDTO.setAddApprovalType(request.getType());
        ApprovalOperateForwardResult forwardResult = operateForwardService.forward(inputDTO);
        return RestResult.success(ApprovalRestConverter.forwardResultConvert(forwardResult));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("减少审批人")
    @RestMapping(path = "/reduce-approval", method = RequestMethod.POST)
    public RestResult<ApprovalOperateResponse> reduceApproval(@RequestBody ApprovalAddRequest request) {
        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
        inputDTO.setOperateType(ApprovalOperateTypeEnum.REDUCE);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setClient(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setOperateDatas(ApprovalRestConverter.operateDataConvert(request.getOperateDatas()));


        ApprovalOperateForwardResult forwardResult = operateForwardService.forward(inputDTO);
        return RestResult.success(ApprovalRestConverter.forwardResultConvert(forwardResult));
    }

    /**
     * 兼容补充意愿信息
     * @param inputDTO
     */
    private void amendsWillAuthIdAndType(ApprovalOperateInputDTO inputDTO) {
        if (StringUtils.isNoneBlank(inputDTO.getWillAuthId(), inputDTO.getWillAuthType())
                || StringUtils.isBlank(inputDTO.getSerialId())
                || CollectionUtils.isEmpty(inputDTO.getOperateDatas())) {
            log.info("skip amendsWillAuthIdAndType");
            return;
        }
        // 如果操作的审批数据中没有用印审批， 无需补偿
        if (inputDTO.getOperateDatas().stream()
                .noneMatch(i -> ApprovalTypeEnum.SEAL.getCode().equals(i.getApprovalType()))) {
            log.info("skip amendsWillAuthIdAndType");
            return;
        }
        // 查询审批意愿任务id
        try {
            String willAuthId = sealApprovalClient.queryApprovalWillAuthId(inputDTO.getSerialId());
            if (StringUtils.isNotBlank(willAuthId)) {
                inputDTO.setWillAuthId(willAuthId);
                inputDTO.setWillAuthType(ApprovalWillAuthTypeEnum.WILLING.getType());
            }
        } catch (FootstoneFlowBizException e) {
            // 如果获取意愿认证id失败， 降级处理，不补偿
            return;
        }
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("撤回审批")
    @RestMapping(path = "/revoke", method = RequestMethod.POST)
    public RestResult<ApprovalOperateResponse> revoke(@RequestBody ApprovalOperateRequest request) {
        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
        inputDTO.setOperateType(ApprovalOperateTypeEnum.REVOKE);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setClient(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setSecurityCode(request.getSecurityCode());
        inputDTO.setOperateDatas(ApprovalRestConverter.operateDataConvert(request.getOperateDatas()));
        inputDTO.setSupportBatch(true);
        ApprovalOperateForwardResult forwardResult = operateForwardService.forward(inputDTO);
        return RestResult.success(ApprovalRestConverter.forwardResultConvert(forwardResult));
    }

    // 重新发起  新接口适配新老数据
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("重新发起审批")
    @RestMapping(path = "/restart", method = RequestMethod.POST)
    public RestResult<ApprovalRestartResponse> restart(@RequestBody ApprovalRestartRequest request) {
        ApprovalRestartInputDTO approvalRestartInputDTO = new ApprovalRestartInputDTO();
        approvalRestartInputDTO.setAppId(RequestContextExtUtils.getAppId());
        approvalRestartInputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        approvalRestartInputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        approvalRestartInputDTO.setApprovalId(request.getApprovalId());
        approvalRestartInputDTO.setApprovalType(request.getApprovalType());
        approvalRestartInputDTO.setApprovalTemplateId(request.getApprovalTemplateId());
        String newApprovalId = approvalStartService.restart(approvalRestartInputDTO);
        ApprovalRestartResponse response = new ApprovalRestartResponse();
        response.setApprovalId(newApprovalId);
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("删除审批")
    @RestMapping(path = "/delete", method = RequestMethod.POST)
    public RestResult delete(@RequestBody ApprovalOperateRequest request) {
        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
        inputDTO.setOperateType(ApprovalOperateTypeEnum.DELETE);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setClient(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setOperateDatas(ApprovalRestConverter.operateDataConvert(request.getOperateDatas()));
        operateForwardService.forward(inputDTO);
        return RestResult.success();
    }

    @ApiOperation("混合审批流")
    @RestMapping(path = "/mix-flow", method = RequestMethod.GET)
    @MultilingualTranslateMethod
    public RestResult<ApprovalMixFlowResponse> mixFlow(@RequestParam(required = false) String approvalId,
                                                       @RequestParam @NotBlank String approvalType,
                                                       @RequestParam(required = false) String bizId,
                                                       @RequestParam(required = false) Integer bizType,
                                                       @RequestParam(required = false) Boolean queryBatchAll) {
        if (StringUtils.isBlank(approvalId) && StringUtils.isNotBlank(bizId)) {
            AssertX.isTrue(null != bizType, "需指定业务id对应的业务类型");
            approvalId = approvalFlowService.queryApprovalCodeByBizId(bizId, bizType, approvalType);
        }
        AssertX.isTrue(StringUtils.isNotBlank(approvalId), "审批流程code不能为空");
        ApprovalMixFlowInputDTO inputDTO = new ApprovalMixFlowInputDTO();
        // 前端中台页面，无法通过请求头穿企业信息
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setApprovalId(approvalId);
        inputDTO.setApprovalType(approvalType);
        inputDTO.setQueryBatchAll(Boolean.TRUE.equals(queryBatchAll));
        ApprovalMixFlowResultDTO resultDTO = approvalStartService.mixFlow(inputDTO);
        List<ApprovalDetailWithLogsDTO> list = resultDTO.getList();
        ApprovalMixFlowResponse response = new ApprovalMixFlowResponse();

        // 合同来源信息
        try {
            ApprovalVOConverter.SourceConvertBO sourceConvertBO =
                    generateSourceConvertBO(resultDTO.getProcessId(), resultDTO.getBizType());
            response.setAppName(sourceConvertBO.getAppName());
            response.setSource(sourceConvertBO.getSource());
            response.setStartType(sourceConvertBO.getStartType());
        } catch (Exception e) {
            // 避免影响主流程
            log.warn("query process source info fail, processId:{}", resultDTO.getProcessId(), e);
        }
        response.setList(ApprovalRestConverter.approvalDetailBO2VO(list));
        return RestResult.success(response);
    }

    @ApiOperation("审批操作详情")
    @RestMapping(path = "/detail", method = RequestMethod.POST)
    @MultilingualTranslateMethod
    public RestResult<ApprovalOperateDetailVO> operateDetail(@RequestBody ApprovalOperateDetailRequest request) {
        String approverId =
                queryActualApproverId(
                        request.getApproverId(), RequestContextExtUtils.getOperatorId());
        ApprovalOperateDetailInputDTO inputDTO = new ApprovalOperateDetailInputDTO();
        inputDTO.setApprovalId(request.getApprovalId());
        inputDTO.setApprovalType(request.getApprovalType());
        inputDTO.setOperatorOid(approverId);
        inputDTO.setMenuId(request.getMenuId());
        inputDTO.setResourceShareId(request.getResourceShareId());
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setClientId(RequestContextExtUtils.getClientId());
        ApprovalOperateDetailDTO approvalOperateDetailBO = approvalStartService.operateDetail(inputDTO);
        ApprovalOperateDetailVO detailVO =
                ApprovalRestConverter.operateDetailBO2VO(approvalOperateDetailBO);
        // 合同来源信息
        try {
            ApprovalVOConverter.SourceConvertBO sourceConvertBO =
                    generateSourceConvertBO(detailVO.getBizId(), detailVO.getBizType());
            detailVO.setAppName(sourceConvertBO.getAppName());
            detailVO.setSource(sourceConvertBO.getSource());
            detailVO.setStartType(sourceConvertBO.getStartType());
            if (Objects.equals(com.timevale.contractapproval.facade.enums.ApprovalBizTypeEnum.STANDARD.getType(), detailVO.getBizType())) {
                ProcessStartFileChangeOutputDTO processStartFileChangeInfo = processStartService.getProcessStartFileChangeInfo(detailVO.getBizId(), RequestContextExtUtils.getTenantId());
                if (processStartFileChangeInfo != null) {
                    detailVO.setProcessStartFileChange(mapperFactory.getMapperFacade().map(processStartFileChangeInfo, ProcessStartFileChangeVO.class));
                }
            }
        } catch (Exception e) {
            // 避免影响主流程
            log.warn("query process source info fail, processId:{}", detailVO.getBizId(), e);
        }
        return RestResult.success(detailVO);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("审批详情链接")
    @RestMapping(path = "/approval-url", method = RequestMethod.POST)
    public RestResult<ApprovalUrlResponse> approvalUrl(@RequestBody ApprovalUrlRequest request) {
        ApprovalUrlInputDTO approvalUrlInputDTO = ApprovalRestConverter.urlRequest2InputConvert(request);
        approvalUrlInputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        approvalUrlInputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        approvalUrlInputDTO.setExtraParams(request.getExtra());
        ApprovalUrlDTO approvalUrlBO = approvalFlowService.approvalUrl(approvalUrlInputDTO);
        ApprovalUrlResponse urlResponse = new ApprovalUrlResponse();
        urlResponse.setShortUrl(approvalUrlBO.getShortUrl());
        urlResponse.setUrl(approvalUrlBO.getUrl());
        return RestResult.success(urlResponse);
    }


    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("催办")
    @RestMapping(path = "/urge", method = RequestMethod.POST)
    public RestResult<Void> approvalUrge(@RequestBody ApprovalUrgeRequest request) {
        ApprovalUrgeInputDTO approvalUrgeInputDTO = new ApprovalUrgeInputDTO();
        approvalUrgeInputDTO.setApprovalType(request.getApprovalType());
        approvalUrgeInputDTO.setApprovalId(request.getApprovalId());
        approvalUrgeInputDTO.setAccountId(request.getAccountId());
        approvalUrgeInputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        approvalUrgeInputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        approvalUrgeInputDTO.setAppId(RequestContextExtUtils.getAppId());
        approvalFlowService.approvalUrge(approvalUrgeInputDTO);
        return RestResult.success(null);
    }


    @ApiOperation("保存个人表头设置")
    @RestMapping(path = "/table-head", method = RequestMethod.POST)
    public RestResult<Void> saveTableHead(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ApprovalTableHeadConfigRequest request) {
        UpdateSaasBizConfigsInputDTO inputDTO = new UpdateSaasBizConfigsInputDTO();
        inputDTO.setOrgOId(accountId);
        BizConfigDTO configDTO = new BizConfigDTO();
        configDTO.setConfigInfo(ApprovalRestConverter.tableHeadConfig2ConfigJsonConvert(request));
        configDTO.setKey(request.getHasGroup() ? ApprovalConstants.APPROVAL_GROUP_TABLE_HEAD_KEY:ApprovalConstants.APPROVAL_TABLE_HEAD_KEY);
        inputDTO.setBizConfigList(Arrays.asList(configDTO));
        saasCommonService.updateSaasBizConfigs(inputDTO);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("获取审批意愿认证地址")
    @RestMapping(path = "/will-auth-url", method = RequestMethod.POST)
    public RestResult<ApprovalWillAuthUrlResponse> willAuthUrl(@RequestBody ApprovalAuthUrlRequest request) {
        ApprovalWillAuthUrlInputDTO inputDTO = new ApprovalWillAuthUrlInputDTO();
        inputDTO.setDnsAppId(RequestContextExtUtils.getDnsAppId());
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        if (CollectionUtils.isNotEmpty(request.getApprovalTasks())) {
            inputDTO.setApprovalTasks(request.getApprovalTasks().stream().map(i -> {
                ApprovalOperateTaskDTO bean = new ApprovalOperateTaskDTO();
                bean.setApprovalCode(getApprovalCode(i));
                bean.setTaskId(i.getTaskId());
                return bean;
            }).collect(Collectors.toList()));
        }
        inputDTO.setOrigin(request.getOrigin());
        inputDTO.setClientType(request.getClientType());
        inputDTO.setRedirectUrl(request.getRedirectUrl());
        inputDTO.setAppScheme(request.getAppScheme());
        inputDTO.setWillTypeHided(request.getWillTypeHided());
        inputDTO.setLanguage(RequestContextExtUtils.getLanguage());
        String dingCorpId = RequestContextExtUtils.getDingCorpId();
        String dingIsvAppId = RequestContextExtUtils.getIsvAppId();
        String dingUserId = RequestContextExtUtils.getDingUserId();
        // 钉钉移动端需要传入钉签用户信息
        if (StringUtils.isNoneBlank(dingCorpId, dingIsvAppId, dingUserId)
                && ClientTypeEnum.H5.getCode().equalsIgnoreCase(request.getClientType())) {
            ApprovalWillAuthUrlInputDTO.DingUserBean dingUserBean = new ApprovalWillAuthUrlInputDTO.DingUserBean();
            dingUserBean.setDingCorpId(dingCorpId);
            dingUserBean.setDingUserId(dingUserId);
            dingUserBean.setDingIsvAppId(dingIsvAppId);
            inputDTO.setDingUser(dingUserBean);
        }
        // 获取审批意愿地址
        ApprovalWillAuthUrlDTO willAuthUrlDTO = approvalFlowService.queryApprovalWillAuthUrl(inputDTO);
        // 组装响应数据
        ApprovalWillAuthUrlResponse response = new ApprovalWillAuthUrlResponse();
        response.setNeedAuth(willAuthUrlDTO.isNeedAuth());
        response.setAuthId(willAuthUrlDTO.getAuthId());
        response.setAuthType(willAuthUrlDTO.getAuthType());
        response.setAuthUrl(willAuthUrlDTO.getAuthUrl());
        response.setShortAuthUrl(willAuthUrlDTO.getShortAuthUrl());
        return RestResult.success(response);
    }

    private String getApprovalCode(ApprovalAuthUrlRequest.ApprovalTask approvalTask) {
        String approvalCode = approvalTask.getApprovalCode();
        if (StringUtils.isNotBlank(approvalCode)) {
            return approvalCode;
        }
        log.info("ApprovalTask doesn't have approvalCode");
        return approvalTask.getApprovalId();
    }

    @ApiOperation("审批操作前置校验")
    @RestMapping(path = "/operate-pre-check", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<ApprovalOperatePreCheckResponse> operatePreCheck(@RequestBody ApprovalOperatePreCheckRequest request) {
        String operatorId = RequestContextExtUtils.getOperatorId();
        String approverId = queryActualApproverId(request.getApproverId(), operatorId);
        ApprovalOperatePreCheckInputDTO inputDTO = new ApprovalOperatePreCheckInputDTO();
        inputDTO.setOperateDatas(ApprovalRestConverter.operateDataConvert(request.getOperateDatas()));
        inputDTO.setTenantId(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorId(operatorId);
        inputDTO.setApproverId(approverId);
        // 校验批量操作的审批流程数量是否达到上限
        AtomicInteger operateDataSize = new AtomicInteger();
        inputDTO.getOperateDatas().forEach(i -> operateDataSize.addAndGet(Optional.ofNullable(i.getDatas()).orElse(Lists.newArrayList()).size()));
        if (operateDataSize.get() > 100) {
            throw new SaasBizException(SAAS_ILLEGAL_PARAM.getCode(), "批量操作的审批流程数量不可超过100");
        }
        ApprovalOperatePreCheckService operatePreCheckService =
                operatePreCheckServiceFactory.getService(request.getOperateType());
        if (null == operatePreCheckService) {
            return RestResult.success(ApprovalOperatePreCheckResponse.pass());
        }
        ApprovalOperatePreCheckDTO preCheckDTO = operatePreCheckService.operatePreCheck(inputDTO);
        // 组装返回结果
        ApprovalOperatePreCheckResponse response =
                ApprovalRestConverter.buildApprovalOperatePreCheckResponse(preCheckDTO);
        return RestResult.success(response);
    }

    @ApiOperation("审批操作前置校验-同一批次的审批流程一起操作")
    @RestMapping(path = "/operate-pre-check-by-process", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<ApprovalOperatePreCheckResponse> operatePreCheckByProcess(
            @RequestBody ApprovalOperatePreCheckByProcessRequest request) {
        String operatorId = RequestContextExtUtils.getOperatorId();
        String approverId = queryActualApproverId(request.getApproverId(), operatorId);
        ApprovalOperatePreCheckByProcessInputDTO inputDTO = new ApprovalOperatePreCheckByProcessInputDTO();
        inputDTO.setApprovalCode(request.getApprovalCode());
        inputDTO.setOperatorId(operatorId);
        inputDTO.setTenantId(RequestContextExtUtils.getTenantId());
        inputDTO.setApproverId(approverId);
        ApprovalOperatePreCheckService operatePreCheckService =
                operatePreCheckServiceFactory.getService(request.getOperateType());
        if (null == operatePreCheckService) {
            return RestResult.success(ApprovalOperatePreCheckResponse.pass());
        }
        ApprovalOperatePreCheckDTO preCheckDTO = operatePreCheckService.operatePreCheckByProcess(inputDTO);
        // 组装返回结果
        ApprovalOperatePreCheckResponse response =
                ApprovalRestConverter.buildApprovalOperatePreCheckResponse(preCheckDTO);
        return RestResult.success(response);
    }

    @ApiOperation("获取审批意愿认证地址-同一批次的审批流程一起操作")
    @RestMapping(path = "/will-auth-url-by-process", method = RequestMethod.POST)
    public RestResult<ApprovalWillAuthUrlResponse> willAuthUrlByProcess(@RequestBody ApprovalAuthUrlByProcessRequest request) {
        String approverId = queryActualApproverId(request.getApproverId(), RequestContextExtUtils.getOperatorId());
        List<ApprovalInBatchTaskDTO> taskDTOS =
                approvalFlowService.queryPendingMeApprovalTasksInBatch(request.getApprovalCode(), approverId);
        if (CollectionUtils.isEmpty(taskDTOS)) {
            throw new SaasBizException(APPROVAL_ALREADY_OPERATED);
        }
        ApprovalAuthUrlRequest authUrlRequest = new ApprovalAuthUrlRequest();
        authUrlRequest.setApprovalTasks(taskDTOS.stream().map(i -> {
            ApprovalAuthUrlRequest.ApprovalTask task = new ApprovalAuthUrlRequest.ApprovalTask();
            task.setApprovalCode(i.getApprovalCode());
            task.setTaskId(i.getTaskId());
            return task;
        }).collect(Collectors.toList()));
        authUrlRequest.setOrigin(request.getOrigin());
        authUrlRequest.setClientType(request.getClientType());
        authUrlRequest.setRedirectUrl(request.getRedirectUrl());
        authUrlRequest.setAppScheme(request.getAppScheme());
        authUrlRequest.setWillTypeHided(request.getWillTypeHided());
        return willAuthUrl(authUrlRequest);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("同意审批-同一批次的审批流程一起操作")
    @RestMapping(path = "/agree-by-process", method = RequestMethod.POST)
    public RestResult agreeByProcess(@RequestBody ApprovalOperateByProcessRequest request) {
        String operatorId = RequestContextExtUtils.getOperatorId();
        List<ApprovalInBatchTaskDTO> taskDTOS = queryOperateApprovalTaskList(operatorId, request);
        ApprovalOperateDataDTO operateDataDTO =
                ApprovalRestConverter.operateDataConvert(
                        request.getApprovalType(), request.getRemark(), taskDTOS);
        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setOperateType(ApprovalOperateTypeEnum.AGREE);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setClient(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setWillAuthType(request.getWillAuthType());
        inputDTO.setWillAuthId(request.getWillAuthId());
        inputDTO.setOperateDatas(Lists.newArrayList(operateDataDTO));
        ApprovalOperateForwardResult forwardResult = operateForwardService.forward(inputDTO);
        return RestResult.success(ApprovalRestConverter.forwardResultConvert(forwardResult));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("拒绝审批-同一批次的审批流程一起操作")
    @RestMapping(path = "/refuse-by-process", method = RequestMethod.POST)
    public RestResult refuseByProcess(@RequestBody ApprovalOperateByProcessRequest request) {
        AssertX.isTrue(StringUtils.isNotBlank(request.getRemark()), "审批意见不能为空");
        String operatorId = RequestContextExtUtils.getOperatorId();
        List<ApprovalInBatchTaskDTO> taskDTOS = queryOperateApprovalTaskList(operatorId, request);
        ApprovalOperateDataDTO operateDataDTO =
                ApprovalRestConverter.operateDataConvert(
                        request.getApprovalType(), request.getRemark(), taskDTOS);
        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setOperateType(ApprovalOperateTypeEnum.REFUSE);
        inputDTO.setSubjectOid(RequestContextExtUtils.getTenantId());
        inputDTO.setClient(RequestContextExtUtils.getClientId());
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setWillAuthType(request.getWillAuthType());
        inputDTO.setWillAuthId(request.getWillAuthId());
        inputDTO.setOperateDatas(Lists.newArrayList(operateDataDTO));
        ApprovalOperateForwardResult forwardResult = operateForwardService.forward(inputDTO);
        return RestResult.success(ApprovalRestConverter.forwardResultConvert(forwardResult));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询审批业务流程合同文件列表")
    @RestMapping(path = "/biz-contract-files", method = RequestMethod.GET)
    public RestResult<ApprovalBizContractFilesResponse> listBizContractFiles(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @URIQueryParam ApprovalBizContractFilesRequest request) {
        ApprovalListContractFilesInputDTO inputDTO = new ApprovalListContractFilesInputDTO();
        inputDTO.setApprovalCode(request.getApprovalCode());
        inputDTO.setOperatorOid(accountId);
        inputDTO.setSubjectOid(tenantId);
        inputDTO.setMenuId(request.getMenuId());
        inputDTO.setResourceShareId(request.getResourceShareId());
        List<ApprovalBizContractFileDTO> contractFilesDTOS = approvalFlowService.queryBizContractFiles(inputDTO);
        ApprovalBizContractFilesResponse response = new ApprovalBizContractFilesResponse();
        response.setContractFiles(ApprovalRestConverter.convertBizContractFileVOs(contractFilesDTOS));
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("审批明细导出")
    @RestMapping(path = "/async-export", method = RequestMethod.POST)
    public RestResult<ApprovalAsyncExportResponse> approvalExport(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody ApprovalExportRequest request) {
        ApprovalExportInputDTO inputDTO = buildListInputDTO(accountId, request, ApprovalExportInputDTO.class);
        if (null == inputDTO) {
            throw new SaasBizException(APPROVAL_EXPORT_NO_DATA);
        }
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        inputDTO.setApprovalCodes(request.getApprovalCodes());
        String exportSerialId = approvalFlowService.approvalAsyncExport(inputDTO);
        return RestResult.success(new ApprovalAsyncExportResponse(exportSerialId, true));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("审批明细导出统计")
    @RestMapping(path = "/async-export/number", method = RequestMethod.POST)
    public RestResult<ApprovalAsyncExportNumberResponse> approvalExportNumber(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody ApprovalExportRequest request) {
        ApprovalExportInputDTO inputDTO = buildListInputDTO(accountId, request, ApprovalExportInputDTO.class);
        if (null == inputDTO) {
            return RestResult.success(new ApprovalAsyncExportNumberResponse(0));
        }
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        inputDTO.setApprovalCodes(request.getApprovalCodes());
        int totalSize = approvalFlowService.approvalAsyncExportCount(inputDTO);
        return RestResult.success(new ApprovalAsyncExportNumberResponse(totalSize));
    }

    @ApiOperation("审批添加评论")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/add-comment", method = RequestMethod.POST)
    public RestResult<ApprovalAddCommentResponse> addComment(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody ApprovalAddCommentRequest request) {
        ApprovalAddCommentInputDTO inputDTO = buildApprovalAddCommentInputDTO(request, accountId, tenantId);
        return RestResult.success(
                new ApprovalAddCommentResponse(approvalFlowService.addComment(inputDTO)));
    }

    private ApprovalAddCommentInputDTO buildApprovalAddCommentInputDTO(ApprovalAddCommentRequest request,
                                                                       String accountId ,String tenantId) {
        ApprovalAddCommentInputDTO inputDTO = new ApprovalAddCommentInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        inputDTO.setApprovalCode(request.getApprovalCode());
        inputDTO.setCommentContent(request.getCommentContent());
        return inputDTO;
    }
    
    
    /**
     * 获取最终使用的审批人id
     * @param assignApproverId
     * @param loginApproverId
     * @return
     */
    private String queryActualApproverId(String assignApproverId, String loginApproverId) {
        if (StringUtils.isBlank(loginApproverId)) {
            return assignApproverId;
        }
        AccountInfoDTO loginApprover = userCenterService.queryAccountDetailByOid(loginApproverId);
        if (StringUtils.isNotBlank(loginApprover.getGid())) {
            return loginApproverId;
        }
        // 如果登录账号的姓名不为空， 且和审批人姓名相同， 则返回指定审批人id
        AccountInfoDTO assignApprover = userCenterService.queryAccountDetailByOid(assignApproverId);
        if (StringUtils.isNotBlank(loginApprover.getName())
                && StringUtils.equalsIgnoreCase(loginApprover.getName(), assignApprover.getName())) {
            return assignApproverId;
        }
        // 如果指定的审批人账号手机号邮箱均为空， 则跳过校验当前登录账号， 直接返回指定审批人账号
        if (StringUtils.isAllBlank(
                assignApprover.getLoginMobile(),
                assignApprover.getLoginEmail(),
                assignApprover.getContactMobile(),
                assignApprover.getContactEmail())) {
            return assignApproverId;
        }
        // 校验登录手机号
        String loginMobile = loginApprover.getLoginMobile();
        String assignLoginMobile = assignApprover.getLoginMobile();
        String assignContactMobile = assignApprover.getContactMobile();
        if (StringUtils.isNotBlank(loginMobile)
                && !StringUtils.isAllBlank(assignLoginMobile, assignContactMobile)
                && StringUtils.equalsAnyIgnoreCase(loginMobile, assignLoginMobile, assignContactMobile)) {
            return assignApproverId;
        }

        // 校验登录邮箱
        String loginEmail = loginApprover.getLoginEmail();
        String assignLoginEmail = assignApprover.getLoginEmail();
        String assignContactEmail = assignApprover.getContactEmail();
        if (StringUtils.isNotBlank(loginEmail)
                && !StringUtils.isAllBlank(assignLoginEmail, assignContactEmail)
                && StringUtils.equalsAnyIgnoreCase(loginEmail, assignLoginEmail, assignContactEmail)) {
            return assignApproverId;
        }
        return loginApproverId;
    }

    /**
     * 获取待操作的审批任务列表
     *
     * @param request
     * @return
     */
    private List<ApprovalInBatchTaskDTO> queryOperateApprovalTaskList(
            String operatorId, ApprovalOperateByProcessRequest request) {
        List<ApprovalInBatchTaskDTO> taskDTOS =
                approvalFlowService.queryPendingMeApprovalTasksInBatch(
                        request.getApprovalCode(), operatorId);
        if (CollectionUtils.isNotEmpty(taskDTOS) && StringUtils.isNoneBlank(request.getWillAuthId(), request.getWillAuthType())) {
            List<ApprovalOperateTaskDTO> operateTaskList = approvalFlowService.queryWillAuthTaskList(request.getWillAuthId(), request.getWillAuthType());
            List<String> approvalCodes = operateTaskList.stream().map(i -> i.getApprovalCode()).collect(Collectors.toList());
            taskDTOS = taskDTOS.stream().filter(i -> approvalCodes.contains(i.getApprovalCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(taskDTOS)) {
            throw new SaasBizException(APPROVAL_ALREADY_OPERATED);
        }
        return taskDTOS;
    }

    /** 生成填充合同来源信息 */
    private ApprovalVOConverter.SourceConvertBO generateSourceConvertBO(
            String processId, Integer approvalType) {
        ApprovalVOConverter.SourceConvertBO sourceConvertBO =
                new ApprovalVOConverter.SourceConvertBO();
        if (ApprovalBizTypeEnum.SEAL_APPLY_WITNESS.getType().equals(approvalType)) {
            return ApprovalVOConverter.convert2SourceConvertBOTianYin();
        }

        if (StringUtils.isBlank(processId)) {
            return sourceConvertBO;
        }
        ProcessStartSourceOutputDTO outputDTO = processService.queryStartSource(processId);
        if (outputDTO == null) {
            return sourceConvertBO;
        }
        return ApprovalVOConverter.convert2SourceConvertBO(outputDTO);
    }
}
