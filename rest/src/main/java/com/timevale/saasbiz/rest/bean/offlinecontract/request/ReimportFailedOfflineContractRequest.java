package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 重新导入记录中所有导入失败的线下合同
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Data
public class ReimportFailedOfflineContractRequest extends ToString {

    @ApiModelProperty(value = "导入记录id", required = true)
    @NotBlank(message = "导入记录id不能为空")
    private String recordId;
}
