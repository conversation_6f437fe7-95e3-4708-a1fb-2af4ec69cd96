package com.timevale.saasbiz.rest.bean.contractreview.rule.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.ReviewResultRuleOutputDTO;
import lombok.Data;

import java.util.List;

@Data
public class ReviewResultRuleOuterResponse extends ToString {
    private String status;
    private List<ReviewResultRuleOutputDTO> ruleResults;
}
