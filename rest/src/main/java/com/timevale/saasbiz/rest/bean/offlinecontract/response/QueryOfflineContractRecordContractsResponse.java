package com.timevale.saasbiz.rest.bean.offlinecontract.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractVO;
import lombok.Data;

import java.util.List;

/**
 * 查询线下合同导入记录合同信息列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class QueryOfflineContractRecordContractsResponse extends ToString {

    /** 总数 */
    private long total;

    /** 线下合同合同信息列表 */
    private List<OfflineContractVO> contracts;
}
