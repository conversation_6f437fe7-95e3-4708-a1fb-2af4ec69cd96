package com.timevale.saasbiz.rest;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

import com.timevale.dayu.facade.enums.AuditLogBizTypeEnum;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.model.base.BaseResult;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.auditlog.AuditLogRecordDTO;
import com.timevale.saasbiz.model.bean.auditlog.input.AuditLogConfigInputDTO;
import com.timevale.saasbiz.model.bean.auditlog.input.AuditLogRecordInputDTO;
import com.timevale.saasbiz.model.bean.auditlog.input.AuditLogSubscriberSaveInputDTO;
import com.timevale.saasbiz.model.bean.auditlog.input.DownloadAuditLogInputDTO;
import com.timevale.saasbiz.model.bean.auditlog.input.ListAuditLogInputDTO;
import com.timevale.saasbiz.model.bean.auditlog.input.QueryDownloadStatusInputDTO;
import com.timevale.saasbiz.model.bean.auditlog.output.*;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasPrivilegeInputDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.enums.AppConfigEnum;
import com.timevale.saasbiz.model.enums.AuditLogTaskTypeEnum;
import com.timevale.saasbiz.model.enums.PrivilegeOperationEnum;
import com.timevale.saasbiz.model.enums.PrivilegeResourceEnum;
import com.timevale.saasbiz.model.utils.AppConfigUtil;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogConfigQueryRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogDownloadRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogListRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogRecordQueryRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogSubscriberSaveRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.QueryDownloadStatusRequest;
import com.timevale.saasbiz.rest.bean.auditlog.response.*;
import com.timevale.saasbiz.rest.bean.auditlog.vo.AuditLogRecordVO;
import com.timevale.saasbiz.rest.converter.AuditLogVOConverter;
import com.timevale.saasbiz.service.auditlog.AuditLogService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023-10-11 15:52
 */
@Api(tags = "审计日志")
@Validated
@ExternalService
@RestMapping(path = "/v1/saas-common/audit-log")
public class AuditLogRest {
    private static final String FILE_NAME_FORMAT = "e签宝操作日志_%s_%s";
    private static final String SUBSCRIBE_FILE_NAME_FORMAT = "风险事件推送记录_%s";


    @Autowired private UserCenterService userCenterService;
    @Autowired private AuditLogService auditLogService;
    @Autowired private MapperFactory mapperFactory;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询审计日志列表")
    @RestMapping(path = "/page-list", method = RequestMethod.POST)
    public BaseResult<AuditLogListResponse> pageList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AuditLogListRequest request) {
        // 检查时间范围
        request.validDuration();
        // 是否有查看日志权限
        userCenterService.checkHasPrivilege(
                new CheckHasPrivilegeInputDTO(
                        accountId,
                        tenantId,
                        PrivilegeResourceEnum.AUDIT_LOG.getType(),
                        PrivilegeOperationEnum.QUERY.getType()),
                true);

        ListAuditLogInputDTO inputDTO = AuditLogVOConverter.convert(request);
        inputDTO.setResourceTenantId(tenantId);
        inputDTO.setOperatorTenantId(request.getTenantId());
        AuditLogListOutputDTO listDTO = auditLogService.pageList(inputDTO);

        return BaseResult.success(AuditLogVOConverter.convert(listDTO));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("业务模块列表")
    @RestMapping(path = "/first-module-list", method = RequestMethod.GET)
    public BaseResult<AuditLogFirstModuleResponse> listFirstModule(@ApiParam(value = "业务类型", required = false) @RequestParam(value = "type", required = false)
            String type) {
        List<AuditLogFirstModuleDTO> list = auditLogService.listFirstModule(type);

        return BaseResult.success(AuditLogVOConverter.convert(list));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询事件列表")
    @RestMapping(path = "/event", method = RequestMethod.GET)
    public BaseResult<QueryEventListResponse> queryAuditLogEventList(
            @RequestParam(value = "module") String module,
            @ApiParam(value = "业务类型", required = false) @RequestParam(value = "type", required = false)
            String type) {
        EventListOutputDTO outputDTO = auditLogService.queryEventList(module, type);
        QueryEventListResponse response = new QueryEventListResponse();
        BeanUtils.copyProperties(outputDTO, response);

        return BaseResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("下载审计日志")
    @RestMapping(path = "/async-download", method = RequestMethod.POST)
    public BaseResult<AuditLogDownloadResponse> asyncDownload(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AuditLogDownloadRequest request) {

        if (StringUtils.isBlank(request.getExportType()) || AuditLogBizTypeEnum.LOG.getType().equals(request.getExportType())) {
            // 是否有导出日志权限
            userCenterService.checkHasPrivilege(
                    new CheckHasPrivilegeInputDTO(
                            accountId,
                            tenantId,
                            PrivilegeResourceEnum.AUDIT_LOG.getType(),
                            PrivilegeOperationEnum.EXPORT.getType()),
                    true);
        }

        AccountInfoDTO account = userCenterService.queryAccountInfoByOid(accountId);
        String nowTime = DateUtils.getWebTodayString();

        DownloadAuditLogInputDTO inputDTO = AuditLogVOConverter.convert(request);
        inputDTO.setResourceTenantId(tenantId);
        inputDTO.setOperatorTenantId(request.getTenantId());
        inputDTO.setOperatorId(accountId);
        inputDTO.setExportFileName(AuditLogTaskTypeEnum.SUBSCRIBE.getType().equals(request.getExportType()) ? String.format(SUBSCRIBE_FILE_NAME_FORMAT, nowTime) : String.format(FILE_NAME_FORMAT, account.getName(), nowTime));
        inputDTO.setTagName(AppConfigUtil.getString(AppConfigEnum.SAAS_DOWNLOAD_AUDIT_LOG_TAG));
        inputDTO.setExportType(request.getExportType());
        AuditLogDownloadOutputDTO outputDTO = auditLogService.asyncDownload(inputDTO);

        return BaseResult.success(AuditLogVOConverter.convert(outputDTO));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询下载审计日志结果")
    @RestMapping(path = "/download-url", method = RequestMethod.GET)
    public BaseResult<QueryDownloadStatusResponse> queryAuditLogDownload(
            @URIQueryParam QueryDownloadStatusRequest request) {
        QueryDownloadStatusInputDTO inputDTO = new QueryDownloadStatusInputDTO();
        BeanUtils.copyProperties(request, inputDTO);
        inputDTO.setOperatorId(RequestContextExtUtils.getOperatorId());
        inputDTO.setOrgId(RequestContextExtUtils.getTenantId());

        DownloadStatusOutputDTO outputDTO = auditLogService.queryDownloadResult(inputDTO);

        QueryDownloadStatusResponse response = new QueryDownloadStatusResponse();
        BeanUtils.copyProperties(outputDTO, response);
        return BaseResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询导出记录")
    @RestMapping(path = "/export-record", method = RequestMethod.GET)
    public BaseResult<QueryAuditLogExportListResponse> queryAuditLogExportList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @ApiParam(value = "页码", required = true) @RequestParam(value = "pageNo") Integer pageNo,
            @ApiParam(value = "页数据量", required = true) @RequestParam(value = "pageSize")
                    Integer pageSize,
            @ApiParam(value = "导出类型", required = false) @RequestParam(value = "exportType", required = false)
                    String exportType) {

        if(StringUtils.isBlank(exportType) || AuditLogBizTypeEnum.LOG.getType().equals(exportType)){
            // 是否有导出日志权限
            userCenterService.checkHasPrivilege(
                    new CheckHasPrivilegeInputDTO(
                            accountId,
                            tenantId,
                            PrivilegeResourceEnum.AUDIT_LOG.getType(),
                            PrivilegeOperationEnum.EXPORT.getType()),
                    true);
        }
        AuditLogExportListOutputDTO outputDTO =
                auditLogService.queryExportList(
                        RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId(),
                        pageSize,
                        pageNo,
                        exportType);

        return BaseResult.success(AuditLogVOConverter.convert(outputDTO));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.MONITOR,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询风险订阅配置")
    @RestMapping(path = "/subscription/config-list", method = RequestMethod.POST)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.MONITOR)
    public BaseResult<AuditLogConfigListResponse> queryConfigList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AuditLogConfigQueryRequest request) {

        AuditLogConfigInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, AuditLogConfigInputDTO.class);
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        AuditLogConfigListOutputDTO outputDTO = auditLogService.queryConfigList(inputDTO);

        return BaseResult.success(mapperFactory.getMapperFacade().map(outputDTO, AuditLogConfigListResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询推送记录")
    @RestMapping(path = "/subscription/record-list", method = RequestMethod.POST)
    public BaseResult<AuditLogRecordListResponse> queryRecordList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AuditLogRecordQueryRequest request) {
        // 检查时间范围
        request.validDuration();
        AuditLogRecordInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, AuditLogRecordInputDTO.class);
        inputDTO.setAccountId(accountId);
        inputDTO.setTenantId(tenantId);
        AuditLogRecordListOutputDTO outputDTO = auditLogService.queryRecordList(inputDTO);

        return BaseResult.success(mapperFactory.getMapperFacade().map(outputDTO, AuditLogRecordListResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询推送记录")
    @RestMapping(path = "/subscription/record-detail", method = RequestMethod.GET)
    public BaseResult<AuditLogRecordVO> getRecordDetail(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam(value = "recordId") String recordId,
            @RequestParam(value = "resourceTenantId") String resourceTenantId) {
        resourceTenantId = StringUtils.isNotBlank(resourceTenantId) ? resourceTenantId : tenantId;
        AuditLogRecordDTO outputDTO = auditLogService.getRecord(resourceTenantId, accountId, recordId);
        return BaseResult.success(mapperFactory.getMapperFacade().map(outputDTO, AuditLogRecordVO.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.MONITOR,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询订阅人")
    @RestMapping(path = "/subscription/subscriber-list", method = RequestMethod.GET)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.MONITOR)
    public BaseResult<AuditLogSubscriberListResponse> querySubscriberList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @ApiParam(value = "订阅配置ID", required = true) @RequestParam(value = "configId")
                    String configId) {

        AuditLogSubscriberListOutputDTO outputDTO = auditLogService.querySubscriberList(tenantId, accountId, configId);
        return BaseResult.success(mapperFactory.getMapperFacade().map(outputDTO, AuditLogSubscriberListResponse.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.MONITOR,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("保存订阅人")
    @RestMapping(path = "/subscription/save-subscriber", method = RequestMethod.POST)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.MONITOR)
    public BaseResult<AuditLogConfigUpdateResponse> saveSubscriber(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AuditLogSubscriberSaveRequest request) {
        AuditLogSubscriberSaveInputDTO outputDTO = mapperFactory.getMapperFacade().map(request, AuditLogSubscriberSaveInputDTO.class);
        outputDTO.setTenantId(tenantId);
        outputDTO.setAccountId(accountId);
        auditLogService.saveSubscriber(outputDTO);
        return BaseResult.success(new AuditLogConfigUpdateResponse(true));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.MONITOR,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("修改订阅状态")
    @RestMapping(path = "/subscription/change-status", method = RequestMethod.GET)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.MONITOR)
    public BaseResult<AuditLogConfigUpdateResponse> changeStatus(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @ApiParam(value = "订阅配置ID", required = true) @RequestParam(value = "configId")
                    String configId) {
        auditLogService.changeStatus(tenantId, accountId, configId);
        return BaseResult.success(new AuditLogConfigUpdateResponse(true));
    }
}
