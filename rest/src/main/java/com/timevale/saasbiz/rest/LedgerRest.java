package com.timevale.saasbiz.rest;

import com.google.common.collect.Lists;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerTemplateStructMatchResultDTO;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecRunModelEnum;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecRunStatusEnum;
import com.timevale.contractanalysis.facade.api.enums.LedgerExtractTypeEnum;
import com.timevale.contractanalysis.facade.api.enums.LedgerFormStatusEnum;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saas.tracking.annotation.Tracking;
import com.timevale.saas.tracking.constants.TrackingModeConstants;
import com.timevale.saasbiz.model.bean.common.dto.TaskProgressDTO;
import com.timevale.saasbiz.model.bean.ledger.bo.LedgerExecSimpleBO;
import com.timevale.saasbiz.model.bean.ledger.bo.LedgerFormBO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerFileUrlInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerListInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerFormSaveInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerFormUpdateAiFieldInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerFormUpdateInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerUpdateFormDataInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerAiPageBalanceResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerExecLastTrialDoneResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerExecTrialProcessFileResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerExtractConfigDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerFileUrlResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerFormDataDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerFormDetailResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerLastRunInfoResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerListResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerTaskProgressResultDTO;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.enums.ledger.LedgerExecShowStatus;
import com.timevale.saasbiz.model.enums.ledger.LedgerExecTrialEmptyReason;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.autoarchive.response.LedgerExtractConfigResponse;
import com.timevale.saasbiz.rest.bean.autoarchive.response.LedgerFormDataResponse;
import com.timevale.saasbiz.rest.bean.common.response.TaskProgressResponse;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerCloseRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerExecCloseTrialDoneConfirmRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerFileUrlRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerFormSaveRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerFormUpdateAiFieldRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerFormUpdateRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerListRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerMatchStructRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerUpdateFormDataRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerUpdateStatusRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerViewFormDataByFormRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerViewFormDataRequest;
import com.timevale.saasbiz.rest.bean.ledger.response.LedgerAiPageBalanceResponse;
import com.timevale.saasbiz.rest.bean.ledger.response.LedgerExecToBeConfirmedTrialResponse;
import com.timevale.saasbiz.rest.bean.ledger.response.LedgerExecTrialProcessFileResponse;
import com.timevale.saasbiz.rest.bean.ledger.response.LedgerFileUrlResponse;
import com.timevale.saasbiz.rest.bean.ledger.response.LedgerFormListResponse;
import com.timevale.saasbiz.rest.bean.ledger.response.LedgerLastRunInfoResponse;
import com.timevale.saasbiz.rest.bean.ledger.response.LedgerNextFormProcessIdResponse;
import com.timevale.saasbiz.rest.bean.ledger.vo.FormDetailVO;
import com.timevale.saasbiz.rest.bean.ledger.vo.FormVO;
import com.timevale.saasbiz.rest.bean.ledger.vo.LedgerTemplateVO;
import com.timevale.saasbiz.service.common.TaskProgressService;
import com.timevale.saasbiz.service.ledger.LedgerFormService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

import java.util.List;
import java.util.stream.Collectors;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.tracking.consts.TrackingKeyConstant.LEDGER_REVIEW;
import static com.timevale.saasbiz.tracking.consts.TrackingServiceConstant.AI_PAGE_BILLING_ARREARS_TIP_TRACKING;
import static com.timevale.saasbiz.tracking.consts.TrackingKeyConstant.AI_PAGE_BILLING_ARREARS_TIP;
import static org.junit.Assert.fail;

/**
 * LedgerRest
 *
 * <AUTHOR>
 * @since 2023/8/23 10:33 上午
 */
@Api(tags = "台账接口")
@Validated
@ExternalService
@RestMapping(path = "v3/contract-ledger")
public class LedgerRest {

    @Autowired
    private TaskProgressService taskProgressService;

    @Autowired
    private LedgerFormService ledgerFormService;

    @Autowired
    private MapperFactory mapperFactory;

    @Autowired private SaasCommonService saasCommonService;

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "台账列表", httpMethod = "POST")
    @RestMapping(path = "/form-list", method = RequestMethod.POST)
    public RestResult<LedgerFormListResponse> list(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerListRequest request) {

        LedgerListInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, LedgerListInputDTO.class);
        LedgerListResultDTO resultDTO = ledgerFormService.pageList(inputDTO, tenantId, accountId);
        LedgerFormListResponse res = new LedgerFormListResponse();
        res.setTotal(resultDTO.getTotal());
        if (CollectionUtils.isEmpty(resultDTO.getList())) {
            res.setList(Lists.newArrayList());
            return RestResult.success(res);
        }
        res.setList(resultDTO.getList().stream().map(this::toFormVO).collect(Collectors.toList()));
        return RestResult.success(res);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.ADD)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "保存台账", httpMethod = "POST")
    @RestMapping(path = "/save-form", method = RequestMethod.POST)
    @Tracking(
        trackingKey = AI_PAGE_BILLING_ARREARS_TIP,
        trackingService = AI_PAGE_BILLING_ARREARS_TIP_TRACKING,
        trackingData = "'taizhang'",
        mode = TrackingModeConstants.MODE_MQ
    )
    public RestResult<String> save(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerFormSaveRequest request) {
        //兼容老版前端
        if (request.getOpenLedger() == null) {
            request.setOpenLedger(true);
            request.setRunModel(LedgerExecRunModelEnum.ALL.getCode());
        }
        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        LedgerFormSaveInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, LedgerFormSaveInputDTO.class);
        return RestResult.success(ledgerFormService.saveForm(inputDTO, tenantId, accountId));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "修改台账", httpMethod = "POST")
    @RestMapping(path = "/update-form", method = RequestMethod.POST)
    @Tracking(
        trackingKey = AI_PAGE_BILLING_ARREARS_TIP,
        trackingService = AI_PAGE_BILLING_ARREARS_TIP_TRACKING,
        trackingData = "'taizhang'",
        mode = TrackingModeConstants.MODE_MQ
    )
    public RestResult<Void> update(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerFormUpdateRequest request) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        LedgerFormUpdateInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, LedgerFormUpdateInputDTO.class);
        ledgerFormService.updateForm(inputDTO, tenantId, accountId);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "修改台账ai提取字段", httpMethod = "POST")
    @RestMapping(path = "/update-form-aiField", method = RequestMethod.POST)
    @Tracking(
        trackingKey = AI_PAGE_BILLING_ARREARS_TIP,
        trackingService = AI_PAGE_BILLING_ARREARS_TIP_TRACKING,
        trackingData = "'taizhang'",
        mode = TrackingModeConstants.MODE_MQ
    )
    public RestResult<Void> updateAiField(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerFormUpdateAiFieldRequest request) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        LedgerFormUpdateAiFieldInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, LedgerFormUpdateAiFieldInputDTO.class);
        ledgerFormService.updateFormAiField(inputDTO, tenantId, accountId);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询台账", httpMethod = "GET")
    @RestMapping(path = "/get-form-detail", method = RequestMethod.GET)
    public RestResult<FormDetailVO> detail(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId, @RequestParam String formId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        LedgerFormDetailResultDTO resultDTO = ledgerFormService.detail(formId, tenantId);
        FormDetailVO detailVO = mapperFactory.getMapperFacade().map(resultDTO, FormDetailVO.class);
        return RestResult.success(detailVO);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "拉取填写控件自动匹配", httpMethod = "POST")
    @RestMapping(path = "/match-template-struct", method = RequestMethod.POST)
    public RestResult<List<LedgerTemplateVO>> matchTemplateStruct(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerMatchStructRequest request) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        LedgerTemplateStructMatchResultDTO ledgerTemplateStructMatchResultDTO = ledgerFormService.matchTemplateStruct(tenantId, request.getTemplateIds());
        List<LedgerTemplateVO> ledgerTemplateVOS = mapperFactory.getMapperFacade().mapAsList(ledgerTemplateStructMatchResultDTO.getTemplates(), LedgerTemplateVO.class);
        return RestResult.success(ledgerTemplateVOS);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.DELETE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "删除台账", httpMethod = "GET")
    @RestMapping(path = "/delete-form", method = RequestMethod.GET)
    public RestResult<Void> delete(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestParam String formId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        ledgerFormService.delete(formId, tenantId, accountId);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "修改台账状态", httpMethod = "POST")
    @RestMapping(path = "/update-form-status", method = RequestMethod.POST)
    @Deprecated
    public RestResult<Void> updateStatus(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerUpdateStatusRequest request) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        ledgerFormService.updateStatus(request.getFormId(), tenantId, accountId, request.getSwitchOn());
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "关闭台账", httpMethod = "POST")
    @RestMapping(path = "/close", method = RequestMethod.POST)
    public RestResult<Void> closeForm(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerCloseRequest request) {
        
        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        ledgerFormService.updateStatus(request.getFormId(), tenantId, accountId, false);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取台账进度", httpMethod = "GET")
    @RestMapping(path = "/get-form-progress", method = RequestMethod.GET)
    public RestResult<TaskProgressResponse> getFormProcess(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId, @RequestParam String formId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);


        LedgerTaskProgressResultDTO resultDTO = ledgerFormService.queryTaskProgress(formId, tenantId);
        TaskProgressResponse res = mapperFactory.getMapperFacade().map(resultDTO, TaskProgressResponse.class);
        return RestResult.success(res);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "重新运行台账", httpMethod = "GET")
    @RestMapping(path = "/rerun-form", method = RequestMethod.GET)
    @Tracking(
        trackingKey = AI_PAGE_BILLING_ARREARS_TIP,
        trackingService = AI_PAGE_BILLING_ARREARS_TIP_TRACKING,
        trackingData = "'taizhang'",
        mode = TrackingModeConstants.MODE_MQ
    )
    public RestResult<Void> rerun(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId, @RequestParam String formId,
            @RequestParam(required = false) Boolean clearHistory,
                                  @RequestParam(required = false) String runModel) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        //兼容老前端
        if (clearHistory == null) {
            clearHistory = false;
        }
        if (runModel != null) {
            LedgerExecRunModelEnum.check(runModel);
        }else {
            runModel = LedgerExecRunModelEnum.ALL.getCode();
        }
        ledgerFormService.rerun(formId, tenantId, accountId, clearHistory, runModel);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "继续运行台账", httpMethod = "GET")
    @RestMapping(path = "/continue-run", method = RequestMethod.GET)
    @Tracking(
        trackingKey = AI_PAGE_BILLING_ARREARS_TIP,
        trackingService = AI_PAGE_BILLING_ARREARS_TIP_TRACKING,
        trackingData = "'taizhang'",
        mode = TrackingModeConstants.MODE_MQ
    )
    public RestResult<Void> rerun(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
                                  @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
                                  @RequestParam String formId,
                                  @RequestParam(required = false) Boolean clearHistory) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        ledgerFormService.continueRun(formId, tenantId, accountId, clearHistory);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "停止台账运行", httpMethod = "GET")
    @RestMapping(path = "/stop-form", method = RequestMethod.GET)
    public RestResult<Void> stop(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId, @RequestParam String formId) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        ledgerFormService.stopForm(formId, tenantId, accountId);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "修改提取值", httpMethod = "POST")
    @RestMapping(path = "/update-form-data", method = RequestMethod.POST)
    public RestResult<Void> updateFormData(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerUpdateFormDataRequest request) {

        saasCommonService.checkSupportFunction(tenantId, null, FunctionCodeConstant.INTELLIGENT_STANDBOOK);

        LedgerUpdateFormDataInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, LedgerUpdateFormDataInputDTO.class);
        ledgerFormService.updateFormData(tenantId, accountId, inputDTO);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询提取值", httpMethod = "POST")
    @RestMapping(path = "/get-form-data", method = RequestMethod.POST)
    public RestResult<LedgerFormDataResponse> getFormData(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerViewFormDataRequest request) {
        LedgerFormDataDTO ledgerFormDataDTO = ledgerFormService.getFormDataResult(request.getMenuId(), request.getProcessId(), tenantId, accountId);
        return RestResult.success(mapperFactory.getMapperFacade().map(ledgerFormDataDTO, LedgerFormDataResponse.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询合同抽取结果", httpMethod = "POST")
    @RestMapping(path = "/process-extract-data", method = RequestMethod.POST)
    public RestResult<LedgerFormDataResponse> getFormData(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerViewFormDataByFormRequest request) {
        LedgerFormDataDTO ledgerFormDataDTO = ledgerFormService.getFormDataResultByFormId(request.getFormId(), request.getProcessId(), tenantId, accountId);
        return RestResult.success(mapperFactory.getMapperFacade().map(ledgerFormDataDTO, LedgerFormDataResponse.class));
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询提取限制", httpMethod = "GET")
    @RestMapping(path = "/get-extract-config", method = RequestMethod.GET)
    public RestResult<LedgerExtractConfigResponse> getExtractConfig(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId) {
        LedgerExtractConfigDTO extractLimit = ledgerFormService.getExtractLimit(tenantId);
        return RestResult.success(mapperFactory.getMapperFacade().map(extractLimit, LedgerExtractConfigResponse.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询最后一次体验完成台账", httpMethod = "GET")
    @RestMapping(path = "/toBeConfirmed-trial", method = RequestMethod.GET)
    public RestResult<LedgerExecToBeConfirmedTrialResponse> getExecToBeConfirmedTrial(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {
        LedgerExecLastTrialDoneResultDTO trialDoneResultDTO = ledgerFormService.queryExecLastTrialDone(tenantId);
        return RestResult.success(mapperFactory.getMapperFacade().map(trialDoneResultDTO, LedgerExecToBeConfirmedTrialResponse.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.UPDATE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "关闭待确认体验完成台账通知", httpMethod = "POST")
    @RestMapping(path = "/close-toBeConfirmed-trial", method = RequestMethod.POST)
    public RestResult<Void> closeExecToBeConfirmedTrial(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody LedgerExecCloseTrialDoneConfirmRequest request) {
        ledgerFormService.closeLedgerTrialDoneConfirm(request.getFormId(), tenantId);
        return RestResult.success();
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "页数余额查询", httpMethod = "GET")
    @RestMapping(path = "/ai-page-balance", method = RequestMethod.GET)
    public RestResult<LedgerAiPageBalanceResponse> aiPageBalance(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId
    ) {
        LedgerAiPageBalanceResultDTO resultDTO = ledgerFormService.aiPageBalance(tenantId);
        return RestResult.success(mapperFactory.getMapperFacade().map(resultDTO, LedgerAiPageBalanceResponse.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "试运行结果合同列表", httpMethod = "GET")
    @RestMapping(path = "/trial-processes", method = RequestMethod.GET)
    @Tracking(
        trackingKey = LEDGER_REVIEW,
        trackingData = "{{#formId}}",
        mode = TrackingModeConstants.MODE_MQ
    )
    public RestResult<LedgerExecTrialProcessFileResponse> trialProcesses(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestParam String formId) {
        LedgerExecTrialProcessFileResultDTO resultDTO = ledgerFormService.queryExecTrialProcessFile(formId, tenantId);
        return RestResult.success(mapperFactory.getMapperFacade().map(resultDTO, LedgerExecTrialProcessFileResponse.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取下一条台账提取记录", httpMethod = "GET")
    @RestMapping(path = "/next-extract-process", method = RequestMethod.GET)
    public RestResult<LedgerNextFormProcessIdResponse> nextFormProcessId(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestParam String formId, @RequestParam String currentProcessId) {
        String processId = ledgerFormService.nextFormProcessId(formId, currentProcessId, tenantId);
        LedgerNextFormProcessIdResponse response = new LedgerNextFormProcessIdResponse();
        response.setProcessId(processId);
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询最后一次台账运行信息", httpMethod = "GET")
    @RestMapping(path = "/last-run-info", method = RequestMethod.GET)
    public RestResult<LedgerLastRunInfoResponse> lastRunInfo(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestParam String formId) {
        LedgerLastRunInfoResultDTO resultDTO = ledgerFormService.queryLastRunInfo(formId, tenantId);
        return RestResult.success(mapperFactory.getMapperFacade().map(resultDTO, LedgerLastRunInfoResponse.class));
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING,
            privilegeKey = PrivilegeOperationConstants.QUERY_BOOK_KEEPING)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "获取台账合同url", httpMethod = "POST")
    @RestMapping(path = "/process-file-url", method = RequestMethod.POST)
    public RestResult<LedgerFileUrlResponse> processFileUrl(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody LedgerFileUrlRequest request) {

        LedgerFileUrlInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, LedgerFileUrlInputDTO.class);
        LedgerFileUrlResultDTO resultDTO = ledgerFormService.queryLedgerFileUrl(inputDTO, tenantId, accountId);
        return RestResult.success(mapperFactory.getMapperFacade().map(resultDTO, LedgerFileUrlResponse.class));
    }


    private FormVO toFormVO(LedgerFormBO formBO) {
        FormVO vo = mapperFactory.getMapperFacade().map(formBO, FormVO.class);
        buildShowStatusAndEmptyReson(vo, formBO);
        LedgerExtractTypeEnum extractTypeEnum = LedgerExtractTypeEnum.getByType(formBO.getExtractType());
        vo.setIsAi(extractTypeEnum != null && extractTypeEnum.isAi());
        vo.setHasHistory(formBO.getHistoryExec() != null);
        return vo;
    }

    private void buildShowStatusAndEmptyReson(FormVO formVO, LedgerFormBO formBO) {
        LedgerExecShowStatus showStatus = parseLedgerExecShowStatus(formBO);
        formVO.setShowStatus(showStatus.getCode());

        if (LedgerExecShowStatus.HISTORY_EXEC_TRIAL_DONE_EMPTY != showStatus) {
            return;
        }
        if (formBO.getHistoryExec() != null){
            Boolean hasArrears = formBO.getHistoryExec().getHasArrears();
            if (LedgerExecRunModelEnum.WAIT_SUB.getCode().equals(formBO.getHistoryExec().getRunModel())) {
                hasArrears = formBO.getHistoryExec().getSubHasArrears();
            }
            LedgerExecTrialEmptyReason emptyReason = BooleanUtils.isTrue(hasArrears) ? LedgerExecTrialEmptyReason.BALANCE : LedgerExecTrialEmptyReason.NOT_FIND;
            formVO.setTrialEmptyReason(emptyReason.getReason());
        }else {
            formVO.setTrialEmptyReason(LedgerExecTrialEmptyReason.NOT_FIND.getReason());
        }
    }

    private LedgerExecShowStatus parseLedgerExecShowStatus(LedgerFormBO formBO) {
        //失效时直接返回
        if (LedgerFormStatusEnum.INVALID.getStatus().equals(formBO.getFormStatus())) {
            return LedgerExecShowStatus.INVALID;
        }
        //没有运行过的台账按状态返回
        if (formBO.getHistoryExec() == null || !BooleanUtils.isTrue(formBO.getHistoryExec().getEffect())) {
            return LedgerFormStatusEnum.SWITCH_OFF.getStatus().equals(formBO.getFormStatus())
                    ? LedgerExecShowStatus.NOT_RUNNING
                    : LedgerExecShowStatus.RUNNING;
        }

        LedgerExecSimpleBO historyExec = formBO.getHistoryExec();
        if (LedgerExecRunModelEnum.ALL.getCode().equals(historyExec.getRunModel())) {
            if (LedgerFormStatusEnum.SWITCH_OFF.getStatus().equals(formBO.getFormStatus())) {
                return LedgerExecShowStatus.NOT_RUNNING;
            }
            if (LedgerExecRunStatusEnum.RUNNING.getCode().equals(historyExec.getRunStatus())) {
                return LedgerExecShowStatus.HISTORY_EXEC_ALL_RUNNING;
            } else if (LedgerExecRunStatusEnum.PAUSE.getCode().equals(historyExec.getRunStatus())) {
                return LedgerExecShowStatus.HISTORY_EXEC_PAUSE;
            } else {
                return LedgerExecShowStatus.RUNNING;
            }
        } else if (LedgerExecRunModelEnum.TRIAL.getCode().equals(historyExec.getRunModel())) {
            if (LedgerExecRunStatusEnum.RUNNING.getCode().equals(historyExec.getRunStatus())) {
                return LedgerExecShowStatus.HISTORY_EXEC_TRIAL_RUNNING;
            } else if (historyExec.getExtractNum() == null || historyExec.getExtractNum() == 0) {
                return LedgerExecShowStatus.HISTORY_EXEC_TRIAL_DONE_EMPTY;
            } else {
                return LedgerExecShowStatus.HISTORY_EXEC_TRIAL_DONE;
            }
        } else {
            if (LedgerExecRunStatusEnum.RUNNING.getCode().equals(historyExec.getRunStatus())) {
                return LedgerExecShowStatus.HISTORY_EXEC_TRIAL_RUNNING;
            } else if (historyExec.getSubExtractNum() == null || historyExec.getSubExtractNum() == 0) {
                return LedgerExecShowStatus.HISTORY_EXEC_TRIAL_DONE_EMPTY;
            } else {
                return LedgerExecShowStatus.HISTORY_EXEC_TRIAL_DONE;
            }
        }
    }
}
