package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.packagewarn.request.PackageWarnIgnoreRequest;
import com.timevale.saasbiz.service.packagewarn.PackageWarnOperateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023-05-17
 */
@Api(tags = "套餐提醒操作")
@ExternalService
@RestMapping(path = "/v1/package/warn")
public class PackageWarnOperateRest {

    @Autowired PackageWarnOperateService packageWarnOperateService;

    @ApiOperation(value = "7天内不提醒", httpMethod = "POST")
    @RestMapping(path = "/short/ignore", method = RequestMethod.POST)
    public RestResult shortIgnore(@RequestBody PackageWarnIgnoreRequest request) {
        packageWarnOperateService.shortIgnoreWarn(
                RequestContextExtUtils.getClientId(),
                request.getAccountId(),
                request.getOrgId(),
                request.getSourceCode());
        return RestResult.success();
    }

    @ApiOperation(value = "不再提醒", httpMethod = "POST")
    @RestMapping(path = "/last/ignore", method = RequestMethod.POST)
    public RestResult lastIgnore(@RequestBody PackageWarnIgnoreRequest request) {
        packageWarnOperateService.lastIgnoreWarn(
                RequestContextExtUtils.getClientId(),
                request.getAccountId(),
                request.getOrgId(),
                request.getSourceCode());
        return RestResult.success();
    }

    @ApiOperation(value = "是否忽略提示", httpMethod = "GET")
    @RestMapping(path = "/ignored", method = RequestMethod.GET)
    public RestResult<Boolean> ignored(
            @RequestParam("orgId") String orgId,
            @RequestParam("accountId") String accountId,
            @RequestParam("sourceCode") String sourceCode) {
        return RestResult.success(
                packageWarnOperateService.isIgnoreWarn(
                        RequestContextExtUtils.getClientId(), accountId, orgId, sourceCode));
    }
}
