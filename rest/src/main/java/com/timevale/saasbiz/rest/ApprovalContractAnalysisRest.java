package com.timevale.saasbiz.rest;

import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalContractAnalysisDetailDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalContractAnalysisFileUrlDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalContractAnalysisPageDTO;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalContractAnalysisCreateRequest;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalContractAnalysisPageRequest;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalContractAnalysisQueryResultRequest;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalContractAnalysisRetryRequest;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisDetailResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisFileUrlResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisPageResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisQueryResultResponse;
import com.timevale.saasbiz.rest.converter.ApprovalContractAnalysisConverter;
import com.timevale.saasbiz.service.approval.ApprovalContractAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

import java.util.List;
import java.util.stream.Collectors;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;

/**
 * TODO 功能说明
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Api(tags = "审批合同比对模块", description = "审批合同比对模块")
@ExternalService
@RestMapping(path = "/v2/approval/contract-analysis")
public class ApprovalContractAnalysisRest {
    @Autowired private ApprovalContractAnalysisService approvalContractAnalysisService;

    @ApiOperation(value = "创建合同比对记录", httpMethod = "POST")
    @RestMapping(path = "/create", method = RequestMethod.POST)
    public BaseResult<String> create(@RequestBody ApprovalContractAnalysisCreateRequest request) {
        return BaseResult.success(
                approvalContractAnalysisService.createApprovalAnalysis(
                        ApprovalContractAnalysisConverter
                                .convert2ApprovalContractAnalysisCreateInputDTO(
                                        request, RequestContextExtUtils.getOperatorId())));
    }

    @ApiOperation(value = "失败重试", httpMethod = "POST")
    @RestMapping(path = "/retry", method = RequestMethod.POST)
    public BaseResult<Void> retry(@RequestBody ApprovalContractAnalysisRetryRequest request) {
        approvalContractAnalysisService.retryCompare(request.getCompareId());
        return BaseResult.success();
    }

    @ApiOperation(value = "查看比对详情", httpMethod = "GET")
    @RestMapping(path = "/detail", method = RequestMethod.GET)
    public BaseResult<ApprovalContractAnalysisDetailResponse> detail(
            @RequestParam(value = "compareId") String compareId) {
        ApprovalContractAnalysisDetailDTO detailDTO =
                approvalContractAnalysisService.detail(
                        RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId(),
                        compareId);
        return detailDTO == null
                ? BaseResult.success()
                : BaseResult.success(
                        ApprovalContractAnalysisConverter
                                .conver2ApprovalContractAnalysisDetailResponse(detailDTO));
    }

    @ApiOperation(value = "查询比对文件url", httpMethod = "GET")
    @RestMapping(path = "/file-url", method = RequestMethod.GET)
    public BaseResult<ApprovalContractAnalysisFileUrlResponse> getCompareDownloadUrl(
            @RequestParam(value = "compareId") String compareId) {
        ApprovalContractAnalysisFileUrlDTO analysisFileUrlDTO =
                approvalContractAnalysisService.getCompareDownloadUrl(
                        RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId(),
                        compareId);
        return analysisFileUrlDTO == null
                ? BaseResult.success()
                : BaseResult.success(
                        ApprovalContractAnalysisConverter
                                .convert2ApprovalContractAnalysisFileUrlDTO(analysisFileUrlDTO));
    }

    @ApiOperation(value = "分页查询合同比对结果", httpMethod = "GET")
    @RestMapping(path = "/list", method = RequestMethod.GET)
    public BaseResult<ApprovalContractAnalysisPageResponse> list(
            @URIQueryParam ApprovalContractAnalysisPageRequest request) {
        ApprovalContractAnalysisPageDTO pageDTO =
                approvalContractAnalysisService.queryContractAnalysisPage(
                        ApprovalContractAnalysisConverter
                                .convert2ApprovalContractAnalysisPageInputDTO(
                                        request, RequestContextExtUtils.getOperatorId()));
        return pageDTO == null
                ? BaseResult.success()
                : BaseResult.success(
                        ApprovalContractAnalysisConverter
                                .convert2ApprovalContractAnalysisPageResponse(pageDTO));
    }

    @ApiOperation(value = "查看文件比对结果", httpMethod = "POST")
    @RestMapping(path = "/result", method = RequestMethod.POST)
    public BaseResult<ApprovalContractAnalysisQueryResultResponse> queryResult(
            @RequestBody ApprovalContractAnalysisQueryResultRequest request) {
        List<ApprovalContractAnalysisDetailDTO> analysisDetailDTOList =
                approvalContractAnalysisService.queryResult(
                        RequestContextExtUtils.getTenantId(),
                        RequestContextExtUtils.getOperatorId(),
                        ApprovalContractAnalysisConverter
                                .convert2ApprovalContractAnalysisQueryResultInputDTO(
                                        RequestContextExtUtils.getOperatorId(), request));
        return CollectionUtils.isEmpty(analysisDetailDTOList)
                ? BaseResult.success()
                : BaseResult.success(
                        new ApprovalContractAnalysisQueryResultResponse(
                                analysisDetailDTOList.stream()
                                        .map(
                                                ApprovalContractAnalysisConverter
                                                        ::convert2ApprovalContractAnalysisRelationVO)
                                        .collect(Collectors.toList())));
    }
}
