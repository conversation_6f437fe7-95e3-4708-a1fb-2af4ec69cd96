package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractSignerConfigVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 下载线下合同录入合同信息的excel模板
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class DownloadOfflineContractExcelRequest extends ToString {

    @NotEmpty(message = "文件名称列表不能为空")
    private List<String> contractFileNames;

    @ApiModelProperty(value = "签署方配置", required = true)
    @NotEmpty(message = "签署方配置不能为空")
    @Size(max = 30, message = "最多支持30个签署方")
    private List<OfflineContractSignerConfigVO> signerConfigs;
}
