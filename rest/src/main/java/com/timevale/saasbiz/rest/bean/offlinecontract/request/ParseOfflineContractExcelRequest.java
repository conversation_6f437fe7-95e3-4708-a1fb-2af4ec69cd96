package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.EnumCheck;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.OfflineContractSignerConfigVO;
import com.timevale.saasbiz.service.excel.enums.ErrorDataHandleWayEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 解析线下合同录入合同信息的excel
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class ParseOfflineContractExcelRequest extends ToString {

    @ApiModelProperty(value = "excel文件fileKey", required = true)
    @NotBlank(message = "excel文件fileKey不能为空")
    private String fileKey;

    /** 错误数据处理方式，默认不处理 */
    @ApiModelProperty(value = "错误数据处理方式，NONE-不处理，IGNORE-直接忽略，ERROR_EXCEL-生成错误数据excel, 默认不处理")
    @EnumCheck(target = ErrorDataHandleWayEnum.class, enumField = "way", message = "不支持的错误数据处理方式")
    private String errorDataHandleWay = ErrorDataHandleWayEnum.NONE.getWay();

    @ApiModelProperty(value = "签署方配置", required = true)
    @NotEmpty(message = "签署方配置不能为空")
    private List<OfflineContractSignerConfigVO> signerConfigs;
}
