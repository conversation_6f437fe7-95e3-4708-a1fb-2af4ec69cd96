package com.timevale.saasbiz.rest.bean.contractcategory.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.SceneContractCategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询系统推荐的合同类型列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("查询系统推荐的合同类型列表响应数据")
public class QuerySysContractCategoriesResponse extends ToString {

    @ApiModelProperty("系统推荐的合同类型列表")
    private List<SceneContractCategoryVO> categories;
}
