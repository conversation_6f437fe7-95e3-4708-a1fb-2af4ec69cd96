package com.timevale.saasbiz.rest.converter.contractreview;

import java.util.List;
import java.util.stream.Collectors;

import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.*;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.request.*;
import org.apache.commons.collections.CollectionUtils;

public class RuleInventoryInputDTOConverter {
    
    public static SaveRuleInventoryInputDTO convertToInputDTO(String accountId, String tenantId, SaveRuleInventoryRequest request) {
        SaveRuleInventoryInputDTO dto = new SaveRuleInventoryInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setInventoryId(request.getInventoryId());
        dto.setInventoryName(request.getInventoryName());
        dto.setContractType(request.getContractType());
        dto.setContractView(request.getContractView());
        dto.setRecordId(request.getRecordId());
        if (CollectionUtils.isNotEmpty(request.getRules())) {
            List<SaveRuleInventoryInputDTO.ReviewRuleBO> ruleBOs = request.getRules().stream()
                .map(vo -> {
                    SaveRuleInventoryInputDTO.ReviewRuleBO bo = new SaveRuleInventoryInputDTO.ReviewRuleBO();
                    bo.setRuleId(vo.getRuleId());
                    return bo;
                })
                .collect(Collectors.toList());
            dto.setRules(ruleBOs);
        }
        return dto;
    }

    public static PageRuleInventoryInputDTO convertToInputDTO(String accountId, String tenantId, PageRuleInventoryRequest request) {
        PageRuleInventoryInputDTO dto = new PageRuleInventoryInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setInventoryName(request.getInventoryName());
        dto.setPageNum(request.getPageNum());
        dto.setPageSize(request.getPageSize());
        return dto;
    }

    public static ListRuleInventoryInputDTO convertToInputDTO(String accountId, String tenantId, ListRuleInventoryRequest request) {
        ListRuleInventoryInputDTO dto = new ListRuleInventoryInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setInventoryName(request.getInventoryName());
        dto.setContractType(request.getContractType());
        dto.setContractView(request.getContractView());
        return dto;
    }

    public static QueryRuleInventoryDetailInputDTO convertToInputDTO(String accountId, String tenantId, QueryRuleInventoryDetailRequest request) {
        QueryRuleInventoryDetailInputDTO dto = new QueryRuleInventoryDetailInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setInventoryId(request.getInventoryId());
        return dto;
    }

    public static DeleteRuleInventoryStatusInputDTO convertToInputDTO(String accountId, String tenantId, DeleteRuleInventoryRequest request) {
        DeleteRuleInventoryStatusInputDTO dto = new DeleteRuleInventoryStatusInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setInventoryId(request.getInventoryId());
        return dto;
    }

    public static UpdateRuleInventoryStatusInputDTO convertToInputDTO(String accountId, String tenantId, UpdateRuleInventoryStatusRequest request) {
        UpdateRuleInventoryStatusInputDTO dto = new UpdateRuleInventoryStatusInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setInventoryId(request.getInventoryId());
        dto.setStatus(request.getStatus());
        return dto;
    }
}
