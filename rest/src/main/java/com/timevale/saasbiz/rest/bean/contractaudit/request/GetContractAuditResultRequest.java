package com.timevale.saasbiz.rest.bean.contractaudit.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-09-01
 */
@Data
public class GetContractAuditResultRequest {
    @ApiModelProperty(value = "记录id")
    private String recordId;
    @ApiModelProperty(value = "合同id")
    private String processId;
    @ApiModelProperty(value = "文件id")
    private String fileId;
    @ApiModelProperty(value = "获取的规则列表")
    private List<String> seqList;
}
