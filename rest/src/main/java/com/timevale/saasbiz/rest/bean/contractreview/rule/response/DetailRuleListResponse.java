package com.timevale.saasbiz.rest.bean.contractreview.rule.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DetailRuleListResponse extends ToString {
    @ApiModelProperty("规则ID")
    private String ruleId;

    @ApiModelProperty("规则组ID")
    private String groupId;

    @ApiModelProperty("规则名称")
    private String ruleName;

    @ApiModelProperty("规则描述")
    private String ruleRemark;

    @ApiModelProperty("规则风险等级")
    private String riskLevel;

    @ApiModelProperty("是否是推荐规则")
    private Integer defaultFlag;

    @ApiModelProperty("规则转换状态")
    private String convertedStatus;

    @ApiModelProperty("是否被该清单选中")
    private Integer isChecked;

    @ApiModelProperty("是否被该清单选中")
    private List<DetailRuleRiskResponse> reviewRuleRisks;

    @ApiModelProperty("规则清单ID")
    private String inventoryId;
}
