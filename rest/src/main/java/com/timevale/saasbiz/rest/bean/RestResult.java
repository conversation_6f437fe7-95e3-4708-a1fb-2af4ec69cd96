package com.timevale.saasbiz.rest.bean;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Rest接口响应体结构
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Data
@NoArgsConstructor
public class RestResult<T> extends ToString {

    private int code;

    private String message;

    private T data;

    private RestResult(T data) {
        this.code = SaasBizResultCode.SUCCESS.getCode();
        this.message = SaasBizResultCode.SUCCESS.getMessage();
        this.data = data;
    }

    private RestResult(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static <T> RestResult<T> success() {
        return new RestResult(null);
    }

    public static <T> RestResult<T> success(T data) {
        return new RestResult(data);
    }

    public static <T> RestResult<T> fail(int code, String message) {
        return new RestResult(code, message);
    }
}
