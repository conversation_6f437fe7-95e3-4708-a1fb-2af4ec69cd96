package com.timevale.saasbiz.rest;

import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import com.timevale.saasbiz.model.bean.cooperation.output.CooperationProgressDTO;
import com.timevale.saasbiz.model.bean.cooperation.input.CooperationBehaviorInputDTO;
import com.timevale.saasbiz.model.bean.cooperation.output.CooperationBehaviorOutputDTO;
import com.timevale.saasbiz.rest.bean.cooperation.response.AsyncCooperationProgressResponse;
import com.timevale.saasbiz.rest.bean.cooperation.request.CooperationBehaviorRequest;
import com.timevale.saasbiz.rest.bean.cooperation.response.CooperationBehaviorResponse;
import com.timevale.saasbiz.service.cooperation.CooperationService;
import com.timevale.saasbiz.service.process.ProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.constraints.NotBlank;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 合同管理 rest
 * @date Date : 2024年03月14日 19:11
 */
@Api(tags = "合同填写流程管理", description = "合同填写流程管理")
@ExternalService
@RestMapping(path = "/v1/cooperation")
@Slf4j
public class CooperationRest {

    @Autowired
    ProcessService processService;

    @Autowired
    CooperationService cooperationService;

    /**
     * 查询用户在填写流程中的行为按钮
     * @param cooperationId
     * @return
     */
    @RestMapping(path = "/{cooperationId}/behavior-buttons", method = RequestMethod.POST)
    @MultilingualTranslateMethod
    //填写页不校验是否企业成员
//    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "查询用户对流程的控制权限")
    public BaseResult<CooperationBehaviorResponse> getCooperationBehaviorButtons(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @ApiParam(value = "流程id", required = true) @PathVariable String cooperationId,
            @RequestBody CooperationBehaviorRequest request
    ) {
        CooperationBehaviorInputDTO behaviorInput = new CooperationBehaviorInputDTO();
        behaviorInput.setProcessId(request.getProcessId());
        behaviorInput.setCooperationId(cooperationId);
        behaviorInput.setTaskId(request.getTaskId());
        behaviorInput.setOperatorId(accountId);
        behaviorInput.setTenantId(tenantId);

        CooperationBehaviorOutputDTO subProcessSupportBehavior = cooperationService.getCooperationSupportBehavior(behaviorInput);
        CooperationBehaviorResponse response = JsonUtils.obj2pojo(subProcessSupportBehavior, CooperationBehaviorResponse.class);
        return BaseResult.success(response);
    }
}
