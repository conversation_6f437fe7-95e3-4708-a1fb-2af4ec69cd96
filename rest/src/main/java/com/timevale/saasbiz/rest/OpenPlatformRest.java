package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.usercenter.dto.AppInfoDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.OpenAppDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.usercenter.GetAppInfoResponse;
import com.timevale.saasbiz.rest.bean.usercenter.SubjectOpenAppResponse;
import com.timevale.saasbiz.rest.bean.usercenter.request.GetAppInfoRequest;
import com.timevale.saasbiz.rest.converter.OpenPlatformConvertor;
import com.timevale.saasbiz.service.openplatform.OpenPlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

@Api(tags = "开放平台信息")
@Validated
@ExternalService
@RestMapping(path = "/v2/open-platform")
public class OpenPlatformRest {

    @Autowired private OpenPlatformService openPlatformService;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("查询企业下的appId")
    @RestMapping(path = "/org-open-app", method = RequestMethod.GET)
    public RestResult<SubjectOpenAppResponse> subjectOpenApp(
            @RequestParam(required = false) String subjectId,
            @RequestHeader(value = HEADER_TENANT_ID) String tenantId) {
        List<OpenAppDTO> openAppDTOList = openPlatformService.appList(tenantId, subjectId);
        SubjectOpenAppResponse response = new SubjectOpenAppResponse();
        response.setList(OpenPlatformConvertor.openAppConvert(openAppDTOList));
        return RestResult.success(response);
    }

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("获取app信息")
    @RestMapping(path = "/get-app-info", method = RequestMethod.POST)
    public RestResult<GetAppInfoResponse> getAppInfo(@RequestBody GetAppInfoRequest request) {
        AppInfoDTO appInfo = 
                openPlatformService.fetchValidatedAppInfo(request.getAppId(), request.getSubjectOid());
        return RestResult.success(OpenPlatformConvertor.appInfoConvert(appInfo));
    }
}
