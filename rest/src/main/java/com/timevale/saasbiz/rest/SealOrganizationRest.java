package com.timevale.saasbiz.rest;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saas.common.manage.common.service.model.base.BaseResult;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saas.common.privilege.aspect.RelationAndPrivilegeCheck;
import com.timevale.saas.multilingual.translate.annotation.MultilingualTranslateMethod;
import com.timevale.saasbiz.model.bean.seal.dto.input.*;
import com.timevale.saasbiz.model.bean.seal.dto.output.*;
import com.timevale.saasbiz.model.constants.AuthRelationBizSceneConstants;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.enums.SealVisibleScopeEnum;
import com.timevale.saasbiz.model.utils.AppConfigUtil;
import com.timevale.saasbiz.model.utils.AssertX;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.seal.SealPartVisibleDeptVO;
import com.timevale.saasbiz.rest.bean.seal.SealPartVisibleMemberVO;
import com.timevale.saasbiz.rest.bean.seal.request.*;
import com.timevale.saasbiz.rest.bean.seal.response.*;
import com.timevale.saasbiz.rest.converter.SealResponseConvertor;
import com.timevale.saasbiz.rest.converter.request.SealInputDTOConverter;
import com.timevale.saasbiz.service.seal.SealOrganizationService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.sealmanager.common.service.enums.SealBizType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.constraints.NotBlank;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizSceneEnum.SEAL_MANAGE;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;
import static com.timevale.saasbiz.model.enums.AppConfigEnum.SAAS_PROJECT_ID;
import static com.timevale.saasbiz.model.enums.AppConfigEnum.SAAS_PROJECT_NAME;

/**
 * <AUTHOR>
 * @since 2023-05-26 13:39
 */
@Api(tags = "企业/法人章印章相关操作")
@ExternalService
@RestMapping(path = "/v1/saas-common/organizations/seals")
public class SealOrganizationRest {
    @Autowired private SealOrganizationService sealOrganizationService;
    @Autowired private UserCenterService userCenterService;

    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation(value = "分页查询企业印章列表", httpMethod = "POST")
    @RestMapping(path = "/simple-list", method = RequestMethod.POST)
    @MultilingualTranslateMethod
    public RestResult<PageSimpleSealListResponse> pageQuerySeals(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String operatorOid,
            @RequestBody PageSimpleSealListRequest request) {
        // 独立企业查询(分为本企业查询和总部查询单企业)
        if (StringUtils.isNotBlank(request.getSealOwnerOid())
                && !StringUtils.equals(tenantId, request.getSealOwnerOid())) {
            // 如果印章归属与操作主体不一致,则认为是总部操作,需要鉴权
            sealOrganizationService.checkGroupHeadquarterResourceManagement(
                    request.getSealOwnerOid(), tenantId, Lists.newArrayList(SEAL_MANAGE.getCode()));
        }
        String sealOwnerOid = StringUtils.defaultIfBlank(request.getSealOwnerOid(), tenantId);
        PageQuerySimpleSealListInputDTO inputDTO = new PageQuerySimpleSealListInputDTO();
        inputDTO.setPageNum(request.getPageNo());
        inputDTO.setPageSize(request.getPageSize());
        inputDTO.setDownloadFlag(request.isDownloadFlag());
        if (CollectionUtils.isNotEmpty(request.getSealBizTypes())) {
            AssertX.isTrue(request.getSealBizTypes().stream().allMatch(i -> null != SealBizType.getByValue(i)), "不支持的印章业务类型");
            inputDTO.setSealBizTypes(request.getSealBizTypes());
        }
        inputDTO.setSealOwnerOids(Lists.newArrayList(sealOwnerOid));
        inputDTO.setSubjectId(tenantId);
        inputDTO.setAccountId(operatorOid);
        inputDTO.setGrantedSeal(request.isGrantedSeal());
        //排掉检索中的*, 写在这里时为了提醒维护人员,不要忽略这个问题
        inputDTO.setSealName(StringUtils.replaceAll(request.getSealName(), "\\*", ""));
        PageQuerySimpleSealListOutputDTO pageQuerySeals = sealOrganizationService.pageQuerySimpleSeals(inputDTO);
        return RestResult.success(SealResponseConvertor.convertPageSimpleSealListResponse(pageQuerySeals));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.QUERY},authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE)
    @ApiOperation(value = "用印统计查询", httpMethod = "GET")
    @RestMapping(path = "/usage-list", method = RequestMethod.POST)
    public RestResult<UsageListResponse> listUsage(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody UsageListRequest request) {
        // 检查查询时间跨度，最多查三个月
        request.checkDuration();
        // 查询
        PageUsageSealInputDTO inputDTO = new PageUsageSealInputDTO();
        BeanUtils.copyProperties(request, inputDTO);
        inputDTO.setOrgOid(
                StringUtils.isBlank(request.getSealOwnerOid())
                        ? tenantId
                        : request.getSealOwnerOid());
        inputDTO.setAppId(getAppId(request.getAppName()));
        PageUsageSealOutputDTO outputDTO = sealOrganizationService.pageUsage(inputDTO);
        if (Objects.isNull(outputDTO) || CollectionUtils.isEmpty(outputDTO.getList())) {
            return RestResult.success(new UsageListResponse(0L, Collections.emptyList()));
        }

        // 结果转换
        List<UsageListResponse.SealUsageRecordVO> list =
                outputDTO.getList().stream()
                        .filter(Objects::nonNull)
                        .map(SealResponseConvertor::buildSealUsageRecordVO)
                        .collect(Collectors.toList());
        return RestResult.success(new UsageListResponse(outputDTO.getTotal(), list));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.UPDATE},authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE)
    @ApiOperation(value = "设置企业默认章", httpMethod = "PUT")
    @RestMapping(path = "/set-default-seal", method = RequestMethod.PUT)
    public BaseResult<Boolean> setDefaultSeal(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody SetOrgDefaultSealRequest request) {
        // 设置
        SetDefaultSealInputDTO inputDTO = new SetDefaultSealInputDTO();
        inputDTO.setAppId(appId);
        inputDTO.setOrgOid(
                StringUtils.isBlank(request.getSealOwnerOid())
                        ? tenantId
                        : request.getSealOwnerOid());
        inputDTO.setOperatorOid(accountId);
        inputDTO.setSealId(request.getSealId());
        sealOrganizationService.setDefaultSeal(inputDTO);

        return BaseResult.success(true);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.QUERY},authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE)
    @ApiOperation(value = "查询印章详情", httpMethod = "POST")
    @RestMapping(path = "/seal-detail", method = RequestMethod.POST)
    public RestResult<SealDetailResponse> getSealDetail(
            @RequestHeader(HEADER_APP_ID) @NotBlank String appId,
            @RequestHeader(value = HEADER_CLIENT_ID, required = false) String clientId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody SealDetailRequest request) {
        // 查询
        SealDetailInputDTO dto = new SealDetailInputDTO();
        BeanUtils.copyProperties(request, dto);
        dto.setOrgOid(
                StringUtils.isBlank(request.getSealOwnerOid())
                        ? tenantId
                        : request.getSealOwnerOid());
        dto.setOperatorOid(accountId);
        dto.setTerminal(clientId);
        dto.setAppId(appId);
        SealDetailOutputDTO outputDTO = sealOrganizationService.getOrgSealDetail(dto);
        if (Objects.isNull(outputDTO)) {
            return RestResult.success(null);
        }

        SealDetailResponse response = new SealDetailResponse();
        BeanUtils.copyProperties(outputDTO, response);
        return RestResult.success(response);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.QUERY})
    @ApiOperation(value = "查询印章部分可见范围详情", httpMethod = "GET")
    @RestMapping(path = "/part-visible-detail", method = RequestMethod.GET)
    public RestResult<SealPartVisibleDetailResponse> getSealVisibleScopeDetail(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @URIQueryParam SealPartVisibleDetailRequest request) {

        // 查询印章部分可见范围详情
        SealPartVisibleDetailInputDTO inputDTO =
                SealInputDTOConverter.convertSealPartVisibleDetailInputDTO(
                        accountId,
                        StringUtils.isBlank(request.getSealOwnerOid())
                                ? tenantId
                                : request.getSealOwnerOid(),
                        request);
        SealPartVisibleDetailOutputDTO outputDTO =
                sealOrganizationService.getSealPartVisibleDetail(inputDTO);

        // 判空
        if (Objects.isNull(outputDTO)
                || (outputDTO.getVisibleDept() == null && outputDTO.getVisibleMember() == null)) {
            return RestResult.success(
                    new SealPartVisibleDetailResponse(
                            0,
                            new SealPartVisibleDeptVO(Collections.emptyList()),
                            new SealPartVisibleMemberVO(Collections.emptyList())));
        }

        // 结果转换
        SealPartVisibleDetailResponse response =
                SealResponseConvertor.convertPartVisibleDetailResponse(outputDTO);
        return RestResult.success(response);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {
                PrivilegeOperationConstants.CREATE,
                PrivilegeOperationConstants.UPDATE
            },authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.SEAL_VISIBLE_SCOPE)
    @ApiOperation(value = "设置印章可见范围", httpMethod = "PUT")
    @RestMapping(path = "/visible-scope", method = RequestMethod.PUT)
    public BaseResult<Boolean> setSealVisibleScope(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody SetSealVisibleScopeRequest request) {
        String currentOid =
                StringUtils.isBlank(request.getSealOwnerOid())
                        ? tenantId
                        : request.getSealOwnerOid();
        // 判断可见范围的类型，如果是部分成员可见，则需要额外的判断逻辑
        if (SealVisibleScopeEnum.PART_MEMBER.getCode().equals(request.getVisibleScope())) {
            // 检查指定的成员和部门集合是否合法
            userCenterService.checkAssignedLegality(
                    accountId,
                    currentOid,
                    request.getAssignedDeptIdList(),
                    request.getAssignedMemberIdList());
        }

        // 设置可见范围
        SetVisibleScopeInputDTO inputDTO =
                SealInputDTOConverter.convertSetVisibleScopeInputDTO(
                        accountId, currentOid, request);
        sealOrganizationService.setVisibleScope(inputDTO);
        return BaseResult.success(true);
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {PrivilegeOperationConstants.QUERY},authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE)
    @ApiOperation(value = "查询印章可见范围", httpMethod = "GET")
    @RestMapping(path = "/visible-scope", method = RequestMethod.GET)
    public RestResult<GetSealVisibleScopeResponse> getSealVisibleScope(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @URIQueryParam GetSealVisibleScopeRequest request) {

        // 查询印章可见范围
        GetSealVisibleScopeInputDTO inputDTO =
                SealInputDTOConverter.convertGetSealVisibleScopeInputDTO(
                        accountId,
                        StringUtils.isBlank(request.getSealOwnerOid())
                                ? tenantId
                                : request.getSealOwnerOid(),
                        request);
        GetSealVisibleScopeOutputDTO outputDTO = sealOrganizationService.getVisibleScope(inputDTO);
        return Objects.isNull(outputDTO)
                ? RestResult.success(null)
                : RestResult.success(
                        SealResponseConvertor.convertGetSealVisibleScopeResponse(outputDTO));
    }

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {
                PrivilegeOperationConstants.CREATE,
                PrivilegeOperationConstants.UPDATE
            },authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE)
    @ApiOperation(value = "删除印章可见范围", httpMethod = "POST")
    @RestMapping(path = "/delete-visible-scope", method = RequestMethod.POST)
    public BaseResult<Boolean> deleteSealVisibleScope(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody DeleteSealVisibleScopeRequest request) {

        // 删除可见范围
        DeleteVisibleScopeInputDTO inputDTO =
                SealInputDTOConverter.convertDeleteVisibleScopeInputDTO(
                        accountId,
                        StringUtils.isBlank(request.getSealOwnerOid())
                                ? tenantId
                                : request.getSealOwnerOid(),
                        request);
        sealOrganizationService.deleteVisibleScope(inputDTO);
        return BaseResult.success(true);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = "#request.sealOwnerOid",
            orgResourceKey = PrivilegeResourceConstants.SEAL,
            orgPrivilegeKey = {
                    PrivilegeOperationConstants.CREATE,
                    PrivilegeOperationConstants.UPDATE
            }, authBizScene = AuthRelationBizSceneConstants.SEAL_MANAGE)
    @ApiOperation(value = "新增企业印章预检查", httpMethod = "POST")
    @RestMapping(path = "/create-seal-pre-check", method = RequestMethod.POST)
    public RestResult<CreateOfficialSealPreCheckResponse> createSealPreCheck(@RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
                                                                             @RequestBody CreateOfficialSealPreCheckRequest request) {

        CreateOfficialSealPreCheckInputDTO inputDTO = SealInputDTOConverter.convertCreateOfficialSealPreCheckInputDTO(tenantId, request);
        CreateOfficialSealPreCheckOutputDTO outputDTO = sealOrganizationService.createOfficialSealPreCheck(inputDTO);
        return RestResult.success(SealResponseConvertor.convertCreateOfficialSealPreCheckResponse(outputDTO));
    }

    private String getAppId(String appName) {
        if (StringUtils.isBlank(appName)) {
            return null;
        }

        if (StringUtils.equals(appName, AppConfigUtil.getString(SAAS_PROJECT_NAME))) {
            return AppConfigUtil.getString(SAAS_PROJECT_ID);
        }

        return appName;
    }
}
