package com.timevale.saasbiz.rest;

import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasPrivilegeInputDTO;
import com.timevale.saasbiz.model.enums.PrivilegeOperationEnum;
import com.timevale.saasbiz.model.enums.PrivilegeResourceEnum;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 基础Rest
 * @date Date : 2023年05月11日 14:34
 */
@Component
public abstract class BaseRest {

    @Autowired
    UserCenterService userCenterService;
    /**
     * 校验是否有指定权限
     * @param tenantId
     * @param operatorId
     */
    protected boolean checkUserPrivilege(String tenantId, String operatorId, PrivilegeResourceEnum resourceEnum, PrivilegeOperationEnum operationEnum, boolean throwExp) {
        return userCenterService.checkHasPrivilege(
                CheckHasPrivilegeInputDTO.builder()
                        .tenantId(tenantId)
                        .accountId(operatorId)
                        .resourceKey(resourceEnum.getType())
                        .privilegeKey(operationEnum.getType())
                        .build(),
                throwExp);
    }
}
