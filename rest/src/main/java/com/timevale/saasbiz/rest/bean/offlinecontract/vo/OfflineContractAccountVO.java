package com.timevale.saasbiz.rest.bean.offlinecontract.vo;

import com.timevale.saas.common.validator.StringCheck;
import com.timevale.saas.common.validator.StringRegex;
import com.timevale.saas.common.validator.enums.StringCheckType;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OfflineContractAccountVO {
    /** 用户账号 */
    @StringRegex(regex = "1[3456789][0-9]{9}|.*@.*", message = "用户账号:'%s'不符合手机号或邮箱格式")
    private String account;
    /** 用户姓名 */
    @StringCheck(types = {StringCheckType.EMOJI}, message = "用户姓名:'%s'包含表情字符")
    private String accountName;
    /** 主体名称 */
    @StringCheck(types = {StringCheckType.EMOJI}, message = "主体名称:'%s'包含表情字符")
    private String subjectName;
    /** 主体类型 */
    @NotBlank(message = "主体类型不能为空")
    private Integer subjectType;
}
