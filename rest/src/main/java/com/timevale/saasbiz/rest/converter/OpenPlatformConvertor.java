package com.timevale.saasbiz.rest.converter;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.usercenter.dto.AppInfoDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.OpenAppDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.SimpleAppInfoDTO;
import com.timevale.saasbiz.rest.bean.usercenter.GetAppInfoResponse;
import com.timevale.saasbiz.rest.bean.usercenter.vo.OpenAppVO;
import com.timevale.saasbiz.rest.bean.usercenter.vo.SimpleAppInfoVo;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 开放平台信息rest层和service层出入参转换器
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
public class OpenPlatformConvertor {

    public static OpenAppVO openAppConvert(OpenAppDTO openAppDTO) {
        if (null == openAppDTO) {
            return null;
        }
        OpenAppVO openAppVO = new OpenAppVO();
        openAppVO.setAppId(openAppDTO.getAppId());
        openAppVO.setAppName(openAppDTO.getAppName());
        openAppVO.setSubjectOid(openAppDTO.getSubjectOid());
        openAppVO.setSubjectGid(openAppDTO.getSubjectGid());
        openAppVO.setCreateTime(openAppDTO.getCreateTime());
        return openAppVO;
    }

    public static List<OpenAppVO> openAppConvert(List<OpenAppDTO> openAppDTOList) {
        if (CollectionUtils.isEmpty(openAppDTOList)) {
            return new ArrayList<>();
        }
        return openAppDTOList.stream().map(OpenPlatformConvertor::openAppConvert).collect(Collectors.toList());
    }

    public static GetAppInfoResponse appInfoConvert(AppInfoDTO appInfo) {
        GetAppInfoResponse appInfoResponse = new GetAppInfoResponse();
        appInfoResponse.setExist(appInfo.getExist());
        
        if (appInfo.getAppInfo() != null) {
            SimpleAppInfoDTO appInfoDTO = appInfo.getAppInfo();
            SimpleAppInfoVo simpleAppInfoVo = new SimpleAppInfoVo();
            simpleAppInfoVo.setAppName(appInfoDTO.getAppName());
            appInfoResponse.setAppInfo(simpleAppInfoVo);
        }
        
        return appInfoResponse;
    }
}
