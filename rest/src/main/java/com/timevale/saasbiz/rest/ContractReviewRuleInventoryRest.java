package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.common.dto.input.BaseInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.ContractTypePullDownBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.RuleInventoryDetailBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.RuleInventoryListBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.*;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.output.PageRuleInventoryOutputDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.request.*;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.response.ContractTypePullDownResponse;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.response.ListRuleInventoryResponse;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.response.PageRuleInventoryResponse;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.response.SaveRuleInventoryResponse;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.vo.RuleInventoryDetailVO;
import com.timevale.saasbiz.rest.converter.contractreview.RuleInventoryInputDTOConverter;
import com.timevale.saasbiz.rest.converter.contractreview.RuleInventoryOutputDTOConverter;
import com.timevale.saasbiz.service.contractreview.ContractReviewRuleInventoryService;
import com.timevale.sterna.contract.review.client.enums.ReviewSourceEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import javax.validation.constraints.NotBlank;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.timevale.saasbiz.model.constants.PrivilegeOperationConstants.*;
import static com.timevale.saasbiz.model.constants.PrivilegeResourceConstants.PRIVILEGE_RESOURCE_INTELLIGENT_TOOL;
import static com.timevale.saasbiz.model.constants.PrivilegeResourceConstants.RESOURCE_CONTRACT_REVIEW;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

@Api(tags = "合同审查清单")
@Validated
@ExternalService
@RestMapping(path = "/v1/contract-review/rule-inventory")
public class ContractReviewRuleInventoryRest {

    @Resource
    public ContractReviewRuleInventoryService contractReviewRuleInventoryService;

    @ApiOperation(value = "规则清单-保存", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = ADD_RULE)
    @RestMapping(path = "/save", method = RequestMethod.POST)
    public RestResult<SaveRuleInventoryResponse> ruleInventorySave(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid SaveRuleInventoryRequest request) {
        SaveRuleInventoryInputDTO inputDTO = RuleInventoryInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        inputDTO.setSource(ReviewSourceEnum.TOOLS.getCode());
        String inventoryId = contractReviewRuleInventoryService.saveRuleInventory(inputDTO);
        SaveRuleInventoryResponse response = new SaveRuleInventoryResponse();
        response.setInventoryId(inventoryId);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则清单-列表", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = {ADD_RULE, DELETE_RULE, USE})
    @RestMapping(path = "/list", method = RequestMethod.POST)
    public RestResult<ListRuleInventoryResponse> ruleInventoryList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid ListRuleInventoryRequest request) {
        ListRuleInventoryInputDTO inputDTO = RuleInventoryInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        List<RuleInventoryListBO> ruleInventoryListBOS = contractReviewRuleInventoryService.listRuleInventory(inputDTO);
        ListRuleInventoryResponse response = new ListRuleInventoryResponse();
        response.setInventoryList(RuleInventoryOutputDTOConverter.convertToListVOS(ruleInventoryListBOS));
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则清单-分页", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = {ADD_RULE, DELETE_RULE, USE})
    @RestMapping(path = "/page", method = RequestMethod.POST)
    public RestResult<PageRuleInventoryResponse> ruleInventoryPage(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid PageRuleInventoryRequest request) {
        PageRuleInventoryInputDTO inputDTO = RuleInventoryInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        PageRuleInventoryOutputDTO outputDTO = contractReviewRuleInventoryService.pageRuleInventory(inputDTO);

        PageRuleInventoryResponse response = new PageRuleInventoryResponse();
        response.setTotal(outputDTO.getTotal());
        response.setInventoryList(RuleInventoryOutputDTOConverter.convertToListVOS(outputDTO.getInventoryList()));
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则清单-详情", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = {ADD_RULE, DELETE_RULE, USE})
    @RestMapping(path = "/detail", method = RequestMethod.POST)
    public RestResult<RuleInventoryDetailVO> ruleInventoryDetail(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid QueryRuleInventoryDetailRequest request) {
        QueryRuleInventoryDetailInputDTO inputDTO = RuleInventoryInputDTOConverter.convertToInputDTO(accountId, tenantId, request);

        RuleInventoryDetailBO ruleInventoryDetailBO = contractReviewRuleInventoryService.ruleInventoryDetail(inputDTO);
        return RestResult.success(RuleInventoryOutputDTOConverter.convertToDetailVO(ruleInventoryDetailBO));
    }

    @ApiOperation(value = "规则清单-删除", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = DELETE_RULE)
    @RestMapping(path = "/del", method = RequestMethod.POST)
    public RestResult ruleInventoryDel(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid DeleteRuleInventoryRequest request) {
        DeleteRuleInventoryStatusInputDTO inputDTO = RuleInventoryInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        contractReviewRuleInventoryService.deleteRuleInventory(inputDTO);
        return RestResult.success();
    }

    @ApiOperation(value = "规则类型下拉", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = USE)
    @RestMapping(path = "/contract-type-pullDown", method = RequestMethod.GET)
    public RestResult<ContractTypePullDownResponse> contractTypePullDown(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId) {
        BaseInputDTO baseInputDTO = new BaseInputDTO();
        baseInputDTO.setAccountId(accountId);
        baseInputDTO.setTenantId(tenantId);
        ContractTypePullDownBO contractTypePullDownBO = contractReviewRuleInventoryService.contractTypePullDown(baseInputDTO);
        return RestResult.success(RuleInventoryOutputDTOConverter.convertToContractTypePullDownBO(contractTypePullDownBO));
    }

//    @ApiOperation(value = "规则清单-状态启停", notes = "无")
//    @UserPrivilegeCheck(
//            resourceKey = RESOURCE_CONTRACT_REVIEW,
//            privilegeKey = {})
//    @RestMapping(path = "/status", method = RequestMethod.POST)
//    public RestResult ruleInventoryStatus(
//            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
//            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
//            @RequestBody @Valid UpdateRuleInventoryStatusRequest request) {
//        UpdateRuleInventoryStatusInputDTO inputDTO = RuleInventoryInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
//        contractReviewRuleInventoryService.updateRuleInventoryStatus(inputDTO);
//        return RestResult.success();
//    }
}
