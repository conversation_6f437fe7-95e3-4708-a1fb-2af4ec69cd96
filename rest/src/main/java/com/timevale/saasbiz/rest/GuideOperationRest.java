package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saasbiz.model.bean.operation.QueryWecomGuideAccountDTO;
import com.timevale.saasbiz.operation.GuideOperationService;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.operation.request.AddWecomLabelRequest;
import com.timevale.saasbiz.rest.bean.operation.request.QueryWecomContactWayRequest;
import com.timevale.saasbiz.rest.bean.operation.response.QueryWecomContactWayResponse;
import com.timevale.saasbiz.rest.bean.operation.response.QueryWecomGuideAccountResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR>
 *
 * @date 2023/4/4
 */
@Api(tags = "引流运营类接口")
@Validated
@ExternalService
@RestMapping(path = "/v1/operational")
public class GuideOperationRest {

    @Autowired GuideOperationService guideOperationService;

    @ApiOperation("查询引导企微账号")
    @RestMapping(path = "/guide-wecom", method = RequestMethod.GET)
    public RestResult<QueryWecomGuideAccountResponse> guideWeComAccount(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @ApiParam(value = "是否判断CRM 默认判断") @RequestParam(required = false) Boolean hasCRM) {
        QueryWecomGuideAccountDTO accountDTO =
                guideOperationService.queryWecomGuideAccount(
                        tenantId, accountId, hasCRM == null ? true : hasCRM);
        return RestResult.success(
                accountDTO == null
                        ? new QueryWecomGuideAccountResponse()
                        : new QueryWecomGuideAccountResponse(
                                accountDTO.getUserId(), accountDTO.getPayStatus()));
    }

    @ApiOperation("生成企微`联系我`二维码")
    @RestMapping(path = "/guide-wecom-qrcode", method = RequestMethod.POST)
    public RestResult<QueryWecomContactWayResponse> guideWeComQrCode(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody QueryWecomContactWayRequest req) {
        String url =
                guideOperationService.queryWecomContactWay(
                        accountId, req.getSource(), req.getUserId());
        return RestResult.success(new QueryWecomContactWayResponse(url));
    }

    @ApiOperation("微信打标")
    @RestMapping(path = "/wecom-label", method = RequestMethod.POST)
    public RestResult<Void> addWeComLabel(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody AddWecomLabelRequest req) {
        guideOperationService.addWeComLabel(accountId, req.getType(), req.getLabelName());
        return RestResult.success();
    }
}
