package com.timevale.saasbiz.rest.bean.offlinecontract.vo;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 线下合同信息
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class OfflineContractVO extends ToString {

    /** 线下合同文件列表 */
    @NotEmpty(message = "线下合同文件列表不能为空")
    private List<OfflineContractFileVO> contractFiles;

    /** 线下合同合同信息 */
    private OfflineContractProcessInfoVO processInfo;
}
