package com.timevale.saasbiz.rest.converter;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saasbiz.model.bean.seal.dto.SealSimpleDTO;
import com.timevale.saasbiz.model.bean.seal.dto.UsageSealRecordDTO;
import com.timevale.saasbiz.model.bean.seal.dto.input.*;
import com.timevale.saasbiz.model.bean.seal.dto.output.*;
import com.timevale.saasbiz.rest.bean.seal.*;
import com.timevale.saasbiz.rest.bean.seal.request.*;
import com.timevale.saasbiz.rest.bean.seal.response.*;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-05-26 16:17
 */
public class SealResponseConvertor {

    public static UsageListResponse.SealUsageRecordVO buildSealUsageRecordVO(
            UsageSealRecordDTO dto) {
        UsageListResponse.SealUsageRecordVO vo = new UsageListResponse.SealUsageRecordVO();
        BeanUtils.copyProperties(dto, vo);

        return vo;
    }

    public static GetSealVisibleScopeResponse convertGetSealVisibleScopeResponse(GetSealVisibleScopeOutputDTO dto) {
        GetSealVisibleScopeResponse response = new GetSealVisibleScopeResponse();
        response.setVisibleScope(dto.getVisibleScope());
        response.setDeptCount(dto.getDeptCount());
        response.setMemberCount(dto.getMemberCount());
        return response;
    }

    public static CreateOfficialSealPreCheckResponse convertCreateOfficialSealPreCheckResponse(CreateOfficialSealPreCheckOutputDTO dto) {
        CreateOfficialSealPreCheckResponse response = new CreateOfficialSealPreCheckResponse();
        if (Objects.nonNull(dto)) {
            response.setNameExist(dto.isNameExist());
        }
        return response;
    }

    public static SealPartVisibleDetailResponse convertPartVisibleDetailResponse(SealPartVisibleDetailOutputDTO dto) {
        List<SealDeptVisibleScopeVO> partDeptList = Optional.ofNullable(dto.getVisibleDept().getPartDeptList())
                .orElse(Collections.emptyList())
                .stream()
                .map(dept -> {
                    SealDeptVisibleScopeVO sealDeptVisibleScopeVO = new SealDeptVisibleScopeVO();
                    sealDeptVisibleScopeVO.setDeptId(dept.getDeptId());
                    sealDeptVisibleScopeVO.setDeptName(dept.getDeptName());
                    return sealDeptVisibleScopeVO;
                })
                .collect(Collectors.toList());

        List<SealMemberVisibleScopeVO> partMemberList = Optional.ofNullable(dto.getVisibleMember().getPartMemberList())
                .orElse(Collections.emptyList())
                .stream()
                .map(member -> {
                    SealMemberVisibleScopeVO sealMemberVisibleScopeVO = new SealMemberVisibleScopeVO();
                    sealMemberVisibleScopeVO.setMemberId(member.getMemberId());
                    sealMemberVisibleScopeVO.setMemberGid(member.getMemberGid());
                    sealMemberVisibleScopeVO.setMemberName(member.getMemberName());
                    return sealMemberVisibleScopeVO;
                })
                .collect(Collectors.toList());

        SealPartVisibleDetailResponse response = new SealPartVisibleDetailResponse();
        response.setTotal(dto.getTotal());
        response.setVisibleDept(new SealPartVisibleDeptVO(partDeptList));
        response.setVisibleMember(new SealPartVisibleMemberVO(partMemberList));
        return response;
    }

    /**
     * 将BatchSealGrantNumOutputDto 转为对应的response
     *
     * @param outputDto
     * @return
     */
    public static BatchSealGrantNumResponse convertBatchSealGrantNumResponse(BatchSealGrantNumOutputDto outputDto) {
        BatchSealGrantNumResponse batchSealGrantNumResponse = new BatchSealGrantNumResponse();
        batchSealGrantNumResponse.setVipLimit(outputDto.getVipLimit());
        batchSealGrantNumResponse.setLimitSealId(outputDto.getLimitSealId());
        batchSealGrantNumResponse.setLimitSealName(outputDto.getLimitSealName());
        //剩余量为  vip最大限制 - 已使用数， 最小为0
        Integer residueGrantNum = (Objects.isNull(outputDto.getVipLimit()) || Objects.isNull(outputDto.getUsedNum()))
                ? 0
                : Math.max(outputDto.getVipLimit() - outputDto.getUsedNum(), 0);
        batchSealGrantNumResponse.setResidueGrantNum(residueGrantNum);
        if (CollectionUtils.isNotEmpty(outputDto.getInvalidGrantSeals())) {
            List<BatchSealGrantNumResponse.InvalidGrantSeal> invalidGrantSealList = outputDto.getInvalidGrantSeals().stream().map(invalidGrantSeal -> JsonUtils.obj2pojo(invalidGrantSeal, BatchSealGrantNumResponse.InvalidGrantSeal.class)).collect(Collectors.toList());
            batchSealGrantNumResponse.setInvalidGrantSeals(invalidGrantSealList);
        }
        batchSealGrantNumResponse.setNearestExpireTime(outputDto.getNearestExpireTime());
        return batchSealGrantNumResponse;
    }

    public static BatchSealGrantNumInputDto convertBatchSealGrantNumInput(BatchSealGrantNumRequest request) {
        BatchSealGrantNumInputDto batchSealGrantNumInputDto = new BatchSealGrantNumInputDto();
        batchSealGrantNumInputDto.setGrantLevel(request.getGrantLevel());
        batchSealGrantNumInputDto.setResourceOwnerOid(request.getResourceOwnerOid());
        batchSealGrantNumInputDto.setResourceBizIds(request.getResourceBizIds());
        return batchSealGrantNumInputDto;


    }

    public static BatchSealGrantInputDto convertBatchSealGrantInput(BatchSealGrantRequest request) {
        BatchSealGrantInputDto batchSealGrantInputDto = new BatchSealGrantInputDto();
        batchSealGrantInputDto.setGrantLevel(request.getGrantLevel());
        batchSealGrantInputDto.setResourceOwnerOid(request.getResourceOwnerOid());
        batchSealGrantInputDto.setResourceOwnerAdminOid(request.getResourceOwnerAdminOid());
        batchSealGrantInputDto.setGrantedResourceIds(request.getGrantedResourceIds());
        batchSealGrantInputDto.setRoleKey(request.getRoleKey());
        batchSealGrantInputDto.setFallType(request.getFallType());
        batchSealGrantInputDto.setScopeList(request.getScopeList());
        batchSealGrantInputDto.setScopeAppIds(request.getScopeAppIds());
        batchSealGrantInputDto.setScopeApprovalTemplateIds(request.getScopeApprovalTemplateIds());
        batchSealGrantInputDto.setNotifySetting(request.getNotifySetting());
        batchSealGrantInputDto.setGrantType(request.getGrantType());
        batchSealGrantInputDto.setGrantedAccountIds(request.getGrantedAccountIds());
        batchSealGrantInputDto.setGrantedRoleIds(request.getGrantedRoleIds());
        batchSealGrantInputDto.setGrantedDepartmentIds(request.getGrantedDepartmentIds());
        batchSealGrantInputDto.setEffectiveTime(request.getEffectiveTime());
        batchSealGrantInputDto.setExpireTime(request.getExpireTime());
        batchSealGrantInputDto.setGrantRedirectUrl(request.getGrantRedirectUrl());
        batchSealGrantInputDto.setNotifyUrl(request.getNotifyUrl());
        batchSealGrantInputDto.setAppId(request.getAppId());
        batchSealGrantInputDto.setH5(request.isH5());
        batchSealGrantInputDto.setClientType(request.getClientType());
        batchSealGrantInputDto.setAuthConfirmMethod(request.getAuthConfirmMethod());
        batchSealGrantInputDto.setOnlyApprovalNotUse(request.isOnlyApprovalNotUse());
        return batchSealGrantInputDto;
    }

    public static GroupBatchSealGrantInputDto convertGroupBatchSealGrantInput(GroupBatchSealGrantRequest request) {
        GroupBatchSealGrantInputDto batchSealGrantInputDto = new GroupBatchSealGrantInputDto();
        batchSealGrantInputDto.setGrantedResourceIds(request.getGrantedResourceIds());
        batchSealGrantInputDto.setRoleKey(request.getRoleKey());
        batchSealGrantInputDto.setFallType(request.getFallType());
        batchSealGrantInputDto.setScopeList(request.getScopeList());
        batchSealGrantInputDto.setScopeApprovalTemplateIds(request.getScopeApprovalTemplateIds());
        batchSealGrantInputDto.setScopeAppIds(request.getScopeAppIds());
        batchSealGrantInputDto.setNotifySetting(request.getNotifySetting());
        batchSealGrantInputDto.setGrantType(request.getGrantType());
        batchSealGrantInputDto.setGrantedAccountIds(request.getGrantedAccountIds());
        batchSealGrantInputDto.setGrantedRoleIds(request.getGrantedRoleIds());
        batchSealGrantInputDto.setGrantedDepartmentIds(request.getGrantedDepartmentIds());
        batchSealGrantInputDto.setEffectiveTime(request.getEffectiveTime());
        batchSealGrantInputDto.setExpireTime(request.getExpireTime());
        batchSealGrantInputDto.setGrantRedirectUrl(request.getGrantRedirectUrl());
        batchSealGrantInputDto.setNotifyUrl(request.getNotifyUrl());
        batchSealGrantInputDto.setH5(request.isH5());
        batchSealGrantInputDto.setClientType(request.getClientType());
        batchSealGrantInputDto.setAuthConfirmMethod(request.getAuthConfirmMethod());
        return batchSealGrantInputDto;
    }

    public static DownloadOfflineLegalAuthLetterInputDTO convertDownloadLegalSealGrantInput(DownloadOfflineLegalAuthLetterRequest request) {
        DownloadOfflineLegalAuthLetterInputDTO inputDTO = new DownloadOfflineLegalAuthLetterInputDTO();
        inputDTO.setLegalName(request.getLegalName());
        inputDTO.setLegalNo(request.getLegalNo());
        inputDTO.setLegalNoType(request.getLegalNoType());
        return inputDTO;
    }

    public static BatchSealGrantResponse convertBatchSealGrantResponse(BatchSealGrantOutputDto batchSealGrantOutputDto) {
        BatchSealGrantResponse batchSealGrantResponse = new BatchSealGrantResponse();
        batchSealGrantResponse.setNeedRedirect(batchSealGrantOutputDto.isNeedRedirect());
        batchSealGrantResponse.setRedirectUrl(batchSealGrantOutputDto.getRedirectUrl());
        batchSealGrantResponse.setAuthConfirmMethod(batchSealGrantResponse.getAuthConfirmMethod());
        batchSealGrantResponse.setGroupId(batchSealGrantResponse.getGroupId());
        return batchSealGrantResponse;
    }

    public static DownloadOfflineLegalAuthLetterResponse convertDownloadOfflineLegalAuthLetterResponse(DownloadOfflineLegalAuthLetterOutPutDTO outputDTO) {
        DownloadOfflineLegalAuthLetterResponse response = new DownloadOfflineLegalAuthLetterResponse();
        response.setDownloadUrl(outputDTO.getDownloadUrl());
        return response;
    }

    public static PageQuerySealsInputDto convertPageSealsInput(PageQuerySealsRequest request) {
        PageQuerySealsInputDto pageQuerySealsInputDto = new PageQuerySealsInputDto();
        pageQuerySealsInputDto.setPageNo(request.getPageNo());
        pageQuerySealsInputDto.setPageSize(request.getPageSize());
        pageQuerySealsInputDto.setDownloadFlag(request.getDownloadFlag());
        pageQuerySealsInputDto.setStatusFlag(request.getStatusFlag());
        pageQuerySealsInputDto.setSealGrantCountFlag(true);
        pageQuerySealsInputDto.setPlatformFlag(true);
        pageQuerySealsInputDto.setImageRefuseFlag(request.isImageRefuseFlag());
        pageQuerySealsInputDto.setSealBizType(request.getSealBizType());
        pageQuerySealsInputDto.setStatus(request.getStatus());
        pageQuerySealsInputDto.setName(request.getName());
        return pageQuerySealsInputDto;
    }

    public static PageQuerySealsResponse convertPageSealsResponse(PageQuerySealOutPutDTO pageQuerySeals) {
        PageQuerySealsResponse pageQuerySealsResponse = new PageQuerySealsResponse();
        pageQuerySealsResponse.setTotal(pageQuerySeals.getTotal());
        if (CollectionUtils.isNotEmpty(pageQuerySeals.getSeals())) {
            List<PageQuerySealsResponse.SealSimpleVO> sealSimpleVOList = pageQuerySeals.getSeals().stream().map(sealInfoDTO -> JsonUtils.obj2pojo(sealInfoDTO, PageQuerySealsResponse.SealSimpleVO.class)).collect(Collectors.toList());
            pageQuerySealsResponse.setSealList(sealSimpleVOList);
        }
        return pageQuerySealsResponse;
    }

    public static PageSimpleSealListResponse convertPageSimpleSealListResponse(PageQuerySimpleSealListOutputDTO pageQuerySeals) {
        PageSimpleSealListResponse response = new PageSimpleSealListResponse();
        response.setTotal(pageQuerySeals.getTotal());
        if (CollectionUtils.isNotEmpty(pageQuerySeals.getSeals())) {
            response.setSeals(pageQuerySeals.getSeals().stream().map(SealResponseConvertor::convertSealSimpleVO).collect(Collectors.toList()));
        }
        return response;
    }

    public static PageSimpleSealListResponse.SealSimpleVO convertSealSimpleVO(SealSimpleDTO model) {
        PageSimpleSealListResponse.SealSimpleVO sealSimpleVO = new PageSimpleSealListResponse.SealSimpleVO();
        sealSimpleVO.setSealId(model.getSealId());
        sealSimpleVO.setSealName(model.getSealName());
        sealSimpleVO.setSealImageUrl(model.getSealUrl());
        sealSimpleVO.setSealBizType(model.getSealBizType());
        sealSimpleVO.setSealBizTypeDesc(model.getSealBizTypeDesc());
        sealSimpleVO.setDefaultSealFlag(model.isDefaultFlag());
        return sealSimpleVO;
    }

    public static PageQueryGrantedSealsInputDto convertPageGrantedSealsInput(PageQueryGrantedSealsRequest request) {
        PageQueryGrantedSealsInputDto pageQueryGrantedSealsInputDto = new PageQueryGrantedSealsInputDto();
        pageQueryGrantedSealsInputDto.setPageNo(request.getPageNo());
        pageQueryGrantedSealsInputDto.setPageSize(request.getPageSize());
        pageQueryGrantedSealsInputDto.setGrantedOid(request.getGrantedOid());
        pageQueryGrantedSealsInputDto.setSealOwnerOids(request.getSealOwnerOids());
        pageQueryGrantedSealsInputDto.setDownloadFlag(request.getDownloadFlag());
        pageQueryGrantedSealsInputDto.setStatusFlag(request.getStatusFlag());
        pageQueryGrantedSealsInputDto.setSealBizType(request.getSealBizType());
        pageQueryGrantedSealsInputDto.setExistValidFlag(request.getExistValidFlag());
        pageQueryGrantedSealsInputDto.setName(request.getName());
        pageQueryGrantedSealsInputDto.setSortKey(request.getSortKey());
        pageQueryGrantedSealsInputDto.setSortType(request.getSortType());
        pageQueryGrantedSealsInputDto.setSealOwnerName(request.getSealOwnerName());
        return pageQueryGrantedSealsInputDto;


    }

    public static PageQueryGrantedSealsResponse convertPageGrantedSealsResponse(PageQueryGrantedSealOutPutDTO pageQuerySeals) {
        PageQueryGrantedSealsResponse pageQueryGrantedSealsResponse =
                new PageQueryGrantedSealsResponse();
        pageQueryGrantedSealsResponse.setTotal(pageQuerySeals.getTotal());
        if (pageQuerySeals.getTotal() == 0
                || CollectionUtils.isEmpty(pageQuerySeals.getGrantedSealList())) {
            pageQueryGrantedSealsResponse.setGrantedSealList(Lists.newArrayList());
            return pageQueryGrantedSealsResponse;
        }
        List<GrantedSealSimpleVO> grantedSealSimpleVOS =
                pageQuerySeals.getGrantedSealList().stream()
                        .map(
                                grantedSealSimpleDTO ->
                                        JsonUtils.obj2pojo(
                                                grantedSealSimpleDTO, GrantedSealSimpleVO.class))
                        .collect(Collectors.toList());
        pageQueryGrantedSealsResponse.setGrantedSealList(grantedSealSimpleVOS);
        return pageQueryGrantedSealsResponse;
    }

    public static GrantedSealOwnerListResponse convertGrantedSealOwnerResponse(
            GrantedSealOwnersOutPutDTO grantedSealOwners) {
        GrantedSealOwnerListResponse grantedSealOwnerListResponse =
                new GrantedSealOwnerListResponse();
        if (grantedSealOwners.getTotal() == 0
                || CollectionUtils.isEmpty(grantedSealOwners.getGrantedSealOwnerList())) {
            grantedSealOwnerListResponse.setTotal(0);
            grantedSealOwnerListResponse.setGrantedSealOwnerList(Lists.newArrayList());
            return grantedSealOwnerListResponse;
        }
        grantedSealOwnerListResponse.setTotal(grantedSealOwners.getTotal());
        List<GrantedSealOwnerListResponse.GrantedSealOwnerVO> grantedSealOwnerVOS =
                grantedSealOwners.getGrantedSealOwnerList().stream()
                        .map(
                                grantedSealOwner ->
                                        JsonUtils.obj2pojo(
                                                grantedSealOwner,
                                                GrantedSealOwnerListResponse.GrantedSealOwnerVO
                                                        .class))
                        .collect(Collectors.toList());
        grantedSealOwnerListResponse.setGrantedSealOwnerList(grantedSealOwnerVOS);
        return grantedSealOwnerListResponse;
    }

    public static GetRuleGrantedListInputDTO convertRuleGrantedListRequest(GetRuleGrantedListRequest request) {
        GetRuleGrantedListInputDTO input = new GetRuleGrantedListInputDTO();
        input.setOrgId(request.getOrgId());
        input.setOffset(request.getOffset());
        input.setSize(request.getSize());
        input.setDownloadFlag(request.isDownloadFlag());
        input.setSecSealGrantNumQueryFlag(request.isSecSealGrantNumQueryFlag());
        input.setSealId(request.getSealId());
        input.setSealOwnerOid(request.getSealOwnerOid());
        return input;
    }

    public static GetRuleGrantedListResponse convertRuleGrantedListResponse(GetRuleGrantedListOutputDTO output) {
        GetRuleGrantedListResponse response = new GetRuleGrantedListResponse();
        response.setTotal(output.getTotal());
        response.setGrantedList(output.getGrantedList());
        return response;
    }

    public static SettingRuleGrantNotifyInputDTO convertRuleGrantNotify(SettingRuleGrantNotifyRequest request) {
        SettingRuleGrantNotifyInputDTO input = new SettingRuleGrantNotifyInputDTO();
        input.setOrgId(request.getOrgId());
        input.setRuleGrantedId(request.getRuleGrantedId());
        input.setShouldNotify(request.getShouldNotify());
        input.setSealOwnerOid(request.getSealOwnerOid());
        return input;
    }

    public static UpdateRuleGrantInputDTO convertUpdateRuleGrantRequest(UpdateRuleGrantRequest request) {
        UpdateRuleGrantInputDTO input = new UpdateRuleGrantInputDTO();
        input.setOrgId(request.getOrgId());
        input.setRuleGrantedId(request.getRuleGrantedId());
        input.setResourceId(request.getResourceId());
        input.setResourceType(request.getResourceType());
        input.setTemplateKey(request.getTemplateKey());
        input.setRoleKey(request.getRoleKey());
        input.setAutoFall(request.getAutoFall());
        input.setFallType(request.getFallType());
        input.setScope(request.getScope());
        input.setScopeAppId(request.getScopeAppId());
        input.setScopeApprovalTemplateId(request.getScopeApprovalTemplateId());
        input.setNotifySetting(request.getNotifySetting());
        input.setGranter(request.getGranter());
        input.setGranterName(request.getGranterName());
        input.setGranterCode(request.getGranterCode());
        input.setGrantedUser(request.getGrantedUser());
        input.setGrantedAccountIds(request.getGrantedAccountIds());
        input.setGrantedRoleIds(request.getGrantedRoleIds());
        input.setGrantedDepartmentIds(request.getGrantedDepartmentIds());
        input.setGrantedUserCode(request.getGrantedUserCode());
        input.setGrantedUserName(request.getGrantedUserName());
        input.setEffectiveTime(request.getEffectiveTime());
        input.setExpireTime(request.getExpireTime());
        input.setExpireReason(request.getExpireReason());
        input.setGrantRedirectUrl(request.getGrantRedirectUrl());
        input.setGrantType(request.getGrantType());
        input.setSealOwnerOid(request.getSealOwnerOid());
        input.setSealOwnerAdminOid(request.getSealOwnerAdminOid());
        input.setH5(request.isH5());
        input.setToken(request.getToken());
        input.setAppScheme(request.getAppScheme());
        input.setAppId(request.getAppId());
        input.setClientType(request.getClientType());
        input.setSignReturnPageType(request.getSignReturnPageType());
        input.setAuthConfirmMethod(request.getAuthConfirmMethod());
        input.setClientId(request.getClientId());
        return input;
    }

    public static UpdateRuleGrantResponse convertUpdateRuleGrantResponse(UpdateRuleGrantOutputDTO output) {
        UpdateRuleGrantResponse response = new UpdateRuleGrantResponse();
        response.setRuleGrantId(output.getRuleGrantId());
        response.setFlowId(output.getFlowId());
        response.setSignUrl(output.getSignUrl());
        response.setLongSignUrl(output.getLongSignUrl());
        response.setAuthConfirmMethod(output.getAuthConfirmMethod());
        response.setGroupId(output.getGroupId());
        return response;
    }

    public static UpdateRuleGrantsInputDTO convertUpdateRuleGrantsRequest(UpdateRuleGrantsRequest request) {
        UpdateRuleGrantsInputDTO input = new UpdateRuleGrantsInputDTO();
        input.setOrgId(request.getOrgId());
        input.setRuleGrantedList(convertBatchRuleGrantedList(request.getRuleGrantedList()));
        input.setResourceId(request.getResourceId());
        input.setType(request.getType());
        input.setGrantRedirectUrl(request.getGrantRedirectUrl());
        input.setH5(request.isH5());
        input.setToken(request.getToken());
        input.setAppScheme(request.getAppScheme());
        input.setSealOwnerOid(request.getSealOwnerOid());
        input.setSealOwnerAdminOid(request.getSealOwnerAdminOid());
        input.setH5(request.isH5());
        input.setToken(request.getToken());
        input.setAppScheme(request.getAppScheme());
        input.setAppId(request.getAppId());
        input.setGrantRedirectUrl(request.getGrantRedirectUrl());
        input.setClientType(request.getClientType());
        input.setSignReturnPageType(request.getSignReturnPageType());
        input.setClientId(request.getClientId());
        return input;
    }

    private static List<UpdateRuleGrantsInputDTO.BatchRuleItemDTO> convertBatchRuleGrantedList(List<UpdateRuleGrantsRequest.BatchRuleItemDTO> ruleGrantedList) {
        List<UpdateRuleGrantsInputDTO.BatchRuleItemDTO> result = Lists.newArrayList();
        for (UpdateRuleGrantsRequest.BatchRuleItemDTO source : ruleGrantedList) {
            UpdateRuleGrantsInputDTO.BatchRuleItemDTO target = new UpdateRuleGrantsInputDTO.BatchRuleItemDTO();
            target.setRuleGrantedId(source.getRuleGrantedId());
            target.setExpireTime(source.getExpireTime());
            target.setEffectiveTime(source.getEffectiveTime());
            result.add(target);
        }
        return result;
    }

    public static UpdateRuleGrantsResponse convertUpdateRuleGrantsResponse(UpdateRuleGrantsOutputDTO output) {
        UpdateRuleGrantsResponse response = new UpdateRuleGrantsResponse();
        response.setSuccessCount(output.getSuccessCount());
        response.setErrorCount(output.getErrorCount());
        response.setFlowId(output.getFlowId());
        response.setSignUrl(output.getSignUrl());
        response.setLongSignUrl(output.getLongSignUrl());
        return response;
    }

    public static GetSecondRuleGrantListResponse convertGetSecondRuleGrantListResponse(GetSecondRuleGrantListOutputDTO outputDTO) {
        GetSecondRuleGrantListResponse response = new GetSecondRuleGrantListResponse();
        response.setTotal(outputDTO.getTotal());
        response.setResult(convertSecondRuleGrantListResponse(outputDTO.getResult()));
        return response;
    }

    private static List<GetSecondRuleGrantListResponse.SecondRuleGrantVO> convertSecondRuleGrantListResponse(List<GetSecondRuleGrantListOutputDTO.SecondRuleGrantDTO> sources) {
        List<GetSecondRuleGrantListResponse.SecondRuleGrantVO> targets = Lists.newArrayList();
        for (GetSecondRuleGrantListOutputDTO.SecondRuleGrantDTO source : sources) {
            GetSecondRuleGrantListResponse.SecondRuleGrantVO target = new GetSecondRuleGrantListResponse.SecondRuleGrantVO();
            target.setSecGrantBizId(source.getSecGrantBizId());
            target.setGranter(source.getGranter());
            target.setGranterName(source.getGranterName());
            target.setGrantedUser(source.getGrantedUser());
            target.setGrantedUserName(source.getGrantedUserName());
            target.setGrantedRoleId(source.getGrantedRoleId());
            target.setGrantedRoleName(source.getGrantedRoleName());
            target.setGrantedDepartmentId(source.getGrantedDepartmentId());
            target.setGrantedDepartmentName(source.getGrantedDepartmentName());
            target.setAutoFall(source.getAutoFall());
            target.setFallType(source.getFallType());
            target.setRoleKey(source.getRoleKey());
            target.setRoleName(source.getRoleName());
            target.setScope(source.getScope());
            target.setScopeName(source.getScopeName());
            target.setScopeApprovalTemplateId(source.getScopeApprovalTemplateId());
            target.setScopeApprovalTemplateName(source.getScopeApprovalTemplateName());
            target.setScopeAppId(source.getScopeAppId());
            target.setScopeAppName(source.getScopeAppName());
            target.setEffectiveTime(source.getEffectiveTime());
            target.setExpireTime(source.getExpireTime());
            target.setNotifySetting(source.getNotifySetting());
            target.setStatus(source.getStatus());
            target.setStatusDesc(source.getStatusDesc());
            target.setReason(source.getReason());
            target.setReasonDesc(source.getReasonDesc());
            target.setFlowId(source.getFlowId());
            target.setAuthConfirmMethod(source.getAuthConfirmMethod());
            target.setLongEffective(source.getLongEffective());
            target.setOnlyApprovalNotUse(source.getOnlyApprovalNotUse());
            targets.add(target);
        }

        return targets;
    }

    public static GetSecondRuleGrantListInputDTO convertGetSecondRuleGrantListRequest(GetSecondRuleGrantListRequest request) {
        GetSecondRuleGrantListInputDTO inputDTO = new GetSecondRuleGrantListInputDTO();
        inputDTO.setSealGrantBizId(request.getSealGrantBizId());
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        inputDTO.setSealOwnerOid(request.getSealOwnerOid());
        return inputDTO;
    }

    public static SecondSealGrantUpdateInputDTO convertUpdateSecondGrantRequest(SecondSealGrantUpdateRequest request) {
        SecondSealGrantUpdateInputDTO inputDTO = new SecondSealGrantUpdateInputDTO();
        inputDTO.setSecondSealGrantBizId(request.getSecondSealGrantBizId());
        inputDTO.setEffectiveTime(request.getEffectiveTime());
        inputDTO.setExpireTime(request.getExpireTime());
        inputDTO.setSealOwnerOid(request.getSealOwnerOid());
        inputDTO.setSealOwnerAdminOid(request.getSealOwnerAdminOid());
        inputDTO.setH5(request.isH5());
        inputDTO.setToken(request.getToken());
        inputDTO.setAppScheme(request.getAppScheme());
        inputDTO.setAppId(request.getAppId());
        inputDTO.setGrantRedirectUrl(request.getGrantRedirectUrl());
        inputDTO.setClientType(request.getClientType());
        inputDTO.setSignReturnPageType(request.getSignReturnPageType());
        inputDTO.setAuthConfirmMethod(request.getAuthConfirmMethod());
        inputDTO.setClientId(request.getClientId());
        return inputDTO;
    }

    public static SecondSealGrantUpdateResponse convertUpdateSecondGrantResponse(SecondSealGrantUpdateOutputDTO outputDTO) {
        SecondSealGrantUpdateResponse response = new SecondSealGrantUpdateResponse();
        response.setSecSealGrantBizId(outputDTO.getSecSealGrantBizId());
        response.setShortSignUrl(outputDTO.getShortSignUrl());
        response.setLongSignUrl(outputDTO.getLongSignUrl());
        response.setAuthConfirmMethod(outputDTO.getAuthConfirmMethod());
        response.setGroupId(outputDTO.getGroupId());
        return response;
    }

    public static QuerySealGrantGroupDetailResponse convertQuerySealGrantGroupDetail(QuerySealGrantGroupDetailOutputDTO output) {
        QuerySealGrantGroupDetailResponse response = new QuerySealGrantGroupDetailResponse();
        response.setGroupId(output.getGroupId());
        response.setGroupStatus(output.getGroupStatus());
        response.setIsAllCreate(output.getIsAllCreate());
        response.setRedirectUrl(output.getRedirectUrl());
        response.setTotal(output.getTotal());
        response.setDetails(convertDetails(output.getDetails()));
        return response;
    }

    private static List<SealGrantGroupDetailResponse> convertDetails(List<SealGrantGroupDetailOutputDTO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return Lists.newArrayList();
        }
        return details.stream().map(detail -> {
            SealGrantGroupDetailResponse result = new SealGrantGroupDetailResponse();
            result.setRuleGrantId(detail.getRuleGrantId());
            result.setSeal(getGrantedSealResponse(detail.getSeal()));
            result.setGrantedObject(getSealGrantedObject(detail.getGrantedObject()));
            result.setGrantedScopes(getSealGrantedScopes(detail.getGrantedScopes()));
            result.setFallType(detail.getFallType());
            result.setGrantDuration(detail.getGrantDuration());
            result.setRoleKey(detail.getRoleKey());
            result.setRoleKeyName(detail.getRoleKeyName());
            return result;
        }).collect(Collectors.toList());
    }

    private static GrantedSealResponse getGrantedSealResponse(GrantedSealOutputDTO output) {
        GrantedSealResponse response = new GrantedSealResponse();
        response.setSealId(output.getSealId());
        response.setSealName(output.getSealName());
        response.setUrl(output.getUrl());
        return response;
    }

    private static List<SealGrantGroupDetailResponse.SealGrantedScope> getSealGrantedScopes(List<SealGrantGroupDetailOutputDTO.SealGrantedScope> grantedScopes) {
        if (CollectionUtils.isEmpty(grantedScopes)) {
            return Lists.newArrayList();
        }
        return grantedScopes.stream().map(scope -> {
            SealGrantGroupDetailResponse.SealGrantedScope result = new SealGrantGroupDetailResponse.SealGrantedScope();
            result.setId(scope.getId());
            result.setType(scope.getType());
            result.setName(scope.getName());
            return result;
        }).collect(Collectors.toList());
    }

    private static SealGrantGroupDetailResponse.SealGrantedObject getSealGrantedObject(SealGrantGroupDetailOutputDTO.SealGrantedObject grantedObject) {
        SealGrantGroupDetailResponse.SealGrantedObject object = new SealGrantGroupDetailResponse.SealGrantedObject();
        object.setId(grantedObject.getId());
        object.setType(grantedObject.getType());
        object.setName(grantedObject.getName());
        return object;
    }
}
