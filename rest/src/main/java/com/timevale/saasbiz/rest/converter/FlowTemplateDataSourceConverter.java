package com.timevale.saasbiz.rest.converter;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.datasource.dto.DataSourceChannelFilterDTO;
import com.timevale.saasbiz.model.bean.lowcode.DataSourceDTO;
import com.timevale.saasbiz.rest.bean.datasource.DataSourceChannelVO;
import com.timevale.saasbiz.rest.bean.flowtemplate.response.FlowTemplateDataSourceDetailResponse;
import com.timevale.saasbiz.rest.bean.flowtemplate.vo.FlowTemplateDataSourceVO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/12 10:56
 */
public class FlowTemplateDataSourceConverter {


    public static List<DataSourceChannelVO> convert(List<DataSourceChannelFilterDTO> beforeList) {
        if (CollectionUtils.isEmpty(beforeList)) {
            return new ArrayList<>();
        }
        return beforeList.stream().map(elm -> {
            DataSourceChannelVO dataSourceChannelVO = new DataSourceChannelVO();
            dataSourceChannelVO.setDataSourceChannel(elm.getDataSourceChannel());
            dataSourceChannelVO.setDataSourceChannelName(elm.getDataSourceChannelName());
            dataSourceChannelVO.setFunctionSupport(elm.getFunctionSupport());
            dataSourceChannelVO.setIcon(elm.getIcon());
            return dataSourceChannelVO;
        }).collect(Collectors.toList());
    }

    public static List<FlowTemplateDataSourceVO> dataSourceConvert(List<DataSourceDTO> beforeList) {
        if (CollectionUtils.isEmpty(beforeList)) {
            return new ArrayList();
        }
        return beforeList.stream().map(elm -> {
            FlowTemplateDataSourceVO after = new FlowTemplateDataSourceVO();
            after.setDataSourceId(elm.getDataSourceId());
            after.setDataSourceName(elm.getDataSourceName());
            after.setDataSourceChannel(elm.getDataSourceChannel());
            after.setCreateTime(elm.getCreateTime());
            after.setCreatorName(elm.getCreatorName());
            return after;
        }).collect(Collectors.toList());
    }

    public static FlowTemplateDataSourceDetailResponse dataSourceConvert(DataSourceDTO elm) {
        if (null == elm) {
            return null;
        }
        FlowTemplateDataSourceDetailResponse after = new FlowTemplateDataSourceDetailResponse();
        after.setDataSourceId(elm.getDataSourceId());
        after.setDataSourceName(elm.getDataSourceName());
        after.setDataSourceChannel(elm.getDataSourceChannel());
        after.setCreateTime(elm.getCreateTime());
        after.setCreatorName(elm.getCreatorName());
        return after;
    }



}
