package com.timevale.saasbiz.rest.bean.contractreview.record.request;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ReviewRecordPageRequest extends ToString {
    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;
    @NotNull(message = "分页数量不能为空")
    private Integer pageSize = 10;

    private String fileName;

    /**
     * 审查已完成
     * 审查未完成
     * 审查失败
     * 正在审查
     */
    private String status;
}
