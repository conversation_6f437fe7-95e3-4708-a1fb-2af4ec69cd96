package com.timevale.saasbiz.rest;

import static com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum.CONTRACT;
import static com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum.SEAL;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_RESOURCE_TENANT_ID;
import static com.timevale.saasbiz.model.enums.PrivilegeResourceEnum.APPROVAL_TEMPLATE;
import static com.timevale.saasbiz.rest.converter.ApprovalTemplateConverter.buildListResponse;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.timevale.cat.toolkit.datacollect.DataCollector;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.manage.common.service.constant.FunctionCodeConstant;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.approval.dto.ApprovalInitConfigResDTO;
import com.timevale.saas.common.privilege.aspect.RelationAndPrivilegeCheck;
import com.timevale.saasbiz.model.approval.dto.ApprovalTemplateEsDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalTemplateDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalTemplateDetailDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalTemplateExistConditionDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalTemplateSealListDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.*;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasPrivilegeInputDTO;
import com.timevale.saasbiz.model.constants.AuthRelationBizSceneConstants;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.enums.PrivilegeOperationEnum;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.approval.request.*;
import com.timevale.saasbiz.rest.bean.approval.response.*;
import com.timevale.saasbiz.rest.converter.ApprovalVOConverter;
import com.timevale.saasbiz.service.approval.ApprovalAdapter;
import com.timevale.saasbiz.service.approval.ApprovalStartService;
import com.timevale.saasbiz.service.approval.ApprovalTemplateService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.saasbiz.tracking.enums.TrackingEventEnum;
import com.timevale.saasbiz.tracking.enums.TrackingFieldEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2023-03-27 09:18
 */
@Slf4j
@Api(tags = "审批流程模板接口")
@Validated
@ExternalService
@RestMapping(path = "/v2/approval/template")
public class ApprovalTemplateRest {

    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private ApprovalTemplateService approvalTemplateService;
    @Autowired
    private ApprovalStartService approvalStartService;
    @Autowired
    private SaasCommonService saasCommonService;

    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("审批模板列表")
    @RestMapping(path = "/list", method = RequestMethod.POST)
    public RestResult<ListApprovalTemplateResponse> list(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ListApprovalTemplateRequest request) {
        // 获取用户有权限查看的审批模板类型，Pair<hasPrivilege, templateType>
        Pair<Boolean, String> privilegeTemplateType =
                smartTemplateType(tenantId, accountId, request.getApprovalTemplateType());

        boolean hasPrivilege = privilegeTemplateType.getLeft();
        String templateType = privilegeTemplateType.getRight();

        // 无权限查看，返回空列表
        if (!hasPrivilege) {
            return RestResult.success(buildListResponse(new PagerResult<>()));
        }

        //主子企业场景，切换到子企业资源，查询子企业的审批列表
        //非主子企业，resourceTenantId = tenantId
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        // 根据用户权限查询对应类型的审批列表
        ListApprovalTemplateInputDTO dto = new ListApprovalTemplateInputDTO();
        BeanUtils.copyProperties(request, dto);
        dto.setTenantOid(resourceTenantId);
        dto.setApprovalTemplateType(templateType);
        dto.setApprovalTemplateStatus(request.getApprovalTemplateStatus());
        PagerResult<ApprovalTemplateEsDTO> pageResult = approvalTemplateService.page(dto);

        return RestResult.success(buildListResponse(pageResult));
    }

    /**
     * 获取用户查看的模板类型
     *
     * @param tenantId 主体id
     * @param accountId 用户id
     * @return Pair<能否查看审批模板, 查看模板类型>, {@link ApprovalTemplateTypeEnum#getCode()}
     */
    private Pair<Boolean, String> smartTemplateType(
            String tenantId, String accountId, Integer templateType) {
        String targetType = Optional.ofNullable(templateType).map(String::valueOf).orElse(null);
        CheckHasPrivilegeInputDTO input = new CheckHasPrivilegeInputDTO();
        input.setAccountId(accountId);
        input.setTenantId(tenantId);
        input.setResourceKey(APPROVAL_TEMPLATE.getType());

        // 能否查看合同审批，不一定是没有权限，也有可能是有权限，但用户选择查看的不是合同审批
        boolean canViewContractApproval = false;
        if (StringUtils.isBlank(targetType) || CONTRACT.getCode().equals(targetType)) {
            input.setPrivilegeKey(PrivilegeOperationEnum.CONTRACT.getType());
            canViewContractApproval = userCenterService.checkHasPrivilege(input, false);
        }

        // 能否查看用印审批，不一定是没有权限，也有可能是有权限，但用户选择查看的不是用印审批
        boolean canViewSealApproval = false;
        if (StringUtils.isBlank(targetType) || SEAL.getCode().equals(targetType)) {
            input.setPrivilegeKey(PrivilegeOperationEnum.SEAL.getType());
            canViewSealApproval = userCenterService.checkHasPrivilege(input, false);
        }

        // 判断用户能查看哪种模板
        if (!canViewContractApproval && !canViewSealApproval) {
            return Pair.of(false, targetType);
        } else if (canViewContractApproval && canViewSealApproval) {
            return Pair.of(true, null);
        } else {
            return Pair.of(true, canViewContractApproval ? CONTRACT.getCode() : SEAL.getCode());
        }
    }



    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("保存审批模版")
    @RestMapping(path = "/save", method = RequestMethod.POST)
    public RestResult<ApprovalTemplateSaveResponse> save(@RequestBody ApprovalTemplateSaveRequest request) {

        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String appId = RequestContextExtUtils.getAppId();
        String clientId = RequestContextExtUtils.getClientId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        ApprovalTemplateSaveInputDTO saveInputDTO = JSON.parseObject(JSON.toJSONString(request),
                ApprovalTemplateSaveInputDTO.class);
        saveInputDTO.setAppId(appId);
        saveInputDTO.setOperatorOid(operatorOid);
        saveInputDTO.setSubjectOid(subjectOid);
        saveInputDTO.setResourceTenantOid(resourceTenantId);
        saveInputDTO.setClientId(clientId);
        String approvalTemplateId = null;
        try {
            approvalTemplateId =
                    approvalTemplateService
                            .approvalTemplateOperate(resourceTenantId, request.getApprovalTemplateType())
                            .createApprovalTemplate(saveInputDTO);
            sensor(subjectOid, request.getApprovalTemplateType(), null);
        } catch (Exception e) {
            sensor(subjectOid, request.getApprovalTemplateType(), e.getMessage());
            throw e;
        }
        ApprovalTemplateSaveResponse response = new ApprovalTemplateSaveResponse();
        response.setApprovalTemplateId(approvalTemplateId);
        return RestResult.success(response);
    }

    private void sensor(String subjectOid, String approvalTemplateType, String failureMsg) {
        try {
            AccountInfoDTO accountInfoDTO = userCenterService.queryAccountInfoByOid(subjectOid);
            Map<String, Object> data = Maps.newHashMap();
            data.put(TrackingFieldEnum.ENTITY_NAME.getKey(), accountInfoDTO.getName());
            data.put(TrackingFieldEnum.AUTHORIZED_OID.getKey(), accountInfoDTO.getOid());
            data.put(TrackingFieldEnum.AUTHORIZED_GID.getKey(), accountInfoDTO.getGid());
            data.put("type", CONTRACT.getCode().equals(approvalTemplateType) ? "hetongshenpi" : "yonyinshenpi");
            data.put(TrackingFieldEnum.SUCCESS.getKey(), StringUtils.isBlank(failureMsg));
            data.put(TrackingFieldEnum.ERROR_MSG.getKey(), Optional.ofNullable(failureMsg).orElse(""));
            DataCollector.sensorsCollect(subjectOid, true, TrackingEventEnum.CREATE_APPROVAL_FLOW_TEMPLATE.getEvent(), data);
        } catch (Exception e) {
            log.info("save template sensor", e);
        }
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("更新审批模版")
    @RestMapping(path = "/update", method = RequestMethod.POST)
    public RestResult<Void> update(@RequestBody ApprovalTemplateUpdateRequest request) {

        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        String appId = RequestContextExtUtils.getAppId();
        String clientId = RequestContextExtUtils.getClientId();
        ApprovalTemplateUpdateInputDTO inputDTO = JSON.parseObject(JSON.toJSONString(request),
                ApprovalTemplateUpdateInputDTO.class);
        BeanUtils.copyProperties(request, inputDTO);
        inputDTO.setAppId(appId);
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        inputDTO.setResourceTenantOid(resourceTenantId);
        inputDTO.setClientId(clientId);
        approvalTemplateService
                .approvalTemplateOperate(resourceTenantId, request.getApprovalTemplateType())
                .updateApprovalTemplate(inputDTO);
        return RestResult.success(null);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("复制审批模版")
    @RestMapping(path = "/copy", method = RequestMethod.POST)
    public RestResult<ApprovalTemplateCopyResponse> copy(@RequestBody ApprovalTemplateCopyRequest request) {

        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        String appId = RequestContextExtUtils.getAppId();
        String clientId = RequestContextExtUtils.getClientId();
        ApprovalTemplateCopyInputDTO inputDTO = new ApprovalTemplateCopyInputDTO();
        BeanUtils.copyProperties(request, inputDTO);
        inputDTO.setAppId(appId);
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        inputDTO.setResourceTenantOid(resourceTenantId);
        inputDTO.setClientId(clientId);
        String newApprovalTemplateId =
                approvalTemplateService
                        .approvalTemplateOperate(resourceTenantId, request.getApprovalTemplateType())
                        .copyApprovalTemplate(inputDTO);

        ApprovalTemplateCopyResponse response = new ApprovalTemplateCopyResponse();
        response.setApprovalTemplateId(newApprovalTemplateId);
        return RestResult.success(response);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("开启审批模版")
    @RestMapping(path = "/open", method = RequestMethod.POST)
    public RestResult<Void> open(@RequestBody ApprovalTemplateOpenRequest request) {

        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String appId = RequestContextExtUtils.getAppId();
        String clientId = RequestContextExtUtils.getClientId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        ApprovalTemplateOpenInputDTO inputDTO = new ApprovalTemplateOpenInputDTO();
        BeanUtils.copyProperties(request, inputDTO);
        inputDTO.setAppId(appId);
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        inputDTO.setResourceTenantOid(resourceTenantId);
        inputDTO.setClientId(clientId);
        approvalTemplateService
                .approvalTemplateOperate(resourceTenantId, request.getApprovalTemplateType())
                .openApprovalTemplate(inputDTO);
        return RestResult.success(null);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("关闭审批模版")
    @RestMapping(path = "/close", method = RequestMethod.POST)
    public RestResult<Void> close(@RequestBody ApprovalTemplateCloseRequest request) {

        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        String appId = RequestContextExtUtils.getAppId();
        String clientId = RequestContextExtUtils.getClientId();
        ApprovalTemplateCloseInputDTO inputDTO = new ApprovalTemplateCloseInputDTO();
        BeanUtils.copyProperties(request, inputDTO);
        inputDTO.setAppId(appId);
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        inputDTO.setResourceTenantOid(resourceTenantId);
        inputDTO.setClientId(clientId);
        approvalTemplateService
                .approvalTemplateOperate(resourceTenantId, request.getApprovalTemplateType())
                .closeApprovalTemplate(inputDTO);
        return RestResult.success(null);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("删除审批模版")
    @RestMapping(path = "/delete", method = RequestMethod.POST)
    public RestResult<Void> delete(@RequestBody ApprovalTemplateDeleteRequest request) {

        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String appId = RequestContextExtUtils.getAppId();
        String clientId = RequestContextExtUtils.getClientId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        ApprovalTemplateDeleteInputDTO inputDTO = new ApprovalTemplateDeleteInputDTO();
        BeanUtils.copyProperties(request, inputDTO);
        inputDTO.setAppId(appId);
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        inputDTO.setResourceTenantOid(resourceTenantId);
        inputDTO.setClientId(clientId);
        approvalTemplateService
                .approvalTemplateOperate(resourceTenantId, request.getApprovalTemplateType())
                .deleteApprovalTemplate(inputDTO);
        return RestResult.success(null);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("获取审批模版详情")
    @RestMapping(path = "/detail", method = RequestMethod.GET)
    public RestResult<ApprovalTemplateDetailVO> delete(@RequestParam String approvalTemplateId,
                                                       @RequestParam String approvalTemplateType) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String appId = RequestContextExtUtils.getAppId();
        String clientId = RequestContextExtUtils.getClientId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        GetApprovalTemplateDetailInputDTO inputDTO = new GetApprovalTemplateDetailInputDTO();
        inputDTO.setAppId(appId);
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        inputDTO.setResourceTenantOid(resourceTenantId);
        inputDTO.setClientId(clientId);
        inputDTO.setApprovalTemplateId(approvalTemplateId);
        inputDTO.setApprovalTemplateType(approvalTemplateType);
        ApprovalTemplateDetailDTO detailBO =
                approvalTemplateService
                        .approvalTemplateOperate(resourceTenantId, approvalTemplateType)
                        .getApprovalTemplateDetail(inputDTO);
        ApprovalTemplateDetailVO detailVO = JSON.parseObject(JSON.toJSONString(detailBO), ApprovalTemplateDetailVO.class);
        return RestResult.success(detailVO);
    }



    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("审批事件是否已存在")
    @RestMapping(path = "/exist-condition", method = RequestMethod.POST)
    public RestResult<ApprovalTemplateExistConditionResponse>
    existCondition(@RequestBody ApprovalTemplateExistConditionRequest request) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String appId = RequestContextExtUtils.getAppId();
        String clientId = RequestContextExtUtils.getClientId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        ApprovalTemplateExistConditionInputDTO inputDTO = new ApprovalTemplateExistConditionInputDTO();
        inputDTO.setAppId(appId);
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        inputDTO.setResourceTenantOid(resourceTenantId);
        inputDTO.setClientId(clientId);
        inputDTO.setApprovalTemplateId(request.getApprovalTemplateId());
        inputDTO.setApprovalTemplateType(request.getApprovalTemplateType());
        inputDTO.setConditionType(request.getConditionType());
        inputDTO.setConditionValues(request.getConditionValues());
        ApprovalTemplateExistConditionDTO existConditionBO =
                approvalTemplateService
                        .approvalTemplateOperate(resourceTenantId, request.getApprovalTemplateType())
                        .existCondition(inputDTO);
        ApprovalTemplateExistConditionResponse response = new ApprovalTemplateExistConditionResponse();
        BeanUtils.copyProperties(existConditionBO, response);
        response.setExistApprovalTemplate(ApprovalVOConverter.approvalTemplateBO2VO(existConditionBO.getExistApprovalTemplate()));
        response.setConditionValuesVO(ApprovalVOConverter.approvalConditionListBO2VO(existConditionBO.getConditionValuesDTO()));
        return RestResult.success(response);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("删除审批事件和审批模版之间的关系")
    @RestMapping(path = "/delete-condition", method = RequestMethod.DELETE)
    public RestResult<Void> deleteCondition(@RequestBody ApprovalTemplateDeleteConditionRequest request) {
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getTenantId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        ApprovalTemplateDeleteConditionInputDTO rpcInput = new ApprovalTemplateDeleteConditionInputDTO();
        rpcInput.setApprovalTemplateId(request.getApprovalTemplateId());
        rpcInput.setSubjectOid(subjectOid);
        rpcInput.setOperatorOid(operatorOid);
        rpcInput.setResourceTenantOid(resourceTenantId);
        rpcInput.setConditionType(request.getConditionType());
        rpcInput.setConditionValue(request.getConditionValue());
        approvalTemplateService.deleteCondition(rpcInput);
        return RestResult.success(null);
    }


    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("发起获取符合条件的模版")
    @RestMapping(path = "/available", method = RequestMethod.GET)
    public RestResult<StartApprovalAvailableTemplateResponse> availableTemplate(@RequestParam(required = false) String approvalTemplateType,
                                                                                @RequestParam(required = false) Integer approvalTemplateConditionType,
                                                                                @RequestParam(required = false) String approvalTemplateConditionValue,
                                                                                @RequestParam(required = false) String approvalId) {
        String subjectOid = RequestContextExtUtils.getTenantId();
        String operatorOid = RequestContextExtUtils.getOperatorId();
        String resourceTenantId = RequestContextExtUtils.getResourceTenantId();
        StartApprovalAvailableTemplateResponse response = new StartApprovalAvailableTemplateResponse();
        //无权限返回空列表
        boolean hasPermission =  saasCommonService.hasSupportFunction(subjectOid, FunctionCodeConstant.MULTI_ORGANIZATION);
        if(!hasPermission){
            return RestResult.success(response);
        }
        // 兼容历史调用场景， 默认合同审批模板
        if (StringUtils.isBlank(approvalTemplateType)) {
            approvalTemplateType = CONTRACT.getCode();
        }

        List<ApprovalTemplateDTO> templateList = null;
        if (StringUtils.isNotBlank(approvalId)) {
            templateList = approvalStartService.reStartAvailableApprovalTemplate(resourceTenantId, operatorOid, approvalId);
        } else {
            templateList = approvalStartService.startAvailableApprovalTemplate(resourceTenantId,
                    operatorOid,
                    approvalTemplateType,
                    approvalTemplateConditionType,
                    approvalTemplateConditionValue);
        }
        response.setList(ApprovalVOConverter.approvalTemplateBO2VO(templateList));
        return RestResult.success(response);
    }
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @ApiOperation("获取审批模板的初始化配置")
    @RestMapping(path = "/init-config", method = RequestMethod.GET)
    public RestResult<ApprovalInitConfigResponse> availableTemplate(@URIQueryParam ApprovalInitConfigRequest request) {

        ApprovalInitConfigInputDTO inputDTO = new ApprovalInitConfigInputDTO();
        inputDTO.setAccountId(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectId(RequestContextExtUtils.getTenantId());
        inputDTO.setResourceTenantId(RequestContextExtUtils.getResourceTenantId());
        ApprovalInitConfigResDTO approvalInitConfigRes = approvalTemplateService.getInitConfig(inputDTO);
        ApprovalInitConfigResponse response = new ApprovalInitConfigResponse();
        response.setMaxFlowTemplateSize(approvalInitConfigRes.getMaxFlowTemplateSize());
        response.setAdminIsolated(approvalInitConfigRes.isAdminIsolated());
        return RestResult.success(response);
    }

    @ApiOperation(value = "分页查询用印审批模板印章列表", httpMethod = "GET")
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.APPROVAL_TEMPLATE,
            orgPrivilegeKey = PrivilegeOperationConstants.SEAL
    )
    @RestMapping(path = "/seal-list", method = RequestMethod.GET)
    public BaseResult<ApprovalTemplateSealListResponse> pageListSeal(
            @URIQueryParam @ModelAttribute ApprovalTemplateSealListRequest request) {

        ApprovalTemplateSealListInputDTO inputDTO = new ApprovalTemplateSealListInputDTO();
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setAccountId(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectId(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setGrantedSeal(request.isGrantedSeal());
        inputDTO.setDownloadFlag(request.isDownloadFlag());
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        ApprovalTemplateSealListDTO sealListDTO = approvalTemplateService.queryApprovalTemplateSealList(inputDTO);

        ApprovalTemplateSealListResponse response = new ApprovalTemplateSealListResponse();
        response.setTotal(sealListDTO.getTotal());
        response.setSeals(ApprovalVOConverter.approvalTemplateSealBO2VO(sealListDTO.getSeals()));
        return BaseResult.success(response);
    }

    @ApiOperation(value = "查询用印审批模板全部印章列表", httpMethod = "GET")
    @RelationAndPrivilegeCheck(
            userOid = SPEL_HEADER_OPERATOR_ID,
            orgOid = SPEL_HEADER_TENANT_ID,
            childId = SPEL_HEADER_RESOURCE_TENANT_ID,
            authBizScene = AuthRelationBizSceneConstants.APPROVAL_MANAGE,
            orgResourceKey = PrivilegeResourceConstants.APPROVAL_TEMPLATE,
            orgPrivilegeKey = PrivilegeOperationConstants.SEAL
    )
    @RestMapping(path = "/seal-list-all", method = RequestMethod.GET)
    public BaseResult<ApprovalTemplateSealListResponse> listAllSeal(
            @URIQueryParam @ModelAttribute ApprovalTemplateAllSealListRequest request) {

        ApprovalTemplateAllSealListInputDTO inputDTO = new ApprovalTemplateAllSealListInputDTO();
        inputDTO.setAppId(RequestContextExtUtils.getAppId());
        inputDTO.setAccountId(RequestContextExtUtils.getOperatorId());
        inputDTO.setSubjectId(RequestContextExtUtils.getResourceTenantId());
        inputDTO.setDownloadFlag(request.isDownloadFlag());
        ApprovalTemplateSealListDTO sealListDTO = approvalTemplateService.queryApprovalTemplateAllSealList(inputDTO);

        ApprovalTemplateSealListResponse response = new ApprovalTemplateSealListResponse();
        response.setTotal(sealListDTO.getTotal());
        response.setSeals(ApprovalVOConverter.approvalTemplateSealBO2VO(sealListDTO.getSeals()));
        return BaseResult.success(response);
    }

}
