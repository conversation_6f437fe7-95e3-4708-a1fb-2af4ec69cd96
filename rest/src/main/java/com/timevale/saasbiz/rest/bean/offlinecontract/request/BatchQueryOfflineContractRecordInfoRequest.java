package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 查询线下合同导入记录基本信息
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
public class BatchQueryOfflineContractRecordInfoRequest extends ToString {

    @ApiModelProperty(value = "导入记录id列表", required = true)
    @NotEmpty(message = "导入记录id列表不能为空")
    private List<String> recordIds;

    /** 是否返回菜单路径 */
    @ApiModelProperty(value = "是否返回菜单路径", example = "false")
    private boolean withMenuPath;
}
