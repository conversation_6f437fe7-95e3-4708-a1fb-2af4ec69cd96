package com.timevale.saasbiz.rest.converter;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Sets;
import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalDetailWithLogsDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalInBatchTaskDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalOperateDetailDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalOperatePreCheckDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalBizContractFileDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalTableHeadDTO;
import com.timevale.saasbiz.model.bean.approval.dto.task.*;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalOperateDataDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalUrlInputDTO;
import com.timevale.saasbiz.model.bean.process.dto.ProcessRemarkDTO;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalOperateDataRequest;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalTableHeadConfigRequest;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalUrlRequest;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalOperatePreCheckResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalOperateResponse;
import com.timevale.saasbiz.rest.bean.approval.response.operateprecheck.MissingCertPreCheckVO;
import com.timevale.saasbiz.rest.bean.approval.vo.*;
import com.timevale.saasbiz.service.approval.approvaloperate.operate.ApprovalOperateForwardResult;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/8 11:45
 */
public class ApprovalRestConverter {


    public static List<ApprovalNodeTaskVO> nodeTaskBO2VO(List<ApprovalNodeTaskBizDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(nodeTaskBO -> {
            ApprovalNodeTaskVO taskVO = new ApprovalNodeTaskVO();
            taskVO.setNodeId(nodeTaskBO.getNodeId());
            taskVO.setNodeType(nodeTaskBO.getNodeType());

            if (CollectionUtils.isEmpty(nodeTaskBO.getApprovalTasks())) {
                return taskVO;
            }
            List<ApprovalTaskVO> approvalTasks = nodeTaskBO.getApprovalTasks()
                    .stream()
                    .map(ApprovalRestConverter::approvalTaskBO2VO)
                    .collect(Collectors.toList());
            taskVO.setApprovalTasks(approvalTasks);
            return taskVO;
        }).collect(Collectors.toList());
    }

    private static ApprovalTaskVO approvalTaskBO2VO(ApprovalTaskBizDTO approvalTaskBO) {
        if (null == approvalTaskBO) {
            return null;
        }
        ApprovalTaskVO approvalTaskVO = new ApprovalTaskVO();
        approvalTaskVO.setTaskId(approvalTaskBO.getTaskId());
        approvalTaskVO.setTaskStatus(approvalTaskBO.getTaskStatus());
        approvalTaskVO.setCreateTime(approvalTaskBO.getCreateTime());
        approvalTaskVO.setFinishTime(approvalTaskBO.getFinishTime());
        approvalTaskVO.setRemark(approvalTaskBO.getRemark());
        approvalTaskVO.setAssignee(approvalAccountConvert(approvalTaskBO.getAssignee()));
        approvalTaskVO.setCandidates(approvalAccountConvert(approvalTaskBO.getCandidates()));
        approvalTaskVO.setTransferLogs(transferLogConvert(approvalTaskBO.getTransferLogs()));
        return approvalTaskVO;
    }

    private static List<ProcessRemarkVO> remarkBO2VO(List<ProcessRemarkDTO> processRemarkDTOS) {
        if (CollectionUtils.isEmpty(processRemarkDTOS)) {
            return null;
        }
        List<ProcessRemarkVO> processRemarkVOS = new ArrayList<>();
        processRemarkDTOS.forEach(processRemarkDTO -> {
            ProcessRemarkVO processRemarkVO = new ProcessRemarkVO();
            processRemarkVO.setAccount(processRemarkDTO.getAccount());
            processRemarkVO.setAccountName(processRemarkDTO.getAccountName());
            processRemarkVO.setRemark(processRemarkDTO.getRemark());
            processRemarkVO.setCreateTime(processRemarkDTO.getCreateTime());
            processRemarkVOS.add(processRemarkVO);
        });
        return processRemarkVOS;
    }

    /**
     * 转换三方审批VO对象
     * @param thirdApprovalBizDTOs
     * @return
     */
    private static List<ThirdApprovalVO> thirdApprovalDTO2VO(
            List<ThirdApprovalBizDTO> thirdApprovalBizDTOs) {
        if (CollectionUtils.isEmpty(thirdApprovalBizDTOs)) {
            return Lists.newArrayList();
        }
        return thirdApprovalBizDTOs.stream().map(i -> {
            ThirdApprovalVO thirdApprovalVO = new ThirdApprovalVO();
            thirdApprovalVO.setThirdPlatform(i.getThirdPlatform());
            thirdApprovalVO.setThirdInstanceId(i.getThirdInstanceId());
            return thirdApprovalVO;
        }).collect(Collectors.toList());
    }

    public static ApprovalTransferLogVO transferLogConvert(ApprovalTransferLogBizDTO approvalTransferLog) {
        if (null == approvalTransferLog) {
            return null;
        }
        ApprovalTransferLogVO approvalTransferLogVO = new ApprovalTransferLogVO();
        approvalTransferLogVO.setFromPerson(approvalAccountConvert(approvalTransferLog.getFromPerson()));
        approvalTransferLogVO.setToPerson(approvalAccountConvert(approvalTransferLog.getToPerson()));
        approvalTransferLogVO.setCreateTime(approvalTransferLog.getCreateTime());
        approvalTransferLogVO.setTransferReason(approvalTransferLog.getTransferReason());
        return approvalTransferLogVO;
    }


    public static List<ApprovalTransferLogVO> transferLogConvert(List<ApprovalTransferLogBizDTO> approvalTransferLogList) {
        if (CollectionUtils.isEmpty(approvalTransferLogList)) {
            return new ArrayList();
        }
        List<ApprovalTransferLogVO> approvalTransferLogVOList = new ArrayList();
        for (ApprovalTransferLogBizDTO approvalTransferLog : approvalTransferLogList) {
            approvalTransferLogVOList.add(transferLogConvert(approvalTransferLog));
        }
        return approvalTransferLogVOList;
    }

    public static ApprovalAccountCandidateVO approvalAccountConvert(ApprovalAccountBizDTO approvalAccountBO) {
        if (null == approvalAccountBO) {
            return null;
        }
        ApprovalAccountCandidateVO approvalAccountVO = new ApprovalAccountCandidateVO();
        approvalAccountVO.setOid(approvalAccountBO.getOid());
        approvalAccountVO.setGid(approvalAccountBO.getGid());
        approvalAccountVO.setName(approvalAccountBO.getName());
        return approvalAccountVO;
    }


    public static List<ApprovalAccountCandidateVO> approvalAccountConvert(List<ApprovalAccountBizDTO> approvalAccountBOList) {
        if (CollectionUtils.isEmpty(approvalAccountBOList)) {
            return new ArrayList();
        }
        List<ApprovalAccountCandidateVO> approvalAccountVOList = new ArrayList();
        for (ApprovalAccountBizDTO approvalAccountBO : approvalAccountBOList) {
            approvalAccountVOList.add(approvalAccountConvert(approvalAccountBO));
        }
        return approvalAccountVOList;
    }

    public static ApprovalOperateDataDTO operateDataConvert(ApprovalOperateDataRequest approvalOperateDataRequest) {
        if (null == approvalOperateDataRequest) {
            return null;
        }
        ApprovalOperateDataDTO approvalOperateDataDTO = new ApprovalOperateDataDTO();
        approvalOperateDataDTO.setApprovalType(approvalOperateDataRequest.getApprovalType());
        approvalOperateDataDTO.setRemark(approvalOperateDataRequest.getRemark());
        approvalOperateDataDTO.setBizGroupId(approvalOperateDataRequest.getBizGroupId());
        approvalOperateDataDTO.setBizGroupQueryType(approvalOperateDataRequest.getBizGroupQueryType());
        if (CollectionUtils.isNotEmpty(approvalOperateDataRequest.getDatas())) {
            List<ApprovalOperateDataDTO.Data> datas = approvalOperateDataRequest.getDatas().stream().map(elm -> {
                ApprovalOperateDataDTO.Data data = new ApprovalOperateDataDTO.Data();
                data.setApprovalId(elm.getApprovalId());
                data.setProcessId(elm.getProcessId());
                data.setTaskId(elm.getTaskId());
                return data;
            }).collect(Collectors.toList());
            approvalOperateDataDTO.setDatas(datas);
        }
        return approvalOperateDataDTO;
    }

    public static ApprovalOperateDataDTO operateDataConvert(
            String approvalType,
            String remark,
            List<ApprovalInBatchTaskDTO> approvalInBatchTaskDTOS) {
        ApprovalOperateDataDTO approvalOperateDataDTO = new ApprovalOperateDataDTO();
        approvalOperateDataDTO.setApprovalType(approvalType);
        approvalOperateDataDTO.setRemark(remark);
        if (CollectionUtils.isNotEmpty(approvalInBatchTaskDTOS)) {
            List<ApprovalOperateDataDTO.Data> datas =
                    approvalInBatchTaskDTOS.stream()
                            .map(
                                    elm -> {
                                        ApprovalOperateDataDTO.Data data =
                                                new ApprovalOperateDataDTO.Data();
                                        data.setApprovalId(elm.getApprovalCode());
                                        data.setTaskId(elm.getTaskId());
                                        return data;
                                    })
                            .collect(Collectors.toList());
            approvalOperateDataDTO.setDatas(datas);
        }
        return approvalOperateDataDTO;
    }

    public static List<ApprovalOperateDataDTO> operateDataConvert(List<ApprovalOperateDataRequest> approvalOperateDataRequestList) {
        if (CollectionUtils.isEmpty(approvalOperateDataRequestList)) {
            return new ArrayList();
        }
        List<ApprovalOperateDataDTO> approvalOperateDataDTOList = new ArrayList();
        for (ApprovalOperateDataRequest approvalOperateDataRequest : approvalOperateDataRequestList) {
            approvalOperateDataDTOList.add(operateDataConvert(approvalOperateDataRequest));
        }
        return approvalOperateDataDTOList;
    }


    public static ApprovalOperateResponse forwardResultConvert(ApprovalOperateForwardResult forwardResult) {
        ApprovalOperateResponse response = new ApprovalOperateResponse();
        response.setGoToJobCenter(forwardResult.isGoToJobCenter());
        response.setSync(forwardResult.isSync());
        return response;
    }

    public static List<ApprovalDetailVO> approvalDetailBO2VO(List<ApprovalDetailWithLogsDTO> list) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(elm -> {
            ApprovalDetailVO detailVO = new ApprovalDetailVO();
            detailVO.setApprovalId(elm.getApprovalId());
            detailVO.setApprovalStatus(elm.getApprovalStatus());
            detailVO.setApprovalType(elm.getApprovalType());
            detailVO.setBizData(elm.getBizData());
            detailVO.setApprovalLogs(JSON.parseArray(JSON.toJSONString(elm.getApprovalLogs()), ApprovalLogVO.class));
            if (CollectionUtils.isNotEmpty(elm.getApprovalSeals())) {
                detailVO.setApprovalSeals(elm.getApprovalSeals().stream().map(i -> {
                    ApprovalSealVO sealVO = new ApprovalSealVO();
                    sealVO.setApprovalCode(i.getApprovalCode());
                    sealVO.setSealId(i.getSealId());
                    sealVO.setSealName(i.getSealName());
                    sealVO.setSealUrl(i.getSealUrl());
                    return sealVO;
                }).collect(Collectors.toList()));
            }
            detailVO.setInitiatorGid(elm.getInitiatorGid());
            detailVO.setInitiatorOid(elm.getInitiatorOid());
            detailVO.setInitiatorName(elm.getInitiatorName());
            detailVO.setCreateTime(elm.getCreateTime());
            detailVO.setFinishTime(elm.getFinishTime());
            return detailVO;
        }).collect(Collectors.toList());
    }

    public static ApprovalOperateDetailVO operateDetailBO2VO(ApprovalOperateDetailDTO operateDetailBO) {
        if (null == operateDetailBO) {
            return null;
        }
        ApprovalOperateDetailVO approvalOperateDetailVO = new ApprovalOperateDetailVO();
        approvalOperateDetailVO.setApprovalId(operateDetailBO.getApprovalCode());
        approvalOperateDetailVO.setApprovalType(operateDetailBO.getApprovalType());
        approvalOperateDetailVO.setApprovalStatus(operateDetailBO.getApprovalStatus());
        approvalOperateDetailVO.setBizId(operateDetailBO.getBizId());
        approvalOperateDetailVO.setBizType(operateDetailBO.getBizType());
        approvalOperateDetailVO.setBizFlowId(operateDetailBO.getBizFlowId());
        approvalOperateDetailVO.setBizStatus(operateDetailBO.getBizStatus());
        approvalOperateDetailVO.setCanApproval(operateDetailBO.getCanApproval());
        approvalOperateDetailVO.setCanRevoke(operateDetailBO.getCanRevoke());
        approvalOperateDetailVO.setCanRestart(operateDetailBO.getCanRestart());
        approvalOperateDetailVO.setCanView(operateDetailBO.getCanView());
        approvalOperateDetailVO.setCanSign(operateDetailBO.getCanSign());
        approvalOperateDetailVO.setCanCompare(operateDetailBO.getCanCompare());
        approvalOperateDetailVO.setThirdApprovals(thirdApprovalDTO2VO(operateDetailBO.getThirdApprovals()));
        approvalOperateDetailVO.setSupportSummary(operateDetailBO.getSupportSummary());
        approvalOperateDetailVO.setCurrentOperatorTask(approvalTaskBO2VO(operateDetailBO.getCurrentOperatorTask()));
        approvalOperateDetailVO.setBizData(operateDetailBO.getBizData());
        approvalOperateDetailVO.setSubjectOid(operateDetailBO.getSubjectOid());
        approvalOperateDetailVO.setSubjectGid(operateDetailBO.getSubjectGid());
        approvalOperateDetailVO.setCanAddComment(operateDetailBO.getCanAddComment());
        approvalOperateDetailVO.setRemarkList(remarkBO2VO(operateDetailBO.getRemarkList()));
        return approvalOperateDetailVO;
    }


    public static ApprovalUrlInputDTO urlRequest2InputConvert(ApprovalUrlRequest approvalUrlRequest) {
        if (null == approvalUrlRequest) {
            return null;
        }
        ApprovalUrlInputDTO approvalUrlInputDTO = new ApprovalUrlInputDTO();
        approvalUrlInputDTO.setApprovalId(approvalUrlRequest.getApprovalId());
        approvalUrlInputDTO.setApprovalType(approvalUrlRequest.getApprovalType());
        approvalUrlInputDTO.setToken(approvalUrlRequest.getToken());
        approvalUrlInputDTO.setH5Flag(approvalUrlRequest.getH5Flag());
        approvalUrlInputDTO.setAssignedAccountId(approvalUrlRequest.getAssignedAccountId());
        approvalUrlInputDTO.setRedirectUrl(approvalUrlRequest.getRedirectUrl());
        approvalUrlInputDTO.setIsNeedShare(approvalUrlRequest.getIsNeedShare());
        approvalUrlInputDTO.setMenuId(approvalUrlRequest.getMenuId());
        return approvalUrlInputDTO;
    }

    public static String tableHeadConfig2ConfigJsonConvert(ApprovalTableHeadConfigRequest request){
        if(request == null || CollectionUtils.isEmpty(request.getTableHeadConfigList())){
            return null;
        }
        int sort = 0;
        List<ApprovalTableHeadDTO> dtoList = new ArrayList<>();
        for (ApprovalTableHeadVo vo:request.getTableHeadConfigList() ) {
            ApprovalTableHeadDTO dto= new ApprovalTableHeadDTO();
            dto.setCode(vo.getCode());
            dto.setHasFreeze(vo.getHasFreeze());
            dto.setSort(sort);
            dtoList.add(dto);
            sort++;
        }
        return JSONObject.toJSONString(dtoList);
    }

    /**
     * 组装审批操作预校验返回结果
     * @param preCheckDTO
     * @return
     */
    public static ApprovalOperatePreCheckResponse buildApprovalOperatePreCheckResponse(ApprovalOperatePreCheckDTO preCheckDTO) {
        ApprovalOperatePreCheckResponse response = new ApprovalOperatePreCheckResponse();
        response.setPass(preCheckDTO.isPass());
        response.setMissingCertSubjectIds(Sets.newHashSet());
        response.setMissingCertResult(org.assertj.core.util.Lists.newArrayList());
        if (CollectionUtils.isEmpty(preCheckDTO.getMissingCertSubjectIds())) {
            return response;
        }
        preCheckDTO.getMissingCertSubjectIds().forEach(i -> {
            if (CollectionUtils.isEmpty(i.getMissingCertSubjectIds())) {
                return;
            }
            response.getMissingCertSubjectIds().addAll(i.getMissingCertSubjectIds());
            MissingCertPreCheckVO preCheckVO = new MissingCertPreCheckVO();
            preCheckVO.setMissingCertAppId(i.getMissingCertAppId());
            preCheckVO.setMissingCertSubjectIds(i.getMissingCertSubjectIds());
            response.getMissingCertResult().add(preCheckVO);
        });
        return response;
    }

    public static List<ApprovalBizContractFileVO> convertBizContractFileVOs(List<ApprovalBizContractFileDTO> contractFileDTOS) {
        if (CollectionUtils.isEmpty(contractFileDTOS)) {
            return Lists.newArrayList();
        }
        return contractFileDTOS.stream().map(i -> {
            ApprovalBizContractFileVO contractFileVO = new ApprovalBizContractFileVO();
            contractFileVO.setFileId(i.getFileId());
            contractFileVO.setFileName(i.getFileName());
            contractFileVO.setContractNo(i.getContractNo());
            return contractFileVO;

        }).collect(Collectors.toList());
    }
}
