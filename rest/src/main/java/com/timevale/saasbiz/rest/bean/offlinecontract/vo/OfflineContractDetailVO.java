package com.timevale.saasbiz.rest.bean.offlinecontract.vo;

import lombok.Data;

/**
 * 线下合同信息详情
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Data
public class OfflineContractDetailVO extends OfflineContractVO {

    /** 线下合同对应的导入流程id */
    private String recordProcessId;

    /** 线下合同对应的processId */
    private String processId;

    /** 导入状态 */
    private String status;

    /** 失败原因 */
    private String failReason;
}
