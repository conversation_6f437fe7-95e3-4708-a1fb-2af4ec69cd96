package com.timevale.saasbiz.rest.bean.contractaudit.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@ApiModel(value = "创建合同审批返回")
public class CreateContractAuditWebResponse extends ToString {
    @ApiModelProperty(value = "审查记录id")
    private String recordId;

    @ApiModelProperty(value = "审查页面url")
    private String url;
}
