package com.timevale.saasbiz.rest.bean.contractaudit.response;

import com.timevale.saasbiz.rest.bean.contractaudit.vo.ContractAuditRecordVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@ApiModel(value = "合同审查查询结果")
public class ContractAuditPageQueryResponse {
    @ApiModelProperty(value = "总记录数")
    private Long total;
    @ApiModelProperty(value = "合同审查任务列表")
    private List<ContractAuditRecordVO> records;
}
