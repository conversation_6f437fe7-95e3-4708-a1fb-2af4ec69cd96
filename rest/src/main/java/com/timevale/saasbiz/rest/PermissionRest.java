package com.timevale.saasbiz.rest;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.PermissionApplyMsgInputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.output.PermissionApplyMsgOutputDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.usercenter.PermissionApplyMsgResponse;
import com.timevale.saasbiz.service.usercenter.PermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * 权限相关
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Api(tags = "权限相关")
@ExternalService
@RestMapping(path = "/v2/saas-common")
public class PermissionRest {

    @Autowired private PermissionService permissionService;

    @RestMapping(path = "/permission/apply/msg/{code}", method = RequestMethod.GET)
    @ApiOperation(value = "查询权限申请文案")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<PermissionApplyMsgResponse> queryPermissionApplyMsg(
            @RequestHeader(HEADER_TENANT_ID) String tenantId, @PathVariable String code) {
        PermissionApplyMsgInputDTO input = new PermissionApplyMsgInputDTO();
        input.setOrgId(tenantId);
        input.setPermissionCode(code);
        // 获取申请文案
        PermissionApplyMsgOutputDTO outputDTO = permissionService.getPermissionApplyMsg(input);
        // 组装返回结果
        PermissionApplyMsgResponse response = new PermissionApplyMsgResponse();
        response.setNoPermissionHintText(outputDTO.getNoPermissionHintText());
        response.setCopySuccessText(outputDTO.getCopySuccessText());
        response.setCopyText(outputDTO.getCopyText());
        return RestResult.success(response);
    }
}
