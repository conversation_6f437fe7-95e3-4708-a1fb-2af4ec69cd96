package com.timevale.saasbiz.rest.converter;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.dedicatedcloud.dto.DedicatedCloudAuthRelationDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.dto.DedicatedCloudDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.input.DedicatedCloudAuthAppIdInputDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.input.DedicatedCloudCreateInputDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.input.DedicatedCloudUpdateInputDTO;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.request.DedicatedCloudAuthAppIdRequest;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.request.DedicatedCloudCreateRequest;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.request.DedicatedCloudUpdateRequest;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.response.DedicatedCloudAuthAppIdVO;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.response.DedicatedCloudAuthRelationVO;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.response.DedicatedCloudVO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/1 10:39
 */
public class DedicatedCloudResetConverter {


    public static DedicatedCloudCreateInputDTO createConvert(DedicatedCloudCreateRequest before) {
        if (null == before) {
            return null;
        }
        DedicatedCloudCreateInputDTO after = new DedicatedCloudCreateInputDTO();
        after.setProjectName(before.getProjectName());
        after.setAppId(before.getAppId());
        after.setServerUrl(before.getServerUrl());
        after.setAuthAppIds(convert(before.getAuthAppIds()));

        return after;
    }

    public static DedicatedCloudUpdateInputDTO updateConvert(DedicatedCloudUpdateRequest before) {
        if (null == before) {
            return null;
        }
        DedicatedCloudUpdateInputDTO after = new DedicatedCloudUpdateInputDTO();
        after.setDedicatedCloudId(before.getDedicatedCloudId());
        after.setProjectName(before.getProjectName());
        after.setServerUrl(before.getServerUrl());
        after.setAuthAppIds(convert(before.getAuthAppIds()));
        return after;
    }


    public static DedicatedCloudAuthAppIdInputDTO convert(DedicatedCloudAuthAppIdRequest before) {
        if (null == before) {
            return null;
        }
        DedicatedCloudAuthAppIdInputDTO after = new DedicatedCloudAuthAppIdInputDTO();
        after.setAppId(before.getAppId());
        after.setSubjectGid(before.getSubjectGid());
        return after;
    }


    public static List<DedicatedCloudAuthAppIdInputDTO> convert(List<DedicatedCloudAuthAppIdRequest> beforeList) {
        if (CollectionUtils.isEmpty(beforeList)) {
            return new ArrayList<>();
        }
        List<DedicatedCloudAuthAppIdInputDTO> afterList = new ArrayList();
        for (DedicatedCloudAuthAppIdRequest before : beforeList) {
            afterList.add(convert(before));
        }
        return afterList;
    }

    public static List<DedicatedCloudAuthRelationVO> authRelationConvert(List<DedicatedCloudAuthRelationDTO> beforeList) {
        if (CollectionUtils.isEmpty(beforeList)) {
            return new ArrayList<>();
        }
        return beforeList.stream()
                // 只查询授权企业的
                .map(elm -> {
                    DedicatedCloudAuthRelationVO authRelationDTO = new DedicatedCloudAuthRelationVO();
                    authRelationDTO.setSubjectGid(elm.getSubjectGid());
                    authRelationDTO.setSubjectOid(elm.getSubjectOid());
                    authRelationDTO.setSubjectName(elm.getSubjectName());
                    authRelationDTO.setAppList(OpenPlatformConvertor.openAppConvert(elm.getAppList()));
                    return authRelationDTO;
                }).collect(Collectors.toList());
    }

    public static DedicatedCloudVO dedicatedCloudConvert(DedicatedCloudDTO before) {
        if (null == before) {
            return null;
        }
        DedicatedCloudVO after = new DedicatedCloudVO();
        after.setDedicatedCloudId(before.getDedicatedCloudId());
        after.setProjectName(before.getProjectName());
        after.setAppId(before.getAppId());
        after.setStatus(before.getStatus());
        after.setSubjectGid(before.getSubjectGid());
        after.setSubjectName(before.getSubjectName());
        after.setServerUrl(before.getServerUrl());
        after.setEffective(before.getEffective());
        after.setProjectVersion(before.getProjectVersion());
        after.setServiceStatus(before.getServiceStatus());
        after.setCreateTime(before.getCreateTime());
        after.setModifyTime(before.getModifyTime());
        after.setCreateByOid(before.getCreateByOid());
        after.setCreateName(before.getCreateName());
        after.setCreateContact(before.getCreateContact());
        after.setModifyByOid(before.getModifyByOid());
        after.setModifyName(before.getModifyName());
        after.setModifyContact(before.getModifyContact());

        after.setOrderExpire(before.getOrderExpire());
        after.setCanEditStatus(before.getCanEditStatus());
        if (CollectionUtils.isNotEmpty(before.getAuthAppIds())) {
            after.setAuthAppIds(before.getAuthAppIds().stream().map(elm -> {
                DedicatedCloudAuthAppIdVO authAppIdVO = new DedicatedCloudAuthAppIdVO();
                authAppIdVO.setAppId(elm.getAppId());
                authAppIdVO.setSubjectGid(elm.getSubjectGid());
                authAppIdVO.setAppName(elm.getAppName());
                authAppIdVO.setSubjectOid(elm.getSubjectOid());
                authAppIdVO.setSubjectName(elm.getSubjectName());
                return authAppIdVO;
            }).collect(Collectors.toList()));
        }

        return after;
    }


    public static List<DedicatedCloudVO> dedicatedCloudConvert(List<DedicatedCloudDTO> beforeList) {
        if (CollectionUtils.isEmpty(beforeList)) {
            return new ArrayList();
        }
        List<DedicatedCloudVO> afterList = new ArrayList();
        for (DedicatedCloudDTO before : beforeList) {
            afterList.add(dedicatedCloudConvert(before));
        }
        return afterList;
    }
}
