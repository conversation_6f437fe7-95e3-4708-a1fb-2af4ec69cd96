package com.timevale.saasbiz.rest.converter;

import com.timevale.saasbiz.model.bean.approval.dto.ApprovalContractAnalysisDetailDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalContractAnalysisFileUrlDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalContractAnalysisPageDTO;
import com.timevale.saasbiz.model.bean.approval.dto.CompareRecordDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalContractAnalysisCreateInputDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalContractAnalysisPageInputDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalContractAnalysisQueryResultInputDTO;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalContractAnalysisCreateRequest;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalContractAnalysisPageRequest;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalContractAnalysisQueryResultRequest;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisDetailResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisFileUrlResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisPageResponse;
import com.timevale.saasbiz.rest.bean.approval.vo.ApprovalContractAnalysisRelationVO;
import com.timevale.saasbiz.rest.bean.approval.vo.CompareRecordVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.stream.Collectors;

/**
 * TODO 功能说明
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
public class ApprovalContractAnalysisConverter {

    public static ApprovalContractAnalysisCreateInputDTO
            convert2ApprovalContractAnalysisCreateInputDTO(
                    ApprovalContractAnalysisCreateRequest request, String accountOid) {
        ApprovalContractAnalysisCreateInputDTO inputDTO =
                new ApprovalContractAnalysisCreateInputDTO();
        inputDTO.setAnalysisBizId(request.getAnalysisBizId());
        inputDTO.setAnalysisBizType(request.getAnalysisBizType());
        inputDTO.setApprovalId(request.getApprovalId());
        inputDTO.setTaskId(request.getTaskId());
        inputDTO.setOriginFileId(request.getOriginFileId());
        inputDTO.setOriginFileName(request.getOriginFileName());
        inputDTO.setOriginFileHash(request.getOriginFileHash());
        inputDTO.setCompareFileId(request.getCompareFileId());
        inputDTO.setCompareFileName(request.getCompareFileName());
        inputDTO.setCompareFileHash(request.getCompareFileHash());
        inputDTO.setAccountOid(accountOid);
        return inputDTO;
    }

    public static ApprovalContractAnalysisDetailResponse
            conver2ApprovalContractAnalysisDetailResponse(
                    ApprovalContractAnalysisDetailDTO detailDTO) {
        ApprovalContractAnalysisDetailResponse detailResponse =
                new ApprovalContractAnalysisDetailResponse();
        detailResponse.setId(detailDTO.getId());
        detailResponse.setAnalysisBizId(detailDTO.getAnalysisBizId());
        detailResponse.setAnalysisBizType(detailDTO.getAnalysisBizType());
        detailResponse.setApprovalId(detailDTO.getApprovalId());
        detailResponse.setTaskId(detailDTO.getTaskId());
        detailResponse.setFileId(detailDTO.getFileId());
        detailResponse.setAccountOid(detailDTO.getAccountOid());
        detailResponse.setAccountGid(detailDTO.getAccountGid());
        detailResponse.setCompareId(detailDTO.getCompareId());
        detailResponse.setCompareResult(detailDTO.getCompareResult());
        detailResponse.setReason(detailDTO.getReason());
        detailResponse.setCreateTime(detailDTO.getCreateTime());
        detailResponse.setModifyTime(detailDTO.getModifyTime());
        return detailResponse;
    }

    public static ApprovalContractAnalysisFileUrlResponse
            convert2ApprovalContractAnalysisFileUrlDTO(
                    ApprovalContractAnalysisFileUrlDTO analysisFileUrlDTO) {
        ApprovalContractAnalysisFileUrlResponse response =
                new ApprovalContractAnalysisFileUrlResponse();
        response.setOriginDownloadUrl(analysisFileUrlDTO.getOriginDownloadUrl());
        response.setCompareDownloadUrl(analysisFileUrlDTO.getCompareDownloadUrl());
        return response;
    }

    public static ApprovalContractAnalysisPageInputDTO convert2ApprovalContractAnalysisPageInputDTO(
            ApprovalContractAnalysisPageRequest request, String accountOid) {
        ApprovalContractAnalysisPageInputDTO inputDTO = new ApprovalContractAnalysisPageInputDTO();
        inputDTO.setAccountOid(accountOid);
        inputDTO.setAnalysisBizId(request.getAnalysisBizId());
        inputDTO.setAnalysisBizType(request.getAnalysisBizType());
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        return inputDTO;
    }

    public static ApprovalContractAnalysisPageResponse convert2ApprovalContractAnalysisPageResponse(
            ApprovalContractAnalysisPageDTO pageDTO) {
        ApprovalContractAnalysisPageResponse response = new ApprovalContractAnalysisPageResponse();
        response.setTotal(pageDTO.getTotal());
        response.setCompareRecordList(
                CollectionUtils.isEmpty(pageDTO.getCompareRecordBeans())
                        ? Collections.emptyList()
                        : pageDTO.getCompareRecordBeans().stream()
                                .map(ApprovalContractAnalysisConverter::convert2CompareRecordVO)
                                .collect(Collectors.toList()));
        return response;
    }

    public static CompareRecordVO convert2CompareRecordVO(CompareRecordDTO compareRecordDTO) {
        CompareRecordVO recordVO = new CompareRecordVO();
        recordVO.setRecordId(compareRecordDTO.getRecordId());
        recordVO.setOriginFileId(compareRecordDTO.getOriginFileId());
        recordVO.setOriginFileName(compareRecordDTO.getOriginFileName());
        recordVO.setOriginFileHash(compareRecordDTO.getOriginFileHash());
        recordVO.setCompareFileId(compareRecordDTO.getCompareFileId());
        recordVO.setCompareFileName(compareRecordDTO.getCompareFileName());
        recordVO.setCompareFileHash(compareRecordDTO.getCompareFileHash());
        recordVO.setCompareTime(compareRecordDTO.getCompareTime());
        recordVO.setJobStatus(compareRecordDTO.getJobStatus());
        recordVO.setHasCompare(compareRecordDTO.getHasCompare());
        recordVO.setFailMessage(compareRecordDTO.getFailMessage());
        recordVO.setCanRetry(compareRecordDTO.getCanRetry());
        recordVO.setCompareStatus(compareRecordDTO.getCompareStatus());
        return recordVO;
    }

    public static ApprovalContractAnalysisQueryResultInputDTO convert2ApprovalContractAnalysisQueryResultInputDTO(String accountOid,ApprovalContractAnalysisQueryResultRequest request){
        ApprovalContractAnalysisQueryResultInputDTO inputDTO=new ApprovalContractAnalysisQueryResultInputDTO();
        inputDTO.setAccountOid(accountOid);
        inputDTO.setAnalysisBizId(request.getAnalysisBizId());
        inputDTO.setAnalysisBizType(request.getAnalysisBizType());
        inputDTO.setFileIds(request.getFileIds());
        return inputDTO;

    }

    public static ApprovalContractAnalysisRelationVO convert2ApprovalContractAnalysisRelationVO(ApprovalContractAnalysisDetailDTO detailDTO){
        ApprovalContractAnalysisRelationVO relationVO =
                new ApprovalContractAnalysisRelationVO();
        relationVO.setAnalysisBizId(detailDTO.getAnalysisBizId());
        relationVO.setAnalysisBizType(detailDTO.getAnalysisBizType());
        relationVO.setApprovalId(detailDTO.getApprovalId());
        relationVO.setTaskId(detailDTO.getTaskId());
        relationVO.setFileId(detailDTO.getFileId());
        relationVO.setAccountOid(detailDTO.getAccountOid());
        relationVO.setAccountGid(detailDTO.getAccountGid());
        relationVO.setCompareId(detailDTO.getCompareId());
        relationVO.setCompareResult(detailDTO.getCompareResult());
        relationVO.setReason(detailDTO.getReason());
        relationVO.setCreateTime(detailDTO.getCreateTime());
        relationVO.setModifyTime(detailDTO.getModifyTime());
        return relationVO;
    }
}
