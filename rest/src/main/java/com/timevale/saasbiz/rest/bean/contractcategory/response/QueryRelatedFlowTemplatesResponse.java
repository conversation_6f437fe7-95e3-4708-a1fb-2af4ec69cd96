package com.timevale.saasbiz.rest.bean.contractcategory.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.contractcategory.request.RelateContractCategoryFlowTemplatesRequest;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.RelateFlowTemplateFileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询合同类型关联的流程模板列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("查询合同类型关联的流程模板列表响应数据")
public class QueryRelatedFlowTemplatesResponse extends ToString {

    @ApiModelProperty("关联的流程模板文件列表")
    private List<RelateFlowTemplateFileVO> relateFlowTemplates;
}
