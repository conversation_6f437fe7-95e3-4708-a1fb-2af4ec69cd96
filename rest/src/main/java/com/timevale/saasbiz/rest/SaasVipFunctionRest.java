package com.timevale.saasbiz.rest;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.weaver.utils.RequestContext;
import com.timevale.saas.common.manage.common.service.model.base.BaseResult;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.vip.dto.input.ApplyTrialFunctionCodeInputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.input.GetFunctionIllustrateInputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.input.QueryApplyLogInputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.input.QueryFunctionInfoInputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.output.ApplyTrialFunctionCodeOutputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.output.GetFunctionIllustrateOutputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.output.QueryApplyLogOutputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.output.QueryFunctionInfoOutputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.output.UpgradeIllustrateOutputDTO;
import com.timevale.saasbiz.model.constants.InfoCollectConstants;
import com.timevale.saasbiz.model.enums.LanguageEnum;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.vip.ApplyTrialQueryRequest;
import com.timevale.saasbiz.rest.bean.vip.ApplyTrialQueryResponse;
import com.timevale.saasbiz.rest.bean.vip.ApplyTrialRequest;
import com.timevale.saasbiz.rest.bean.vip.ApplyTrialResponse;
import com.timevale.saasbiz.rest.bean.vip.GetFunctionIllustrateRequest;
import com.timevale.saasbiz.rest.bean.vip.GetFunctionIllustrateResponse;
import com.timevale.saasbiz.rest.bean.vip.QueryVersionFunctionInfosResponse;
import com.timevale.saasbiz.rest.bean.vip.UpgradeIllustrateResponse;
import com.timevale.saasbiz.service.vip.FunctionIllustrateService;
import com.timevale.saasbiz.service.vip.FunctionInfoService;
import com.timevale.saasbiz.service.vip.impl.TrialFunctionCodeServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2023-12-28 18:02
 */
@Api(tags = "会员中心管理")
@Validated
@ExternalService
@RestMapping(path = "/v2/saas-common/vipmanage")
public class SaasVipFunctionRest {

    @Autowired private MapperFactory mapperFactory;

    @Autowired private FunctionIllustrateService functionIllustrateService;

    @Resource
    private FunctionInfoService functionInfoService;
    
    @Autowired
    private TrialFunctionCodeServiceImpl trialFunctionCodeService;

    @ApiOperation(value = "功能引导", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/function-illustrate", method = RequestMethod.POST)
    public BaseResult<GetFunctionIllustrateResponse> guideFunctionIllustrate(@RequestBody GetFunctionIllustrateRequest request) {
        GetFunctionIllustrateInputDTO inputDTO = new GetFunctionIllustrateInputDTO();
        inputDTO.setFunctionCode(request.getFunctionCode());
        inputDTO.setSubFunctionCode(request.getSubFunctionCode());
        inputDTO.setExtendParam(request.getExtendParam());
        String lang = RequestContextExtUtils.getLanguage();
        inputDTO.setLang(StringUtils.isNotBlank(lang) ? lang_format(lang) : LanguageEnum.ZH_CN.getType());
        inputDTO.setTenantOid(RequestContextExtUtils.getTenantId());
        inputDTO.setOperatorOid(RequestContextExtUtils.getOperatorId());
        inputDTO.setClientId(RequestContextExtUtils.getClientId());
        inputDTO.setFunctionLimitIllustrate(request.isFunctionLimitIllustrate());
        inputDTO.setFunctionLimitScene(request.getFunctionLimitScene());
        if (inputDTO.getExtendParam() == null) {
            inputDTO.setExtendParam(new HashMap<>());
        }
        // todo tianlei 临时升级使用，后续删除掉
        inputDTO.getExtendParam().put(InfoCollectConstants.UPGRADE_HEADER, upgrade());
        GetFunctionIllustrateOutputDTO outputDTO = functionIllustrateService.guideFunctionIllustrate(inputDTO);
        GetFunctionIllustrateResponse response = mapperFactory.getMapperFacade()
                .map(outputDTO, GetFunctionIllustrateResponse.class);
        return BaseResult.success(response);
    }

    private String lang_format(String lang) {
        String[] langs = lang.split("-");
        if (langs.length < 2) {
            return lang;
        }
        return String.format("%s-%s", langs[0].toLowerCase(), langs[1].toUpperCase());
    }

    private boolean upgrade() {
        String upgrade = RequestContext.getRequest().getHeader(InfoCollectConstants.UPGRADE_HEADER);
        return Boolean.TRUE.toString().equals(upgrade);
    }

    @ApiOperation(value = "版本功能信息列表", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/version/function-infos", method = RequestMethod.POST)
    public BaseResult<QueryVersionFunctionInfosResponse> queryVersionInfos() {
        String tenantId = RequestContextExtUtils.getTenantId();
        QueryFunctionInfoInputDTO inputDTO = new QueryFunctionInfoInputDTO();
        inputDTO.setAccountId(tenantId);

        QueryFunctionInfoOutputDTO outputDTO = functionInfoService.queryFunctionInfo(inputDTO);
        return BaseResult.success(convert(outputDTO));
    }

    private QueryVersionFunctionInfosResponse convert(QueryFunctionInfoOutputDTO outputDTO) {
        QueryVersionFunctionInfosResponse response = new QueryVersionFunctionInfosResponse();
        List<QueryVersionFunctionInfosResponse.FunctionInfo> infos = Lists.newArrayList();

        for (QueryFunctionInfoOutputDTO.FunctionInfo functionInfo : outputDTO.getFunctionInfos()) {
            QueryVersionFunctionInfosResponse.FunctionInfo info = new QueryVersionFunctionInfosResponse.FunctionInfo();
            info.setDesc(functionInfo.getDesc());
            info.setIconUrl(functionInfo.getIconUrl());
            infos.add(info);
        }

        response.setFunctionInfos(infos);
        return response;
    }

    @ApiOperation(value = "获取升级引导信息", httpMethod = "POST")
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/upgrade-illustrate", method = RequestMethod.POST)
    public BaseResult<UpgradeIllustrateResponse> getUpgradeIllustrate(
            @ApiParam(value = "引导二维码类型") @RequestParam(required = false) String illustrateQRCodeType) {
        String tenantId = RequestContextExtUtils.getTenantId();
        String client = RequestContextExtUtils.getClientId();
        UpgradeIllustrateOutputDTO outputDTO = functionIllustrateService.getUpgradeIllustrate(tenantId, client,illustrateQRCodeType);

        UpgradeIllustrateResponse response = new UpgradeIllustrateResponse();
        response.setQrCodeType(outputDTO.getQrCodeType());
        response.setQrCodeFileUrl(outputDTO.getQrCodeFileUrl());
        return BaseResult.success(response);
    }

    @ApiOperation(value = "申请试用", httpMethod = "POST")
    @RestMapping(path = "/apply-trial", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ApplyTrialResponse> applyTrial(@RequestBody ApplyTrialRequest request) {
        String operatorId = RequestContextExtUtils.getOperatorId();
        String tenantId = RequestContextExtUtils.getTenantId();
        String clientId = RequestContextExtUtils.getClientId();
        ApplyTrialFunctionCodeOutputDTO outputDTO = 
                trialFunctionCodeService.applyTrialFunctionCode(buildApplyTrialFunctionCodeInputDTO(request, operatorId, tenantId, clientId));
        return BaseResult.success(new ApplyTrialResponse(outputDTO.getFlag(), outputDTO.getTips(), outputDTO.getQrCodeFileUrl(), outputDTO.getQrCodeType()));
    }

    private ApplyTrialFunctionCodeInputDTO buildApplyTrialFunctionCodeInputDTO(ApplyTrialRequest request, String operatorId, 
                                                                               String tenantId, String clientId) {
        ApplyTrialFunctionCodeInputDTO inputDTO = new ApplyTrialFunctionCodeInputDTO();
        inputDTO.setClient(clientId);
        inputDTO.setFuncCode(request.getFunctionCode());
        inputDTO.setApplicantOid(request.getApplicantOid());
        inputDTO.setApplicantSubjectOid(request.getApplicantSubjectOid());
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setOperatorSubjectOid(tenantId);
        return inputDTO;
    }

    @ApiOperation(value = "申请落地页回显", httpMethod = "POST")
    @RestMapping(path = "/query-trial-function-apply", method = RequestMethod.POST)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public BaseResult<ApplyTrialQueryResponse> queryTrialFunctionApply(@RequestBody ApplyTrialQueryRequest request) {
        String operatorId = RequestContextExtUtils.getOperatorId();
        String tenantId = RequestContextExtUtils.getTenantId();
        String language = RequestContextExtUtils.getLanguage();
        QueryApplyLogOutputDTO logOutputDTO = trialFunctionCodeService.queryApplyLog(buildQueryApplyLogInputDTO(request, operatorId, tenantId, language));
        return BaseResult.success(convertToQueryResponse(logOutputDTO));
    }

    private QueryApplyLogInputDTO buildQueryApplyLogInputDTO(ApplyTrialQueryRequest request ,String operatorId ,String tenantId ,String language) {
        QueryApplyLogInputDTO inputDTO = new QueryApplyLogInputDTO();
        inputDTO.setLanguage(language);
        inputDTO.setApplicantOid(request.getApplicantOid());
        inputDTO.setOperatorId(operatorId);
        inputDTO.setSubjectOid(tenantId);
        inputDTO.setFuncCode(request.getFunctionCode());
        inputDTO.setApplicantSubjectOid(request.getApplicantSubjectOid());
        return inputDTO;
    }

    private ApplyTrialQueryResponse convertToQueryResponse(QueryApplyLogOutputDTO logOutputDTO) {
        ApplyTrialQueryResponse applyTrialQueryResponse = new ApplyTrialQueryResponse();
        applyTrialQueryResponse.setFunctionIntroduction(logOutputDTO.getFunctionIntroduction());
        applyTrialQueryResponse.setApplicationFunctionTitle(logOutputDTO.getApplicationFunctionTitle());
        applyTrialQueryResponse.setApplicationFunctionCode(logOutputDTO.getApplicationFunctionCode());
        applyTrialQueryResponse.setApplicationName(logOutputDTO.getApplicationName());
        applyTrialQueryResponse.setApplicationTime(logOutputDTO.getApplicationTime());
        applyTrialQueryResponse.setVipLogoUrl(logOutputDTO.getVipLogoUrl());
        applyTrialQueryResponse.setApplicantOid(logOutputDTO.getApplicantOid());
        applyTrialQueryResponse.setApplicantSubjectOid(logOutputDTO.getApplicantSubjectOid());
        return applyTrialQueryResponse;
    }
}
