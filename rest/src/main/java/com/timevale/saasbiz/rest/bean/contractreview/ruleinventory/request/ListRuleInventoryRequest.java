package com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListRuleInventoryRequest extends ToString {

    @ApiModelProperty("审查清单名称")
    @Length(max = 20, message = "审查清单名称不能超过20字符")
    private String inventoryName;

    @ApiModelProperty("合同类型")
    private String contractType;

    @ApiModelProperty("审查视角")
    private String contractView;
}