package com.timevale.saasbiz.rest.converter;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.witness.bo.WitnessFlowBO;
import com.timevale.saasbiz.model.bean.witness.bo.WitnessFlowSignInfoBO;
import com.timevale.saasbiz.model.bean.witness.dto.output.*;
import com.timevale.saasbiz.rest.bean.witness.response.*;
import com.timevale.saasbiz.rest.bean.witness.vo.WitnessFlowVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 天印流程接口response转换类
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
public class WitnessFlowResponseConverter {

    /**
     * 转换对外返回的天印流程列表
     *
     * @param output
     * @return
     */
    public static QueryWitnessFlowsResponse convertQueryWitnessFlowsResponse(
            QueryWitnessFlowsOutputDTO output) {
        QueryWitnessFlowsResponse response = new QueryWitnessFlowsResponse();
        response.setTotal(output.getTotal());
        response.setFlows(convertWitnessFlowVOs(output.getFlows()));
        return response;
    }

    /**
     * 转换对外返回的天印流程概要信息
     *
     * @param output
     * @return
     */
    public static QueryWitnessFlowOutlineResponse convertQueryWitnessFlowOutlineResponse(
            QueryWitnessFlowOutlineOutputDTO output) {
        QueryWitnessFlowOutlineResponse response = new QueryWitnessFlowOutlineResponse();
        response.setFlowId(output.getFlowId());
        response.setFlowName(output.getFlowName());
        response.setHashSign(output.isHashSign());
        response.setContainsAutoExecute(output.isContainsAutoExecute());
        response.setFlowStartTime(output.getFlowStartTime());
        response.setFlowEndTime(output.getFlowEndTime());
        if (CollectionUtils.isNotEmpty(output.getSignDocs())) {
            response.setSignDocs(
                    output.getSignDocs().stream()
                            .map(i -> new QueryWitnessFlowOutlineResponse.WitnessDocVO(i.getFileName()))
                            .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(output.getSigners())) {
            response.setSigners(
                    output.getSigners().stream()
                            .map(i -> convertWitnessSignerVO(i))
                            .collect(Collectors.toList()));
        }

        return response;
    }

    /**
     * 转换对外返回的批量签署流程列表
     *
     * @param output
     * @return
     */
    public static GetWitnessFlowBatchSignListResponse convertBatchSignListResponse(
            GetWitnessFlowBatchSignListOutputDTO output) {
        GetWitnessFlowBatchSignListResponse response = new GetWitnessFlowBatchSignListResponse();
        response.setBatchSerialId(output.getBatchSerialId());
        if (null != output.getBatchSignList()) {
            response.setBatchSignList(
                    output.getBatchSignList().stream()
                            .map(i -> convertWitnessAppFlowVO(i))
                            .collect(Collectors.toList()));
        }
        response.setNotSupportBatchSignList(
                convertWitnessFlowVOs(output.getNotSupportBatchSignList()));

        return response;
    }

    /**
     * 转换对外返回的批量签署地址
     *
     * @param output
     * @return
     */
    public static GetWitnessFlowBatchSignUrlResponse convertBatchSignUrlResponse(
            GetWitnessFlowBatchSignUrlOutputDTO output) {
        GetWitnessFlowBatchSignUrlResponse response = new GetWitnessFlowBatchSignUrlResponse();
        response.setSignSerialId(output.getSignSerialId());
        response.setBatchSignUrl(output.getBatchSignUrl());
        return response;
    }

    /**
     * 转换对外返回的当前批量签署批次对应的流程列表
     *
     * @param output
     * @return
     */
    public static GetWitnessFlowBatchSignSerialInfoResponse convertBatchSignSerialInfoResponse(
            GetWitnessFlowBatchSignSerialInfoOutputDTO output) {
        GetWitnessFlowBatchSignSerialInfoResponse response =
                new GetWitnessFlowBatchSignSerialInfoResponse();
        if (CollectionUtils.isNotEmpty(output.getFlows())) {
            response.setFlows(
                    output.getFlows().stream()
                            .map(i -> convertWitnessFlowSignInfoVO(i))
                            .collect(Collectors.toList()));
        }
        return response;
    }

    /**
     * 转换天印流程信息列表
     *
     * @param witnessFlowBOS
     * @return
     */
    private static List<WitnessFlowVO> convertWitnessFlowVOs(List<WitnessFlowBO> witnessFlowBOS) {
        if (CollectionUtils.isEmpty(witnessFlowBOS)) {
            return Lists.newArrayList();
        }
        return witnessFlowBOS.stream()
                .map(i -> convertWitnessFlowVO(i))
                .collect(Collectors.toList());
    }

    /**
     * 转换流程签署信息， 仅包含流程id+人+主体
     *
     * @param witnessFlowSignInfoBO
     * @return
     */
    private static GetWitnessFlowBatchSignSerialInfoResponse.WitnessFlowSignInfoVO
            convertWitnessFlowSignInfoVO(WitnessFlowSignInfoBO witnessFlowSignInfoBO) {
        GetWitnessFlowBatchSignSerialInfoResponse.WitnessFlowSignInfoVO witnessFlowSignInfoVO =
                new GetWitnessFlowBatchSignSerialInfoResponse.WitnessFlowSignInfoVO();
        witnessFlowSignInfoVO.setAccountId(witnessFlowSignInfoBO.getAccountId());
        witnessFlowSignInfoVO.setSubjectId(witnessFlowSignInfoBO.getSubjectId());
        witnessFlowSignInfoVO.setFlowId(witnessFlowSignInfoBO.getFlowId());
        return witnessFlowSignInfoVO;
    }

    /**
     * 转换基于appId聚合的流程列表
     *
     * @param witnessAppFlowsBO
     * @return
     */
    private static GetWitnessFlowBatchSignListResponse.WitnessAppFlowsVO convertWitnessAppFlowVO(
            GetWitnessFlowBatchSignListOutputDTO.WitnessAppFlowsBO witnessAppFlowsBO) {
        GetWitnessFlowBatchSignListResponse.WitnessAppFlowsVO witnessAppFlowsVO =
                new GetWitnessFlowBatchSignListResponse.WitnessAppFlowsVO();
        witnessAppFlowsVO.setAppId(witnessAppFlowsBO.getAppId());
        witnessAppFlowsVO.setAppName(witnessAppFlowsBO.getAppName());
        witnessAppFlowsVO.setFlows(convertWitnessFlowVOs(witnessAppFlowsBO.getFlows()));
        return witnessAppFlowsVO;
    }

    /**
     * 转换概要信息中的签署人信息
     *
     * @param witnessSignerBO
     * @return
     */
    private static QueryWitnessFlowOutlineResponse.WitnessSignerVO convertWitnessSignerVO(
            QueryWitnessFlowOutlineOutputDTO.WitnessSignerBO witnessSignerBO) {
        QueryWitnessFlowOutlineResponse.WitnessSignerVO witnessSignerVO =
                new QueryWitnessFlowOutlineResponse.WitnessSignerVO();
        witnessSignerVO.setSignerName(witnessSignerBO.getSignerName());
        witnessSignerVO.setSignerAccountId(witnessSignerBO.getSignerAccountId());
        witnessSignerVO.setSubjectName(witnessSignerBO.getSubjectName());
        witnessSignerVO.setSubjectAccountId(witnessSignerBO.getSubjectAccountId());
        witnessSignerVO.setSubjectAccountType(witnessSignerBO.getSubjectAccountType());
        witnessSignerVO.setSignStatus(witnessSignerBO.getSignStatus());

        return witnessSignerVO;
    }

    /**
     * 转换天印流程信息
     *
     * @param witnessFlowBO
     * @return
     */
    private static WitnessFlowVO convertWitnessFlowVO(WitnessFlowBO witnessFlowBO) {
        WitnessFlowVO witnessFlowVO = new WitnessFlowVO();
        witnessFlowVO.setAppId(witnessFlowBO.getAppId());
        witnessFlowVO.setAppName(witnessFlowBO.getAppName());
        witnessFlowVO.setFlowId(witnessFlowBO.getFlowId());
        if (null != witnessFlowBO.getFlowKeyInfo()) {
            WitnessFlowVO.WitnessFlowKeyInfoVO witnessFlowKeyInfoVO =
                    new WitnessFlowVO.WitnessFlowKeyInfoVO();
            witnessFlowKeyInfoVO.setOauthCallbackUrl(
                    witnessFlowBO.getFlowKeyInfo().getOauthCallbackUrl());
            witnessFlowKeyInfoVO.setWillingCallbackUrl(
                    witnessFlowBO.getFlowKeyInfo().getWillingCallbackUrl());
            witnessFlowKeyInfoVO.setUserName(witnessFlowBO.getFlowKeyInfo().getUserName());
            witnessFlowKeyInfoVO.setLicenseNo(witnessFlowBO.getFlowKeyInfo().getLicenseNo());
            witnessFlowVO.setFlowKeyInfo(witnessFlowKeyInfoVO);
        }
        witnessFlowVO.setFlowName(witnessFlowBO.getFlowName());
        witnessFlowVO.setFlowDesc(witnessFlowBO.getFlowDesc());
        witnessFlowVO.setFlowStatus(witnessFlowBO.getFlowStatus());
        witnessFlowVO.setFlowStatusDesc(witnessFlowBO.getFlowStatusDesc());
        witnessFlowVO.setFlowStartTime(witnessFlowBO.getFlowStartTime());
        witnessFlowVO.setFlowModifyTime(witnessFlowBO.getFlowModifyTime());
        witnessFlowVO.setFlowEndTime(witnessFlowBO.getFlowEndTime());
        witnessFlowVO.setSignValidity(witnessFlowBO.getSignValidity());
        witnessFlowVO.setContractValidity(witnessFlowBO.getContractValidity());
        witnessFlowVO.setInitiator(convertWitnessAccountVO(witnessFlowBO.getInitiator()));
        if (CollectionUtils.isNotEmpty(witnessFlowBO.getSigners())) {
            witnessFlowVO.setSigners(
                    witnessFlowBO.getSigners().stream()
                            .map(i -> convertWitnessSignerDetailVO(i))
                            .collect(Collectors.toList()));
        }
        witnessFlowVO.setClientName(witnessFlowBO.getClientName());
        witnessFlowVO.setClientVersion(witnessFlowBO.getClientVersion());

        return witnessFlowVO;
    }

    /**
     * 转换天印流程信息中的签署人信息
     *
     * @param signerDetailBO
     * @return
     */
    private static WitnessFlowVO.WitnessSignerDetailVO convertWitnessSignerDetailVO(
            WitnessFlowBO.WitnessSignerDetailBO signerDetailBO) {
        WitnessFlowVO.WitnessSignerDetailVO signerDetailVO =
                new WitnessFlowVO.WitnessSignerDetailVO();
        signerDetailVO.setSignStatus(signerDetailBO.getSignStatus());
        signerDetailVO.setPerson(convertAccountVO(signerDetailBO.getPerson()));
        signerDetailVO.setSubject(convertAccountVO(signerDetailBO.getSubject()));
        signerDetailVO.setSubjectAccountType(signerDetailBO.getSubjectAccountType());
        signerDetailVO.setCurrentUser(signerDetailBO.isCurrentUser());
        return signerDetailVO;
    }

    /**
     * 转换天印流程信息中的用户+主体信息
     *
     * @param witnessAccountBO
     * @return
     */
    private static WitnessFlowVO.WitnessAccountVO convertWitnessAccountVO(
            WitnessFlowBO.WitnessAccountBO witnessAccountBO) {
        WitnessFlowVO.WitnessAccountVO witnessAccountVO = new WitnessFlowVO.WitnessAccountVO();
        witnessAccountVO.setPerson(convertAccountVO(witnessAccountBO.getPerson()));
        witnessAccountVO.setSubject(convertAccountVO(witnessAccountBO.getSubject()));
        witnessAccountVO.setSubjectAccountType(witnessAccountBO.getSubjectAccountType());
        witnessAccountVO.setCurrentUser(witnessAccountBO.isCurrentUser());
        return witnessAccountVO;
    }

    /**
     * 转换天印流程信息中的用户信息
     *
     * @param accountBO
     * @return
     */
    private static WitnessFlowVO.AccountVO convertAccountVO(WitnessFlowBO.AccountBO accountBO) {
        WitnessFlowVO.AccountVO accountVO = new WitnessFlowVO.AccountVO();
        accountVO.setGid(accountBO.getGid());
        accountVO.setOid(accountBO.getOid());
        accountVO.setName(accountBO.getName());
        accountVO.setMobile(accountBO.getMobile());
        accountVO.setEmail(accountBO.getEmail());
        accountVO.setNickname(accountBO.getNickname());
        accountVO.setOrgan(accountBO.getOrgan());
        accountVO.setDeleted(accountBO.getDeleted());
        return accountVO;
    }
}
