package com.timevale.saasbiz.rest.bean.contractcategory.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 编辑合同类型请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("编辑合同类型请求参数")
public class UpdateContractCategoryRequest extends SaveContractCategoryRequest {
    /** 合同类型id */
    @ApiModelProperty("合同类型id")
    @NotBlank(message = "合同类型id不能为空")
    private String categoryId;
}
