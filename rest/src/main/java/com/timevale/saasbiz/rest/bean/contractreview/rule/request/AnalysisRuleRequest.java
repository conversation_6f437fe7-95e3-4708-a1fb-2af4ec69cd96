package com.timevale.saasbiz.rest.bean.contractreview.rule.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisRuleRequest extends ToString {

    @ApiModelProperty("规则ID")
    private String ruleId;

    @NotBlank(message = "规则描述不能为空")
    @ApiModelProperty("规则描述")
    @Length(max = 2000, message = "规则描述不能超过2000字符")
    private String ruleRemark;

    @NotBlank(message = "规则组ID不能为空")
    @ApiModelProperty("规则组ID")
    private String groupId;
}