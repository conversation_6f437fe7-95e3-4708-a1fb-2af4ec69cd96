package com.timevale.saasbiz.rest.bean.offlinecontract.vo;

import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.validator.EnumCheck;
import com.timevale.saas.common.validator.StringRegex;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 线下合同文件信息
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class OfflineContractFileVO extends ToString {

    /** 文件id */
    @NotBlank(message = "文件id不能为空")
    private String fileId;

    /** 文件名称 */
    @NotBlank(message = "文件名称不能为空")
    private String fileName;

    /** 文件类型 */
    @NotBlank(message = "文件类型不能为空")
    @EnumCheck(target = ProcessFileType.class, enumField = "code")
    private String fileType;

    /** 合同编号 */
    @StringRegex(regex = "[0-9A-Za-z-_]{0,50}", message = "合同编号最长50个字，仅允许填写字母、数字、-、_")
    private String contractNo;

    /** 合同类型id */
    private String categoryId;

    /** 合同类型名称 */
    private String categoryName;
}
