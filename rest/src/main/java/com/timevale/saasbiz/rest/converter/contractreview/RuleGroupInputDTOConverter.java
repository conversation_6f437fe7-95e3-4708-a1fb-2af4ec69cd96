package com.timevale.saasbiz.rest.converter.contractreview;

import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.DeleteRuleGroupInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.ListRuleGroupInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.SaveRuleGroupInputDTO;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.request.DeleteRuleGroupRequest;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.request.ListRuleGroupRequest;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.request.SaveRuleGroupRequest;

public class RuleGroupInputDTOConverter {
    public static ListRuleGroupInputDTO convertToInputDTO(String accountId, String tenantId, ListRuleGroupRequest request) {
        ListRuleGroupInputDTO dto = new ListRuleGroupInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setInventoryId(request.getInventoryId());
        return dto;
    }

    public static SaveRuleGroupInputDTO convertToInputDTO(String accountId, String tenantId, SaveRuleGroupRequest request) {
        SaveRuleGroupInputDTO dto = new SaveRuleGroupInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setGroupName(request.getGroupName());
        dto.setGroupId(request.getGroupId());
        dto.setGroupIds(request.getGroupIds());
        return dto;
    }

    public static DeleteRuleGroupInputDTO convertToInputDTO(String accountId, String tenantId, DeleteRuleGroupRequest request) {
        DeleteRuleGroupInputDTO dto = new DeleteRuleGroupInputDTO();
        dto.setAccountId(accountId);
        dto.setTenantId(tenantId);
        dto.setGroupIds(request.getGroupIds());
        return dto;
    }
}
