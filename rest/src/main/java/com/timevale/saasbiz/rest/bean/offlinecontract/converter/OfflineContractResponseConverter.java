package com.timevale.saasbiz.rest.bean.offlinecontract.converter;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.*;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.output.QueryOfflineContractRecordInfoOutputDTO;
import com.timevale.saasbiz.rest.bean.offlinecontract.response.QueryOfflineContractRecordInfoResponse;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.*;
import com.timevale.saasbiz.service.excel.listener.offlinecontract.OfflineContractExcelInfoDTO;

import java.util.stream.Collectors;

/**
 * 线下合同响应数据转换类
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public class OfflineContractResponseConverter {

    /**
     * 转换线下合同详情
     *
     * @param offlineContractDetailDTO
     * @return
     */
    public static OfflineContractDetailVO convert2OfflineContractDetailVO(
            OfflineContractDetailDTO offlineContractDetailDTO) {
        OfflineContractDetailVO offlineContractVO = new OfflineContractDetailVO();
        offlineContractVO.setRecordProcessId(offlineContractDetailDTO.getRecordProcessId());
        offlineContractVO.setProcessId(offlineContractDetailDTO.getProcessId());
        offlineContractVO.setStatus(offlineContractDetailDTO.getStatus());
        offlineContractVO.setFailReason(offlineContractDetailDTO.getFailReason());
        offlineContractVO.setContractFiles(
                offlineContractDetailDTO.getContractFiles().stream()
                        .map(i -> convert2ContractFileVO(i))
                        .collect(Collectors.toList()));
        offlineContractVO.setProcessInfo(
                convert2ProcessInfoVO(offlineContractDetailDTO.getProcessInfo()));
        return offlineContractVO;
    }

    /**
     * 转换导入记录列表单条数据
     *
     * @param listDTO
     * @return
     */
    public static OfflineContractRecordListVO convert2RecordListVO(
            OfflineContractRecordListDTO listDTO) {
        OfflineContractRecordListVO listVO = new OfflineContractRecordListVO();
        listVO.setRecordId(listDTO.getRecordId());
        listVO.setMenuId(listDTO.getMenuId());
        listVO.setMenuName(listDTO.getMenuName());
        listVO.setMenuPath(listDTO.getMenuPath());
        listVO.setImporter(listDTO.getImporter());
        listVO.setImportTime(listDTO.getImportTime());
        listVO.setImportWay(listDTO.getImportWay());
        listVO.setExtractWay(listDTO.getExtractWay());
        listVO.setContractSize(listDTO.getContractSize());
        listVO.setSuccessSize(listDTO.getSuccessSize());
        listVO.setFailedSize(listDTO.getFailedSize());
        listVO.setStatus(listDTO.getStatus());
        return listVO;
    }

    /**
     * 转换导入记录基本信息
     *
     * @param outputDTO
     * @return
     */
    public static QueryOfflineContractRecordInfoResponse convert2RecordInfoResponse(
            QueryOfflineContractRecordInfoOutputDTO outputDTO) {
        QueryOfflineContractRecordInfoResponse response =
                new QueryOfflineContractRecordInfoResponse();
        response.setRecordId(outputDTO.getRecordId());
        response.setMenuId(outputDTO.getMenuId());
        response.setMenuName(outputDTO.getMenuName());
        response.setMenuPath(outputDTO.getMenuPath());
        response.setImportWay(outputDTO.getImportWay());
        response.setExtractWay(outputDTO.getExtractWay());
        response.setContractSize(outputDTO.getContractSize());
        response.setSuccessSize(outputDTO.getSuccessSize());
        response.setFailedSize(outputDTO.getFailedSize());
        response.setExtractConfig(convert2ExtractConfigVO(outputDTO.getExtractConfig()));
        response.setStatus(outputDTO.getStatus());
        return response;
    }

    /**
     * 转换线下合同文件信息
     *
     * @param fileDTO
     * @return
     */
    public static OfflineContractFileVO convert2ContractFileVO(OfflineContractFileDTO fileDTO) {
        OfflineContractFileVO fileVO = new OfflineContractFileVO();
        fileVO.setFileId(fileDTO.getFileId());
        fileVO.setFileName(fileDTO.getFileName());
        fileVO.setFileType(fileDTO.getFileType());
        fileVO.setContractNo(fileDTO.getContractNo());
        fileVO.setCategoryId(fileDTO.getCategoryId());
        fileVO.setCategoryName(fileDTO.getCategoryName());
        return fileVO;
    }

    /**
     * 转换线下合同对应的合同信息
     *
     * @param processInfoDTO
     * @return
     */
    public static OfflineContractExcelInfoVO convert2ExcelInfoVO(
            OfflineContractExcelInfoDTO processInfoDTO) {
        if (null == processInfoDTO) {
            return null;
        }
        OfflineContractExcelInfoVO processInfoVO = new OfflineContractExcelInfoVO();
        processInfoVO.setContractNo(processInfoDTO.getContractNo());
        processInfoVO.setCategoryId(processInfoDTO.getCategoryId());
        processInfoVO.setCategoryName(processInfoDTO.getCategoryName());
        processInfoVO.setTitle(processInfoDTO.getTitle());
        if (CollectionUtils.isNotEmpty(processInfoDTO.getSigners())) {
            processInfoVO.setSigners(
                    processInfoDTO.getSigners().stream()
                            .map(i -> convert2ContractAccountVO(i))
                            .collect(Collectors.toList()));
        }
        processInfoVO.setContractValidity(processInfoDTO.getContractValidity());

        return processInfoVO;
    }

    /**
     * 转换线下合同对应的合同信息
     *
     * @param processInfoDTO
     * @return
     */
    public static OfflineContractProcessInfoVO convert2ProcessInfoVO(
            OfflineContractProcessInfoDTO processInfoDTO) {
        if (null == processInfoDTO) {
            return null;
        }
        OfflineContractProcessInfoVO processInfoVO = new OfflineContractProcessInfoVO();
        processInfoVO.setTitle(processInfoDTO.getTitle());
        if (CollectionUtils.isNotEmpty(processInfoDTO.getSigners())) {
            processInfoVO.setSigners(
                    processInfoDTO.getSigners().stream()
                            .map(i -> convert2ContractAccountVO(i))
                            .collect(Collectors.toList()));
        }
        processInfoVO.setContractValidity(processInfoDTO.getContractValidity());

        return processInfoVO;
    }

    /**
     * 转换线下流程用户信息
     *
     * @param accountDTO
     * @return
     */
    private static OfflineContractAccountVO convert2ContractAccountVO(
            OfflineContractAccountDTO accountDTO) {
        OfflineContractAccountVO accountVO = new OfflineContractAccountVO();
        accountVO.setAccount(accountDTO.getAccount());
        accountVO.setAccountName(accountDTO.getAccountName());
        accountVO.setSubjectName(accountDTO.getSubjectName());
        accountVO.setSubjectType(accountDTO.getSubjectType());
        return accountVO;
    }

    /**
     * 转换线下合同提取配置
     *
     * @param configDTO
     * @return
     */
    private static OfflineContractExtractConfigVO convert2ExtractConfigVO(
            OfflineContractExtractConfigDTO configDTO) {
        if (null == configDTO) {
            return null;
        }
        OfflineContractExtractConfigVO configVO = new OfflineContractExtractConfigVO();
        if (CollectionUtils.isNotEmpty(configDTO.getFields())) {
            configVO.setFields(
                    configDTO.getFields().stream()
                            .map(i -> convert2ExtractFieldVO(i))
                            .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(configDTO.getSigners())) {
            configVO.setSigners(
                    configDTO.getSigners().stream()
                            .map(i -> convert2SignerConfigVO(i))
                            .collect(Collectors.toList()));
        }
        return configVO;
    }

    /**
     * 转换提取字段
     *
     * @param extractFieldDTO
     * @return
     */
    private static OfflineExtractFieldVO convert2ExtractFieldVO(
            OfflineExtractFieldDTO extractFieldDTO) {
        OfflineExtractFieldVO extractFieldVO = new OfflineExtractFieldVO();
        extractFieldVO.setFieldCode(extractFieldDTO.getFieldCode());
        extractFieldVO.setEnable(extractFieldDTO.isEnable());
        return extractFieldVO;
    }

    /**
     * 转换签署方配置
     *
     * @param signerConfigDTO
     * @return
     */
    private static OfflineContractSignerConfigVO convert2SignerConfigVO(
            OfflineContractSignerConfigDTO signerConfigDTO) {
        OfflineContractSignerConfigVO signerConfigVO = new OfflineContractSignerConfigVO();
        signerConfigVO.setSignerKeyword(signerConfigDTO.getSignerKeyword());
        signerConfigVO.setSignerSubjectType(signerConfigDTO.getSignerSubjectType());
        return signerConfigVO;
    }
}
