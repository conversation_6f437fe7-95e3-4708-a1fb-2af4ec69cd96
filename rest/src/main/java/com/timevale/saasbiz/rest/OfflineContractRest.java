package com.timevale.saasbiz.rest;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.mandarin.common.annotation.URIQueryParam;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.input.*;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.output.ExistsArrearsImportProcessOutputDTO;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.output.PageQueryOfflineContractRecordsOutputDTO;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.output.QueryOfflineContractRecordContractsOutputDTO;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.output.QueryOfflineContractRecordInfoOutputDTO;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.utils.RequestContextExtUtils;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.offlinecontract.converter.OfflineContractDTOConverter;
import com.timevale.saasbiz.rest.bean.offlinecontract.request.*;
import com.timevale.saasbiz.rest.bean.offlinecontract.response.*;
import com.timevale.saasbiz.service.excel.bean.ParseExcelResponse;
import com.timevale.saasbiz.service.excel.listener.offlinecontract.OfflineContractExcelInfoDTO;
import com.timevale.saasbiz.service.offlinecontract.OfflineContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.stream.Collectors;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;
import static com.timevale.saasbiz.rest.bean.offlinecontract.converter.OfflineContractDTOConverter.convert2ExtractConfigDTO;
import static com.timevale.saasbiz.rest.bean.offlinecontract.converter.OfflineContractDTOConverter.convert2SignerConfigDTO;
import static com.timevale.saasbiz.rest.bean.offlinecontract.converter.OfflineContractResponseConverter.*;

/**
 * 线下合同相关接口
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Api(tags = "线下合同接口")
@Validated
@ExternalService
@RestMapping(path = "/v2/offline-contracts")
public class OfflineContractRest {

    @Autowired OfflineContractService offlineContractService;

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @ApiOperation("查询线下合同导入记录列表")
    @RestMapping(path = "/records", method = RequestMethod.GET)
    public RestResult<PageQueryOfflineContractRecordsResponse> queryRecords(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @URIQueryParam @ModelAttribute PageQueryOfflineContractRecordsRequest request) {
        // 查询导入记录列表
        PageQueryOfflineContractRecordsInputDTO inputDTO =
                new PageQueryOfflineContractRecordsInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setAccountId(accountId);
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        PageQueryOfflineContractRecordsOutputDTO outputDTO =
                offlineContractService.pageQueryRecords(inputDTO);
        // 组装返回数据
        PageQueryOfflineContractRecordsResponse response =
                new PageQueryOfflineContractRecordsResponse();
        response.setTotal(outputDTO.getTotal());
        response.setRecords(
                outputDTO.getRecords().stream()
                        .map(i -> convert2RecordListVO(i))
                        .collect(Collectors.toList()));

        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.IMPORT)
    @ApiOperation("导入线下合同")
    @RestMapping(path = "/import", method = RequestMethod.POST)
    public RestResult<ImportOfflineContractResponse> importOfflineContracts(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestHeader(HEADER_CLIENT_ID) String clientId,
            @RequestBody ImportOfflineContractRequest request) {

        ImportOfflineContractInputDTO inputDTO = new ImportOfflineContractInputDTO();
        inputDTO.setAccountId(accountId);
        inputDTO.setSubjectId(subjectId);
        inputDTO.setClientId(clientId);
        inputDTO.setMenuId(request.getMenuId());
        inputDTO.setImportWay(request.getImportWay());
        inputDTO.setExtractWay(request.getExtractWay());
        inputDTO.setContracts(
                request.getContracts().stream()
                        .map(i -> OfflineContractDTOConverter.convert2OfflineContractDTO(i))
                        .collect(Collectors.toList()));
        inputDTO.setExtractConfig(convert2ExtractConfigDTO(request.getExtractConfig()));
        inputDTO.setMasterProcessId(request.getMasterProcessId());
        if (StringUtils.isNotBlank(request.getDedicatedCloudId())) {
            inputDTO.setDedicatedCloudId(request.getDedicatedCloudId());
        }
        String recordId = offlineContractService.importOfflineContracts(inputDTO);

        ImportOfflineContractResponse response = new ImportOfflineContractResponse();
        response.setRecordId(recordId);
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @ApiOperation("查询线下合同导入记录基本信息")
    @RestMapping(path = "/record-info", method = RequestMethod.GET)
    public RestResult<QueryOfflineContractRecordInfoResponse> queryRecordInfo(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @URIQueryParam @ModelAttribute QueryOfflineContractRecordInfoRequest request) {
        // 查询导入记录基本信息
        QueryOfflineContractRecordInfoInputDTO inputDTO =
                new QueryOfflineContractRecordInfoInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setRecordId(request.getRecordId());
        inputDTO.setWithMenuPath(request.isWithMenuPath());
        QueryOfflineContractRecordInfoOutputDTO outputDTO =
                offlineContractService.queryOfflineContractRecordInfo(inputDTO);
        // 组装返回数据
        QueryOfflineContractRecordInfoResponse response = convert2RecordInfoResponse(outputDTO);
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @ApiOperation("批量查询线下合同导入记录基本信息")
    @RestMapping(path = "/batch-record-info", method = RequestMethod.POST)
    public RestResult<BatchQueryOfflineContractRecordInfoResponse> batchQueryRecordInfo(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody BatchQueryOfflineContractRecordInfoRequest request) {
        // 查询导入记录基本信息
        BatchQueryOfflineContractRecordInfoInputDTO inputDTO =
                new BatchQueryOfflineContractRecordInfoInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setRecordIds(request.getRecordIds());
        inputDTO.setWithMenuPath(request.isWithMenuPath());
        List<QueryOfflineContractRecordInfoOutputDTO> outputDTO =
                offlineContractService.batchQueryOfflineContractRecordInfo(inputDTO);
        // 组装返回数据
        BatchQueryOfflineContractRecordInfoResponse response =
                new BatchQueryOfflineContractRecordInfoResponse();
        response.setRecords(
                outputDTO.stream()
                        .map(i -> convert2RecordInfoResponse(i))
                        .collect(Collectors.toList()));
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @ApiOperation("查询线下合同导入记录合同文件信息")
    @RestMapping(path = "/record-contract-files", method = RequestMethod.GET)
    public RestResult<QueryOfflineContractRecordContractsResponse> queryRecordContractFiles(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @URIQueryParam @ModelAttribute QueryOfflineContractRecordContractsRequest request) {

        QueryOfflineContractRecordContractsInputDTO inputDTO =
                new QueryOfflineContractRecordContractsInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setRecordId(request.getRecordId());
        inputDTO.setWithExtract(request.isWithExtract());
        inputDTO.setPageNum(request.getPageNum());
        inputDTO.setPageSize(request.getPageSize());
        QueryOfflineContractRecordContractsOutputDTO outputDTO =
                offlineContractService.pageQueryRecordContracts(inputDTO);

        // 组装返回数据
        QueryOfflineContractRecordContractsResponse response =
                new QueryOfflineContractRecordContractsResponse();
        response.setTotal(outputDTO.getTotal());
        if (CollectionUtils.isNotEmpty(outputDTO.getContracts())) {
            response.setContracts(
                    outputDTO.getContracts().stream()
                            .map(i -> convert2OfflineContractDetailVO(i))
                            .collect(Collectors.toList()));
        }
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.DELETE)
    @ApiOperation("删除线下合同导入记录")
    @RestMapping(path = "/delete-records", method = RequestMethod.POST)
    public RestResult<Void> deleteRecord(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody BatchOfflineContractRecordsRequest request) {
        BatchOfflineContractRecordsInputDTO inputDTO = new BatchOfflineContractRecordsInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setRecordIds(request.getRecordIds());
        offlineContractService.deleteRecords(inputDTO);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.IMPORT)
    @ApiOperation("停止导入线下合同")
    @RestMapping(path = "/stop-import", method = RequestMethod.POST)
    public RestResult<Void> stopImport(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody BatchOfflineContractRecordsRequest request) {
        BatchOfflineContractRecordsInputDTO inputDTO = new BatchOfflineContractRecordsInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setRecordIds(request.getRecordIds());
        offlineContractService.stopImportRecords(inputDTO);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.IMPORT)
    @ApiOperation("下载线下合同合同信息录入excel模板")
    @RestMapping(path = "/download-contract-excel", method = RequestMethod.POST)
    public RestResult<DownloadOfflineContractExcelResponse> downloadExcel(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody DownloadOfflineContractExcelRequest request) {

        DownloadOfflineContractExcelInputDTO inputDTO = new DownloadOfflineContractExcelInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setContractFileNames(request.getContractFileNames());
        inputDTO.setSignerConfigs(
                request.getSignerConfigs().stream()
                        .map(i -> convert2SignerConfigDTO(i))
                        .collect(Collectors.toList()));
        String downloadUrl = offlineContractService.downloadExcel(inputDTO);

        DownloadOfflineContractExcelResponse response = new DownloadOfflineContractExcelResponse();
        response.setDownloadUrl(downloadUrl);
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.IMPORT)
    @ApiOperation("解析线下合同合同信息录入excel")
    @RestMapping(path = "/parse-contract-excel", method = RequestMethod.POST)
    public RestResult<ParseOfflineContractExcelResponse> parseExcel(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody ParseOfflineContractExcelRequest request) {
        // 组装业务层请求参数
        ParseOfflineContractExcelInputDTO inputDTO = new ParseOfflineContractExcelInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setClientId(RequestContextExtUtils.getClientId());
        inputDTO.setFileKey(request.getFileKey());
        inputDTO.setErrorDataHandleWay(request.getErrorDataHandleWay());
        inputDTO.setSignerConfigs(
                request.getSignerConfigs().stream()
                        .map(i -> convert2SignerConfigDTO(i))
                        .collect(Collectors.toList()));
        // 解析excel
        ParseExcelResponse<OfflineContractExcelInfoDTO> result =
                offlineContractService.parseExcel(inputDTO);
        // 组装返回数据
        ParseOfflineContractExcelResponse response = new ParseOfflineContractExcelResponse();
        response.setSuccessCount(result.getCorrectCount());
        response.setFailCount(result.getErrorCount());
        response.setFailDownloadUrl(result.getErrorDataDownloadUrl());
        if (CollectionUtils.isNotEmpty(result.getDataList())) {
            response.setProcessInfos(
                    result.getDataList().stream()
                            .map(i -> convert2ExcelInfoVO(i))
                            .collect(Collectors.toList()));
        }
        return RestResult.success(response);
    }



    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.IMPORT)
    @ApiOperation("恢复导入线下合同")
    @RestMapping(path = "/recover-import", method = RequestMethod.POST)
    public RestResult<Void> recoverImport(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody BatchOfflineContractRecordsRequest request) {
        BatchOfflineContractRecordsInputDTO inputDTO = new BatchOfflineContractRecordsInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setRecordIds(request.getRecordIds());
        offlineContractService.recoverImportRecords(inputDTO);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.IMPORT)
    @ApiOperation("重新导入所有导入失败的线下合同")
    @RestMapping(path = "/failed-reimport", method = RequestMethod.POST)
    public RestResult restartImport(
            @RequestHeader(HEADER_OPERATOR_ID) String accountId,
            @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestBody ReimportFailedOfflineContractRequest request) {
        // 组装业务层请求参数
        ReimportFailedOfflineContractInputDTO inputDTO =
                new ReimportFailedOfflineContractInputDTO();
        inputDTO.setSubjectId(subjectId);
        inputDTO.setRecordId(request.getRecordId());
        offlineContractService.reimportFailed(inputDTO);
        return RestResult.success();
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.RESOURCE_OFFLINE_CONTRACT,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @ApiOperation("查询是否存在欠费的提取记录")
    @RestMapping(path = "/has-arrears-extract", method = RequestMethod.GET)
    public RestResult<ExistExtreactArrearsResponse> existExtreactArrears(
                @RequestHeader(HEADER_TENANT_ID) String subjectId,
            @RequestParam String recordId){
        ExistsArrearsImportProcessInputDTO inputDTO = new ExistsArrearsImportProcessInputDTO();
        inputDTO.setRecordId(recordId);
        inputDTO.setTenantId(subjectId);
        ExistsArrearsImportProcessOutputDTO outputDTO = offlineContractService.existsArrearsImportProcess(inputDTO);
        ExistExtreactArrearsResponse response = new ExistExtreactArrearsResponse();
        response.setExists(outputDTO.getExists());
        return RestResult.success(response);
    }
}
