package com.timevale.saasbiz.rest.bean.contractaudit.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
public class ContractAuditRuleTreeVO {
    @ApiModelProperty(value = "规则序号")
    private String ruleSeq;
    @ApiModelProperty(value = "规则标题")
    private String ruleTitle;
    @ApiModelProperty(value = "子规则")
    private List<ContractAuditRuleTreeVO> subRules;
}
