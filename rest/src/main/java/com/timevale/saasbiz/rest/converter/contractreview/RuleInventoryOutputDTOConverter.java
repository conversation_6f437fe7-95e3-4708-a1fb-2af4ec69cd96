package com.timevale.saasbiz.rest.converter.contractreview;
import java.util.*;

import com.google.common.collect.Lists;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.ContractTypePullDownBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.RuleInventoryDetailBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.RuleInventoryListBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.ReviewRuleBO;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.response.ContractTypePullDownResponse;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.vo.ContractTypePullDownVO;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.vo.RuleInventoryDetailVO;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.vo.RuleInventoryListVO;
import com.timevale.saasbiz.rest.bean.contractreview.ruleinventory.vo.ReviewRuleVO;

import java.util.stream.Collectors;

public class RuleInventoryOutputDTOConverter {

    public static List<RuleInventoryListVO> convertToListVOS(List<RuleInventoryListBO> boList) {
        return Optional.ofNullable(boList).orElse(Lists.newArrayList()).stream()
                .map(RuleInventoryOutputDTOConverter::convertToListVO)
                .collect(Collectors.toList());
    }

    public static RuleInventoryDetailVO convertToDetailVO(RuleInventoryDetailBO detailBO) {
        if (null == detailBO) {
            return null;
        }
        RuleInventoryDetailVO detailVO = new RuleInventoryDetailVO();
        detailVO.setInventoryId(detailBO.getInventoryId());
        detailVO.setInventoryName(detailBO.getInventoryName());
        detailVO.setContractType(detailBO.getContractType());
        detailVO.setContractView(detailBO.getContractView());
        detailVO.setCreateTime(detailBO.getCreateTime());
        detailVO.setModifyTime(detailBO.getModifyTime());
        detailVO.setRules(convertToRuleVOS(detailBO.getRules()));
        return detailVO;
    }

    private static RuleInventoryListVO convertToListVO(RuleInventoryListBO bo) {
        RuleInventoryListVO vo = new RuleInventoryListVO();
        vo.setInventoryId(bo.getInventoryId());
        vo.setInventoryName(bo.getInventoryName());
        vo.setContractType(bo.getContractType());
        vo.setContractView(bo.getContractView());
        vo.setCreateTime(bo.getCreateTime());
        vo.setModifyTime(bo.getModifyTime());
        vo.setRuleSize(bo.getRuleSize());
        vo.setRuleMaxSize(bo.getRuleMaxSize());
        vo.setOperatorName(bo.getOperatorName());
        return vo;
    }

    private static List<ReviewRuleVO> convertToRuleVOS(List<ReviewRuleBO> ruleBOs) {
        return Optional.ofNullable(ruleBOs).orElse(Lists.newArrayList()).stream()
                .map(RuleInventoryOutputDTOConverter::convertToRuleVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static ReviewRuleVO convertToRuleVO(ReviewRuleBO ruleBO) {
        if (null == ruleBO) {
            return null;
        }
        ReviewRuleVO vo = new ReviewRuleVO();
        vo.setRuleId(ruleBO.getRuleId());
        vo.setGroupId(ruleBO.getGroupId());
        vo.setRuleName(ruleBO.getRuleName());
        vo.setRuleRemark(ruleBO.getRuleRemark());
        vo.setRiskLevel(ruleBO.getRiskLevel());
        vo.setDefaultFlag(ruleBO.getDefaultFlag());
        return vo;
    }

    public static ContractTypePullDownResponse convertToContractTypePullDownBO(ContractTypePullDownBO contractTypePullDownBO) {
        List<ContractTypePullDownVO> voList = new ArrayList<>();
        contractTypePullDownBO.getContractTypeRespList().stream().forEach(res->{
            ContractTypePullDownVO vo = new ContractTypePullDownVO();
            vo.setContractTypeId(res.getContractTypeId());
            vo.setContractTypeName(res.getContractTypeName());
            voList.add(vo);
        });


        ContractTypePullDownResponse response = new ContractTypePullDownResponse();
        response.setSelected(contractTypePullDownBO.getSelected());
        response.setContractTypeRespList(voList);

        return response;
    }
}
