package com.timevale.saasbiz.rest;

import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saas.common.privilege.aspect.MemberCheck;
import com.timevale.saasbiz.model.bean.account.dto.input.UpdateOrgTransferConfigsInputDTO;
import com.timevale.saasbiz.model.bean.account.dto.output.QueryOrgTransferConfigsOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.UserDeptDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasAnyPrivilegeInputDTO;
import com.timevale.saasbiz.model.enums.transfer.TransferConfigTypeEnum;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.common.request.CheckDeptHaveBelongMultiBizRequest;
import com.timevale.saasbiz.rest.bean.common.response.CheckAuthDeptBelongMultiBizResponse;
import com.timevale.saasbiz.rest.bean.common.response.QueryAccountInfoResponse;
import com.timevale.saasbiz.rest.bean.common.response.QueryUserDirectDeptResponse;
import com.timevale.saasbiz.rest.bean.account.request.UpdateOrgTransferConfigsRequest;
import com.timevale.saasbiz.rest.bean.account.response.QueryOrgTransferConfigsResponse;
import com.timevale.saasbiz.rest.converter.AccountConvertor;
import com.timevale.saasbiz.service.account.AccountService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;

import java.util.List;
import java.util.Objects;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

@Api(tags = "用户信息")
@Validated
@ExternalService
@RestMapping(path = "/v2/common/user")
public class AccountRest {

    @Autowired private UserCenterService userCenterService;

    @Autowired private AccountService accountService;

    @RestMapping(path = "/accountInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取用户基本信息", httpMethod = "GET")
    public RestResult<QueryAccountInfoResponse> queryAccountInfo(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户id不能为空") String accountId) {
        AccountDetailDTO accountDetail = userCenterService.queryAccountDetailByOid(accountId);
        QueryAccountInfoResponse response = new QueryAccountInfoResponse();
        response.setAccountId(accountDetail.getOid());
        response.setAccountName(accountDetail.getName());
        response.setAccountMobile(accountDetail.obtainMobile());
        response.setAccountEmail(accountDetail.obtainEmail());
        response.setAccountRealNamed(accountDetail.isRealNamed());
        return RestResult.success(response);
    }

    @RestMapping(path = "/permission", method = RequestMethod.GET)
    @ApiOperation(value = "用户权限校验", httpMethod = "GET")
    public RestResult<Boolean> permissionList(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank(message = "用户id不能为空") String operatorId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank(message = "主体id不能为空") String tenantId,
            @ApiParam(value = "权限类型") @RequestParam String resource,
            @ApiParam(value = "权限名称") @RequestParam String operation) {
        CheckHasAnyPrivilegeInputDTO inputDTO = new CheckHasAnyPrivilegeInputDTO();
        inputDTO.setAccountId(operatorId);
        inputDTO.setTenantId(tenantId);
        inputDTO.addResourcePrivilege(resource, operation);
        inputDTO.setFilterIsolationPrivilege(false);
        // 校验用户是否有指定权限
        boolean hasPrivilege = userCenterService.checkHasAnyPrivilege(inputDTO, false);
        // 返回结果
        return RestResult.success(hasPrivilege);
    }


    @RestMapping(path = "/direct-dept", method = RequestMethod.GET)
    @ApiOperation(value = "获取用户直属部门", httpMethod = "GET")
    public RestResult<QueryUserDirectDeptResponse> userDirectDept() {

        String operatorOid = RequestContextExtUtils.getOperatorId();
        String subjectOid = RequestContextExtUtils.getResourceTenantId();
        List<UserDeptDTO> userDirectDept = userCenterService.getUserDirectDept(subjectOid, operatorOid);
        QueryUserDirectDeptResponse response = new QueryUserDirectDeptResponse();
        response.setDepts(userDirectDept);
        return RestResult.success(response);
    }

    @ApiOperation(value = "批量查询企业转交配置信息", httpMethod = "GET")
    @RestMapping(path = "/org-transfer-configs-get", method = RequestMethod.GET)
    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    public RestResult<QueryOrgTransferConfigsResponse> queryOrgTransferConfigs(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId) {

        QueryOrgTransferConfigsOutputDTO output = accountService.queryOrgTransferConfigs(TransferConfigTypeEnum.getEnumList(), tenantId);

        if (Objects.isNull(output)) {
            return RestResult.success();
        }

        QueryOrgTransferConfigsResponse response = AccountConvertor.convertQueryOrgTransferConfigsResponse(output);
        return RestResult.success(response);
    }

    @ApiOperation(value = "批量更新企业转交配置信息", httpMethod = "POST")
    @RestMapping(path = "/org-transfer-configs-save", method = RequestMethod.POST)
    public BaseResult<Boolean> updateOrgTransferConfigs(
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestBody UpdateOrgTransferConfigsRequest request) {

        UpdateOrgTransferConfigsInputDTO input = AccountConvertor.convertUpdateOrgTransferConfigsInputDTO(request, tenantId);
        accountService.updateOrgTransferConfigs(input, accountId);
        return BaseResult.success(true);
    }




    @MemberCheck(accountId = SPEL_HEADER_OPERATOR_ID, subjectId = SPEL_HEADER_TENANT_ID)
    @RestMapping(path = "/dept-have-belong-multi-biz", method = RequestMethod.POST)
    @ApiOperation(value = "获取某些部门是否有隔离部门", httpMethod = "POST")
    public BaseResult<CheckAuthDeptBelongMultiBizResponse> deptHaveBelongMultiBiz(
            @RequestBody CheckDeptHaveBelongMultiBizRequest request) {
        boolean have = userCenterService.deptHaveBelongMultiBiz(RequestContextExtUtils.getTenantId(),
                RequestContextExtUtils.getOperatorId(),
                request.getDeptIds());
        CheckAuthDeptBelongMultiBizResponse response = new CheckAuthDeptBelongMultiBizResponse();
        response.setHave(have);
        return BaseResult.success(response);
    }
}
