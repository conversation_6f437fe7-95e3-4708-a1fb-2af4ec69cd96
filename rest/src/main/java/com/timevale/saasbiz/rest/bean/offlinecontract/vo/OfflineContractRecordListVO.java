package com.timevale.saasbiz.rest.bean.offlinecontract.vo;

import lombok.Data;

@Data
public class OfflineContractRecordListVO {
    /** 导入记录id */
    private String recordId;
    /** 归档菜单id */
    private String menuId;
    /** 归档菜单名称 */
    private String menuName;
    /** 归档菜单路径 */
    private String menuPath;
    /** 导入人 */
    private String importer;
    /** 导入时间 */
    private Long importTime;
    /** 导入方式 */
    private String importWay;
    /** 合同信息提取方式 */
    private String extractWay;
    /** 合同数量 */
    private long contractSize;
    /** 导入成功数量 */
    private long successSize;
    /** 导入失败数量 */
    private long failedSize;
    /** 状态 */
    private String status;
}
