package com.timevale.saasbiz.rest.bean.contractreview.rulegroup.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class DeleteRuleGroupRequest extends ToString {
    @NotEmpty(message = "请至少选择一个审查规则")
    @ApiModelProperty("规则ID列表")
    private List<String> groupIds;
}
