package com.timevale.saasbiz.rest;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

import com.timevale.saas.common.manage.common.service.annotation.FunctionPrivilegeCheck;
import com.timevale.saasbiz.model.bean.compare.dto.output.EmbedCompareQueryStatusOutputDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.rest.bean.compare.response.CompareDetailUrlResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.compare.dto.input.EmbedCompareCheckShowInputDTO;
import com.timevale.saasbiz.model.bean.compare.dto.input.EmbedCompareCreateInputDTO;
import com.timevale.saasbiz.model.bean.compare.dto.input.EmbedCompareQueryStatusInputDTO;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.compare.request.EmbedCompareCreateRequest;
import com.timevale.saasbiz.rest.bean.compare.response.EmbedCompareCheckShowResponse;
import com.timevale.saasbiz.rest.bean.compare.response.EmbedCompareCreateResponse;
import com.timevale.saasbiz.rest.bean.compare.response.EmbedCompareQueryStatusResponse;
import com.timevale.saasbiz.service.compare.CompareService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import ma.glasnost.orika.MapperFactory;

import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_OPERATOR_ID;
import static com.timevale.saas.common.privilege.handler.SPELRequestHeaderConstants.SPEL_HEADER_TENANT_ID;

/**
 * 合同比对
 * 
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "合同比对相关")
@Validated
@ExternalService
@RestMapping(path = "v1/intelligent")
public class CompareRest {

    @Autowired
    private CompareService compareService;

    @Autowired
    private MapperFactory mapperFactory;

    @ApiOperation(value = "查询是否展示合同比对按钮", httpMethod = "GET")
    @RestMapping(path = "/embed-compare-check-show", method = RequestMethod.GET)
    public RestResult<EmbedCompareCheckShowResponse> embedCompareCheckShow(
            @RequestHeader(HEADER_TENANT_ID) String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) String operatorId,
            @ApiParam(value = "合同id", required = true) @RequestParam(value = "processId", required = true) String processId) {
        EmbedCompareCheckShowInputDTO inputDTO = new EmbedCompareCheckShowInputDTO();
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setSubjectOid(tenantId);
        inputDTO.setProcessId(processId);
        boolean show = compareService.embedCompareCheckShow(inputDTO);
        EmbedCompareCheckShowResponse response = new EmbedCompareCheckShowResponse();
        response.setShow(show);
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = PrivilegeOperationConstants.ADD)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INTELLIGENT_COMPARE)
    @ApiOperation(value = "创建合同比对", httpMethod = "POST")
    @RestMapping(path = "/embed-compare-create", method = RequestMethod.POST)
    public RestResult<EmbedCompareCreateResponse> embedCompareCreate(
        @RequestHeader(HEADER_TENANT_ID) String tenantId,
        @RequestHeader(HEADER_OPERATOR_ID) String operatorId,
        @RequestBody EmbedCompareCreateRequest request) {
        EmbedCompareCreateInputDTO inputDTO = mapperFactory.getMapperFacade().map(request, EmbedCompareCreateInputDTO.class);
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setSubjectOid(tenantId);
        String compareId = compareService.embedCompareCreate(inputDTO);
        EmbedCompareCreateResponse response = new EmbedCompareCreateResponse();
        response.setCompareId(compareId);
        return RestResult.success(response);
    }

    @UserPrivilegeCheck(
            accountId = SPEL_HEADER_OPERATOR_ID,
            subjectId = SPEL_HEADER_TENANT_ID,
            resourceKey = PrivilegeResourceConstants.PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = PrivilegeOperationConstants.QUERY)
    @FunctionPrivilegeCheck(function = FunctionCodeConstants.INTELLIGENT_COMPARE)
    @ApiOperation(value = "查询合同比对状态", httpMethod = "GET")
    @RestMapping(path = "/embed-compare-query-status", method = RequestMethod.GET)
    public RestResult<EmbedCompareQueryStatusResponse> queryCompareStatus(
        @RequestHeader(HEADER_TENANT_ID) String tenantId,
        @RequestHeader(HEADER_OPERATOR_ID) String operatorId,
        @ApiParam(value = "比对id", required = true) @RequestParam(value = "compareId", required = true) String compareId){
        EmbedCompareQueryStatusInputDTO inputDTO = new EmbedCompareQueryStatusInputDTO();
        inputDTO.setOperatorOid(operatorId);
        inputDTO.setSubjectOid(tenantId);
        inputDTO.setCompareId(compareId);
        EmbedCompareQueryStatusOutputDTO outputDTO = compareService.queryCompareStatus(inputDTO);
        EmbedCompareQueryStatusResponse response = new EmbedCompareQueryStatusResponse();
        response.setCompareStatus(outputDTO.getCompareStatus());
        response.setFailMessage(outputDTO.getFailMessage());
        return RestResult.success(response);
    }

    @ApiOperation(value = "获取比对详情页地址", httpMethod = "GET")
    @RestMapping(path = "/compare-detail-url", method = RequestMethod.GET)
    public RestResult<CompareDetailUrlResponse> queryCompareDetailUrl(){
        String url = compareService.queryCompareDetailUrl();
        CompareDetailUrlResponse response = new CompareDetailUrlResponse();
        response.setUrl(url);
        return RestResult.success(response);
    }
}
