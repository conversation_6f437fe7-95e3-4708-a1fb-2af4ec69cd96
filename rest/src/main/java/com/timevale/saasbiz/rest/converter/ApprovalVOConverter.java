package com.timevale.saasbiz.rest.converter;

import com.timevale.account.flow.service.enums.ApprovalBizTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalStatusEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.contractmanager.common.service.utils.ProcessSourceMappingUtil;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saasbiz.model.approval.dto.ApprovalAggrDTO;
import com.timevale.saasbiz.model.approval.dto.ApprovalFlowItemDTO;
import com.timevale.saasbiz.model.approval.dto.ApprovalGroupItemDTO;
import com.timevale.saasbiz.model.approval.dto.ApprovalParticipantDTO;
import com.timevale.saasbiz.model.bean.approval.bo.ApprovalTemplateSealBO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalConditionValuesDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalTemplateDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalTableHeadDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ListApprovalFlowBaseInputDTO;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessStartSourceOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.enums.approval.ApprovalOperateTypeEnum;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.rest.bean.approval.request.ListApprovalFlowBaseRequest;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalAggregateResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalConditionValuesVO;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalTemplateVO;
import com.timevale.saasbiz.rest.bean.approval.response.ListApprovalFlowResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ListApprovalGroupResponse;
import com.timevale.saasbiz.rest.bean.approval.vo.ApprovalAccountVO;
import com.timevale.saasbiz.rest.bean.approval.vo.ApprovalParticipantVO;
import com.timevale.saasbiz.rest.bean.approval.vo.ApprovalTemplateSealVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.timevale.saasbiz.model.constants.FunctionCodeConstants.*;
import static com.timevale.saasbiz.model.exception.SaasBizResultCode.SAAS_BIZ_ERROR;

/**
 * <AUTHOR>
 * @since 2023-04-01 08:18
 */
public class ApprovalVOConverter {
    /**
     * @param targetAccount 要查询审批的用户信息
     * @param targetTenant 要查询的企业
     * @param request 查询参数
     * @return ListApprovalFlowInputDTO
     */
    public static <T extends ListApprovalFlowBaseInputDTO> T buildInputDTO(
            AccountInfoDTO targetAccount,
            AccountInfoDTO targetTenant,
            ListApprovalFlowBaseRequest request,
            Class<T> inputClass) {
        try {
            T inputDTO = inputClass.newInstance();
            inputDTO.setApprovalStatus(request.getApprovalStatusList());
            inputDTO.setApprovalName(request.getApprovalName());
            inputDTO.setApprovalType(request.getApprovalType());
            inputDTO.setMinCreateTime(request.getMinCreateTime());
            inputDTO.setMaxCreateTime(request.getMaxCreateTime());
            inputDTO.setMinOperateTime(request.getMinOperateTime());
            inputDTO.setMaxOperateTime(request.getMaxOperateTime());
            inputDTO.setParticipantName(request.getParticipantName());
            inputDTO.setParticipantSubjectName(request.getParticipantSubjectName());
            inputDTO.setContractNo(request.getContractNo());
            inputDTO.setGroupId(request.getGroupId());
            inputDTO.setQueryType(request.getQueryType());
            inputDTO.setInitiatorName(request.getInitiatorName());
            inputDTO.setOperatorOid(targetAccount.getOid());
            inputDTO.setOperatorGid(targetAccount.getGid());
            if (Objects.nonNull(targetTenant)) {
                inputDTO.setSubjectOid(targetTenant.getOid());
                inputDTO.setSubjectGid(targetTenant.getGid());
            }
            inputDTO.setKeyword(request.getKeyword());
            inputDTO.setProcessId(request.getProcessId());
            inputDTO.setApprovalCode(request.getApprovalCode());
            inputDTO.setApprovalNodeType(request.getApprovalNodeType());

            return inputDTO;
        } catch (Exception e) {
            throw new SaasBizException(SAAS_BIZ_ERROR.getCode(), "审批列表参数转换异常");
        }
    }

    public static ListApprovalGroupResponse.ApprovalGroupVO buildVO(ApprovalGroupItemDTO item) {
        ListApprovalGroupResponse.ApprovalGroupVO vo =
                new ListApprovalGroupResponse.ApprovalGroupVO();
        vo.setGroupId(item.getBizGroupId());
        vo.setGroupName(item.getBizGroupName());
        vo.setCreateTime(item.getCreateTime());
        vo.setApprovalCount(item.getTotalCount());
        vo.setFinishApprovalCount(item.getFinishCount());
        // 老审批组不支持转交/删除
        vo.setSupportDelete(Boolean.FALSE.equals(item.getOldApprovalGroup()));
        vo.setSupportTransfer(Boolean.FALSE.equals(item.getOldApprovalGroup()));
        return vo;
    }

    public static ListApprovalFlowResponse.ApprovalFlowVO buildVO(
            ApprovalFlowItemDTO item,
            List<String> supportFunctions,
            Map<String, ProcessStartSourceOutputDTO> startSourceMap) {
        ListApprovalFlowResponse.ApprovalFlowVO vo = new ListApprovalFlowResponse.ApprovalFlowVO();
        vo.setApprovalId(item.getApprovalId());
        vo.setApprovalName(item.getApprovalName());
        vo.setApprovalType(item.getApprovalType());
        vo.setApprovalStatus(item.getApprovalStatus());
        vo.setApprovalBizType(item.getApprovalBizType());
        vo.setProcessId(item.getProcessId());
        vo.setSubjectOid(item.getSubjectOid());
        vo.setApprovalInitiator(buildVO(item.getApprovalInitiator()));
        vo.setCreateTime(item.getCreateTime());
        vo.setUpdateTime(item.getUpdateTime());
        vo.setFinishTime(item.getFinishTime());
        vo.setCcFlag(item.getCcFlag());
        vo.setPause(item.getPause());
        vo.setPauseReason(item.getPauseReason());
        vo.setCurrentOperatorTask(buildVO(item.getCurrentOperatorTask()));
        vo.setContractAnalysisExist(item.getContractAnalysisExist());
        vo.setSourceDescription(item.getSourceDescription());
        vo.setParticipant(item.getParticipant());
        vo.setContractNo(item.getContractNo());
        vo.setParticipantSubject(item.getParticipantSubject());
        vo.setReason(item.getReason());
        vo.setIsCurrentOperator(item.getIsCurrentOperator());
        vo.setFlowId(item.getFlowId());
        vo.setApprovalContent(item.getApprovalContent());
        // 老审批不支持转交
        vo.setSupportTransfer(Boolean.FALSE.equals(item.getOldContractData()));
        // 合同流程发起来源信息
        vo.setInitiateFrom(buildProcessInitiateFrom(
                item.getProcessId(), item.getApprovalBizType(), item.getFlowSource(), startSourceMap));
        vo.setApprovalTemplateName(item.getApprovalTemplateName());
        // 审批操作列表
        vo.setOperations(buildOperations(item, supportFunctions));
        return vo;
    }

    public static ListApprovalFlowResponse.TableHeadVO buildVO(ApprovalTableHeadDTO item){
        ListApprovalFlowResponse.TableHeadVO vo = new ListApprovalFlowResponse.TableHeadVO();
        vo.setCode(item.getCode());
        vo.setName(item.getName());
        vo.setSort(item.getSort());
        vo.setHasFreeze(item.getHasFreeze());
        vo.setHasShow(item.getHasShow());
        return vo;
    }

    /**
     * 组装审批操作列表
     * @param item
     * @param supportFunctions
     * @return
     */
    private static List<ListApprovalFlowResponse.OperationVO> buildOperations(
            ApprovalFlowItemDTO item, List<String> supportFunctions) {
        // 获取可操作类型列表
        List<ApprovalOperateTypeEnum> operations = buildOperateTypes(item, supportFunctions);
        // 组装操作列表
        List<ListApprovalFlowResponse.OperationVO> operationVOs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(operations)) {
            operations.sort(Comparator.comparingInt(i -> i.getOrder()));
            operations.forEach(i -> operationVOs.add(new ListApprovalFlowResponse.OperationVO(i)));
        }
        return operationVOs;
    }

    /**
     * 组装审批可操作类型列表
     * @param item
     * @param supportFunctions
     * @return
     */
    private static List<ApprovalOperateTypeEnum> buildOperateTypes(
            ApprovalFlowItemDTO item, List<String> supportFunctions) {
        List<ApprovalOperateTypeEnum> operations = Lists.newArrayList();
        // 详情
        operations.add(ApprovalOperateTypeEnum.DETAIL);
        // 查看审批流
        operations.add(ApprovalOperateTypeEnum.PROCESS);
        // 查看用印审批合同比对记录
        if (Boolean.TRUE.equals(item.getContractAnalysisExist()) && supportFunctions.contains(SEAL_APPROVAL_COMPARE)) {
            operations.add(ApprovalOperateTypeEnum.COMPARE_RECORD);
        }
        if ((ApprovalStatusEnum.APPROVALING.getCode().equals(item.getApprovalStatus()) || 
                ApprovalStatusEnum.APPROVAL_PASS.getCode().equals(item.getApprovalStatus()) ||
                ApprovalStatusEnum.APPROVAL_REFUSE.getCode().equals(item.getApprovalStatus())||
                ApprovalStatusEnum.APPROVAL_REVOKE.getCode().equals(item.getApprovalStatus()))) {
            operations.add(ApprovalOperateTypeEnum.DOWNLOAD);
        }
        // 查看合同摘要
        if (supportFunctions.contains(CONTRACT_SUMMARY)) {
            operations.add(ApprovalOperateTypeEnum.AI_SUMMARY);
        }
        // 审批状态非审批中， 直接返回
        if (!ApprovalStatusEnum.APPROVALING.getCode().equals(item.getApprovalStatus())) {
            return operations;
        }
        // 审批暂停
        if (Boolean.TRUE.equals(item.getPause())) {
            // 删除操作
            operations.add(ApprovalOperateTypeEnum.DELETE);
            return operations;
        }
        // 当前用户是发起人
        if (Boolean.TRUE.equals(item.getIsInitiator())) {
            // 撤回操作
            operations.add(ApprovalOperateTypeEnum.REVOKE);
            // 合同审批支持催办、更换审批流操作, 用印审批不支持
            if (ApprovalTypeEnum.CONTRACT.getCode().equalsIgnoreCase(item.getApprovalType())) {
                // 催办
                operations.add(ApprovalOperateTypeEnum.RUSH);
                // 更换审批流
                operations.add(ApprovalOperateTypeEnum.CHANGE);
            }
        }
        if (Boolean.TRUE.equals(item.getIsCurrentOperator())) {
            // 同意
            operations.add(ApprovalOperateTypeEnum.AGREE);
            // 驳回
            operations.add(ApprovalOperateTypeEnum.REFUSE);
            // 判断是否老审批， 老审批不支持转交
            if (Boolean.FALSE.equals(item.getOldContractData()) && supportFunctions.contains(APPROVAL_TRANSFER)) {
                operations.add(ApprovalOperateTypeEnum.TRANSFER);
            }
        }
        return operations;
    }

    /**
     * 组装发起来源
     * @param processId
     * @param approvalBizType
     * @param startSourceMap
     * @return
     */
    @Deprecated
    public static String buildProcessInitiateFrom(
            String processId, Integer approvalBizType, String flowSource, Map<String, ProcessStartSourceOutputDTO> startSourceMap) {
        if (ApprovalBizTypeEnum.SEAL_APPLY_WITNESS.getType().equals(approvalBizType)) {
            return "天印";
        }
        ProcessStartSourceOutputDTO outputDTO = startSourceMap.get(processId);
        Integer startType = null == outputDTO ? null : outputDTO.getStartType();
        Integer source = null == outputDTO ? null : outputDTO.getSource();
        if (StringUtils.isNotBlank(flowSource)) {
            ClientEnum clientEnum = ClientEnum.valueOfType(flowSource);
            source = null == clientEnum ? source : Integer.valueOf(clientEnum.getClientNo());
        }
        return ProcessSourceMappingUtil.getProcessInitiateFromLabel(source, startType);
    }


    public static ApprovalParticipantVO buildVO(ApprovalParticipantDTO approvalAccount) {
        if (Objects.isNull(approvalAccount)) {
            return null;
        }
        ApprovalParticipantVO vo = new ApprovalParticipantVO();

        if (Objects.nonNull(approvalAccount.getPerson())) {
            ApprovalAccountVO personVO = new ApprovalAccountVO();
            BeanUtils.copyProperties(approvalAccount.getPerson(), personVO);
            vo.setPerson(personVO);
        }

        if (Objects.nonNull(approvalAccount.getSubject())) {
            ApprovalAccountVO subjectVO = new ApprovalAccountVO();
            BeanUtils.copyProperties(approvalAccount.getSubject(), subjectVO);
            vo.setSubject(subjectVO);
        }

        return vo;
    }

    public static ListApprovalFlowResponse.TaskVO buildVO(ApprovalFlowItemDTO.Task task) {
        if (Objects.isNull(task)) {
            return null;
        }

        ListApprovalFlowResponse.TaskVO vo = new ListApprovalFlowResponse.TaskVO();
        List<ApprovalAccountVO> candidateVO =
                Optional.ofNullable(task.getCandidate()).orElse(Collections.emptyList()).stream()
                        .map(
                                candidate -> {
                                    ApprovalAccountVO accountVO = new ApprovalAccountVO();
                                    BeanUtils.copyProperties(candidate, accountVO);
                                    return accountVO;
                                })
                        .collect(Collectors.toList());

        vo.setTaskId(task.getTaskId());
        vo.setCandidate(candidateVO);
        return vo;
    }

    public static ApprovalAggregateResponse.ApprovalAggregateVO buildVO(ApprovalAggrDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        ApprovalAggregateResponse.ApprovalAggregateVO vo =
                new ApprovalAggregateResponse.ApprovalAggregateVO();
        vo.setSubjectName(dto.getSubjectName());
        vo.setSubjectId(dto.getSubjectOid());
        vo.setCount(Optional.ofNullable(dto.getCount()).map(Long::valueOf).orElse(0L));

        return vo;
    }


    public static ApprovalTemplateVO approvalTemplateBO2VO(ApprovalTemplateDTO approvalTemplateBO) {
        if (null == approvalTemplateBO) {
            return null;
        }
        ApprovalTemplateVO approvalTemplateVO = new ApprovalTemplateVO();
        approvalTemplateVO.setApprovalTemplateId(approvalTemplateBO.getApprovalTemplateId());
        approvalTemplateVO.setApprovalTemplateName(approvalTemplateBO.getApprovalTemplateName());
        approvalTemplateVO.setApprovalTemplateType(approvalTemplateBO.getApprovalTemplateType());
        approvalTemplateVO.setApprovalTemplateDescription(approvalTemplateBO.getApprovalTemplateDescription());
        approvalTemplateVO.setContainsDynamicVar(approvalTemplateBO.isContainsDynamicVar());
        return approvalTemplateVO;
    }

    public static List<ApprovalConditionValuesVO> approvalConditionListBO2VO(List<ApprovalConditionValuesDTO> approvalConditionValuesDTOList) {
        if (CollectionUtils.isEmpty(approvalConditionValuesDTOList)) {
            return null;
        }
        
        return approvalConditionValuesDTOList.stream().map(item -> {
            ApprovalConditionValuesVO conditionValuesVO = new ApprovalConditionValuesVO();
            conditionValuesVO.setConditionValue(item.getConditionValue());
            conditionValuesVO.setDisable(item.isDisable());
            return conditionValuesVO;
        }).collect(Collectors.toList());
    }


    public static List<ApprovalTemplateVO> approvalTemplateBO2VO(List<ApprovalTemplateDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(ApprovalVOConverter::approvalTemplateBO2VO).collect(Collectors.toList());
    }

    public static List<ApprovalTemplateSealVO> approvalTemplateSealBO2VO(List<ApprovalTemplateSealBO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(ApprovalVOConverter::approvalTemplateSealBO2VO).collect(Collectors.toList());
    }

    public static ApprovalTemplateSealVO approvalTemplateSealBO2VO(ApprovalTemplateSealBO approvalTemplateSealBO) {
        if (null == approvalTemplateSealBO) {
            return null;
        }
        ApprovalTemplateSealVO approvalTemplateSealVO = new ApprovalTemplateSealVO();
        approvalTemplateSealVO.setSealId(approvalTemplateSealBO.getSealId());
        approvalTemplateSealVO.setSealName(approvalTemplateSealBO.getSealName());
        approvalTemplateSealVO.setUrl(approvalTemplateSealBO.getUrl());
        approvalTemplateSealVO.setApprovalTemplateId(approvalTemplateSealBO.getApprovalTemplateId());
        approvalTemplateSealVO.setApprovalTemplateName(approvalTemplateSealBO.getApprovalTemplateName());
        return approvalTemplateSealVO;
    }

    public static SourceConvertBO convert2SourceConvertBO(ProcessStartSourceOutputDTO outputDTO) {
        SourceConvertBO convertBO = new SourceConvertBO();
        if (outputDTO.isFromSaas()) {
            convertBO.setSource(ProcessSourceMappingUtil.getBizClient(outputDTO.getSource()));
        } else {
            convertBO.setAppName(outputDTO.getAppName());
        }
        convertBO.setStartType(
                ProcessSourceMappingUtil.getStartTypeLabel(outputDTO.getStartType()));
        return convertBO;
    }

    public static SourceConvertBO convert2SourceConvertBOTianYin() {
        SourceConvertBO convertBO = new SourceConvertBO();
        convertBO.setSource("天印");
        return convertBO;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SourceConvertBO {
        /** 发起端 */
        private String source;

        /** 发起方式 */
        private String startType;

        /** 应用名称,非标准签才会有值 */
        private String appName;
    }
}
