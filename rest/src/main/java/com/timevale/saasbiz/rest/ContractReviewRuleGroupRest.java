package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.timevale.mandarin.common.annotation.ExternalService;
import com.timevale.mandarin.common.annotation.RestMapping;
import com.timevale.saas.common.privilege.aspect.UserPrivilegeCheck;
import com.timevale.saasbiz.model.bean.contractreview.RuleDelOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.bo.RuleGroupListBO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.DeleteRuleGroupInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.ListRuleGroupInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.SaveRuleGroupInputDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.contractreview.CascadeOutputResponse;
import com.timevale.saasbiz.rest.bean.contractreview.RuleDelListOutputResponse;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.request.DeleteRuleGroupRequest;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.request.ListRuleGroupRequest;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.request.SaveRuleGroupRequest;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.response.ListRuleGroupResponse;
import com.timevale.saasbiz.rest.bean.contractreview.rulegroup.response.SaveRuleGroupResponse;
import com.timevale.saasbiz.rest.converter.contractreview.RuleGroupInputDTOConverter;
import com.timevale.saasbiz.rest.converter.contractreview.RuleGroupOutputDTOConverter;
import com.timevale.saasbiz.service.contractreview.ContractReviewRuleGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

import static com.timevale.saasbiz.model.constants.PrivilegeOperationConstants.*;
import static com.timevale.saasbiz.model.constants.PrivilegeResourceConstants.PRIVILEGE_RESOURCE_INTELLIGENT_TOOL;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

@Api(tags = "合同审查规则组")
@Validated
@ExternalService
@RestMapping(path = "/v1/contract-review/rule-group")
public class ContractReviewRuleGroupRest {

    @Autowired ContractReviewRuleGroupService contractReviewRuleGroupService;

    @ApiOperation(value = "规则组-列表", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = {ADD_RULE, DELETE_RULE, USE})
    @RestMapping(path = "/list", method = RequestMethod.POST)
    public RestResult<ListRuleGroupResponse> ruleGroupList(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody ListRuleGroupRequest request) {
        ListRuleGroupInputDTO inputDTO = RuleGroupInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        List<RuleGroupListBO> ruleGroupListBOS = contractReviewRuleGroupService.listRuleGroup(inputDTO);
        ListRuleGroupResponse response = new ListRuleGroupResponse();
        response.setGroupList(RuleGroupOutputDTOConverter.convertToListVOS(ruleGroupListBOS));
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则组-保存", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = ADD_RULE)
    @RestMapping(path = "/save", method = RequestMethod.POST)
    public RestResult<SaveRuleGroupResponse> ruleGroupSave(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid SaveRuleGroupRequest request) {
        SaveRuleGroupInputDTO inputDTO = RuleGroupInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        String groupId = contractReviewRuleGroupService.saveRuleGroup(inputDTO);
        SaveRuleGroupResponse response = new SaveRuleGroupResponse();
        response.setGroupId(groupId);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则组-删除确认", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = DELETE_RULE)
    @RestMapping(path = "/del/confirm", method = RequestMethod.POST)
    public RestResult<RuleDelListOutputResponse> ruleGroupDelConfirm(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid DeleteRuleGroupRequest request) {
        DeleteRuleGroupInputDTO inputDTO = RuleGroupInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        RuleDelOutputDTO ruleDelOutputDTO = contractReviewRuleGroupService.confirmDeleteRuleGroup(inputDTO);
        RuleDelListOutputResponse response = JSON.parseObject(JSON.toJSONString(ruleDelOutputDTO), RuleDelListOutputResponse.class);
        return RestResult.success(response);
    }

    @ApiOperation(value = "规则组-删除", notes = "无")
    @UserPrivilegeCheck(
            resourceKey = PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,
            privilegeKey = DELETE_RULE)
    @RestMapping(path = "/del", method = RequestMethod.POST)
    public RestResult<CascadeOutputResponse> ruleGroupDel(
            @RequestHeader(HEADER_TENANT_ID) @NotBlank String tenantId,
            @RequestHeader(HEADER_OPERATOR_ID) @NotBlank String accountId,
            @RequestBody @Valid DeleteRuleGroupRequest request) {
        DeleteRuleGroupInputDTO inputDTO = RuleGroupInputDTOConverter.convertToInputDTO(accountId, tenantId, request);
        contractReviewRuleGroupService.deleteRuleGroup(inputDTO);
        return RestResult.success();
    }
}
