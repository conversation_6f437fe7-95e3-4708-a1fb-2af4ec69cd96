package com.timevale.saasbiz.rest.bean.contractcategory.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.ContractCategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量查询合同类型详情响应数据
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
@ApiModel("批量查询合同类型详情响应数据")
public class BatchQueryContractCategoriesDetailResponse extends ToString {

    @ApiModelProperty("合同类型详情列表")
    private List<ContractCategoryVO> categories;
}
