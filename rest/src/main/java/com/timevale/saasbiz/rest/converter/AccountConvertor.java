package com.timevale.saasbiz.rest.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.timevale.mandarin.base.enums.BaseResultCodeEnum;
import com.timevale.saasbiz.model.bean.account.dto.OrgTransferToInfoDTO;
import com.timevale.saasbiz.model.bean.account.dto.input.UpdateOrgTransferConfigsInputDTO;
import com.timevale.saasbiz.model.bean.account.dto.output.QueryOrgTransferConfigsOutputDTO;
import com.timevale.saasbiz.model.bean.saascommon.dto.BizConfigDTO;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.rest.bean.account.OrgTransferConfigVO;
import com.timevale.saasbiz.rest.bean.account.request.UpdateOrgTransferConfigsRequest;
import com.timevale.saasbiz.rest.bean.account.response.QueryOrgTransferConfigsResponse;

import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.timevale.saasbiz.model.enums.transfer.TransferToUserTypeEnum.ADMIN;

/**
 * 用户信息rest层和service层出入参转换器
 *
 * <AUTHOR>
 * @since 2023-07-30 19:07
 */
public class AccountConvertor {
    public static QueryOrgTransferConfigsResponse convertQueryOrgTransferConfigsResponse(
            QueryOrgTransferConfigsOutputDTO dto) {
        QueryOrgTransferConfigsResponse response = new QueryOrgTransferConfigsResponse();
        response.setTransferConfigList(
                Optional.ofNullable(dto.getOrgTransferConfigList())
                        .map(
                                list ->
                                        list.stream()
                                                .map(
                                                        item -> {
                                                            OrgTransferConfigVO vo =
                                                                    new OrgTransferConfigVO();
                                                            vo.setType(item.getType());
                                                            vo.setTransferToUserType(
                                                                    item.getTransferToUserType());
                                                            vo.setTransferToUserOid(
                                                                    item.getTransferToUserOid());
                                                            vo.setTransferToUserGid(
                                                                    item.getTransferToUserGid());
                                                            vo.setTransferToUserName(
                                                                    item.getTransferToUserName());
                                                            return vo;
                                                        })
                                                .collect(Collectors.toList()))
                        .orElse(Collections.emptyList()));
        return response;
    }

    public static UpdateOrgTransferConfigsInputDTO convertUpdateOrgTransferConfigsInputDTO(
            UpdateOrgTransferConfigsRequest request, String orgOid) {
        UpdateOrgTransferConfigsInputDTO input = new UpdateOrgTransferConfigsInputDTO();
        input.setOrgOId(orgOid);
        input.setBizConfigList(
                request.getTransferConfigList().stream()
                        .map(
                                list -> {
                                    BizConfigDTO dto = new BizConfigDTO();
                                    dto.setKey(list.getType());

                                    OrgTransferToInfoDTO configInfo = new OrgTransferToInfoDTO();
                                    configInfo.setType(list.getTransferToUserType());
                                    if (list.getTransferToUserType().equals(ADMIN.getType())) {
                                        configInfo.setUserOid(list.getTransferToUserOid());
                                    }

                                    try {
                                        dto.setConfigInfo(
                                                new ObjectMapper().writeValueAsString(configInfo));
                                    } catch (JsonProcessingException e) {
                                        throw new SaasBizException(
                                                BaseResultCodeEnum.SYSTEM_ERROR.getNCode(),
                                                "转为配置信息时失败");
                                    }

                                    return dto;
                                })
                        .collect(Collectors.toList()));
        return input;
    }
}
