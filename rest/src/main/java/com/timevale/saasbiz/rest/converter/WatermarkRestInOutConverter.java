package com.timevale.saasbiz.rest.converter;

import com.timevale.saas.common.manage.common.service.model.bean.watermark.WatermarkTemplate;
import com.timevale.saasbiz.model.bean.watermark.dto.input.GenerateWatermarkSnapshotInputDTO;
import com.timevale.saasbiz.model.bean.watermark.dto.input.SaveWatermarkTemplateInputDTO;
import com.timevale.saasbiz.model.bean.watermark.dto.input.WatermarkTemplatePageInputDTO;
import com.timevale.saasbiz.model.bean.watermark.dto.output.WatermarkTemplateDetailOutputDTO;
import com.timevale.saasbiz.model.bean.watermark.dto.output.WatermarkTemplatePageOutputDTO;
import com.timevale.saasbiz.rest.bean.watermark.request.GenerateWatermarkSnapshotRequest;
import com.timevale.saasbiz.rest.bean.watermark.request.SaveWatermarkTemplateRequest;
import com.timevale.saasbiz.rest.bean.watermark.request.WatermarkTemplatePageRequest;
import com.timevale.saasbiz.rest.bean.watermark.response.WatermarkTemplateDetailResponse;
import com.timevale.saasbiz.rest.bean.watermark.response.WatermarkTemplatePageResponse;
import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 水印rest层和service层出入参转换器
 * @date Date : 2023年04月20日 9:47
 */
public class WatermarkRestInOutConverter {

    private static final MapperFacade mapper;

    static {
        MapperFactory mapperFactory = new DefaultMapperFactory.Builder().build();

        //注册classMap
        mapperFactory.classMap(WatermarkTemplatePageRequest.class, WatermarkTemplatePageInputDTO.class)
                .byDefault()
                .register();

        mapperFactory.classMap(WatermarkTemplatePageOutputDTO.class, WatermarkTemplatePageResponse.class)
                .byDefault()
                .register();

        mapperFactory.classMap(SaveWatermarkTemplateRequest.class, SaveWatermarkTemplateInputDTO.class)
                .byDefault()
                .register();

        mapperFactory.classMap(WatermarkTemplate.class, WatermarkTemplateDetailResponse.class)
                .byDefault()
                .register();
        mapper = mapperFactory.getMapperFacade();
    }
    /**
     * 分页查询request转为分页查询inputDTO
     * @param request
     * @param tenantId
     * @param operatorId
     * @return
     */
    public static WatermarkTemplatePageInputDTO pageRequest2pageInput(WatermarkTemplatePageRequest request, String tenantId, String operatorId){
        WatermarkTemplatePageInputDTO pageInputDTO = mapper.map(request, WatermarkTemplatePageInputDTO.class);
        pageInputDTO.setTenantId(tenantId);
        pageInputDTO.setOperatorOid(operatorId);
        return pageInputDTO;
    }

    /**
     * 分页查询output转为分页查询response
     * @param pageOutputDTO
     * @return
     */
    public static WatermarkTemplatePageResponse pageOutput2PageResponse(WatermarkTemplatePageOutputDTO pageOutputDTO) {
        return mapper.map(pageOutputDTO, WatermarkTemplatePageResponse.class);
    }

    /**
     * 保存水印模板saveRequest转为保存水印模板的inputDTO
     * @param request
     * @param tenantId
     * @param operatorId
     * @return
     */
    public static SaveWatermarkTemplateInputDTO saveRequest2SaveInput(SaveWatermarkTemplateRequest request, String tenantId, String operatorId) {

        SaveWatermarkTemplateInputDTO saveInput = mapper.map(request, SaveWatermarkTemplateInputDTO.class);
        saveInput.setTenantId(tenantId);
        saveInput.setOperatorOid(operatorId);
        return saveInput;
    }

    public static GenerateWatermarkSnapshotInputDTO saveRequest2SnapshotInput(GenerateWatermarkSnapshotRequest request, String tenantId, String operatorId) {
        GenerateWatermarkSnapshotInputDTO inputDTO = new GenerateWatermarkSnapshotInputDTO();
        inputDTO.setWatermarkId(request.getWatermarkId());
        inputDTO.setContent(request.getContent());
        inputDTO.setTenantId(tenantId);
        inputDTO.setOperatorOid(operatorId);
        return inputDTO;
    }

    /**
     * 水印模板详情output转为response
     * @param watermarkTemplateDetailOutputDTO
     * @return
     */
    public static WatermarkTemplateDetailResponse detailOutput2DetailResponse(WatermarkTemplateDetailOutputDTO watermarkTemplateDetailOutputDTO) {
        return mapper.map(watermarkTemplateDetailOutputDTO.getWatermarkTemplateDetail(), WatermarkTemplateDetailResponse.class);
    }
}
