package com.timevale.saasbiz.rest.bean.offlinecontract.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量操作线下合同导入记录
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class BatchOfflineContractRecordsRequest extends ToString {

    @ApiModelProperty(value = "导入记录id列表", required = true)
    @NotEmpty(message = "导入记录id列表不能为空")
    private List<String> recordIds;
}
