<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.timevale.saasbiz</groupId>
        <artifactId>saasbiz-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>saasbiz-model</artifactId>
    <name>saasbiz/model</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.timevale.saas</groupId>
            <artifactId>common-util</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.saas-common-manage</groupId>
            <artifactId>saas-common-manage-common-service-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.contractmanager</groupId>
            <artifactId>contractmanager-common-service-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.docmanager</groupId>
            <artifactId>docmanager-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.timevale.doc-cooperation</groupId>
            <artifactId>doc-cooperation-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.timevale.privilege</groupId>
            <artifactId>privilege-facade</artifactId>
            <version>2.3.9-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- 多语言 -->
        <dependency>
            <groupId>com.timevale.saas-utils</groupId>
            <artifactId>multilingual-translate-util</artifactId>
        </dependency>
    </dependencies>
</project>
