package com.timevale.saasbiz.model.enums.approval;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/4/4 14:28
 */
public enum ApprovalVarQueryTypeEnum {

    USER(0,"人"),
    ROL<PERSON>(1, "角色"),
    DEPT(2, "部门"),
    FLOW_TEMPLATE(3, "模版"),
    ;

    ApprovalVarQueryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static ApprovalVarQueryTypeEnum from(Integer code) {
        if (null == code) {
            return null;
        }
        for (ApprovalVarQueryTypeEnum statusEnum : ApprovalVarQueryTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Getter
    private Integer code;

    @Getter
    private String desc;
}
