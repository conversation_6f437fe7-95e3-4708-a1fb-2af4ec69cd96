package com.timevale.saasbiz.model.constants;

public class RemoteConstants {

    /**
     * 设置连接超时时间 根据业务调整
     */
    public static final Integer CONNECTION_TIMEOUT = 20 * 1000;

    /**
     * 设置等待数据超时时间秒钟 根据业务调整
     */
    public static final Integer SOCKET_TIMEOUT = 60 * 1000;

    /**
     * 请求响应超时时间设置请求超时60秒钟 根据业务调整
     */
    public static final Integer REQUEST_TIMEOUT = 60 * 1000;

    /**
     * 设置整个连接池最大连接数 根据自己的场景决定
     */
    public static final Integer POOL_MAX = 200;

    /**
     *  每个主机的并发最多
     */
    public static final Integer POOL_PER_ROUTE = 100;


}
