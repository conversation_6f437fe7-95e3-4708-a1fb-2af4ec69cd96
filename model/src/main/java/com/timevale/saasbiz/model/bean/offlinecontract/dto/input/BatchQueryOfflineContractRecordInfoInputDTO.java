package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 批量查询线下合同导入记录基本信息
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
public class BatchQueryOfflineContractRecordInfoInputDTO extends ToString {

    @NotBlank(message = "主体id不能为空")
    private String subjectId;

    @NotBlank(message = "导入记录id列表不能为空")
    private List<String> recordIds;

    /** 是否返回菜单路径 */
    private boolean withMenuPath;
}
