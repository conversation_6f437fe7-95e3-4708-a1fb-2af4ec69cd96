package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 解析线下合同录入合同信息的excel
 *
 * <AUTHOR>
 * @since 2023-08-29
 */
@Data
public class ReimportFailedOfflineContractInputDTO extends ToString {

    @NotBlank(message = "主体id不能为空")
    private String subjectId;

    @NotBlank(message = "导入记录id不能为空")
    private String recordId;
}
