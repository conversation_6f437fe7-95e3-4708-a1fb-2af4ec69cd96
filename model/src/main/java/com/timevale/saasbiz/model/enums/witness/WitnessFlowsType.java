package com.timevale.saasbiz.model.enums.witness;

import lombok.Getter;

/**
 * 天印流程查询场景枚举
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Getter
public enum WitnessFlowsType {
    WITNESS_FLOWS_USER(0, "用户参与的"),
    WITNESS_FLOWS_VIEW(1, "用户可查看的"),
    WITNESS_FLOWS_ISSUE(2, "用户可出证的"),
    ;

    WitnessFlowsType(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    private int type;

    private String msg;
}
