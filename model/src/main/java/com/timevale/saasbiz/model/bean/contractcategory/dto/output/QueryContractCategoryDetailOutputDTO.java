package com.timevale.saasbiz.model.bean.contractcategory.dto.output;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractcategory.bo.ContractCategoryBO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 查询合同类型详情响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("查询合同类型详情响应数据")
public class QueryContractCategoryDetailOutputDTO extends ContractCategoryBO {}
