package com.timevale.saasbiz.model.constants;

import com.google.common.collect.Lists;
import com.timevale.privilege.service.enums.BuiltinRoleType;

import java.util.List;

/**
 * BizConstants
 *
 * <AUTHOR>
 * @since 2023/4/3 9:59 上午
 */
public class BizConstants {

    public static final String AUTH_RELATION_LOG_PREFIX = "authRelation ";

    public static final Long SAAS_PRODUCT_ID = 64L;

    // SAAS产品编号
    public static final String SAAS_PRODUCT_NO = "service-T-023";

    public static final String AUTH_RELATION_TITLE = "组织关联确认书";

    public static final String PRODUCT_NAME = "多企业合同管理商品";

    public static final String ORDER_PRODUCT_NAME = "企业拓展包";

    public static final String AUTH_RELATION_IMPORT_CONFIG_TRUE = "是";

    public static final String RESOURCE_ORG = " 组织管理（机构、部门、员工和角色权限管理）";

    public static final String RESOURCE_CONTRACT = " 企业合同（企业合同归档、查看、下载等管理）";

    public static final String RESOURCE_SEAL=" 印章管理（印章增删改查，印章授权等管理）";

    public static final String RESOURCE_API = " API调用（通过API接口调用和管理授权的资源）";

    public static final String RESOURCE_TEMPLATE = " 合同模板（使用、增删改查、授权分公司的合同模板）";
    public static final String RESOURCE_APPROVAL = " 审批管理（可管理分公司的审批模板）";

    public static final String RESOURCE_TEMPLATE_USE = " 合同模板使用";
    public static final String RESOURCE_TEMPLATE_DELETE = " 合同模板下载, 新增, 编辑, 删除";
    public static final String RESOURCE_TEMPLATE_AUTH = " 合同模板授权";


    public static final String PROCESS_TRANSFER_TYPE = "process_transfer";

    public static final String SEAL_APPROVAL_TRANSFER_TYPE = "seal_approval_transfer";

    public static final String CONTRACT_APPROVAL_TRANSFER_TYPE = "contract_approval_transfer";

    public static final String ADMIN_TYPE = "admin";

    public static final String DEPT_HEAD_TYPE = "dept_head";

    public static final List<String> ALL_ROLE = Lists.newArrayList(
            BuiltinRoleType.ADMIN, BuiltinRoleType.ORGAN_LEGAL, BuiltinRoleType.SUBSIDIARY_ADMIN);

    public static final List<String> FORCE_ROLE = Lists.newArrayList(
            BuiltinRoleType.AUTOSIGN_AUTHORIZER, BuiltinRoleType.SEAL_USER, BuiltinRoleType.FEE_ADMIN,
            BuiltinRoleType.DEVELOPER, BuiltinRoleType.MEMBER, BuiltinRoleType.SPACE_ADMIN, "ZQB_MEMBER");

    /** 开放平台配置：是否启用合同摘要 */
    public static final String OPEN_CONFIG_SHOW_PROCESS_SUMMARY = "showProcessSummary";

    /** 开放平台配置：强制意愿是否开启 */
    public static final String OPEN_CONFIG_COERCIVE_WILL = "coerciveWill";
    /** 开放平台配置：强制意愿是否开启, 开启情况下对应的值 */
    public static final String OPEN_CONFIG_VALUE_COERCIVE_WILL_ENABLE = "1";
    /**
     * 台账最小可运行算力
     */
    public static final Integer LEDGER_MIN_PAGE_ENOUGH = 3;

    /**
     * 关联企业共享标识
     */
    public static final String RELATION_SHARE_KEY_SIGN = "shareSign";

    public static final String DEDICATED_CLOUD_CANNOT_AI = "专属云合同流程不支持AI合同摘要操作";

    public static final Integer SUCCESS_CODE = 0;

    /**
     * 签署详情页agent
     */
    public static final String APP_CONFIG_SIGN_PAGE_AGENT = "signPageAgent";

    /** 水印模板快照数据前缀 */
    public static final String WATERMARK_SNAP_SHOOT_PREFIX = "SS_";

}
