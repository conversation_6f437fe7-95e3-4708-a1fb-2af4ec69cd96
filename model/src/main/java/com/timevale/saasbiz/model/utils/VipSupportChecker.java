package com.timevale.saasbiz.model.utils;

import com.timevale.saas.common.manage.common.service.enums.VipCodeEnum;

import java.util.HashMap;
import java.util.Map;

public class VipSupportChecker {
    // 自定义优先级映射（需根据业务逻辑定义）
    private static final Map<VipCodeEnum, Integer> PRIORITY_MAP = new HashMap<>();
    static {
        PRIORITY_MAP.put(VipCodeEnum.TRIAL, 0);
        PRIORITY_MAP.put(VipCodeEnum.BASE, 1);
        PRIORITY_MAP.put(VipCodeEnum.PROFESSIONAL, 2);
        PRIORITY_MAP.put(VipCodeEnum.SENIOR, 3);
        PRIORITY_MAP.put(VipCodeEnum.FLAGSHIP, 4);
    }

    /**
     * 判断当前版本是否支持目标版本
     * @param currentVersion 当前版本
     * @param targetVersion 目标版本
     * @return 是否支持
     */
    public static boolean supports(VipCodeEnum currentVersion, VipCodeEnum targetVersion) {
        // 确保枚举值在映射中存在
        if (!PRIORITY_MAP.containsKey(currentVersion) || !PRIORITY_MAP.containsKey(targetVersion)) {
            throw new IllegalArgumentException("未知的版本类型");
        }
        return PRIORITY_MAP.get(currentVersion) >= PRIORITY_MAP.get(targetVersion);
    }

}
