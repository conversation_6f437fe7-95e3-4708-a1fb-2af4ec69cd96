package com.timevale.saasbiz.model.exception;

import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * 运行时异常类
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
public class SaasBizRuntimeException extends BaseBizRuntimeException {

    public SaasBizRuntimeException(String detailMessage) {
        this(SaasBizResultCode.SAAS_SERVICE_ERROR.getCode(), detailMessage);
    }

    public SaasBizRuntimeException(SaasBizResultCode resultCode) {
        this(resultCode.getCode(), resultCode.getMessage());
    }

    public SaasBizRuntimeException(SaasBizResultCode resultCode, Object... args) {
        this(resultCode.getCode(), resultCode.getMessage());
        if (StringUtils.isNotBlank(resultCode.getMessage())) {
            this.message = String.format(message, args);
        }
    }

    public SaasBizRuntimeException(int code, String detailMessage) {
        super(String.valueOf(code), detailMessage);
    }
}
