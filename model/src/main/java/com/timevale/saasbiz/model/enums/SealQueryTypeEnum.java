package com.timevale.saasbiz.model.enums;
/**
 * <AUTHOR>
 * @since 2023-07-21 11:34
 */
public enum SealQueryTypeEnum {
    ORGAN_DEPT("ORGAN_DEPT", "部门"),
    ORGAN_MEMBER("ORGAN_MEMBER", "成员");

    private final String code;
    private final String desc;

    SealQueryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
