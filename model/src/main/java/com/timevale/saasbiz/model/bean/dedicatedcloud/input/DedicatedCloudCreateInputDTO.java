package com.timevale.saasbiz.model.bean.dedicatedcloud.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/1 10:35
 */
@Data
public class DedicatedCloudCreateInputDTO {

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("绑定appId")
    private  String appId;

    @ApiModelProperty("专属云地址")
    private String serverUrl;

    @ApiModelProperty("授权appId")
    List<DedicatedCloudAuthAppIdInputDTO> authAppIds;

    @ApiModelProperty("当前企业oid")
    private String subjectOid;

    @ApiModelProperty("当前操作人oid")
    private String operatorOid;
}
