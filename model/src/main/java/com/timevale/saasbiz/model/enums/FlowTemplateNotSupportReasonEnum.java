package com.timevale.saasbiz.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/8/14 17:14
 */
@Getter
public enum FlowTemplateNotSupportReasonEnum {

    OA("DING_DING_OA", "钉钉审批模板该端暂不支持"),
    DATA_SOURCE("FLOW_TEMPLATE_DATA_SOURCE", "数据源模板该端暂不支持"),
    PARTICIPANT_START_TYPE("PARTICIPANT_START_TYPE", "个人签署方存在证件号信息或者企业签署方存在指定企业名称/企业角色暂不支持"),
    NO_DOC_TEMPLATE("NO_DOC_TEMPLATE", "请在模板中设置文件"),
    ;
    private final String code;
    private final String desc;

    FlowTemplateNotSupportReasonEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
