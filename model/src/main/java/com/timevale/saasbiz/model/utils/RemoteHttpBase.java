package com.timevale.saasbiz.model.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpResponse;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

@Slf4j
public class RemoteHttpBase {

    /** Default initial size of the response buffer if content length is unknown. */
    private static final int DEFAULT_INITIAL_BUFFER_SIZE = 64 * 1024;

    protected static byte[] getResponseBody(HttpResponse response) throws IOException {
        if (response == null) {
            return new byte[0];
        }

        InputStream instream = response.getEntity().getContent();
        if (instream == null) {
            return new byte[0];
        }

        long contentLength = getResponseContentLength(response);
        // guard below cast from overflow
        if (contentLength > Integer.MAX_VALUE) {
            throw new IOException("Content too large to be buffered: " + contentLength + " bytes");
        }

        if (contentLength == -1) {
            log.warn(
                    "Going to buffer response body of large or unknown size. "
                            + "Using getResponseBodyAsStream instead is recommended.");
        }
        log.debug("Buffering response body");
        ByteArrayOutputStream outstream =
                new ByteArrayOutputStream(
                        contentLength > 0 ? (int) contentLength : DEFAULT_INITIAL_BUFFER_SIZE);
        byte[] buffer = new byte[8192];
        int len;
        while ((len = instream.read(buffer)) > 0) {
            outstream.write(buffer, 0, len);
        }
        outstream.close();
        return outstream.toByteArray();
    }

    protected static long getResponseContentLength(HttpResponse response) {
        Header[] headers = response.getHeaders("Content-Length");
        if (headers.length == 0) {
            return -1;
        }
        if (headers.length > 1) {
            log.warn("Multiple content-length headers detected");
        }
        for (int i = headers.length - 1; i >= 0; i--) {
            Header header = headers[i];
            try {
                return Long.parseLong(header.getValue());
            } catch (NumberFormatException e) {
                if (log.isWarnEnabled()) {
                    log.warn("Invalid content-length value: " + e.getMessage());
                }
            }
            // See if we can have better luck with another header, if present
        }
        return -1;
    }
}
