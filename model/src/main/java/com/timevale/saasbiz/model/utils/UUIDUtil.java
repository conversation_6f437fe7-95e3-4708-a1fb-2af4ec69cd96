package com.timevale.saasbiz.model.utils;

import java.util.UUID;

/**
 * 随机数生成工具类
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public class UUIDUtil {

    /**
     * 生成32位的uuid随机数
     *
     * @return
     */
    public static String genUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成32位的uuid随机数并添加前缀
     *
     * @return
     */
    public static String genUUIDWithPrefix(String prefix) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        if (prefix == null) {
            return uuid;
        }
        return prefix + uuid;
    }
}
