package com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.output;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.RuleInventoryListBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageRuleInventoryOutputDTO extends ToString {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("审查清单列表")
    private List<RuleInventoryListBO> inventoryList;
}