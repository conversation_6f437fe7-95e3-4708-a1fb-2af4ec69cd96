package com.timevale.saasbiz.model.enums.contractaudit;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Getter
@AllArgsConstructor
public enum ContractAuditCheckSceneEnum {
    NORMAL("NORMAL", "正常使用"),
    GRAY_NOT_OPEN("GRAY_NOT_OPEN", "灰度未开启"),
    VIP_NOT_SUPPORT("VIP_NOT_SUPPORT", "vip版本不支持"),
    USER_NOT_AGREEMENT("USER_NOT_AGREEMENT", "用户未授权"),
    OTHER_NOT_SUPPORT("OTHER_NOT_SUPPORT", "其他不支持场景"),
    ;
    private final String code;
    private final String desc;
}
