package com.timevale.saasbiz.model.enums.process;

/**
 * 流程发起场景
 *
 * <AUTHOR>
 * @since 2023-05-21
 */
public enum ProcessStartScene {

    /** 本地直接发起 */
    DIRECT_START(1),
    /** 模板发起 */
    TEMPLATE_START(2),
    ;
    ProcessStartScene(int scene) {
        this.scene = scene;
    }

    private int scene;

    public int getScene() {
        return scene;
    }

    public static ProcessStartScene getScene(Integer scene){
        for(ProcessStartScene value : ProcessStartScene.values()){
            if(value.getScene() == scene)   return value;
        }
        throw new IllegalArgumentException();
    }
}
