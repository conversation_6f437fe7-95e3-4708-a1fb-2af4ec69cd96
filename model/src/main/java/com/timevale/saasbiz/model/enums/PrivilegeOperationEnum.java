package com.timevale.saasbiz.model.enums;

import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 用户权限资操作类型枚举
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum PrivilegeOperationEnum {

    ALL(PrivilegeOperationConstants.ALL, "所有权限", ""),
    QUERY("QUERY", "查询权限", "查询"),
    LOOK("LOOK", "查询权限", "查询"),
    EXPORT("EXPORT", "导出", "导出"),
    PRIVILEGE_QUERY_BOOK_KEEPING("QUERY_BOOK_KEEPING", "查看台账", "查看台账"),
    QUERY_LOG("QUERY_LOG", "查询日志权限", ""),
    ADD("ADD", "新增权限", "新增"),
    ADD_RULE(PrivilegeOperationConstants.ADD_RULE, "新增自定义规则", "新增"),
    AUTH("AUTH", "授权权限", "授权"),
    CREATE("CREATE", "新增权限", "新增"),
    UPDATE(PrivilegeOperationConstants.UPDATE, "修改权限", "修改"),
    DELETE("DELETE", "删除权限", "删除"),
    CONTRACT("CONTRACT", "新增、编辑、删除、启用、禁用合同审批模板", ""),
    SEAL("SEAL", "可以新增、编辑、复制、删除、启用、禁用用印审批模板", ""),
    ADD_CONTRACT_CATEGORY(PrivilegeOperationConstants.ADD_CONTRACT_CATEGORY, "新增合同类型", ""),
    QUERY_CONTRACT_CATEGORY(PrivilegeOperationConstants.QUERY_CONTRACT_CATEGORY, "查看合同类型", ""),
    UPDATE_CONTRACT_CATEGORY(PrivilegeOperationConstants.UPDATE_CONTRACT_CATEGORY, "编辑合同类型", ""),
    DELETE_CONTRACT_CATEGORY(PrivilegeOperationConstants.DELETE_CONTRACT_CATEGORY, "删除合同类型", ""),
    ;

    private String type;
    private String desc;
    private String tip;

    public static String getTip(String type){
        for (PrivilegeOperationEnum value : values()) {
            if (value.getType().equalsIgnoreCase(type)) {
                return value.getTip();
            }
        }
        return type;
    }
}
