package com.timevale.saasbiz.model.enums.authrelation;

import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationSortTypeEnum;
import lombok.Getter;

@Getter
public enum AuthRelationQuerySceneEnum {

    NORMAL("NORMAL", "通用场景", AuthRelationSortTypeEnum.UPDATE_TIME_DESC.getType()),
    RENEW("RENEW", "查询可续签列表", AuthRelationSortTypeEnum.EFFECTIVE_TO_ASC.getType()),
    ;

    AuthRelationQuerySceneEnum(String scene, String desc, String sortType) {
        this.scene = scene;
        this.desc = desc;
        this.sortType = sortType;
    }

    private final String scene;

    private final String desc;

    private final String sortType;

    public static AuthRelationQuerySceneEnum from(String scene) {
        for (AuthRelationQuerySceneEnum value : values()) {
            if (value.getScene().equalsIgnoreCase(scene)) {
                return value;
            }
        }
        return NORMAL;
    }
}
