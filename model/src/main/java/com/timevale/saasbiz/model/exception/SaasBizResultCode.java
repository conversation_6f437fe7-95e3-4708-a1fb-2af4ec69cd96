package com.timevale.saasbiz.model.exception;

import lombok.Getter;

/**
 * Rest接口结果码枚举, 错误码长度为8位
 *
 * <p>错误码注册：http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=172205760
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
@Getter
public enum SaasBizResultCode {
    /** 成功 */
    SUCCESS(0, "成功"),

    /** 通用结果码， 结果码范围 120000000- 120000099 */
    SAAS_SERVICE_BUSY(120000000, "系统繁忙，请稍后重试"),
    SAAS_BIZ_ERROR(120000001, "业务异常"),
    SAAS_SERVICE_ERROR(120000002, "服务异常"),
    SAAS_IDEMPOTENT_FAILED(120000003, "请勿重复提交"),
    SAAS_ILLEGAL_PARAM(120000004, "参数错误: %s"),
    CRM_SERVICE_ERROR(120000005,"CRM服务异常"),
    ALIAS_MISS_ERROR(120000006,"获取用户信息失败， 请重新登录"),
    EXCEL_NOT_EXIST(120000007,"excel信息未找到"),
    EXCEL_TOO_LARGE(120000008,"导入Excel文件最大支持%sM，超出请分批处理"),
    EXCEL_CHANGED(120000009,"excel格式错误，请重新下载最新Excel模板并正确填写上传"),
    EXCEL_DOWNLOAD_FAILED(*********,"下载表格失败"),
    EXCEL_TOO_MANY_ROW(*********,"导入Excel文件最大支持%s行数据，超出请分批处理"),
    QUERY_START_TIME_BEFORE_END_TIME(*********, "结束时间不能早于开始时间"),
    EXCEL_IS_BEHIND(*********,"导入Excel模版不支持，请重新下载"),


    /** 用户中心结果码， 结果码范围 *********- ********* */
    USER_ACCOUNT_NOT_EXIST(*********, "账号不存在或已注销"),
    ORG_MEMBER_NOT_EXIST(*********, "企业成员不存在"),
    HAS_NO_PRIVILEGE(*********, "用户无操作权限"),
    ORG_NOT_EXIST(*********, "企业不存在或已注销"),
    APP_NOT_EXIST(*********, "应用不存在"),
    USER_CENTER_ACCOUNT_QUERY_FAIL(*********, "用户中心查询账户失败: %s"),
    ES_QUERY__QUERY_FAIL(*********, "es查询失败"),
    PERMISSION_CODE_MISSING_APPLY_MSG(*********, "当前权限暂未配置申请文案"),
    ORG_DEPT_NOT_EXIST(*********, "企业部门不存在"),

    DEPT_AND_MEMBER_EMPTY(*********, "未指定可见部门或成员"),
    UNREALIZED_MEMBER_EXIST(*********, "存在未实名成员"),
    DEPT_NOT_BELONG_TO_ORG(*********, "部分部门不属于当前企业"),
    MEMBER_NOT_BELONG_TO_ORG(*********, "部分成员不属于当前企业"),
    USER_AGREEMENT_NOT_SUPPORT(*********, "系统暂未配置对应的用户协议，请稍后重试"),
    USER_AGREEMENT_NOT_SUPPORT_NOTICE(*********, "系统暂不支持发送通知，请稍后重试"),
    ORG_ADMIN_NOT_FOUND(*********, "当前企业无管理员，请通过‘企业找回’方式申请成为管理员"),


    /** 天印流程结果码， 结果码范围 *********- ********* */
    WITNESS_ILLEGAL_PARAM( *********, "参数错误: %s"),
    WITNESS_NOT_ISSUE_PRIVILEGE(120000301, "当前用户无出证权限，无法获取出证列表"),
    WITNESS_NO_BATCH_SIGN_FLOWS(120000302, "没有可批量签署的流程"),
    WITNESS_NOT_SUPPORT_BATCH_SIGN(120000303, "当前来源不支持批量签署"),
    WITNESS_BATCH_SIGN_OVER_TIME(120000304, "批量签署已超时， 需返回列表页重新发起"),
    WITNESS_BATCH_SIGN_SERIAL_AUTH_FAIL(120000305, "无权获取批量签署流程信息"),
    WITNESS_REQUEST_OVER_MAX_LIMIT(120000306, "当前页码超限,请重置查询条件后重试"),
    NO_PROCESS_VIEW_PRIVILEGE(120000307, "您没有查看当前流程的权限"),

    /** 流程模板结果码， 结果码范围 120000400- 120000499 */
    NO_FLOW_TEMPLATE_PRIVILEGE(120000400, "您没有模板%s权限，请先联系管理员授权"),
    VIP_FUNCTION_NOT_SUPPORT(120000401, "版本功能不支持"),
    FLOW_TEMPLATE_NOT_EXIST(120000402, "流程模板不存在"),
    FLOW_TEMPLATE_OWNER_FAIL(120000403, "流程模板不属于该账号"),
    FLOW_TEMPLATE_NOT_ENABLE(120000404, "该模板未启用，请联系管理员进入PC端模板列表启用"),
    FLOW_TEMPLATE_CHECK_ERROR(120000405, "流程模板校验失败： %s"),
    FLOW_TEMPLATE_FILE_NOT_EXIST(120000406, "流程模板中不存在该文件"),
    FLOW_TEMPLATE_AI_PARSE_STRUCT_FAIL(120000407, "AI识别控件失败"),
    FLOW_TEMPLATE_ENABLE_ERROR(120000408, "开启失败: %s"),
    FLOW_TEMPLATE_SHARE_CODE_ERROR(120000409, "模板共享码错误"),
    FLOW_TEMPLATE_SHARE_CODE_EXPIRED(120000410, "该模板共享码已经过期"),
    FLOW_TEMPLATE_DELETED(120000411, "该模板已经被删除"),
    FLOW_TEMPLATE_CREATE_LIMIT(120000412, "您的会员版本最多只支持创建%s个模板，需要购买更高级的版本。"),
    FLOW_TEMPLATE_INVALID_BUILTIN(120000413, "当前系统模板未启用"),
    FLOW_TEMPLATE_SHARE_TYPE_NOT_SUPPORT(120000414,"流程模板分享类型不支持"),
    FLOW_TEMPLATE_NOT_EXIST_OR_EXPIRE(120000415, "流程模板导入链接不存在或已失效"),
    PROCESS_BATCH_EXCEL_PLUGINS_NOT_MATCH(120000416,"未匹配到自定义批量发起Excel表头信息的业务插件"),
    PROCESS_BATCH_EXCEL_PLUGINS_FAIL(120000417,"插件处理批量发起Excel表头信息失败"),
    AFFILIATED_ENTERPRISE_NOT_EXIT(120000418,"授权企业与当前企业关联关系不存在或已失效，请检查后重试"),
    QUERY_CONNECT_ENTERPRISE_ERR(120000419,"查询关联企业失败"),
    FLOW_TEMPLATE_AUTH_SUB_ENTERPRISE_LIMIT(120000420,"流程模板最多支持授权给%s个关联企业，请重新选择"),
    FLOW_TEMPLATE_CAN_NOT_ACROSS_ENTERPRISE(120000421,"该模板由上级关联企业授权，仅允许您企业内部员工使用，不支持再授权给下级关联企业"),
    FLOW_TEMPLATE_FILE_CREATE_FAIL(120000422,"模板文件创建失败：%s"),
    FLOW_TEMPLATE_FILE_COPY_FAIL(120000423,"模板文件复制失败，请稍后重试"),
    DYNAMIC_FLOW_TEMPLATE_NOT_SUPPORT_VIP(120000424,"当前企业不支持动态模板，请升级会员版本或联系客服添加该功能！"),
    DYNAMIC_FLOW_TEMPLATE_NOT_SUPPORT_GRAY(120000425,"当前企业不支持动态模板，请联系客服添加该灰度功能进行体验！"),
    FLOW_TEMPLATE_START_SCENE_NOT_SUPPORT(120000426,"流程模板创建/更新场景不支持"),
    FLOW_TEMPLATE_LINK_APPROVAL_ERROR(120000427, "流程模板关联审批模板失败"),
    FLOW_TEMPLATE_SIGNER_DELETED(120000428, "签署方 %s %s 账号已注销，需修改模板后再发起签署"),
    FLOW_TEMPLATE_CC_DELETED(120000429, "抄送人 %s %s 账号已注销，需修改模板后再发起签署"),
    FLOW_TEMPLATE_NO_OPERATION_PERMIT(120000431, "未设置权限，请先设置模板管理权限"),
    FLOW_TEMPLATE_NO_AUTH_RELATION(120000432, "无权查询关联企业模板"),
    CREATE_RELATION_ENTERPRISES_MAX(120000433, "已超出关联企业添加上限，至多可添加%s个"),
    FLOW_TEMPLATE_SCHEDULE_START_NOT_SUPPORT(120000434, "当前流程模板不支持提交定时发起"),

    /** 流程结果码， 结果码范围 120000500- 120000599 */
    EXPERIENCE_START_ALREADY_DONE(120000500, "该企业已进行过体验签署，无需重复发起"),
    PROCESS_NOT_EXIST(120000501, "流程不存在"),
    PROCESS_SUMMARY_TYPE_ERROR(120000502, "不能提取合同摘要的合同类型"),
    PROCESS_SUMMARY_WRITE_NOT_DONE(120000503, "未填写完成的合同，不支持合同摘要生成"),
    PROCESS_SUMMARY_LACK_NECESSARY_DATA(120000504, "合同数据不完整，请稍后再试"),
    PROCESS_SUMMARY_FUNC_NOT_SUPPORT(120000505, "当前版本功能不支持"),
    COOPERATION_FAILED(120000506, "%s"),
    COOPERATION_FLOW_NOT_EXIST(120000507, "协作流程不存在"),
    PROCESS_COOPERATION_NOT_COOPERATING(120000508, "流程未在填写状态"),
    PROCESS_SUMMARY_AI_NOT_AGGREMENT(120000509, "请先同意ai数据授权协议"),
    PROCESS_SUMMARY_APPID_CONFIG_NOT_SUPPORT(1200005010, "当前不支持合同摘要，若需要请联系销售、客户成功进行配置"),
    PROCESS_SUMMARY_INITIATOR(120000511, "只有流程发起方可生成合同摘要"),
    DEDICATED_CLOUD_NOT_SUPPORT(1200005012, "专属云合同无法使用AI合同摘要"),
    AIAGENT_NOT_IN_SUPPORT(1200005013, "当前不支持，若需要请联系销售、客户成功进行配置"),


    /** 审批 结果码范围 120000600- 120000699 */
    APPROVAL_NO_PERMISSION(120000600, "您无权限查看"),
    NO_BALANCE_APPROVAL_AGREE_ERROR(120000601,"企业套餐份额不足，请订购套餐后重新审批，份额不足的企业：%s"),
    APPROVAL_ALREADY_OPERATED(120000602,"该流程已被其他人审批，无需再审批"),
    APPROVAL_WILL_AUTH_OVERDUE(120000603,"审批意愿未发起或超时未完成， 请重新操作"),
    APPROVAL_LIST_URL_NOT_CONFIG(120000604,"审批列表地址未配置，暂不支持获取"),
    APPROVAL_CONTRACT_ANALYSIS_CREATE_FAILED(120000605,"创建合同比对记录失败"),
    APPROVAL_CONTRACT_ANALYSIS_UPDATE_FAILED(120000606,"合同比对更新失败"),
    APPROVAL_CONTRACT_ANALYSIS_NOT_EXISTS(120000607,"合同比对记录不存在"),
    APPROVAL_CONTRACT_ANALYSIS_FILE_URL_DENY_ERROR(120000608,"您无权查看合同比对文件"),
    APPROVAL_LIST_MEMBER_QUERY_LIMIT(120000609,"当前用户无权查看企业成员的审批列表数据"),
    APPROVAL_VIEW_RANGE_UPPER_LIMIT(120000610,"人员可见范围设置不能超过%s个部门、角色或成员"),
    APPROVAL_TEMPLATE_OR_SEAL_UPPER_LIMIT(120000611,"印章和模板设置不能超过%s个"),
    APPROVAL_EXPORT_NO_DATA(120000612,"无可导出的审批数据"),
    APPROVAL_EXPORT_LIMIT(120000613,"当前导出数据超出上限, 请修改导出条件后重新导出"),
    APPROVAL_LIST_MEMBER_QUERY_LIMIT_ADMIN(120000614,"仅支持管理员查看企业全部审批列表数据"),
    ILLEGAL_APPROVAL_COMMENTS(120000615,"非审批流参与人员无法对审批流程进行评论，如需评论请联系管理员添加为审批人或抄送人"),
    APPROVAL_STATUS_NOT_COMMENT(120000616,"当前审批状态不支持评论"),



    /** 证书服务结果码，结果码范围 120000700- 120000719 */
    CERT_TYPE_ERROR(120000700, "证书类型不匹配，请确认"),
    GET_CERT_INFO_ERROR(120000701, "获取证书信息异常，请稍后再试"),

    /** 线下合同结果码，结果码范围 120000730- 120000759 */
    OFFLINE_CONTRACT_NOT_SUPPORT(120000730, "当前企业不支持纸质合同导入功能"),
    OFFLINE_CONTRACT_IMPORT_LIMIT(120000731, "单次导入最多支持%s条"),
    OFFLINE_CONTRACT_IMPORT_OVER(120000732, "当前企业已超出最大纸质文件导入上限，请联系客户经理"),


    /** 水印相关错误码，结果码范围 120000800- 120000899 **/
    WATERMARK_CONTENT_OVER_LIMIT(120000800,"水印模板的内容长度超出规定范围"),

    /** 印章相关错误码，结果范围 120000900-120000999 */
    SEAL_USAGE_DURATION_OVER_LIMIT(120000900, "当前仅支持统计跨度不超过3个月的用印记录"),
    SEAL_OWNER_ID_NULL_ERR(120000901, "印章所属企业ID不能为空"),
    ORG_NO_PRIVILEGE_OPERATE(120000902, "当前企业无权操作该批次印章"),
    SEAL_QUERY_FAIL(120000903, "印章查询失败: %s"),
    LEGAL_USER_SEAL_AUTH_QUERY_FAILED(120000904, "查询法人章授权记录失败: %s"),
    GROUP_SEAL_OWNER_AUTH_NOT_MATCH(120000905, "当前选择的印章中存在授权关系失效的印章，请刷新列表后重新操作"),

    /** 自动归档，结果范围 120001000-120001099 */
    AUTO_ARCHIVE_BIND_ERROR(120001000, "该分类已绑定规则，请重新选择"),

    /** 台账，结果范围 120001100-120001199 */
    LEDGER_BIND_FORM_NOT_EXIST_ERROR(120001100, "绑定的台账规则不存在"),
    LEDGER_RUNNING(120001101, "台账正在运行"),
    LEDGER_NOT_PAUSE(120001102, "台账不是暂停状态"),
    LEDGER_NOT_EXIST(120001103, "台账不存在"),

    /** 信息采集错误码，结果范围 120001100-120001199 */
    INFO_COLLECT_IMPORT_SIZE_LIMIT(120001100, "导入数据数量超过限制"),

    /** 审计日志错误码，结果范围 120001200-120001299 */
    QUERY_AUDIT_LOG_MORE_THAN_ONE_YEARS(120001200, "超出最近1年的查询数据，需要导出表格查看，不能直接在线查看数据"),
    DOWNLOAD_AUDIT_LOG_URL_ERROR(120001201, "查询审计日志异常下载链接异常：%s"),
    /** 会员功能相关错误码，结果范围 120001300-120001350 */
    VIP_FUNCTION_EXIST_ERROR(120001300, "会员功能不存在"),

    /** commonRest, 结果范围 120001350-120001399 */
    NOT_IMPL_FUNC_CODE(120001350, "未支持的功能标识"),

    /** llm-balance, 结果范围 120001400-120001499 */
    BALANCE_NOT_ENOUGH(120001400, "算力余额不足，请到企业账户内充值后继续使用"),

    /** 比对相关, 结果范围 120001500-120001599 */
    NOT_SUPPORT_COMPARE(120001500, "当前企业不支持合同比对"),
    DEDICATED_CLOUD_NOT_SUPPORT_COMPARE(120001501, "专属云不支持合同比对"),
    CAN_NOT_CREATE_COMPARE(120001502, "无权查看该流程或文件，无法生成比对"),
    FIND_COMPARE_FILE_FAIL(120001503, "查询比对文件失败, %s"),
    FILE_TYPE_NOT_SUPPORT_COMPARE(120001504, "当前文件类型暂不支持比对：%s"),

    COPY_FILES_FAIL(120001505, "批量复制文件失败, %s"),

    /** 合同审查，结果范围 120001600-120001699 */
    CONTRACT_AUDIT_NOT_SUPPORT(120001600, "企业暂不支持合同审查"),
    CONTRACT_REVIEW_FILE_NOT_UPLOAD(120001601, "审查文件未上传"),
    CONTRACT_REVIEW_FILE_NOT_OWNER(120001602, "无权审查他人文件"),
    CONTRACT_REVIEW_COMMON_ERROR(120001603, "%s"),
    REVIEW_STATUS_ERROR_NO_DOWNLOAD(120001604,"审查未完成，无法下载"),

    /** 通用文件鉴权 120001700-120001799 */
    DEDICATED_CLOUD_NOT_SUPPORT_BIZ(120001701, "专属云不支持%s"),
    CAN_NOT_CREATE_BIZ(120001702, "无权查看该流程或文件，无法创建%s"),

    /** 智能体通用入参，结果范围 120001800-120001899 */
    AGENT_PARAM_PROCESS_ID_MISS(120001800, "process_id不能为空"),
    AGENT_SCENE_NOT_FOUND(120001801, "场景不存在"),
    AGENT_PARAM_PROCESS_NOT_FOUND(120001802, "流程不存在"),
    AGENT_PARAM_FLOW_TYPE_MISS(120001803, "流程类型不能为空"),

    /** 合同结构化，结果范围 120001900-120001999*/
    DOC_ANALYSIS_COMMON_ERROR(120001900, "%s"),
    ;

    private int code;
    private String message;

    SaasBizResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
