package com.timevale.saasbiz.model.enums.approval;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-02-28
 */
@Getter
public enum ApprovalBizStatusEnum {
    NORMAL("NORMAL", "业务流程正常进行"),
    REVOKED("REVOKED", "业务流程已撤回"),
    REFUSED("REFUSED", "业务流程已拒绝"),
    TERMINATED("TERMINATED", "业务流程已终止"),
    EXPIRED("EXPIRED", "业务流程已过期"),
    CANCELLED("CANCELLED", "业务流程已作废"),
    FROZEN("FROZEN", "业务流程已冻结"),
    ;

    /** 操作类型 */
    private String type;
    /** 操作描述 */
    private String desc;

    ApprovalBizStatusEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
