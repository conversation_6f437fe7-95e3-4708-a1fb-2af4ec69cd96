package com.timevale.saasbiz.model.bean.dedicatedcloud.input;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/1 16:14
 */
@Data
public class DedicatedCloudUpdateInputDTO {


    @ApiModelProperty("项目id")
    private String dedicatedCloudId;

    @ApiModelProperty("项目名称")
    @Length(max = 200, message = "projectName最大支持200长度")
    @NotBlank(message = "projectName不能为空")
    private String projectName;

    @ApiModelProperty("专属云地址")
    private String serverUrl;

    @ApiModelProperty("授权appId")
    @Size(max = 100, message = "授权appId不允许超过100")
    @Valid
    List<DedicatedCloudAuthAppIdInputDTO> authAppIds;

    @ApiModelProperty("当前企业oid")
    private String subjectOid;

    @ApiModelProperty("当前操作人oid")
    private String operatorOid;
}
