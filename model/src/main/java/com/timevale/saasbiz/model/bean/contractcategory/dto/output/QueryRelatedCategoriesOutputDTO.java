package com.timevale.saasbiz.model.bean.contractcategory.dto.output;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractcategory.bo.FileContractCategoryBO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 查询流程模板关联的合同类型列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
public class QueryRelatedCategoriesOutputDTO extends ToString {

    /** 文件合同类型列表 */
    private List<FileContractCategoryBO> categoryies;
}
