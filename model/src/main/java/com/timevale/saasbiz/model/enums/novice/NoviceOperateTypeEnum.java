package com.timevale.saasbiz.model.enums.novice;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-17
 */
@Getter
public enum NoviceOperateTypeEnum {
    UNDEFINED("undefined_operate_type", "未定义的操作类型", false, false),
    IGNORE_TASK("ignore_novice_task", "忽略任务", false),
    SHOW_NOVICE_NOTICE_DIALOG("show_novice_notice_dialog", "显示新手任务提示框", true),
    INITIATE_NOVICE_GUIDE("initiate_novice_guide", "发起新人引导", true),
    ASSIGNED_POS_NOVICE_GUIDE("assigned_pos_novice_guide", "指定位置新人引导", true),
    PROCESS_LIST_NOVICE_GUIDE("process_list_novice_guide", "合同管理新人引导", true),
    CHECK_AMDIN_ORGS_INITIATE("check_admin_orgs_initiate", "校验用户管理员企业及发起情况", false, false),
    CHECK_MULTI_AGENT_EXCEL("check_multi_agent_excel", "多方批量发起Excel表格引导", false, false),
    DING_VERSION_UPGRADE_POP_WINDOW("ding_version_upgrade_pop_window", "钉签版本升级弹框", false, false),
    DING_VERSION_UPGRADE_NOTICE("ding_version_upgrade_notice", "钉签版本升级公告", false, false),
    COMPLETE_EXPERIENCE_START("complete_experience_start", "体验签署流程", false, true),
    ;

    private String name;
    private String desc;
    /** 如果不存在操作记录，是否默认已完成 */
    private boolean notExistAsDone;
    /** 如果账号没有gid， 是否默认已完成 */
    private boolean needGid;

    NoviceOperateTypeEnum(String name, String desc, boolean notExistAsDone) {
        this.name = name;
        this.desc = desc;
        this.notExistAsDone = notExistAsDone;
        this.needGid = true;
    }

    NoviceOperateTypeEnum(String name, String desc, boolean notExistAsDone, boolean needGid) {
        this.name = name;
        this.desc = desc;
        this.needGid = needGid;
        this.notExistAsDone = notExistAsDone;
    }

    public static NoviceOperateTypeEnum from(String name) {
        for (NoviceOperateTypeEnum value : values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return UNDEFINED;
    }

    public static boolean checkRegisterOperate(String operateName) {
        List<String> operateTypeEnums = Lists.newArrayList();
        operateTypeEnums.add(SHOW_NOVICE_NOTICE_DIALOG.getName());
        operateTypeEnums.add(INITIATE_NOVICE_GUIDE.getName());
        operateTypeEnums.add(ASSIGNED_POS_NOVICE_GUIDE.getName());
        operateTypeEnums.add(PROCESS_LIST_NOVICE_GUIDE.getName());
        return operateTypeEnums.stream().anyMatch(item -> item.equals(operateName));
    }
}
