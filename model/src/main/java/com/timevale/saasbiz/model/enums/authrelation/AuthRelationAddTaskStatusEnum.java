package com.timevale.saasbiz.model.enums.authrelation;

import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by t<PERSON><PERSON><PERSON> on 2022/2/15
 */
public enum AuthRelationAddTaskStatusEnum {


    UN_DO(0, "未处理"),
    SUCCESS(1, "成功"),
    FAILURE(2, "失败");


    AuthRelationAddTaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static final Set<Integer> COMPLETED_STATUS_SET = new HashSet<>();

    static {
        COMPLETED_STATUS_SET.add(SUCCESS.getCode());
        COMPLETED_STATUS_SET.add(FAILURE.getCode());
    }


    @Getter
    private Integer code;

    private String desc;
}
