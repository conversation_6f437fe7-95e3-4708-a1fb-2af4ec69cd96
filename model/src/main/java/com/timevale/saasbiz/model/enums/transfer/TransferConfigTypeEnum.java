package com.timevale.saasbiz.model.enums.transfer;

import com.google.common.base.Joiner;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.timevale.saasbiz.model.constants.BizConstants.*;

/**
 * 转交配置类型
 *
 * <AUTHOR>
 * @since 2023-08-03 19:46
 */
@Getter
@AllArgsConstructor
public enum TransferConfigTypeEnum {

    PROCESS_TRANSFER(PROCESS_TRANSFER_TYPE, "合同转交"),

    SEAL_APPROVAL_TRANSFER(SEAL_APPROVAL_TRANSFER_TYPE, "用印转交"),

    CONTRACT_APPROVAL_TRANSFER(CONTRACT_APPROVAL_TRANSFER_TYPE, "合同审批转交");



    private final String type;
    private final String desc;

    // 用于@Patten正则表达式
    public static final String regexp = PROCESS_TRANSFER_TYPE + "|" + SEAL_APPROVAL_TRANSFER_TYPE + "|" + CONTRACT_APPROVAL_TRANSFER_TYPE;

    public static List<String> getEnumList() {
        return Arrays.stream(TransferConfigTypeEnum.values())
                .map(TransferConfigTypeEnum::getType)
                .collect(Collectors.toList());
    }
}
