package com.timevale.saasbiz.model.enums.novice;

import lombok.Getter;

/**
 * 新手任务类型枚举
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Getter
public enum NoviceTaskTypeEnum {
    UNKNOWN(-1, "未知任务"),
    REAL_NAME(0, "创建企业并实名"),
    INITIATE_PROCESS(1, "上传文件发起签署"),
    SIGN(2, "签署第一份合同"),
    ;

    private int type;
    private String desc;

    NoviceTaskTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static NoviceTaskTypeEnum from(Integer type) {
        if (null != type) {
            for (NoviceTaskTypeEnum value : values()) {
                if (value.getType() == type) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }
}
