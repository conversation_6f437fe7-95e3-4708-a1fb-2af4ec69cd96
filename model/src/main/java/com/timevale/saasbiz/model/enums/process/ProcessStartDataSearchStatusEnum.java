package com.timevale.saasbiz.model.enums.process;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 15:13
 */
@Getter
@AllArgsConstructor
public enum ProcessStartDataSearchStatusEnum {

    WILL_STAT("willStart", "待发起"),
    FAILURE("failure", "发起失败")
            ;

    private final String code;
    private final String desc;


}
