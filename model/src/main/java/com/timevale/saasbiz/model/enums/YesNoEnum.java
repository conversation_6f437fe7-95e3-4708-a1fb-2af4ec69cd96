package com.timevale.saasbiz.model.enums;


public enum YesNoEnum {
    /** 是 */
    YES(1),
    /** 否 */
    NO(0);

    private int code;

    YesNoEnum(int code) {
        this.code = code;
    }

    public int code(){
        return code;
    }

    public static YesNoEnum from(Integer code){
        if (code != null && code == YES.code()){
            return YES;
        }else {
            return NO;
        }
    }

    public static YesNoEnum from(Boolean flag){
        if (flag != null && flag){
            return YES;
        }else {
            return NO;
        }
    }
}
