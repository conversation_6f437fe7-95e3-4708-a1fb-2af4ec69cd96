package com.timevale.saasbiz.model.enums.fulfillment;

import lombok.Getter;

/**
 * 履约操作按钮
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
@Getter
public enum FulfillmentOperateTypeEnum {

    EDIT("EDIT", "编辑", 1),
    DELETE("DELETE", "删除", 2),
    ;
    
    /** 操作类型 */
    private String type;
    /** 操作描述 */
    private String desc;
    /** 操作顺序, 正序显示， 顺序越小越靠前 */
    private int order;

    FulfillmentOperateTypeEnum(String type, String desc, int order) {
        this.type = type;
        this.desc = desc;
        this.order = order;
    }
}
