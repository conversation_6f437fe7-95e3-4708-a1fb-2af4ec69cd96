package com.timevale.saasbiz.model.enums.approval;

import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/4/7 11:34
 * 操作执行策略
 */
public enum ApprovalOperateExecutionStrategyEnum {

    //同步 单个操作，或者少量操作
    SYNC_CURRENT_THREAD(0, "同步单线程"),

    //同步 暂时没用
//    SYNC_MULTI_THREAD(1, "同步多线程"),

    // pc - 批量操作任务中心
    ASYNC_TASK_CENTER(2, "异步任务中心"),

    // App - 批量场景
    ASYNC(3, "异步"),
    ;

    ApprovalOperateExecutionStrategyEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static ApprovalOperateExecutionStrategyEnum from(Integer code) {
        if (null == code) {
            return null;
        }
        for (ApprovalOperateExecutionStrategyEnum statusEnum : ApprovalOperateExecutionStrategyEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    @Getter
    private Integer code;

    @Getter
    private String desc;
}
