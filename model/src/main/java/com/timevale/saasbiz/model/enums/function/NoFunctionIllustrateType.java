package com.timevale.saasbiz.model.enums.function;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Getter
public enum NoFunctionIllustrateType {

    NO_FUNCTION_CODE("NO_FUNCTION_CODE", "没会员功能"),
    
    NO_PRIVILEGE("NO_PRIVILEGE", "没权限"),

    OVER_FUNCTION_COUNT_LIMIT("OVER_FUNCTION_COUNT_LIMIT", "超出功能数量限制"),
    ;

    private String type;

    private String description;

    NoFunctionIllustrateType(String type, String description) {
        this.type = type;
        this.description = description;
    }
}
