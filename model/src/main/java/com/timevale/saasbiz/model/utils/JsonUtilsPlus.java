package com.timevale.saasbiz.model.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.JsonUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 基于现有JsonUtils的扩展，与现有JsonUtils使用同一个ObjectMapper实例
 *
 * <AUTHOR>
 * @since 2023-03-23 15:39
 */
public class JsonUtilsPlus {
    /** 现有JsonUtils的ObjectMapper实例 */
    private static final ObjectMapper objectMapper = JsonUtils.getObjectMapperInstance();

    /**
     * json转map，map的value必须是String或基本类型
     *
     * <p>如果value是自定义类型，使用{@link JsonUtils#json2map(String, Class)}
     *
     * @param jsonString json字符串
     * @return 转换的目标对象
     * @param <T> 目标对象类型
     */
    public static <T> Map<String, T> json2baseMap(String jsonString) {
        return parseObject(jsonString, new TypeReference<Map<String, T>>() {});
    }

    /**
     * json转list，列表元素必须是String或基本类型，
     *
     * <p>如果value是自定义对象类型使用{@link JsonUtils#json2list(String, Class)}
     *
     * @param jsonString
     * @param <T>
     * @return
     */
    public static <T> List<T> json2baseList(String jsonString) {
        return parseObject(jsonString, new TypeReference<List<T>>() {});
    }

    /**
     * 带泛型的json字符串转换
     *
     * @param jsonString json字符串
     * @param typeReference 带泛型的类型
     * @return 目标类型的对象
     * @param <T> 目标对象类型
     */
    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(jsonString, typeReference);
        } catch (IOException e) {
            throw new BaseRuntimeException("JsonUtilsPlus parse object error.", e);
        }
    }
}
