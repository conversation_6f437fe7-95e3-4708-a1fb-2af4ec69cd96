package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.OfflineContractSignerConfigDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 解析线下合同录入合同信息的excel
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class ParseOfflineContractExcelInputDTO extends ToString {

    @NotBlank(message = "主体id不能为空")
    private String subjectId;

    /** 操作端 */
    private String clientId;

    @NotBlank(message = "excel文件fileKey不能为空")
    private String fileKey;

    /** 错误数据处理方式 */
    private String errorDataHandleWay;

    @NotEmpty(message = "签署方配置不能为空")
    private List<OfflineContractSignerConfigDTO> signerConfigs;
}
