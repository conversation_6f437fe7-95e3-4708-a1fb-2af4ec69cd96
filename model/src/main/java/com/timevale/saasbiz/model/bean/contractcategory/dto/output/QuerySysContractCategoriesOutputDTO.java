package com.timevale.saasbiz.model.bean.contractcategory.dto.output;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractcategory.bo.SceneContractCategoryBO;
import lombok.Data;

import java.util.List;

/**
 * 查询系统推荐的合同类型列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
public class QuerySysContractCategoriesOutputDTO extends ToString {

    /** 合同类型列表 */
    private List<SceneContractCategoryBO> categories;
}
