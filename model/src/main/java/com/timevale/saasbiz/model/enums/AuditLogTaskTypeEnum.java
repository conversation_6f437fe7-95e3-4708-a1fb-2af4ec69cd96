package com.timevale.saasbiz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * AuditLogTaskTypeEnum
 *
 * <AUTHOR>
 * @since 2025/3/5 下午5:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum AuditLogTaskTypeEnum {

    LOG("log", "审计日志"),
    SUBSCRIBE("subscribe", "风险订阅"),
    ;

    private String type;

    private String name;
}
