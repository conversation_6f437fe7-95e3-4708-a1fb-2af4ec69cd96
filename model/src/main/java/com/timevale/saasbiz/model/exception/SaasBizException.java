package com.timevale.saasbiz.model.exception;

import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * 业务异常类
 *
 * <AUTHOR>
 * @since 2023-01-29
 */
public class SaasBizException extends BaseBizRuntimeException {

    private int errCode;

    public SaasBizException(String detailMessage) {
        this(SaasBizResultCode.SAAS_BIZ_ERROR.getCode(), detailMessage);
    }

    public SaasBizException(SaasBizResultCode resultCode) {
        this(resultCode.getCode(), resultCode.getMessage());
    }

    public SaasBizException(SaasBizResultCode resultCode, Object... args) {
        this(resultCode.getCode(), resultCode.getMessage());
        if (StringUtils.isNotBlank(resultCode.getMessage())) {
            this.message = String.format(message, args);
        }
    }

    public SaasBizException(int code, String detailMessage) {
        super(String.valueOf(code), detailMessage);
        this.errCode = code;
    }

    public int getErrCode() {
        return errCode;
    }
}
