package com.timevale.saasbiz.model.constants;

import com.timevale.mandarin.weaver.utils.RequestContext;

/**
 * <AUTHOR>
 * @since 2023/8/30 17:27
 */
public class InfoCollectConstants {

    public static final String ASYNC_DATA_IMPORT_TOPIC = "info_collect_async_data_import_topic";
    public static final String ASYNC_DATA_IMPORT_TAG = "default";


    public static final String PRIVILEGE_RESOURCE = "INFO_COLLECT_FORM";
    public static final String PRIVILEGE_RESOURCE_KEY_CREATE = "CREATE";
    public static final String PRIVILEGE_RESOURCE_KEY_EDIT = "EDIT";
    public static final String PRIVILEGE_RESOURCE_KEY_DELETE = "DELETE";
    public static final String PRIVILEGE_RESOURCE_KEY_RELATION_FLOW_TEMPLATE = "RELATION_FLOW_TEMPLATE";
    public static final String PRIVILEGE_RESOURCE_KEY_COPY = "COPY";
    public static final String PRIVILEGE_RESOURCE_KEY_QUERY = "QUERY";

    public static final String UPGRADE_HEADER = "X-Tsign-Saas-Info-Collect-Upgrade";



}
