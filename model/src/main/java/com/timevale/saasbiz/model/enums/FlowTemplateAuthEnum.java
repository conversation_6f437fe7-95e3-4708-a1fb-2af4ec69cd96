package com.timevale.saasbiz.model.enums;

import com.google.common.collect.Lists;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationSecondResourceEnum;
import com.timevale.saasbiz.model.bean.usercenter.dto.PermissionInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 模板管理权限
 *
 * @author: 悔桑
 * @since: 2023-08-09 13:20
 */
@Getter
@AllArgsConstructor
public enum FlowTemplateAuthEnum {
    UPDATE("UPDATE", "编辑"),
    USE("USE", "使用"),
    QUERY("QUERY", "查看"),
    DOWNLOAD("DOWNLOAD", "下载"),
    AUTH("AUTH", "授权"),
    CREATE("CREATE", "新增"),
    DELETE("DELETE", "删除"),
    ;
    private String type;
    private String description;


    private static final List<FlowTemplateAuthEnum> queryManage = Lists.newArrayList(FlowTemplateAuthEnum.USE, FlowTemplateAuthEnum.QUERY);

    public static List<PermissionInfoDTO> getPermissionList() {
        return Arrays.stream(FlowTemplateAuthEnum.values())
                .map(
                        templateAuth -> {
                            PermissionInfoDTO permissionInfo = new PermissionInfoDTO();
                            permissionInfo.setCode(templateAuth.getType());
                            permissionInfo.setDesc(templateAuth.getDescription());
                            return permissionInfo;
                        })
                .collect(Collectors.toList());
    }

    public static List<PermissionInfoDTO> getPermissionList(String maxResource, List<PermissionInfoDTO> permissionInfoList) {
        if (StringUtils.isEmpty(maxResource)) {
            return permissionInfoList;
        }
        if (maxResource.equals(AuthRelationSecondResourceEnum.QUERY_MANAGE.getCode())) {
            return queryManage.stream().map(
                    templateAuth -> {
                        PermissionInfoDTO permissionInfo = new PermissionInfoDTO();
                        permissionInfo.setCode(templateAuth.getType());
                        permissionInfo.setDesc(templateAuth.getDescription());
                        return permissionInfo;
                    })
                    .collect(Collectors.toList());
        } else if (maxResource.equals(AuthRelationSecondResourceEnum.DELETE_MANAGE.getCode())) {
            permissionInfoList.removeIf(t -> t.getCode().equals(FlowTemplateAuthEnum.AUTH.getType()));
            return permissionInfoList;
        } else if (maxResource.equals(AuthRelationSecondResourceEnum.AUTH_MANAGE.getCode())) {
            return permissionInfoList;
        }

        return new ArrayList<>();
    }

}
