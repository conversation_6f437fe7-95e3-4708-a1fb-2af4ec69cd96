package com.timevale.saasbiz.model.enums.novice;

import lombok.Getter;

/**
 * 新手任务状态枚举
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Getter
public enum NoviceTaskStatusEnum {
    NOT_START(0, "待开始"),
    EXECUTING(1, "进行中"),
    FINISH(2, "已完成"),
    TERMINATE(3, "已终止"),
    ;

    private int status;
    private String desc;

    NoviceTaskStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static boolean executing(Integer status) {
        if (null == status) {
            return false;
        }
        return EXECUTING.getStatus() == status;
    }

    public static boolean finish(Integer status) {
        if (null == status) {
            return false;
        }
        return FINISH.getStatus() == status;
    }

    public static boolean terminate(Integer status) {
        if (null == status) {
            return false;
        }
        return TERMINATE.getStatus() == status;
    }

    public static boolean canTerminate(Integer status) {
        return unfinished(status);
    }

    public static boolean unfinished(Integer status) {
        if (null == status) {
            return false;
        }
        return NOT_START.getStatus() == status || EXECUTING.getStatus() == status;
    }
}
