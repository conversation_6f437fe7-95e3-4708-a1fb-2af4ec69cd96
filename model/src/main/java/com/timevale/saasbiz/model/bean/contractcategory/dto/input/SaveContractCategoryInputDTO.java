package com.timevale.saasbiz.model.bean.contractcategory.dto.input;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractcategory.bo.ExtractFieldBO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 保存合同类型请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
public class SaveContractCategoryInputDTO extends ToString {

    /** 用户oid */
    private String accountId;
    /** 主体oid */
    private String subjectId;
    /** 合同类型id */
    private String categoryId;
    /** 合同类型名称 */
    private String categoryName;
    /** 提取字段列表 */
    private List<ExtractFieldBO> extractFields;
    /** 关联的系统推荐合同类型id */
    private String refSysCategoryId;
}
