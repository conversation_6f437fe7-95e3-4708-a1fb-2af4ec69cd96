package com.timevale.saasbiz.model.enums.vip;

import lombok.Getter;

/**
 * 商机申请试用场景
 *
 * <AUTHOR>
 * @since 2025-04-28
 */
@Getter
public enum IllustrateTrialSceneTypeEnum {
    DIRECT_APPLY("DIRECT_APPLY", "可直接申请(管理员、法人、子管理员)"),
    
    ADMIN_UPGRADE_REQUIRED("ADMIN_UPGRADE_REQUIRED", "需要管理员升级的场景"),
    ;

    private final String type;
    private final String desc;

    IllustrateTrialSceneTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
