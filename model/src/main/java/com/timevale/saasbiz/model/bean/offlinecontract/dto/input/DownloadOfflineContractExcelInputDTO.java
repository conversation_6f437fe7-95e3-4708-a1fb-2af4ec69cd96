package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.OfflineContractSignerConfigDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 下载线下合同录入合同信息的excel模板
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class DownloadOfflineContractExcelInputDTO extends ToString {

    @NotBlank(message = "主体id不能为空")
    private String subjectId;

    @NotEmpty(message = "文件名称列表不能为空")
    private List<String> contractFileNames;

    @NotEmpty(message = "签署方配置不能为空")
    private List<OfflineContractSignerConfigDTO> signerConfigs;
}
