package com.timevale.saasbiz.model.utils;

import com.alibaba.fastjson.JSON;
import com.timevale.framework.tedis.core.ZSetOperations;
import com.timevale.framework.tedis.util.TedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 缓存工具类
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Slf4j
public class CacheUtil {

    /**
     * 获取缓存的值，不降级处理
     *
     * @param key
     */
    public static <T> T get(String key) {
        return TedisUtil.get(key);
    }

    /**
     * 获取缓存的值, 降级处理
     *
     * @param key
     */
    public static <T> T degradeGet(String key) {
        try {
            return TedisUtil.get(key);
        } catch (Exception e) {
            log.error("tedis get have Exception {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取任一缓存的值， 根据入参控制是否降级处理
     *
     * @param keys
     */
    public static <T> T getAny(boolean degrade, String... keys) {
        if (null == keys || keys.length == 0) {
            return null;
        }
        for (String key : keys) {
            T value = degrade ? degradeGet(key) : get(key);
            if (null != value) {
                return value;
            }
        }
        return null;
    }

    /**
     * 删除缓存的值，不降级处理
     *
     * @param key
     */
    public static void delete(String key) {
        TedisUtil.delete(key);
    }

    /**
     * 批量删除缓存的值，不降级处理
     *
     * @param keys
     */
    public static void delete(String... keys) {
        TedisUtil.delete(keys);
    }

    /**
     * 删除缓存的值，降级处理
     *
     * @param key
     */
    public static void degradeDelete(String key) {
        try {
            delete(key);
        } catch (Exception e) {
            log.error("tedis delete have Exception {}", e.getMessage());
        }
    }

    /**
     * 批量删除缓存的值，降级处理
     *
     * @param keys
     */
    public static void degradeDelete(String... keys) {
        try {
            delete(keys);
        } catch (Exception e) {
            log.error("tedis delete have Exception {}", e.getMessage());
        }
    }

    /**
     * 设置缓存的值，不降级处理
     *
     * @param key
     * @param val
     * @param timeout
     * @param unit
     * @param <T>
     */
    public static <T> void set(String key, T val, long timeout, TimeUnit unit) {
        // 如果值为空或字符串类型， 直接缓存
        if (null == val || val instanceof String) {
            TedisUtil.set(key, val, timeout, unit);
            return;
        }
        // 如果值为其他类型，转成JSON字符串缓存
        TedisUtil.set(key, JSON.toJSONString(val), timeout, unit);
    }

    /**
     * 设置缓存的值，降级处理
     *
     * @param key
     * @param val
     * @param timeout
     * @param unit
     * @param <T>
     */
    public static <T> void degradeSet(String key, T val, long timeout, TimeUnit unit) {
        try {
            set(key, val, timeout, unit);
        } catch (Exception e) {
            log.error("tedis set have Exception {}", e.getMessage());
        }
    }

    /**
     * 设置缓存的值，降级处理
     *
     * @param key
     * @param val
     * @param timeout
     * @param unit
     * @param <T>
     */
    public static <T> void degradeObjSet(String key, T val, long timeout, TimeUnit unit) {
        try {
            TedisUtil.set(key, val, timeout, unit);
        } catch (Exception e) {
            log.error("tedis set have Exception {}", e.getMessage());
        }
    }

    public static Object listIndex(String key, long index) {
        return TedisUtil.list().index(key, index);
    }

    public static List<Object> listRange(String key, long start, long end) {
        return TedisUtil.list().range(key, start, end);
    }

    public static Long zSetRemove(String key, Object... values) {
        return TedisUtil.zSet().remove(key, values);
    }

    public static Long listRightPushAll(String key, Object... values) {
        return TedisUtil.list().rightPushAll(key, values);
    }

    public static Boolean expire(String key, long timeout, TimeUnit unit) {
        return TedisUtil.expire(key, timeout, unit);
    }

    public static Boolean zSetAdd(String key, Object value, double score) {
        return TedisUtil.zSet().add(key, value, score);
    }

    public static Set<ZSetOperations.TypedTuple<Object>> zSetRangeWithScores(String key, long start, long end) {
        return TedisUtil.zSet().rangeWithScores(key, start, end);
    }

    public static void listSet(String key, long index, Object value) {
        TedisUtil.list().set(key, index, value);
    }

    /**
     * 设置分布式锁
     * @param key
     * @param timeout
     * @param timeUnit
     * @return
     */
    public static boolean setNx(String key, int timeout, TimeUnit timeUnit) {
        try {
            if (!TedisUtil.setNx(key, key)) {
                return false;
            }
            TedisUtil.expire(key, timeout, timeUnit);
            return true;
        } catch (Exception e) {
            log.error("setNx throw exception:{}", ExceptionUtils.getStackTrace(e));
            throw e;
        }
    }

    /**
     * 设置缓存的值，降级处理
     *
     * @param key
     * @param timeout
     * @param unit
     */
    public static boolean degradeSetNx(String key, int timeout, TimeUnit unit) {
        try {
            return setNx(key, timeout, unit);
        } catch (Exception e) {
            log.error("tedis set nx have Exception {}", e.getMessage());
        }
        return true;
    }
}
