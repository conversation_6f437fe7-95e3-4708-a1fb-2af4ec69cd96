package com.timevale.saasbiz.model.bean.contractcategory.dto.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 修改合同类型状态请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
public class UpdateContractCategoryStatusInputDTO extends ToString {

    /** 用户oid */
    private String accountId;

    /** 主体oid */
    private String subjectId;

    /** 合同类型id列表 */
    private List<String> categoryIds;

    /** 合同类型是否启用 */
    private boolean enable;
}
