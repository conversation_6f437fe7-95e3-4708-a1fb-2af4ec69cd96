package com.timevale.saasbiz.model.bean.offlinecontract.dto.output;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.OfflineContractDTO;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.OfflineContractDetailDTO;
import lombok.Data;

import java.util.List;

/**
 * 查询线下合同导入记录合同信息列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class QueryOfflineContractRecordContractsOutputDTO extends ToString {

    /** 总数 */
    private long total;

    /** 线下合同合同信息列表 */
    private List<OfflineContractDetailDTO> contracts;
}
