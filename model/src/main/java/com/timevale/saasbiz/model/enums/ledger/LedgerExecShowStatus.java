package com.timevale.saasbiz.model.enums.ledger;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @since 2024-02-03
 */
@AllArgsConstructor
@Getter
public enum LedgerExecShowStatus {
    INVALID("invalid", "已失效"),
    NOT_RUNNING("not-running", "未运行"),
    RUNNING("running", "运行中"),
    HISTORY_EXEC_ALL_RUNNING("exec-all-running", "数据提取运行中"),
    HISTORY_EXEC_TRIAL_RUNNING("exec-trial-running", "3份合同体验中"),
    HISTORY_EXEC_PAUSE("exec-pause", "已暂停"),
    HISTORY_EXEC_TRIAL_DONE("exec-trial-pause", "3份合同体验结束"),
    HISTORY_EXEC_TRIAL_DONE_EMPTY("exec-trial-pause-empty", "3份合同体验结束，无匹配合同"),
    ;
    private final String code;
    private final String desc;
}
