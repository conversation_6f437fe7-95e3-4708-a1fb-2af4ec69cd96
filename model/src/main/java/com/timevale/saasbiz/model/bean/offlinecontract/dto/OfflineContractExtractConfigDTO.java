package com.timevale.saasbiz.model.bean.offlinecontract.dto;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 线下合同提取配置
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class OfflineContractExtractConfigDTO extends ToString {

    /** 提取字段配置 */
    private List<OfflineExtractFieldDTO> fields;
    /** 签署方配置 */
    private List<OfflineContractSignerConfigDTO> signers;
}
