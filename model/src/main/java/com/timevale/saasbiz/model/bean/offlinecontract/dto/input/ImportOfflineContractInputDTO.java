package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.OfflineContractDTO;
import com.timevale.saasbiz.model.bean.offlinecontract.dto.OfflineContractExtractConfigDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 导入线下合同请求参数
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class ImportOfflineContractInputDTO extends ToString {

    @NotBlank(message = "用户oid不能为空")
    private String accountId;

    @NotBlank(message = "主体oid不能为空")
    private String subjectId;

    /** 导入端 */
    private String clientId;

    /** 归档菜单id */
    @NotBlank(message = "归档菜单id不能为空")
    private String menuId;

    /** 导入方式 */
    @NotBlank(message = "导入方式不能为空")
    private String importWay;

    /** 合同信息提取方式 */
    @NotBlank(message = "合同信息提取方式不能为空")
    private String extractWay;

    /** 线下合同列表 */
    @NotEmpty(message = "线下合同列表不能为空")
    private List<OfflineContractDTO> contracts;

    /** 合同信息提取配置 */
    private OfflineContractExtractConfigDTO extractConfig;

    /** 关联合同的原合同id */
    private String masterProcessId;

    /** 专属云项目id */
    private String dedicatedCloudId;
}
