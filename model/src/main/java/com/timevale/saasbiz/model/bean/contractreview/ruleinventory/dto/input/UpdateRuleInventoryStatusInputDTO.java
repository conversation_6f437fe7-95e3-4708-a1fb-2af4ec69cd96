package com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input;

import com.timevale.saasbiz.model.bean.common.dto.input.BaseInputDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateRuleInventoryStatusInputDTO extends BaseInputDTO {

    @ApiModelProperty("审查清单id")
    private String inventoryId;

    @ApiModelProperty("状态")
    private String status;
}