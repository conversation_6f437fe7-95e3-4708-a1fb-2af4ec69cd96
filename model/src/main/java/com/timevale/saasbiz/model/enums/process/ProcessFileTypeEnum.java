package com.timevale.saasbiz.model.enums.process;

/**
 * 流程文件类型
 *
 * <AUTHOR>
 * @since 2019/11/6
 */
public enum ProcessFileTypeEnum {
    /** 合同文件 */
    CONTRACT_FILE(1),
    /** 附件 */
    ATTACHMENT_FILE(2);

    ProcessFileTypeEnum(int type) {
        this.type = type;
    }

    private int type;

    public int getType() {
        return type;
    }

    /**
     * 校验是否合同文件
     *
     * @param type
     * @return
     */
    public static boolean isContract(Integer type) {
        return null != type && CONTRACT_FILE.getType() == type;
    }

    /**
     * 校验是否附件
     *
     * @param type
     * @return
     */
    public static boolean isAttachment(Integer type) {
        return null != type && ATTACHMENT_FILE.getType() == type;
    }
}
