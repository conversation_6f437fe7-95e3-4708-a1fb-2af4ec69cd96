package com.timevale.saasbiz.model.constants;

import com.google.common.collect.Lists;
import com.timevale.saas.common.manage.common.service.constant.AuthRelationShareConfigKeyConstant;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-13
 */
public interface AuthRelationAuthInfoHandlerConstants {

    default List<String> getAllAuthInfoHandler() {
        return Lists.newArrayList(AUTH_SHARE_APP_COUNT_HANDLER, AUTH_RELATION_HANDLER);
    }

    String AUTH_SHARE_APP_COUNT_HANDLER = "authShareAppCountHandler";

    String AUTH_RELATION_HANDLER = "authRelationHandler";
}
