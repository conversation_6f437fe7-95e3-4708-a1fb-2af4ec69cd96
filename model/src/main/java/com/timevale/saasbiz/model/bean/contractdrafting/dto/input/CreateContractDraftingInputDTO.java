package com.timevale.saasbiz.model.bean.contractdrafting.dto.input;

import com.google.common.collect.Maps;
import com.timevale.saasbiz.model.bean.common.dto.input.BaseInputDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CreateContractDraftingInputDTO extends BaseInputDTO {

    /** 文件列表 */
    private List<DraftingFile> files;
    /** 编辑类型 */
    private String draftingType;
    /** 页面关闭地址 */
    private String closeRedirectUrl;
    /** 页面保存地址 */
    private String saveRedirectUrl;
    /** 业务上下文 */
    private Map<String, Object> bizContext = Maps.newHashMap();

    @Data
    public static class DraftingFile {
        /** 文件id */
        private String draftingFileId;
        /** 合同审查页面配置 */
        private Map<String, Object> contractReviewPageConfig;
    }
}
