package com.timevale.saasbiz.model.bean.contractcategory.dto.input;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 删除合同类型请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
public class DeleteContractCategoryInputDTO extends ToString {

    /** 用户oid */
    private String accountId;

    /** 主体oid */
    private String subjectId;

    /** 合同类型id列表 */
    private List<String> categoryIds;
}
