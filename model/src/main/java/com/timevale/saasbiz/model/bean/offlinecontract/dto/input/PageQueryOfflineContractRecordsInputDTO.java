package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分页查询线下合同导入记录
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class PageQueryOfflineContractRecordsInputDTO extends ToString {
    
    private String accountId;
    
    @NotBlank(message = "主体oid不能为空")
    private String subjectId;

    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @NotNull(message = "每页数据大小不能为空")
    private Integer pageSize;
}
