package com.timevale.saasbiz.model.enums.witness;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;

/**
 * 天印流程排序字段枚举
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Getter
public enum WitnessFlowSortEnum {
    UNKNOWN("unknown", "unknown", "未定义的排序字段"),
    FLOW_NAME("flowName", "title", "合同主题"),
    FLOW_STATUS("flowStatus", "flowStatus", "合同状态"),
    INITIATOR("initiator", "initiator", "发起人"),
    SIGNERS("signers", "participant", "参与人"),
    FLOW_START_TIME("flowStartTime", "createTime", "发起时间"),
    FLOW_END_TIME("flowEndTime", "completeTime", "完成时间"),
    CONTRACT_VALIDITY("contractValidity", "contractValidity", "合同有效时间"),
    SIGN_VALIDITY("signValidity", "signValidity", "签署有效时间"),
    ;
    private String key;
    private String esKey;
    private String desc;

    WitnessFlowSortEnum(String key, String esKey, String desc) {
        this.key = key;
        this.esKey = esKey;
        this.desc = desc;
    }

    public static WitnessFlowSortEnum from(String key) {
        if (StringUtils.isNotBlank(key)) {
            for (WitnessFlowSortEnum value : values()) {
                if (value.getKey().equals(key)) {
                    return value;
                }
            }
        }
        return UNKNOWN;
    }
}
