package com.timevale.saasbiz.model.enums;
/**
 * <AUTHOR>
 * @since 2023-11-19 11:34
 */
public enum SealGrantLevelEnum {
    FIRST_GRANT(1, "一级授权"),
    SECOND_GRANT(2, "二级授权");

    private final int code;
    private final String desc;

    SealGrantLevelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
