package com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.common.dto.input.BaseInputDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryRuleInventoryDetailInputDTO extends BaseInputDTO {

    @ApiModelProperty("审查清单id")
    private String inventoryId;
}