package com.timevale.saasbiz.model.utils;

import com.alibaba.fastjson.JSONObject;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saasbiz.model.enums.AppConfigEnum;
import com.timevale.saasbiz.model.enums.PuppeteerConfigEnum;
import org.assertj.core.util.Lists;

import java.util.*;

import static com.timevale.framework.puppeteer.core.ConfigConsts.NAMESPACE_APPLICATION;
import static com.timevale.saasbiz.model.constants.SystemConstants.NAMESPACE_SAAS_PUBLIC;

/**
 * 配置中心工具
 *
 * <AUTHOR>
 * @since 2023-03-22 17:11
 * @see AppConfigEnum 配置枚举
 */
public class AppConfigUtil {

    /** Saas配置namespace, 包含application和saas-public配置 */
    public static final List<String> NAMESPACE_SAAS =
            Lists.newArrayList(NAMESPACE_APPLICATION, NAMESPACE_SAAS_PUBLIC);

    /**
     * 获取配置的map，注意map的value只能是基本类型或String
     *
     * @param config {@link AppConfigEnum 配置枚举}
     * @return 配置值
     */
    public static <T> Map<String, T> getBaseMap(PuppeteerConfigEnum config) {
        String jsonStr = getString(config);

        if (StringUtils.isBlank(jsonStr)) {
            return Collections.emptyMap();
        }

        return JsonUtilsPlus.json2baseMap(jsonStr);
    }

    /**
     * 获取配置的map，map的value是自定义对象类型
     *
     * @param config {@link AppConfigEnum 配置枚举}
     * @return 配置值
     */
    public static <T> Map<String, T> getObjectMap(PuppeteerConfigEnum config, Class<T> clazz) {
        String jsonStr = getString(config);

        if (StringUtils.isBlank(jsonStr)) {
            return Collections.emptyMap();
        }

        return JsonUtils.json2map(jsonStr, clazz);
    }

    /**
     * 获取配置的字符串
     *
     * @param config {@link AppConfigEnum 配置枚举}
     * @return 配置值
     */
    public static String getString(PuppeteerConfigEnum config) {
        if (Objects.isNull(config)) {
            return "";
        }

        // 读配置
        return ConfigService.getAppConfig().getProperty(config.getKey(), config.getDefaultValue());
    }

    /**
     * 获取配置的Boolean值
     *
     * @param config {@link AppConfigEnum 配置枚举}
     * @return 配置值
     */
    public static Boolean getBoolean(PuppeteerConfigEnum config) {
        if (Objects.isNull(config)) {
            return null;
        }
        // 默认值
        Boolean defaultValue = BooleanUtils.toBoolean(config.getDefaultValue());
        // 读配置
        return ConfigService.getAppConfig().getBooleanProperty(config.getKey(), defaultValue);
    }

    /**
     * 获取配置的整数
     *
     * @param config {@link AppConfigEnum 配置枚举}
     * @return 配置值
     */
    public static Integer getInteger(PuppeteerConfigEnum config) {
        if (Objects.isNull(config)) {
            return -1;
        }

        Integer defaultValue =
                Optional.ofNullable(config.getDefaultValue())
                        .filter(StringUtils::isNotBlank)
                        .filter(StringUtils::isNumeric)
                        .map(Integer::parseInt)
                        .orElse(-1);
        // 读配置
        return ConfigService.getAppConfig().getIntProperty(config.getKey(), defaultValue);
    }

    /**
     * 获取list类型配置数据，list的元素只能是string和基本类型
     *
     * @param <T>
     * @param config
     * @return
     */
    public static <T> List<T> getBaseList(PuppeteerConfigEnum config) {
        String jsonStr = getString(config);

        if (StringUtils.isBlank(jsonStr)) {
            return Collections.emptyList();
        }

        return JsonUtilsPlus.json2baseList(jsonStr);
    }

    /** 获取配置的字符串 */
    public static String getString(List<String> namespaces, String key, String defaultValue) {
        if (StringUtils.isBlank(key)) {
            return "";
        }
        if (namespaces.size() == 1) {
            return ConfigService.getConfig(namespaces.get(0)).getProperty(key, defaultValue);
        }
        // 读配置
        String property = null;
        for (String namespace : namespaces) {
             property = ConfigService.getConfig(namespace).getProperty(key, null);
             if (null != property) {
                 break;
             }
        }
        return null == property ? defaultValue : property;
    }

    /** 获取配置的整数 */
    public static Integer getInteger(List<String> namespaces, String key, Integer defaultValue) {
        String property = getString(namespaces, key, null);
        if (StringUtils.isBlank(key)) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(property);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /** 获取配置的长整数 */
    public static Long getLong(List<String> namespaces, String key, Long defaultValue) {
        String property = getString(namespaces, key, null);
        if (StringUtils.isBlank(key)) {
            return defaultValue;
        }
        try {
            return Long.parseLong(property);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /** 获取list类型配置数据 */
    public static <T> List<T> getList(
            List<String> namespaces, String key, String defaultValue, Class<T> tclass) {
        String jsonStr = getString(namespaces, key, defaultValue);
        if (StringUtils.isBlank(jsonStr)) {
            return Collections.emptyList();
        }
        return JSONObject.parseArray(jsonStr, tclass);
    }

    public static <T> Map<String, T> getBaseMap(List<String> namespaces, String key, String defaultValue) {
        String jsonStr = getString(namespaces, key, defaultValue);

        if (StringUtils.isBlank(jsonStr)) {
            return Collections.emptyMap();
        }

        return JsonUtilsPlus.json2baseMap(jsonStr);
    }
}
