package com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input;

import com.timevale.saasbiz.model.bean.common.dto.input.BaseInputDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageRuleInventoryInputDTO extends BaseInputDTO {

    @ApiModelProperty("审查清单名称")
    private String inventoryName;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码")
    private Integer pageNum = 1;

    @NotNull(message = "每页数量不能为空")
    @ApiModelProperty("每页数量")
    private Integer pageSize = 10;
}