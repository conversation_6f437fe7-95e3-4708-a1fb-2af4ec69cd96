package com.timevale.saasbiz.model.constants;
/**
 * <AUTHOR>
 * @since 2023-03-09 14:32
 */
public class FunctionCodeConstants {
    /** 流程模板分类功能 */
    public static final String FLOW_TEMPLATE_CATEGORY = "flow_template_category";

    /** 用印审批模版 */
    public static final String APPROVAL_TEMPLATE_SEAL = "use_seal_approve";

    /** 用印审批模版 */
    public static final String APPROVAL_TEMPLATE_CONTRACT = "org_approve_template_manage";

    /** 批量审批通过*/
    public static final String APPROVE_BATCH_AGREE = "approve_batch_agree";

    /** 批量审批拒绝*/
    public static final String APPROVE_BATCH_REFUSE = "approve_batch_refuse";

    /** 批量审批撤回*/
    public static final String APPROVE_BATCH_REVOKE = "approve_batch_revoke";
    /** 合同摘要*/
    public static final String CONTRACT_SUMMARY = "contract_summary";

    /** 线下合同管理 */
    public static final String OFFLINE_CONTRACT_MANAGEMENT = "offline_contract_management";

    /** 审批转交 */
    public static final String APPROVAL_TRANSFER = "approval_transfer";

    /** 用印审批合同比对 */
    public static final String SEAL_APPROVAL_COMPARE = "seal_approval_compare";

    /** 专属云 */
    public static final String DEDICATED_CLOUD = "dedicated_cloud";
    
    /**
     * 智能工具合同比对
     */
    public static final String INTELLIGENT_COMPARE = "intelligent_compare";

    /** 合同审查 */
    public static final String CONTRACT_AUDIT = "contract_audit";

    // 印章功能版本划分文档
    // https://alidocs.dingtalk.com/i/nodes/MNDoBb60VLr4xPZwi7jnAYpP8lemrZQ3?corpId=ding680bcf152e9f5427&utm_medium=im_card&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_scene=person_space&utm_source=im

    public final static String SEAL_VISIBLE_SCOPE = "seal_visible_scope";
    public final static String ORG_SEAL_GRANT = "org_seal_grant";
    public final static String BATCH_SEAL_AUTH = "batch_seal_auth";
    public final static String BATCH_DEL_AUTH = "batch_del_auth";
    public final static String BATCH_UPDATE_AUTH = "batch_update_auth";
    public final static String AUTH_ROLE = "auth_role";
    public final static String AUTH_DEPARTMENT = "auth_department";
    public final static String AUTH_ALL = "auth_all";
    // 20241121 临时修改 印章授权-按模板 额外控制 印章授权-按应用 印章授权-按审批流模板 来进行控制，后续再进行拆分
    public final static String SEAL_AUTH_WITH_TEMPLATE = "seal_auth_with_template";
    public final static String SEAL_AUTH_AUTO_FALL = "seal_auth_auto_fall";
    public final static String AUTH_FOREVER = "auth_forever";

    public final static String AUTH_APPID = "auth_appid";
    public final static String AUTH_APPROVAL_TEMPLATE = "auth_approval_template";
    public final static String EXTERIOR_AUTH_APPID = "exterior_auth_appid";
    public final static String EXTERIOR_AUTH_ADMIN = "exterior_auth_admin";

    /** 定时发起 */
    public static final String SCHEDULE_INITIATE = "initiate_schedule";

    public static final String MONITOR = "monitor";

    /** 详情页智能体 */
    public static final String CONTRACT_DETAIL_AGENT = "ContractDetailAgent";
}

