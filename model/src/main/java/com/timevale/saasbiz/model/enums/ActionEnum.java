package com.timevale.saasbiz.model.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023-03-24 16:23
 */
public enum ActionEnum {
    INSERT,
    UPDATE,
    DELETE;

    public static ActionEnum from(String action) {
        return Arrays.stream(ActionEnum.values())
                .filter(actionEnum -> actionEnum.name().equals(action))
                .findFirst()
                .orElse(null);
    }
}
