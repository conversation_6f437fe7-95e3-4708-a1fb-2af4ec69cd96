package com.timevale.saasbiz.model.bean.offlinecontract.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/** 线下合同合同信息 */
@Data
public class OfflineContractProcessInfoDTO {
    /** 合同名称 */
    @NotBlank(message = "合同名称不能为空")
    private String title;
    /** 签署方 */
    @Valid
    private List<OfflineContractAccountDTO> signers;
    /** 合同到期时间 */
    private Long contractValidity;
}
