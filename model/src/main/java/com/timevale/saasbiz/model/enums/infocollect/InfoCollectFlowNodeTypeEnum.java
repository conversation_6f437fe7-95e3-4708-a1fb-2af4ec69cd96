package com.timevale.saasbiz.model.enums.infocollect;

/**
 * <AUTHOR>
 * @since 2024/11/18 17:54
 */
public enum InfoCollectFlowNodeTypeEnum {

    START("start", "开始"),
    WRITE("write", "填写"),
    APPROVAL("approval", "审批"),
    END("end", "结束"),

    ;

    private String code;
    private String desc;

    InfoCollectFlowNodeTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
