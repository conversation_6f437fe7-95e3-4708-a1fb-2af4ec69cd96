package com.timevale.saasbiz.model.bean.contractcategory.dto.output;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractcategory.bo.ContractCategoryListBO;
import lombok.Data;

import java.util.List;

/**
 * 分页查询合同类型列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
public class PageQueryContractCategoriesOutputDTO extends ToString {

    /** 总数 */
    private long total;

    /** 合同类型列表 */
    private List<ContractCategoryListBO> categories;
}
