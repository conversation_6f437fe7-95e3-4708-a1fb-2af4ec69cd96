package com.timevale.saasbiz.model.enums.transfer;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.timevale.saasbiz.model.constants.BizConstants.ADMIN_TYPE;
import static com.timevale.saasbiz.model.constants.BizConstants.DEPT_HEAD_TYPE;

/**
 * 系统被转交人配置类型
 *
 * <AUTHOR>
 * @since 2023-08-03 19:47
 */
@Getter
@AllArgsConstructor
public enum TransferToUserTypeEnum {

    ADMIN(ADMIN_TYPE, "管理员"),

    DEPT_HEAD(DEPT_HEAD_TYPE, "部门负责人");

    // 用于@Patten正则表达式
    public static final String regexp = ADMIN_TYPE + "|" + DEPT_HEAD_TYPE;

    private final String type;
    private final String desc;
}
