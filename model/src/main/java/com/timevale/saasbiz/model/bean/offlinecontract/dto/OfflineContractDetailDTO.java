package com.timevale.saasbiz.model.bean.offlinecontract.dto;

import lombok.Data;

/**
 * 线下合同详情
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
@Data
public class OfflineContractDetailDTO extends OfflineContractDTO {

    /** 线下合同对应的导入流程id */
    private String recordProcessId;

    /** 线下合同对应的processId */
    private String processId;

    /** 导入状态 */
    private String status;

    /** 失败原因 */
    private String failReason;
}
