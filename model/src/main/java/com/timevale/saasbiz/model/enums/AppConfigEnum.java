package com.timevale.saasbiz.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 配置枚举
 *
 * <AUTHOR>
 * @since 2023-03-24 18:03
 */
@Getter
@AllArgsConstructor
public enum AppConfigEnum implements PuppeteerConfigEnum {

    /** 功能开关配置 */
    FUNCTION_SWITCH_MAP("function.switch.map", "{}"),
    FUNCTION_GRAY_SWITCH_MAP("function.gray.switch.map", "{}"),

    /* 流程模板相关 */
    /** 流程模板批量授权最大值 */
    FLOW_TEMPLATE_BATCH_AUTH_ENTERPRISE_MAX("flow.template.auth.batch.enterprise.max", "50"),

    /* 用户中心相关 */
    /** 权限服务校验相关缓存默认1分钟 */
    PERMISSION_SERVICE_EXPIRE("permission.service.expire.second", "60"),
    /** 用户中心相关缓存时间最大生存时间 */
    USER_CENTER_EXPIRE("user.center.expire.second", "600"),

    /** thanos的授权token */
    THANOS_TOKEN(
            "thanos.token",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwcm9maWxlIjoidGVzdCIsIm1kNSI6IjE2Mzk2NTEwNzk4ODYifQ.uddiCn_7lxXG6YF1g99IDb6PaLdixaZTi11nlQASwo8"),

    /** 连接es的最大超时时间(单位：毫秒)，服务器最大默认2min，若该值设置<0或者>2min,服务器会默认使用2min */
    THANOS_MAX_TIMEOUT_MILS("thanos.maxTimeoutMils", "-1"),

    WE_COM_STAFF_LIST("wecom.staff.list", "[]"),
    WE_COM_STAFF_LIST_APPLY("wecom.staff.list.apply", "[]"),
    WE_COM_STAFF_LIST_APPLY_PAID("wecom.staff.list.apply.paid", "[]"),
    WE_COM_STAFF_LIST_APPLY_UNPAID("wecom.staff.list.apply.unpaid", "[]"),
    WE_COM_CUSTOM_LIMIT("wecom.custom.limit", "18000"),
    GUIDE_AB_TEST_CODE("guide.abtest.code", "guide_experiment"),
    GUIDE_WECOM_NODE_CODE("guide.wecom.node.code", "wecom_guide"),
    PERMISSION_APPLY_MSG_MAPPING("permission.apply.msg.mapping", "{}"),
    /** 新手任务appId */
    NOVICE_TASK_APP_ID("novice.task.app.id", ""),
    /** 新手任务开关 */
    NOVICE_TASK_SWITCH("novice.task.switch", "true"),
    /** 轩辕saas的appid */
    SAAS_PROJECT_ID("saas.project.id", ""),
    SAAS_PROJECT_NAME("saas.project.name", "e签宝官网"),
    /** 线下合同导入上限自定义配置 */
    OFFLINE_CONTRACT_MAX_COUNT_CUSTOMIZE("offline.contract.max.count.customize", "{}"),
    /** 杭州天谷的激活实名组织oid */
    ESIGN_ORG_OID("esign.org.oid", ""),

    AUDIT_LOG_FIRST_MODULE("saas.audit.log.first.module", "{}"),

    AUDIT_LOG_SUBSCRIBE_FIRST_MODULE("saas.audit.log.subscribe.first.module", "{}"),

    SAAS_DOWNLOAD_AUDIT_LOG_TAG("saas.audit.log.download.tag", "saas_download_audit_log_tag"),

    AUDIT_LOG_DOWNLOAD_MAX_TIMES("audit.log.download.max.times", "3"),

    APPROVAL_TABLE_HEAD_CONFIG("approval.table.head.config","[]"),

    APPROVAL_GROUP_TABLE_HEAD_CONFIG("approval.group.table.head.config","[]"),

    // 会员功能引导
    VIP_FUNCTION_PRIVILEGE_KEY_MAP("vip.function.privilege.key.map", "{}"),
    VIP_FUNCTION_LEVEL_VERSION_FILE_KEYS("vip.function.level.version.file.keys", "{}"),
    VIP_FUNCTION_ILLUSTRATE_CONTROL_CONFIG("vip.function.illustrate.control.config", "{}"),
    VIP_FUNCTION_ILLUSTRATE_WECHAT_CONFIG("vip.function.illustrate.wechat.config", "{}"),
    VIP_FUNCTION_USED_CONFIG("vip.function.used.config", "{}"),
    VIP_FUNCTION_LIMIT_ILLUSTRATE("vip.function.limit.illustrate","[]"),
    /** 审批列表地址配置 */
    APPROVAL_LIST_URL("approval.list.url", ""),

    SAAS_GRAFT_EPAAS_TEMPLATE_SUPPORT_CLIENT("saas.graft.epaas.template.support.clients", "[\"WEB#web-treaty-front_2.0\"]"),

    FEI_SHU_ILLUSTRATE_QR_CODE("fei.shu.illustrate.qr.code", ""),

    SAAS_COMMODITY_PACKAGE_SHOW_INFO("saas.commodity.package.show.info", "[]"),

    SAAS_TEMPLATE_FIELDS_SUPPORT_CLIENT("saas.template.fields.support.clients", "[\"WEB#web-treaty-front_2.0\"]"),

    LEGAL_NOTIFY_BLACK_LIST("legal.notify.black.list", "[]"),

    BILLING_ASSET_SUPPORT_THIRD_CLIENT("billing.asset.support.third.client", "[]"),
    BILL_COMBINATION_PRODUCT_COMMODITY_MAPPING("bill.combination.product.commodity.mapping", null),
    /** 在线拟定回调项目标识 */
    CONTRACT_DRAFTING_CALLBACK_GROUP("contract.drafting.callback.group", null),

    BATCH_ADD_AUTH_RELATION_NEW_ENABLE("batch.add.auth.relation.new.enable", "true"),
    BATCH_ADD_AUTH_RELATION_NEW_COMPATIBLE_ENABLE("batch.add.auth.relation.new.compatible.enable", "true"),
    BATCH_ADD_AUTH_RELATION_NEW_PROGRESS_MAX_SIZE("batch.add.auth.relation.new.progress.max.size", "1"),



    ;

    /** 配置key */
    private final String key;

    /** 配置默认值 */
    private final String defaultValue;
}
