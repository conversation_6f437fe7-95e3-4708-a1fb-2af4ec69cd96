package com.timevale.saasbiz.model.enums;

/**
 * <AUTHOR>
 * @since 2023-07-19 09:55
 */
public enum SealVisibleScopeEnum {
    ALL("ALL", "所有人可见"),
    IN_CORP("IN_CORP", "企业内成员可见"),
    PART_MEMBER("PART_MEMBER", "部分成员可见");

    private final String code;
    private final String desc;

    SealVisibleScopeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
