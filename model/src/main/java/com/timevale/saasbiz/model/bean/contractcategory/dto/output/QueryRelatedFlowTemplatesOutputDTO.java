package com.timevale.saasbiz.model.bean.contractcategory.dto.output;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractcategory.bo.RelateFlowTemplateFileBO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 查询合同类型关联的流程模板列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
public class QueryRelatedFlowTemplatesOutputDTO extends ToString {
    /** 关联模板文件列表 */
    private List<RelateFlowTemplateFileBO> flowTemplateFiles;
}
