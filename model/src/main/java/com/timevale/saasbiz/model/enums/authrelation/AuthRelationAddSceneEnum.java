package com.timevale.saasbiz.model.enums.authrelation;

import lombok.Getter;

@Getter
public enum AuthRelationAddSceneEnum {

    ADD("ADD", "新增关联企业"),
    RENEW("RENEW", "续签关联企业"),
    ;

    AuthRelationAddSceneEnum(String scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }

    private final String scene;

    private final String desc;

    public static AuthRelationAddSceneEnum from(String scene) {
        for (AuthRelationAddSceneEnum value : values()) {
            if (value.getScene().equalsIgnoreCase(scene)) {
                return value;
            }
        }
        return ADD;
    }
}
