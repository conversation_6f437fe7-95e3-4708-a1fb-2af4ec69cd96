package com.timevale.saasbiz.model.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.saasbiz.model.constants.RemoteConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Header;
import org.apache.http.HeaderElement;
import org.apache.http.HeaderElementIterator;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.FileEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class RemoteHttpUtil extends RemoteHttpBase{

    public static final String JSON = "application/json";
    public static final String FORM = "application/x-www-form-urlencoded";
    public static final String STREAM = "application/octet-stream";
    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    public static final String HEADER_CONTENT_MD5 = "Content-MD5";
    public static final Charset CHARSET = Charset.forName("UTF-8");


    private static final Object lock = new Object();

    private static volatile HttpClient HTTP_CLIENT;



    private static ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    public static byte[] executeGetMethodAsBytes(String url) {
        HttpGet request = new HttpGet(url);
        byte[] data;
        try {
            checkClient();
            HttpResponse response = HTTP_CLIENT.execute(request);
            data =  getResponseBody(response);
        }   catch (IOException e) {
            throw new BaseRuntimeException("an IOException occurred while executing get request", e);
        }
        return data;
    }



    public static ApiHttpResult sendBody(String url, String param, Map<String, String> headers) {
        HttpPost sendMobileVerifyUrl = new HttpPost(url);
        addHeaders(headers, sendMobileVerifyUrl);
        sendMobileVerifyUrl.setHeader(HEADER_CONTENT_TYPE, JSON);
        sendMobileVerifyUrl.setEntity(new StringEntity(param, CHARSET));
        try {
            checkClient();
            return HTTP_CLIENT.execute(sendMobileVerifyUrl, API_RESPONSE_HANDLER);
        } catch (Exception e) {
            throw new BaseRuntimeException(e);
        }
    }

    public static JsonNode sendByFormSubmit(String url, Map<String, String> objectMap) {
        List<NameValuePair> urlParameters = new ArrayList<>();
        for (Map.Entry<String, String> keyAndValue : objectMap.entrySet()) {
            urlParameters.add(new BasicNameValuePair(keyAndValue.getKey(), keyAndValue.getValue()));
        }
        HttpPost formEntity = new HttpPost(url);
        formEntity.setHeader(HEADER_CONTENT_TYPE, FORM);
        try {
            formEntity.setEntity(new UrlEncodedFormEntity(urlParameters));
        } catch (UnsupportedEncodingException e) {
            log.error("sendByFormSubmit error", e);
            return null;
        }
        return getResponse(formEntity, JsonNode.class);
    }

    public static JsonNode sendByPost(String url, Object obj) {
        return sendByPost(url, obj, null, JsonNode.class);
    }

    public static <T> T sendByPost(String url, Object obj, Class<T> valueType) {
        return sendByPost(url, obj, null, valueType);
    }

    public static JsonNode sendByPost(String url, Object obj, Map<String, String> headers) {
        return sendByPost(url, obj, headers, JsonNode.class);
    }

    public static <T> T sendByGet(String url, Map<String, String> headers, Class<T> valueType) {
        HttpGet sendMobileVerifyUrl = new HttpGet(url);
        addHeaders(headers, sendMobileVerifyUrl);
        sendMobileVerifyUrl.setHeader(HEADER_CONTENT_TYPE, JSON);
        return getResponse(sendMobileVerifyUrl, valueType);
    }

    public static <T> T sendByDelete(String url, Map<String, String> headers, Class<T> valueType) {
        HttpDelete sendMobileVerifyUrl = new HttpDelete(url);
        addHeaders(headers, sendMobileVerifyUrl);
        sendMobileVerifyUrl.setHeader(HEADER_CONTENT_TYPE, JSON);
        return getResponse(sendMobileVerifyUrl, valueType);
    }

    public static JsonNode sendByPut(String url, Object obj) {
        return sendByPut(url, obj, null, JsonNode.class);
    }

    public static  <T> T  sendByPut(String url, Object obj, Class<T> valueType) {
        return sendByPut(url, obj, null, valueType);
    }

    public static JsonNode sendByGet(String url) {
        HttpGet sendMobileVerifyUrl = new HttpGet(url);
        sendMobileVerifyUrl.setHeader(HEADER_CONTENT_TYPE, JSON);
        return getResponse(sendMobileVerifyUrl, JsonNode.class);
    }

    public static JsonNode sendByGet(String url, String content) {
        HttpGet sendMobileVerifyUrl;
        if (url.contains("?")) {
            sendMobileVerifyUrl = new HttpGet(url + "&" + content);
        } else {
            sendMobileVerifyUrl = new HttpGet(url + "?" + content);
        }
        sendMobileVerifyUrl.setHeader(HEADER_CONTENT_TYPE, JSON);
        return getResponse(sendMobileVerifyUrl, JsonNode.class);
    }

    public static JsonNode sendByGet(String url, Map<String, String> headers) {
        HttpGet sendMobileVerifyUrl = new HttpGet(url);
        addHeaders(headers, sendMobileVerifyUrl);
        sendMobileVerifyUrl.setHeader(HEADER_CONTENT_TYPE, JSON);
        return getResponse(sendMobileVerifyUrl, JsonNode.class);
    }

    public static <T> T sendByPost(String url, Object obj, Map<String, String> headers, Class<T> valueType) {
        HttpPost sendMobileVerifyUrl = new HttpPost(url);
        addHeaders(headers, sendMobileVerifyUrl);
        sendMobileVerifyUrl.setHeader(HEADER_CONTENT_TYPE, JSON);
        sendMobileVerifyUrl.setEntity(new StringEntity(obj instanceof String ? (String) obj : getJsonStr(obj), CHARSET));
        return getResponse(sendMobileVerifyUrl, valueType);
    }

    public static <T> T sendByPut(String url, Object obj,
                                  Map<String, String> headers, Class<T> valueType) {
        HttpPut sendMobileVerifyUrl = new HttpPut(url);
        addHeaders(headers, sendMobileVerifyUrl);
        sendMobileVerifyUrl.setHeader(HEADER_CONTENT_TYPE, JSON);
        sendMobileVerifyUrl.setEntity(new StringEntity(obj instanceof String ? (String) obj : getJsonStr(obj), CHARSET));
        return getResponse(sendMobileVerifyUrl, valueType);
    }

    public static JsonNode sendBytesToUrl(String url, byte[] data, String contentType, String md5) {
        HttpPut send = new HttpPut(url);
        if(StringUtils.isNotBlank(contentType)){
            send.setHeader(HEADER_CONTENT_TYPE, contentType);
        } else {
            send.setHeader(HEADER_CONTENT_TYPE, STREAM);
        }

        if(StringUtils.isNotBlank(md5)){
            send.setHeader(HEADER_CONTENT_MD5, md5);
        }
        send.setEntity(new ByteArrayEntity(data));
        return getResponse(send, JsonNode.class);
    }

    public static JsonNode sendFileToUrl(String url, File file, String contentType, String md5) {
        HttpPut send = new HttpPut(url);
        if(StringUtils.isNotBlank(contentType)){
            send.setHeader(HEADER_CONTENT_TYPE, contentType);
        } else {
            send.setHeader(HEADER_CONTENT_TYPE, STREAM);
        }

        if(StringUtils.isNotBlank(md5)){
            send.setHeader(HEADER_CONTENT_MD5, md5);
        }
        send.setEntity(new FileEntity(file, ContentType.APPLICATION_OCTET_STREAM));
        return getResponse(send, JsonNode.class);
    }

    /**
     * 添加http头
     *
     * @param headers
     * @param sendMobileVerifyUrl
     */
    private static void addHeaders(Map<String, String> headers, HttpRequestBase sendMobileVerifyUrl) {
        if (headers != null) {
            Set<Map.Entry<String, String>> headerSet = headers.entrySet();
            Iterator<Map.Entry<String, String>> it = headerSet.iterator();
            while (it.hasNext()) {
                Map.Entry<String, String> entry = it.next();
                sendMobileVerifyUrl.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    private static String getJsonStr(Object objectMap) {
        try {
            String result = mapper.writeValueAsString(objectMap);
            return result;
        } catch (JsonProcessingException e) {
            throw new BaseRuntimeException(e);
        }
    }

    private static <T> T getResponse(HttpUriRequest request, Class<T> valueType) {
        checkClient();
        try {
            long start = System.currentTimeMillis();
            String result = HTTP_CLIENT.execute(request, RESPONSE_HANDLER);
            log.debug("method:{}  耗时:{} ms   uri:{}   ", request.getMethod(), System.currentTimeMillis() - start, request.getURI());
            return mapper.readValue(result, valueType);
        } catch (Exception e) {
            log.error("对接接口请求失败,url:" + request.getURI(), e);
            throw new BaseRuntimeException(e);
        }
    }

    private static void  checkClient(){
        if (HTTP_CLIENT == null) {
            synchronized (lock) {
                if (HTTP_CLIENT == null) {
                    try {
                        HTTP_CLIENT = createHttpClient_AcceptsUntrustedCerts();
                    } catch (KeyStoreException e) {
                        e.printStackTrace();
                    } catch (NoSuchAlgorithmException e) {
                        e.printStackTrace();
                    } catch (KeyManagementException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private static final ResponseHandler<String> RESPONSE_HANDLER = new ResponseHandler<String>() {

        @Override
        public String handleResponse(final HttpResponse response) throws IOException {
            int status = response.getStatusLine().getStatusCode();
            HttpEntity entity = response.getEntity();
            String resultContent = EntityUtils.toString(entity, CHARSET);
            if (status >= 200 && status < 300) {
                return resultContent;
            } else {
                log.error("get error status:" + status);
                throw new ClientProtocolException("Unexpected response status: " + status);
            }
        }

    };

    public static class ApiHttpResult {
        /**
         * head信息
         */
        private Map<String, String> head;

        // 结果信息
        private String result;

        public ApiHttpResult(Map<String, String> head, String result) {
            this.head = head;
            this.result = result;
        }

        public Map<String, String> getHead() {
            return head;
        }

        public void setHead(Map<String, String> head) {
            this.head = head;
        }

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }
    }

    private static final ResponseHandler<ApiHttpResult> API_RESPONSE_HANDLER = new ResponseHandler<ApiHttpResult>() {
        @Override
        public ApiHttpResult handleResponse(HttpResponse response) throws IOException {
            int status = response.getStatusLine().getStatusCode();
            HttpEntity entity = response.getEntity();
            Header[] headers = response.getAllHeaders();
            Map<String, String> head = new HashMap<>();
            if (headers != null) {
                for (Header h : headers) {
                    head.put(h.getName(), h.getValue());
                }
            }
            String resultContent = EntityUtils.toString(entity, CHARSET);
            if (status == 200) {
                return new ApiHttpResult(head, resultContent);
            } else {
                throw new ClientProtocolException("Unexpected response status: " + status);
            }
        }
    };


    private static CloseableHttpClient createHttpClient_AcceptsUntrustedCerts()
            throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        HttpClientBuilder b = HttpClientBuilder.create();
        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
            @Override
            public boolean isTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
                return true;
            }
        }).build();
        b.setSslcontext(sslContext);

        HostnameVerifier hostnameVerifier = SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER;

        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", sslSocketFactory)
                .build();

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        connectionManager.setMaxTotal(RemoteConstants.POOL_MAX);
        connectionManager.setDefaultMaxPerRoute(RemoteConstants.POOL_PER_ROUTE);

        ConnectionKeepAliveStrategy keepAliveStrategy = new ConnectionKeepAliveStrategy() {
            @Override
            public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
                HeaderElementIterator it = new BasicHeaderElementIterator
                        (response.headerIterator(HTTP.CONN_KEEP_ALIVE));
                while (it.hasNext()) {
                    HeaderElement he = it.nextElement();
                    String param = he.getName();
                    String value = he.getValue();
                    if (value != null && "timeout".equalsIgnoreCase(param)) {
                        return Long.parseLong(value) * 1000;
                    }
                }
                return 5 * 1000;
            }
        };
        b.setConnectionManager(connectionManager).setKeepAliveStrategy(keepAliveStrategy);

        //设置请求超时时间， 当前连接超时时间为100秒, 请求响应超时时间为1分钟， 客户端响应超时时间1分钟
        RequestConfig config = RequestConfig.custom()
                //the time to establish the connection
                .setConnectTimeout(RemoteConstants.CONNECTION_TIMEOUT)
                //wait for a connection from the connection manager
                .setConnectionRequestTimeout(RemoteConstants.REQUEST_TIMEOUT)
                // the time waiting for data after the connection was established;
                .setSocketTimeout(RemoteConstants.SOCKET_TIMEOUT).build();
        b.setDefaultRequestConfig(config);

        CloseableHttpClient client = b.build();
        return client;
    }


}
