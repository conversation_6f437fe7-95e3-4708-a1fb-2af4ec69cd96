package com.timevale.saasbiz.model.bean.contractcategory.dto.input;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 分页查询合同类型列表请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("分页查询合同类型列表请求参数")
public class PageQueryContractCategoriesInputDTO extends ToString {

    /** 用户oid */
    private String accountId;

    /** 主体oid */
    private String subjectId;

    /** 合同类型状态列表 */
    private List<String> statusList;

    /** 合同类型id */
    private String categoryId;

    /** 合同类型名称 */
    private String categoryName;

    /** 字段id */
    private String fieldId;

    /** 流程模板id */
    private String flowTemplateId;

    /** 流程模板名称 */
    private String flowTemplateName;

    /** 页码 */
    private Integer pageNum;

    /** 每页数据大小 */
    private Integer pageSize;
}
