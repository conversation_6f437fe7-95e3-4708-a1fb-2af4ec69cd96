package com.timevale.saasbiz.model.bean.offlinecontract.dto;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OfflineContractAccountDTO {
    /** 用户账号 */
    private String account;
    /** 用户姓名 */
    private String accountName;
    /** 主体名称 */
    private String subjectName;
    /** 主体类型 */
    @NotBlank(message = "主体类型不能为空")
    private Integer subjectType;

    /** 判断是否账号信息为空 */
    public boolean emptyAccount() {
        return StringUtils.isAllBlank(account, accountName, subjectName);
    }
}
