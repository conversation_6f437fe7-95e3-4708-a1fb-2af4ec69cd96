package com.timevale.saasbiz.model.enums.authrelation;

import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by t<PERSON><PERSON><PERSON> on 2022/2/15
 */
public enum AuthRelationBatchAddTaskStatusEnum {


    DOING(0, "处理中"),
    COMPLETED_ALL_SUCCESS(1, "处理完成-全部成功"),
    COMPLETED_PART_SUCCESS(2, "处理完成-部分成功");


    AuthRelationBatchAddTaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static final Set<Integer> COMPLETED_STATUS_SET = new HashSet<>();

    static {
        COMPLETED_STATUS_SET.add(COMPLETED_ALL_SUCCESS.getCode());
        COMPLETED_STATUS_SET.add(COMPLETED_PART_SUCCESS.getCode());
    }

    @Getter
    private Integer code;

    private String desc;

}
