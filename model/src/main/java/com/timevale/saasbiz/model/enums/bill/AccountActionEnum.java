package com.timevale.saasbiz.model.enums.bill;

import lombok.Getter;

@Getter
public enum AccountActionEnum {
    WITHDRAW("withdraw", "提现", "提现"),
    PURCHASE("purchase", "购买", "购买"),
    GUIDE_PURCHASE("guide-purchase", "购买", "(引导)购买"),
    UPGRADE_PURCHASE("upgrade-purchase", "购买", "(升级)购买"),
    BILLING_RECORDS("billing-records", "明细", "(账单)明细"),
    BALANCE_TRANSACTIONS("balance-transactions", "明细", "(余额)明细"),
    CONSULT("consult", "咨询", "咨询"),
    ;

    /**
     * 操作类型
     */
    private final String code;

    /**
     * 展示名称
     */
    private final String displayName;

    /**
     * 描述
     */
    private final String desc;

    AccountActionEnum(String code, String displayName, String desc) {
        this.code = code;
        this.displayName = displayName;
        this.desc = desc;
    }

    public static AccountActionEnum fromCode(String code) {
        for (AccountActionEnum accountActionEnum : AccountActionEnum.values()) {
            if (accountActionEnum.getCode().equals(code)) {
                return accountActionEnum;
            }
        }
        return null;
    }

    public static String getDisplayName(String code) {
        AccountActionEnum accountActionEnum = fromCode(code);
        if (accountActionEnum == null) {
            return null;
        }
        return accountActionEnum.getDisplayName();
    }
}
