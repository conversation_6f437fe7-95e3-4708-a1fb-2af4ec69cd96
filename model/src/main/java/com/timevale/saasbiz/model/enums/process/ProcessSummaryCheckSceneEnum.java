package com.timevale.saasbiz.model.enums.process;

import lombok.Getter;

/**
 * 合同摘要校验场景
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Getter
public enum ProcessSummaryCheckSceneEnum {
    NORMAL("NORMAL", "正常使用"),
    APPID_CONFIG_NOT_SUPPORT("APPID_CONFIG_NOT_SUPPORT", "appid配置不支持"),

    VIP_NOT_SUPPORT("VIP_NOT_SUPPORT", "vip版本不支持"),

    USER_NOT_AUTH("USER_NOT_AUTH", "用户未授权"),

    NOT_IN_GRAY("NOT_IN_GRAY", "不在灰度内"),

    OTHER_NOT_SUPPORT("OTHER_NOT_SUPPORT", "其他不支持场景");

    private final String code;
    private final String desc;

    ProcessSummaryCheckSceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
