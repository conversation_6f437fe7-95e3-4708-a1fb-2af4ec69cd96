package com.timevale.saasbiz.model.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.timevale.mandarin.base.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 字符串转列表工具类
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
public class IdsUtil {

    public static final String SPLIT_LETTER = ",";

    public static List<String> getIdList(final String idString) {
        List<String> ids = Lists.newArrayList();
        parseIds(ids, idString, SPLIT_LETTER);
        return ids;
    }

    public static List<String> getIdList(final String idString, final String separatorChars) {
        List<String> ids = Lists.newArrayList();
        parseIds(ids, idString, separatorChars);
        return ids;
    }

    public static Set<String> getIdSet(final String idString) {
        Set<String> ids = Sets.newHashSet();
        parseIds(ids, idString, SPLIT_LETTER);
        return ids;
    }

    public static Set<String> getIdSet(final String idString, final String separatorChars) {
        Set<String> ids = Sets.newHashSet();
        parseIds(ids, idString, separatorChars);
        return ids;
    }

    public static Set<String> getIdSet(final String idString, final List<String> separatorCharss) {
        Set<String> idSet = Sets.newHashSet();
        idSet.add(idString);
        for (String separatorChars : separatorCharss) {
            Set<String> idAllSet = Sets.newHashSet();
            for (String text : idSet) {
                idSet = getIdSet(text, separatorChars);
                idAllSet.addAll(idSet);
            }
            idSet = idAllSet;
        }
        return idSet;
    }

    private static void parseIds(
            final Collection<String> collection,
            final String idString,
            final String separatorChars) {
        if (StringUtils.isBlank(idString) || collection == null) {
            return;
        }
        String[] strings = idString.split(separatorChars);
        for (String id : strings) {
            if (StringUtils.isBlank(id)) {
                continue;
            }
            collection.add(id);
        }
    }

    public static Set<Integer> getIntegerSet(String idString) {
        Set<Integer> ids = Sets.newHashSet();
        parseIntegerIds(ids, idString, SPLIT_LETTER);
        return ids;
    }

    public static List<Integer> getIntegerIdList(String idString) {
        List<Integer> ids = Lists.newArrayList();
        parseIntegerIds(ids, idString, SPLIT_LETTER);
        return ids;
    }

    public static String toString(Collection<String> idList) {
        return StringUtils.join(idList, SPLIT_LETTER);
    }

    private static void parseIntegerIds(
            final Collection<Integer> collection,
            final String idString,
            final String separatorChars) {
        if (StringUtils.isBlank(idString) || collection == null) {
            return;
        }
        String[] strings = idString.split(separatorChars);
        for (String id : strings) {
            if (StringUtils.isBlank(id)) {
                continue;
            }
            collection.add(Integer.valueOf(id));
        }
    }
}
