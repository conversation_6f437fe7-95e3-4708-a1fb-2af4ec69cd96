package com.timevale.saasbiz.model.bean.offlinecontract.dto;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 线下合同信息
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Data
public class OfflineContractDTO extends ToString {

    /** 线下合同文件列表 */
    private List<OfflineContractFileDTO> contractFiles;

    /** 线下合同合同信息 */
    private OfflineContractProcessInfoDTO processInfo;
}
