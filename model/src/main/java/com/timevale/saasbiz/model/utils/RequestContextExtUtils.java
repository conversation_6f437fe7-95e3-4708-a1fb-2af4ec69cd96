package com.timevale.saasbiz.model.utils;


import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.weaver.utils.RequestContext;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

public class RequestContextExtUtils extends RequestContext {

    /**
     * 获取token
     *
     * @return
     */
    public static String getToken() {
        return getString(HEADER_TOKEN,false);
    }
    /**
     * 获取空间ID
     *
     * @return
     */
    public static String getTenantId() {
        String tenantId =
                RequestContext.getRequest() == null
                        ? null
                        : RequestContext.getRequest().getHeader(HEADER_TENANT_ID);
        if (StringUtils.isBlank(tenantId)) {
            Object o = get(HEADER_TENANT_ID);
            tenantId = o == null ? null : o.toString();
        }
        return tenantId;
    }

    /**
     * header中设置租户id
     *
     * @param tenantId 租户id
     */
    public static void setTenantId(String tenantId) {
        put(HEADER_TENANT_ID, tenantId);
    }

    /**
     * 获取请求clientId
     *
     * @return 请求clientId
     */
    public static String getClientId() {
        return getString(HEADER_CLIENT_ID, false);
    }

    /**
     * 获取请求clientId
     *
     * @return 请求clientId
     */
    public static void setClientId(String clientId) {
        if (StringUtils.isNotBlank(clientId)) {
            put(HEADER_CLIENT_ID, clientId);
        }
    }

    /**
     * 获取操作人id
     *
     * @return 操作人id
     */
    public static String getOperatorId() {
        String operatorId =
                RequestContext.getRequest() == null
                        ? null
                        : RequestContext.getRequest().getHeader(HEADER_OPERATOR_ID);
        if (StringUtils.isBlank(operatorId)) {
            Object o = get(HEADER_OPERATOR_ID);
            operatorId = o == null ? null : o.toString();
        }
        return operatorId;
    }

    /**
     * 获取语言
     *
     * @return
     */
    public static String getLanguage() {
        String language =
                RequestContext.getRequest() == null
                        ? null
                        : RequestContext.getRequest().getHeader(HEADER_LANGUAGE);
        if (StringUtils.isBlank(language)) {
            Object o = get(HEADER_LANGUAGE);
            language = o == null ? null : o.toString();
        }
        return language;
    }

    /**
     * 设置操作人id
     *
     * @param operatorId 操作人id
     */
    public static void setOperatorId(String operatorId) {
        put(HEADER_OPERATOR_ID, operatorId);
    }


    /**
     * 获取钉签IsvAppId
     *
     * @return 钉签IsvAppId
     */
    public static String getIsvAppId() {
        return getString(HEADER_ISV_APP_ID, false);
    }


    /**
     * 获取钉签userId
     *
     * @return 钉签userId
     */
    public static String getDingUserId() {
        return getString(HEADER_DING_USER_ID, false);
    }

    /**
     * 设置钉签IsvAppId
     *
     * @return 钉签IsvAppId
     */
    public static void setIsvAppId(String isvAppId) {
        if (StringUtils.isNotBlank(isvAppId)) {
            put(HEADER_ISV_APP_ID, isvAppId);
        }
    }

    /**
     * 获取钉签CorpId
     *
     * @return 钉签CorpId
     */
    public static String getDingCorpId() {
        return getString(HEADER_DING_CORP_ID, false);
    }

    /**
     * 获取钉签CorpId
     *
     * @return 钉签CorpId
     */
    public static void setDingCorpId(String dingCorpId) {
        if (StringUtils.isNotBlank(dingCorpId)) {
            put(HEADER_DING_CORP_ID, dingCorpId);
        }
    }

    /**
     * 获取请求的IP地址
     * @return
     */
    public static String getRequestIp(){
        String requestIp =
                RequestContext.getRequest() == null
                        ? null
                        : RequestContext.getRequest().getHeader(HEADER_WEBSERVER_REQUEST_IP);
        if (StringUtils.isBlank(requestIp)) {
            Object o = get(HEADER_WEBSERVER_REQUEST_IP);
            requestIp = o == null ? null : o.toString();
        }
        return requestIp;
    }

    /**
     * 设置请求的IP地址
     * @param requestIp
     */
    public static void setRequestIp(String requestIp){
        if (StringUtils.isNotBlank(requestIp)) {
            put(HEADER_WEBSERVER_REQUEST_IP, requestIp);
        }
    }

    /**
     * 获取DNS应用id
     * @return
     */
    public static String getDnsAppId() {
       return getString(HEADER_DNS_APP_ID, false);
    }



    /**
     * 设置相关入参
     * @return
     */
    public static void setRequestContextRequest(String operatorId,String tenantId){
        setOperatorId(operatorId);
        setTenantId(tenantId);
    }

    /**
     * 获取指定请求头值
     *
     * @param header 请求头key值
     * @param requestOnly 是否只获取Request请求中的值， 如果否，则支持获取RequestContext本地缓存的值
     * @return
     */
    private static String getString(String header, boolean requestOnly) {
        String value = null;
        if (RequestContext.getRequest() != null) {
            value = RequestContext.getRequest().getHeader(header);
        }
        if (StringUtils.isBlank(value) && !requestOnly) {
            Object obj = get(header);
            value = null == obj ? null : obj.toString();
        }
        return value;
    }

    public static String getAppName() {
        return getString(HEADER_KEY_APP_NAME, false);
    }
}
