package com.timevale.saasbiz.model.bean.contractcategory.dto.input;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractcategory.bo.RelateFlowTemplateFileBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 合同类型关联流程模板请求参数
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
public class RelateContractCategoryFlowTemplatesInputDTO extends ToString {
    /** 用户oid */
    private String accountId;
    /** 主体oid */
    private String subjectId;
    /** 合同类型id */
    private String categoryId;
    /** 关联的流程模板文件列表 */
    private List<RelateFlowTemplateFileBO> relateFlowTemplates;
}
