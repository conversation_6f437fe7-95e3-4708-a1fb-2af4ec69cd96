package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询线下合同导入记录基本信息
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class QueryOfflineContractRecordInfoInputDTO extends ToString {

    @NotBlank(message = "主体id不能为空")
    private String subjectId;

    @NotBlank(message = "导入记录id不能为空")
    private String recordId;

    /** 是否返回菜单路径 */
    private boolean withMenuPath;
}
