package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量操作线下合同导入记录
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class BatchOfflineContractRecordsInputDTO extends ToString {

    @NotBlank(message = "主体oid不能为空")
    private String subjectId;

    @NotEmpty(message = "导入记录id列表不能为空")
    private List<String> recordIds;
}
