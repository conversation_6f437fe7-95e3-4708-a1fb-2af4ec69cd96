package com.timevale.saasbiz.model.enums;

import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import lombok.Getter;

/**
 * 用户权限资源枚举
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Getter
public enum PrivilegeResourceEnum {

    ADMIN(PrivilegeResourceConstants.RESOURCE_ADMIN, "管理员"),
    APP_AUTH_RECORD("APP_AUTH_RECORD", "应用授权记录管理"),
    ISSUE("ISSUE", "出证管理"),
    APPROVAL_TEMPLATE(PrivilegeResourceConstants.APPROVAL_TEMPLATE, "审批模板"),
    TEMPLATE(PrivilegeResourceConstants.RESOURCE_TEMPLATE, "文件模板"),
    AUTH_RELATION("AUTH_RELATION", "关联企业"),
    SEAL("SEAL", "印章管理"),
    BOOK_KEEPING(PrivilegeResourceConstants.RESOURCE_BOOK_KEEPING, "智能台账"),
    AUTO_ARCHIVE("AUTO_ARCHIVE", "智能归档"),
    AUDIT_LOG("AUDIT_LOG", "审计日志"),
    INTELLIGENT_TOOL(PrivilegeResourceConstants.PRIVILEGE_RESOURCE_INTELLIGENT_TOOL,"智能工具"),
    WATERMARK(PrivilegeResourceConstants.WATERMARK,"水印设置"),
    ;

    private String type;
    private String desc;

    PrivilegeResourceEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
