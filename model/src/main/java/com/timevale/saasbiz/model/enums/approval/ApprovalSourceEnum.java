package com.timevale.saasbiz.model.enums.approval;
/**
 * 审批来源
 *
 * <AUTHOR>
 * @since 2022/4/4 15:15
 */
public enum ApprovalSourceEnum {
    SAAS(1, "SAAS"),
    TIAN_YIN(2, "天印"),
    DING_DING(3, "钉钉"),
    ;

    private final int code;
    private final String desc;

    ApprovalSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
