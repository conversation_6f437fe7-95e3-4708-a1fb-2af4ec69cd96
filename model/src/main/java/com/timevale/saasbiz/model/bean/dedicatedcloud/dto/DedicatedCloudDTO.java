package com.timevale.saasbiz.model.bean.dedicatedcloud.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/1 14:35
 */
@Data
public class DedicatedCloudDTO {

    @ApiModelProperty("项目id")
    private String dedicatedCloudId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("绑定appId")
    private String appId;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("归属企业gid")
    private String subjectGid;

    @ApiModelProperty("归属企业名称")
    private String subjectName;

    @ApiModelProperty("地址链接")
    private String serverUrl;

    @ApiModelProperty("是否有效")
    private Boolean effective;

    @ApiModelProperty("版本")
    private String projectVersion;

    @ApiModelProperty("服务状态")
    private Integer serviceStatus;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("修改时间")
    private Long modifyTime;

    @ApiModelProperty("创建人Oid")
    private String createByOid;

    @ApiModelProperty("创建人name")
    private String createName;

    @ApiModelProperty("创建人联系方式")
    private String createContact;

    @ApiModelProperty("修改人oid")
    private String modifyByOid;

    @ApiModelProperty("修改人姓名")
    private String modifyName;

    @ApiModelProperty("修改人联系方式")
    private String modifyContact;

    @ApiModelProperty("订单是否失效")
    private Boolean orderExpire;

    @ApiModelProperty("是否可编辑状态")
    private Boolean canEditStatus;

    @ApiModelProperty("授权appId")
    List<DedicatedCloudAuthAppIdDTO> authAppIds;

}
