package com.timevale.saasbiz.model.enums.approval;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/4/8 14:41
 */
@Getter
public enum ApprovalOperateTypeEnum {
    REVOKE("REVOKE", "撤回", 30),
    AGREE("AGREE", "同意", 10),
    REFUSE("REFUSE", "驳回", 20),
    DELETE("DELETE", "删除", 45),
    DETAIL("DETAIL", "详情", 50),
    RUSH("RUSH", "催办", 40),
    CHANGE("CHANGE", "更换审批流", 70),
    PROCESS("PROCESS", "查看审批流", 80),
    TRANSFER("TRANSFER", "转交", 60),
    ADD("ADD", "增加审批人", 85),
    REDUCE("REDUCE", "减少审批人", 86),
    COMPARE_RECORD("COMPARE_RECORD", "查看比对记录", 90),
    AI_SUMMARY("AI_SUMMARY", "AI合同摘要", 100),
    DOWNLOAD("DOWNLOAD", "下载", 110),
    ;

    /** 操作类型 */
    private String type;
    /** 操作描述 */
    private String desc;
    /** 操作顺序, 正序显示， 顺序越小越靠前 */
    private int order;

    ApprovalOperateTypeEnum(String type, String desc, int order) {
        this.type = type;
        this.desc = desc;
        this.order = order;
    }
}
