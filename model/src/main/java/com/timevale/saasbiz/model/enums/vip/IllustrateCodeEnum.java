package com.timevale.saasbiz.model.enums.vip;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-01-02 09:46
 */
@Getter
public enum IllustrateCodeEnum {
    NO_BUTTON(0, "无按钮"),
    NO_FUNCTION_LEVEL(1, "无版本"),
    NO_FUNCTION_AUTH(2, "无权限"),
    APPLY_TRIAL(3,"申请试用开通"),
    DIRECT_TRIAL(4,"直接开通试用"),
    ;

    private final Integer code;

    private final String desc;

    IllustrateCodeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
