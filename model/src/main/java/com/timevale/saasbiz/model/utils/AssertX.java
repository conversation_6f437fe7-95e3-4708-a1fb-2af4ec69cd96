package com.timevale.saasbiz.model.utils;


import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;

import java.util.Optional;
import java.util.function.Supplier;

/**
 * Created by tianlei on 2022/3/17
 *
 */
public class AssertX {

    public static void isTrue(boolean expression, String hintInfo) {
        isTrue(expression, () -> hintInfo);
    }

    public static void isTrue(boolean expression, Supplier<String> hintInfoSupplier) {
        isTrue(expression, SaasBizResultCode.SAAS_ILLEGAL_PARAM , hintInfoSupplier);
    }

    public static void isTrue(boolean expression, SaasBizResultCode errorCodeEnum, String msg) {
        isTrue(expression, errorCodeEnum, () -> msg);
    }

    public static void isTrue(boolean expression, SaasBizResultCode errorCodeEnum, Supplier<String> hintInfoSupplier) {
        if (!expression) {
            throw new SaasBizException(errorCodeEnum.getCode(),
                    Optional.ofNullable(hintInfoSupplier.get()).orElseGet(errorCodeEnum::getMessage));
        }
    }

    public static void isTrueThenAction(boolean expression, Action action) {
        if (!expression) {
            if (null == action) {
                throw new IllegalArgumentException("isTrueThenExecute 未设置执行行为");
            }
            action.execute();
        }
    }

    @FunctionalInterface
    public interface Action {
        void execute();
    }


}
