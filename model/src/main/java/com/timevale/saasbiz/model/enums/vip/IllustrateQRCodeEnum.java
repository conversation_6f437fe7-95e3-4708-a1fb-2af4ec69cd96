package com.timevale.saasbiz.model.enums.vip;

import lombok.Getter;

@Getter
public enum IllustrateQRCodeEnum {
    SERVICE_GROUP("SERVICE_GROUP", "服务群二维码"),
    PAID_CUSTOMER_SDR("PAID_CUSTOMER_SDR", "付费客户专属客服"),
    NEW_CUSTOMER_SDR("NEW_CUSTOMER_SDR", "新客户专属客服"),
    FEI_SHU_SDR("FEI_SHU_SDR", "飞书专属客服"),
    MONTH_REPORT_SDR("MONTH_REPORT_SDR", "月度客户专属客服"),
    ;

    private final String code;
    private final String desc;

    IllustrateQRCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
