package com.timevale.saasbiz.model.enums.authrelation;

import com.timevale.saas.common.manage.common.service.constant.AuthRelationShareConfigKeyConstant;
import com.timevale.saasbiz.model.constants.AuthRelationAuthInfoGroupTypeConstants;
import com.timevale.saasbiz.model.constants.AuthRelationAuthInfoHandlerConstants;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024-12-13
 */
@Getter
public enum AuthRelationAuthInfoTypeEnum {
    SHARE_APP_COUNT(
            AuthRelationShareConfigKeyConstant.SHARE_APP_COUNT,
            AuthRelationAuthInfoHandlerConstants.AUTH_SHARE_APP_COUNT_HANDLER,
            AuthRelationAuthInfoGroupTypeConstants.SHARE_CONFIG,
            "共享应用数量"),
    AUTH_RELATION(
            "authRelation",
            AuthRelationAuthInfoHandlerConstants.AUTH_RELATION_HANDLER,
            AuthRelationAuthInfoGroupTypeConstants.AUTH_RESOURCE,
            "关联企业授权");

    private final String code;
    private final String handlerName;
    private final String group;
    private final String desc;

    AuthRelationAuthInfoTypeEnum(String code, String handlerName, String group, String desc) {
        this.code = code;
        this.handlerName = handlerName;
        this.group = group;
        this.desc = desc;
    }

    public static AuthRelationAuthInfoTypeEnum parseWithCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        return Arrays.stream(values()).filter(v->v.getCode().equals(code)).findAny().orElse(null);
    }
}
