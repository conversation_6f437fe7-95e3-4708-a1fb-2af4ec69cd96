package com.timevale.saasbiz.model.bean.contractcategory.dto.output;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saasbiz.model.bean.contractcategory.bo.BaseContractCategoryBO;
import com.timevale.saasbiz.model.bean.contractcategory.bo.BaseContractCategoryListBO;
import com.timevale.saasbiz.model.bean.contractcategory.bo.ContractCategoryBO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 查询可用的合同类型列表响应数据
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Data
@ApiModel("查询可用的合同类型列表响应数据")
public class QueryUsableContractCategoriesOutputDTO extends ToString {

    /** 可用的合同类型列表 */
    private List<BaseContractCategoryListBO> categories;
}
