package com.timevale.saasbiz.model.enums.infocollect;

/**
 * 任务结果按钮类型
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public enum InfoCollectTaskButtonTypeEnum {
    
    GO_SIGN("goSign","去签署"),
    ;

    private String code;
    private String desc;

    InfoCollectTaskButtonTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
