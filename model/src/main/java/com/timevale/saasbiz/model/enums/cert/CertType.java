package com.timevale.saasbiz.model.enums.cert;

import lombok.Getter;

/**
 * 证书类型
 * <AUTHOR>
 * @since 2023-05-09
 */
@Getter
public enum CertType {
    PERSON("PERSON", "个人证书"),
    ORG("ORG", "企业证书");

    private final String type;
    private final String desc;

    CertType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 是否为个人证书类型
     *
     * @param certType
     * @return
     */
    public static boolean  isPerson(CertType certType) {
        return certType == PERSON;
    }

    /**
     * 是否为企业证书类型
     *
     * @param certType
     * @return
     */
    public static boolean isOrg(CertType certType) {
        return certType == ORG;
    }
}
