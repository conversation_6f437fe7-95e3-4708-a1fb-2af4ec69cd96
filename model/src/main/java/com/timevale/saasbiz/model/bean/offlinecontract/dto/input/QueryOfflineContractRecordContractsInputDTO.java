package com.timevale.saasbiz.model.bean.offlinecontract.dto.input;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 查询线下合同导入记录合同信息列表
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
public class QueryOfflineContractRecordContractsInputDTO extends ToString {

    @NotBlank(message = "主体id不能为空")
    private String subjectId;

    @NotBlank(message = "导入记录id不能为空")
    private String recordId;

    /** 是否返回提取信息 */
    private boolean withExtract;

    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @NotNull(message = "每页数据大小不能为空")
    private Integer pageSize;
}
