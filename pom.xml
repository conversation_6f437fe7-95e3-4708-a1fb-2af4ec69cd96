<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.timevale</groupId>
        <artifactId>mandarin-bom</artifactId>
        <version>2.12.8</version>
    </parent>

    <groupId>com.timevale.saasbiz</groupId>
    <artifactId>saasbiz-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>saasbiz/Parent</name>
    <url>https://www.tsign.cn/</url>
    <description>saasbiz Application Parent</description>

    <modules>
        <module>integration</module>
        <module>model</module>
        <module>service</module>
        <module>deploy</module>
        <module>rest</module>
        <module>dal</module>
        <module>task</module>
    </modules>

    <properties>
        <swagger2.version>2.8.0</swagger2.version>
        <thanos-sdk.verison>2.4.7</thanos-sdk.verison>
        <saas-common.manager>1.4.6-SNAPSHOT</saas-common.manager>
        <saas-common.utils>1.0.13-SNAPSHOT</saas-common.utils>
        <doc-manager>3.0.4-SNAPSHOT</doc-manager>
        <doc-cooperation>4.6.8-SNAPSHOT</doc-cooperation>
        <saas-common.util.privilege>1.0.8-SNAPSHOT</saas-common.util.privilege>
        <doc-manager>3.1.4-SNAPSHOT</doc-manager>
        <ai-athena>1.0.13-SNAPSHOT</ai-athena>
        <ding.starter.version>1.2.0-SNAPSHOT</ding.starter.version>
        <easy.excel.version>2.2.10</easy.excel.version>
        <saas-integration.version>1.2.0-SNAPSHOT</saas-integration.version>
        <saas.common.util.version>0.0.2-SNAPSHOT</saas.common.util.version>
        <open.protal.core.version>1.3.7-SNAPSHOT</open.protal.core.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger2.version}</version>

                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-aop</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-tx</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-orm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-jdbc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-oxm</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-aop</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-tx</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-orm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-jdbc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-oxm</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>1.9.1</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.thanos</groupId>
                <artifactId>thanos-sdk</artifactId>
                <version>${thanos-sdk.verison}</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.saas-common-manage</groupId>
                <artifactId>saas-common-manage-common-service-facade</artifactId>
                <version>${saas-common.manager}</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.docmanager</groupId>
                <artifactId>docmanager-facade</artifactId>
                <version>${doc-manager}</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.doc-cooperation</groupId>
                <artifactId>doc-cooperation-facade</artifactId>
                <version>${doc-cooperation}</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.athena</groupId>
                <artifactId>ai-athena-facade</artifactId>
                <version>${ai-athena}</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.contractmanager</groupId>
                <artifactId>contractmanager-common-service-facade</artifactId>
                <version>1.5.5-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.contractanalysis</groupId>
                <artifactId>contractanalysis-facade</artifactId>
                <version>1.2.21-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.dayu</groupId>
                <artifactId>config-facade</artifactId>
                <version>1.0.7-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.saas</groupId>
                <artifactId>common-privilege-util</artifactId>
                <version>${saas-common.util.privilege}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.timevale.saas-common-manage</groupId>
                        <artifactId>saas-common-manage-common-service-facade</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.timevale.saas</groupId>
                <artifactId>common-util</artifactId>
                <version>${saas-common.utils}</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.datarefresh</groupId>
                <artifactId>datarefresh-facade</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.flowmanager</groupId>
                <artifactId>flowmanager-common-service-facade</artifactId>
                <version>2.1.5-SNAPSHOT</version>
            </dependency>

            <!--多语言工具包-->
            <dependency>
                <groupId>com.timevale.saas-utils</groupId>
                <artifactId>multilingual-translate-util</artifactId>
                <version>0.0.5-SNAPSHOT</version>
            </dependency>
            <!--钉系服务starter包-->
            <dependency>
                <groupId>com.timevale.saas-auth-api</groupId>
                <artifactId>ding-spring-boot-starter</artifactId>
                <version>${ding.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.1</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.clmc</groupId>
                <artifactId>clmc-facade</artifactId>
                <version>1.0.12-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.footstone-base</groupId>
                <artifactId>footstone-base-model</artifactId>
                <version>1.0.8-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easy.excel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.saas-integration</groupId>
                <artifactId>saas-integration-facade</artifactId>
                <version>${saas-integration.version}</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.besp.lowcode.tripartite</groupId>
                <artifactId>tripartite-rpc-facade</artifactId>
                <version>0.0.15-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.open.platform.portal.core</groupId>
                <artifactId>open-platform-portal-core-facade</artifactId>
                <version>${open.protal.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.timevale.dayu</groupId>
                <artifactId>dayu-facade</artifactId>
                <version>1.2.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.timevale.sterna.aiagent</groupId>
                <artifactId>saas-ai-agent-generic-rpc</artifactId>
                <version>1.0.3-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.xiaoymin</groupId>
                        <artifactId>knife4j-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
