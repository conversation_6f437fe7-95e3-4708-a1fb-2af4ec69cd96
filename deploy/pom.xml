<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.timevale.saasbiz</groupId>
        <artifactId>saasbiz-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>saasbiz-deploy</artifactId>
    <name>saasbiz/deploy</name>
    <packaging>jar</packaging>

    <properties>
        <capuslue.maven.plugin.version>1.5.1</capuslue.maven.plugin.version>
        <tgtest.base.version>1.4-SNAPSHOT</tgtest.base.version>
        <aspectj.version>1.9.4</aspectj.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>saasbiz-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>saasbiz-task</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>saasbiz-integration</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>saasbiz-rest</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.23.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-testng</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.testng</groupId>
                    <artifactId>testng</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>tgtest</groupId>
            <artifactId>tgtest-base</artifactId>
            <version>${tgtest.base.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-configuration</groupId>
                    <artifactId>commons-configuration</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.timevale.epaas-i18n-sdk</groupId>
            <artifactId>epaas-i18n-spring-boot-starter</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- 部署工具 -->
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>capsule-linux-daemon</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>saasbiz-deploy</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.5.10.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <mainClass>com.timevale.saasbiz.deploy.Application</mainClass>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M3</version>
                <configuration>
                    <suiteXmlFiles>
                        <suiteXmlFile>src/test/resources/testng.xml</suiteXmlFile>
                    </suiteXmlFiles>
                    <argLine>
                        -javaagent:"${settings.localRepository}/org/aspectj/aspectjweaver/${aspectj.version}/aspectjweaver-${aspectj.version}.jar"
                    </argLine>
                    <argLine>${surefireArgLine}</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <propertyName>surefireArgLine</propertyName>
                    <excludes>
                        <exclude>com/timevale/saasbiz/rest/bean/**/*</exclude>
                        <exclude>com/timevale/saasbiz/rest/converter/**/*</exclude>
                        <exclude>com/timevale/saasbiz/rest/*</exclude>
                        <exclude>com/timevale/saasbiz/service/approval/templateoperate/SealApprovalTemplateOperateService.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/approval/impl/ApprovalInstanceClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/approval/impl/OldContractClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/approval/impl/SealApprovalClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/model/approval/dto/*</exclude>
                        <exclude>com/timevale/saasbiz/rest/approval/dto/*</exclude>
                        <exclude>com/timevale/saasbiz/service/approval/convert/*</exclude>
<!--                        转发无业务逻辑-->
                        <exclude>com/timevale/saasbiz/service/approval/approvaloperate/executor/ApprovalOperateAsyncTaskExecutor.*</exclude>
                        <exclude>com/timevale/saasbiz/service/approval/approvaloperate/operate/SealApprovalAgreeProcessor.*</exclude>
                        <exclude>com/timevale/saasbiz/service/approval/approvaloperate/operate/SealApprovalRefuseProcessor.*</exclude>
                        <exclude>com/timevale/saasbiz/service/approval/approvaloperate/operate/SealApprovalRevokeProcessor.*</exclude>
                        <exclude>com/timevale/saasbiz/rest/DedicatedCloudRest.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/dedicatedcloud/impl/DedicatedCloudClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/dedicatedcloud/impl/DedicatedCloudClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/app/impl/AppClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/infocollect/impl/InfoCollectClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/infocollect/impl/InfoCollectClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/flowtemplate/impl/FlowTemplateClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/usercenter/impl/UserCenterClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/integration/process/impl/ProcessStartDataClientImpl.*</exclude>
                        <exclude>com/timevale/saasbiz/tracking/tracking/FlowTemplateBatchAuthTrackingService.*</exclude>
                        <exclude>com/timevale/saasbiz/mq/bean/*.*</exclude>

                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>