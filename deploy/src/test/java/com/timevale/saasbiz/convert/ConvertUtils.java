package com.timevale.saasbiz.convert;


import com.timevale.saasbiz.model.bean.dedicatedcloud.dto.DedicatedCloudDTO;
import com.timevale.saasbiz.rest.bean.dedicatedcloud.response.DedicatedCloudVO;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * @auth tianlei
 */
public class ConvertUtils {

    public static void convert(Map<Class<?>, Class<?>> subConvertClass) {
        subConvertClass.forEach((ken, value) -> convert(ken, value, "convert"));
    }

    public static void convert(Class fromClass, Class toClass, String methodName) {

        String fromName = "before";
        String toName = "after";
        //from->to
        //构造输出
        System.out.println("public static " + toClass.getSimpleName() + " " + methodName + "(" + fromClass.getSimpleName() + " " + fromName + "){");

        System.out.println(" if(null==" + fromName + "){\n"
                + "            return null;\n"
                + "        }");

        System.out.println(toClass.getSimpleName() + " " + toName + "=new " + toClass.getSimpleName() + "();");

        Field[] toField = toClass.getDeclaredFields();

        for (Field field : toField) {

            String name = field.getName();

            if (StringUtils.equals(name, "serialVersionUID")) {
                continue;
            }

            //首字母大写
            String tempName = toUpperCaseFirstOne(name);
            System.out.println(toName + ".set" + tempName + "(" + fromName + ".get" + tempName + "());");

            // 如果这个字段是个业务队形，是否也能进行copy

        }

        System.out.println("return " + toName + ";");
        System.out.println("}");

        System.out.println("");
        System.out.println("");
        //from->to list
        //构造输出
        System.out.println("public static List<" + toClass.getSimpleName() + "> " + methodName + "(List<" + fromClass.getSimpleName() + "> " + fromName + "List){");
        System.out.println("return Optional.ofNullable(beforeList).orElse(new ArrayList<>()).stream().map(elm -> model2do(elm)).collect(Collectors.toList());");
        System.out.println("}");

    }

    public static String toUpperCaseFirstOne(String s) {
        if (Character.isUpperCase(s.charAt(0))) {
            return s;
        } else {
            return (new StringBuilder()).append(Character.toUpperCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }

    public static String toLowerCaseFirstOne(String s) {
        if (Character.isLowerCase(s.charAt(0))) {
            return s;
        } else {
            return (new StringBuilder()).append(Character.toLowerCase(s.charAt(0))).append(s.substring(1)).toString();
        }
    }

    public static void main(String args[]) {
        Map<Class<?>, Class<?>> subConvertClass = new HashMap<Class<?>, Class<?>>() {{
//            put(ProcessStartDataQueryModel.class, ProcessStartDataDTO.class);
        }};
        convert(subConvertClass);
    }

}
