package com.timevale.saasbiz.client;

import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.Test;

import com.timevale.contractanalysis.facade.api.ao.QueryNextFormDataIdRequest;
import com.timevale.contractanalysis.facade.api.bo.QueryNextFormDataIdResult;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecCloseTrialDoneConfirmInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecDetailOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecDetailQueryInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecLastTrialDoneInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecLastTrialDoneOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExistsInputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExistsOutputDTO;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecRunModelEnum;
import com.timevale.contractanalysis.facade.api.form.FormDataReadFacadeService;
import com.timevale.contractanalysis.facade.api.ledger.LedgerInnerFacadeService;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.ledger.impl.RpcLedgerInnerClientImpl;

@JsonDataIgnore
public class RpcLedgerInnerClientMockTest extends BaseMockTest {

    @InjectMocks
    private RpcLedgerInnerClientImpl rpcLedgerInnerClient;

    @Mock
    private LedgerInnerFacadeService ledgerInnerFacadeService;

    @Mock
    private FormDataReadFacadeService formDataReadFacadeService;
    
    @Test
    public void testQueryFormExists() {
        String tenantGid = "tenantGId";
        String formId = "formId";
        boolean exists = true;

        LedgerExistsOutputDTO outputDTO = new LedgerExistsOutputDTO();
        outputDTO.setExists(exists);
        LedgerExistsInputDTO inputDTO = new LedgerExistsInputDTO();
        inputDTO.setFormId(formId);
        inputDTO.setTenantGid(tenantGid);

        when(ledgerInnerFacadeService.queryFormExists(inputDTO)).thenReturn(outputDTO);

        boolean result = rpcLedgerInnerClient.queryFormExists(formId, tenantGid);
        verify(ledgerInnerFacadeService, atLeast(1)).queryFormExists(inputDTO);
        Assert.assertEquals(result, exists);
    }

    @Test
    public void testQueryExecDetail() {
        String formId = "formId";
        String runModel = LedgerExecRunModelEnum.ALL.getCode();

        LedgerExecDetailQueryInputDTO infoDTO = new LedgerExecDetailQueryInputDTO();
        infoDTO.setFormId(formId);
        LedgerExecDetailOutputDTO outputDTO = new LedgerExecDetailOutputDTO();
        outputDTO.setFormId(formId);
        outputDTO.setRunModel(runModel);

        when(ledgerInnerFacadeService.queryLedgerHistoryExecDetail(infoDTO)).thenReturn(outputDTO);
        
        LedgerExecDetailOutputDTO aResult = rpcLedgerInnerClient.queryExecDetail(formId);
        verify(ledgerInnerFacadeService, atLeast(1)).queryLedgerHistoryExecDetail(infoDTO);
        Assert.assertEquals(aResult.getRunModel(), runModel);
        Assert.assertEquals(aResult.getFormId(), formId);
    }

    @Test
    public void testQueryExecLastTrialDone() {
        String tenantGid = "tenantGId";
        String formId = "formId";
        String formName = "formName";

        LedgerExecLastTrialDoneInputDTO inputDTO = new LedgerExecLastTrialDoneInputDTO();
        inputDTO.setTenantGid(tenantGid);
        LedgerExecLastTrialDoneOutputDTO outputDTO = new LedgerExecLastTrialDoneOutputDTO();
        outputDTO.setFormId(formId);
        outputDTO.setFormName(formName);

        when(ledgerInnerFacadeService.queryLastTrialDone(inputDTO)).thenReturn(outputDTO);

        LedgerExecLastTrialDoneOutputDTO aResult = rpcLedgerInnerClient.queryExecLastTrialDone(tenantGid);
        verify(ledgerInnerFacadeService, atLeast(1)).queryLastTrialDone(inputDTO);
        Assert.assertEquals(aResult.getFormId(), formId);
        Assert.assertEquals(aResult.getFormName(), formName);
    }

    @Test
    public void testCloseLedgerTrialDoneConfirm() {
        String formId = "formId";

        LedgerExecCloseTrialDoneConfirmInputDTO inputDTO = new LedgerExecCloseTrialDoneConfirmInputDTO();
        inputDTO.setFormId(formId);

        rpcLedgerInnerClient.closeLedgerTrialDoneConfirm(formId);
        verify(ledgerInnerFacadeService, atLeast(1)).closeFormTrialDoneConfirm(inputDTO);
    }

    @Test
    public void testQueryNextFormProcessId() {
        String formId = "formId";
        String processId = "processId";
        String nextProcessId = "nextProcessId";

        QueryNextFormDataIdRequest request = new QueryNextFormDataIdRequest();
        request.setFormId(formId);
        request.setProcessId(processId);
        QueryNextFormDataIdResult response = new QueryNextFormDataIdResult();
        response.setRowDataId(nextProcessId);

        when(formDataReadFacadeService.getNextFormDataId(request)).thenReturn(response);

        String result = rpcLedgerInnerClient.queryNextFormProcessId(formId, processId);
        verify(formDataReadFacadeService, atLeast(1)).getNextFormDataId(request);
        Assert.assertNotNull(result);
        Assert.assertEquals(result, nextProcessId);
    }
}
