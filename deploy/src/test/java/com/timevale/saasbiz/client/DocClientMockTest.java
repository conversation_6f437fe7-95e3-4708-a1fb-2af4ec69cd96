package com.timevale.saasbiz.client;

import com.timevale.docmanager.service.api.DocPlusService;
import com.timevale.docmanager.service.exception.DocManagerException;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.doc.impl.DocClientImpl;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.Test;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: docclient测试类
 * @date Date : 2024年07月09日 10:25
 */
@JsonDataIgnore
public class DocClientMockTest extends BaseMockTest {

    @InjectMocks
    DocClientImpl docClient;

    @Mock
    DocPlusService docPlusService;


    @Test
    public void testGetFileInfoFromDocOrTemplate() {

        when(docPlusService.getFileInfoFromDocOrTemplate(any())).thenThrow(new DocManagerException("文件不存在"));

        SaasBizException saasBizException = Assert.expectThrows(SaasBizException.class, () -> docClient.getFileInfoFromDocOrTemplate("docId"));
        Assert.assertNotNull(saasBizException);
        Assert.assertEquals(saasBizException.getErrCode(), SaasBizResultCode.FIND_COMPARE_FILE_FAIL.getCode());
    }

}
