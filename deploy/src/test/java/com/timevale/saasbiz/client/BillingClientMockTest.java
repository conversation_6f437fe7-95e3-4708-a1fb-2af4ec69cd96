package com.timevale.saasbiz.client;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.Map;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import com.google.common.collect.Maps;
import com.timevale.billing.manager.sdk.api.OrderSearchAPI;
import com.timevale.billing.manager.sdk.model.AggreEffectiveOrderModel;
import com.timevale.mandarin.common.result.QueryResult;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.bill.impl.BillClientImpl;

@JsonDataIgnore
public class BillingClientMockTest extends BaseMockTest{

    @InjectMocks
    private BillClientImpl billClient;

    @Mock
    private OrderSearchAPI orderSearchApi;
    
    @Test
    public void testBatchSearchBalance() {
        String gid = "gid";
        Long sellProductNo = 123L;
        BigDecimal margin = new BigDecimal(100);

        QueryResult<Map<Long, AggreEffectiveOrderModel>> result = new QueryResult<Map<Long, AggreEffectiveOrderModel>>();
        Map<Long, AggreEffectiveOrderModel> resMap = Maps.newHashMap();
        AggreEffectiveOrderModel aggreEffectiveOrderModel = new AggreEffectiveOrderModel();
        aggreEffectiveOrderModel.setMargin(margin);
        resMap.put(sellProductNo, aggreEffectiveOrderModel);
        result.setResultObject(resMap);

        when(orderSearchApi.getAggregateEffectiveMargin(any())).thenReturn(result);
        
        Map<Long, Long> aResult = billClient.batchSearchBalance(gid, Lists.newArrayList(sellProductNo));
        Assert.assertNotNull(aResult);
        Assert.assertEquals(aResult.get(sellProductNo).longValue(), margin.longValue());
    }
}
