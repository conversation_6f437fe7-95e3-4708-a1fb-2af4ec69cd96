package com.timevale.saasbiz.integration.bill;

import com.google.common.collect.Lists;
import com.timevale.billing.manager.facade.model.order.OrderModel;
import com.timevale.billing.promotion.common.service.model.ActiveInfoModel;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.constants.BizConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;

import static com.timevale.saasbiz.constant.TestAccountConstants.PROFESSIONAL_SUBJECT_1;

/**
 * <AUTHOR>
 * @since 2023/9/7
 */
@Slf4j
@JsonDataIgnore
public class BillClientTest extends BaseServiceTest {
    @Autowired private BillClient billClient;


    @Test
    public void testQueryActiveInfos() {
        List<ActiveInfoModel> activeInfoModels =
                billClient.queryActiveInfos(Lists.newArrayList("company_realname"));
        Assert.assertNotNull(activeInfoModels);
        Assert.assertEquals(activeInfoModels.size(), 1);
    }

    @Test
    public void testQueryLatestOrder() {
        OrderModel orderModel = billClient.queryLatestOrder("e0b9cf6f7b404a03885b9d30d002b34e", 1, "DING_TALK");
        Assert.assertNotNull(orderModel);
        Assert.assertNotNull(orderModel.getId());
    }

    @Test
    public void testCheckAccountBill(){
      boolean result=  billClient.checkAccountBill("DING_TALK","e0b9cf6f7b404a03885b9d30d002b34e",BizConstants.SAAS_PRODUCT_NO,1);
      Assert.assertTrue(result);
    }


}
