package com.timevale.saasbiz.rest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.account.response.QueryOrgTransferConfigsResponse;
import com.timevale.saasbiz.rest.bean.bill.request.QueryAccountUnifiedAssetRequest;
import com.timevale.saasbiz.rest.bean.bill.request.QueryCommodityRequest;
import com.timevale.saasbiz.rest.bean.bill.response.QueryAccountUnifiedAssetResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR>
 * @since 2024-09-02
 */
@Slf4j
@JsonDataIgnore
public class BillRestTest extends BaseServiceTest {

    @Test
    public void testGetSaasCommodities() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/saas-common/bills/saas-commodities")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test
    public void testGetSaasCommoditiesWithShowCaseNo() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/saas-common/bills/saas-commodities")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId).param("showcaseNo","pd001"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test
    public void testGetSaasCommoditiesWithTrialOrg() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/saas-common/bills/saas-commodities")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test
    public void testQueryCommodities() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        QueryCommodityRequest request = new QueryCommodityRequest();
        request.setProductCode("ELECTRONIC_SIGNATURE_TRAFFIC");

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/saas-common/bills/commodity/packages")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test
    public void testQueryPersonCommodities() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.ACCOUNT_ID_1;
        QueryCommodityRequest request = new QueryCommodityRequest();
        request.setProductCode("ELECTRONIC_SIGNATURE_TRAFFIC");

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/saas-common/bills/commodity/packages")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test
    public void testQueryAccountUnifiedAsset() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        QueryAccountUnifiedAssetRequest request = new QueryAccountUnifiedAssetRequest();

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/saas-common/bills/assets/unified-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test
    public void testQueryAccountUnifiedThirdAsset() throws Exception {
        String operatorId = "2b06ac92aeb243458267b773ae406187";
        String tenantId = "d9ec3169a6e04d148e5a8cc08ab3c13d";
        QueryAccountUnifiedAssetRequest request = new QueryAccountUnifiedAssetRequest();
        request.setPrimaryOid("d9ec3169a6e04d148e5a8cc08ab3c13d");

        MvcResult mvcResult=  mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/saas-common/bills/assets/unified-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId).header(HEADER_CLIENT_ID, "DING_TALK"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        RestResult<QueryAccountUnifiedAssetResponse>  response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<QueryAccountUnifiedAssetResponse>>() {});
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData().getAssets()));
        Assert.assertTrue(StringUtils.isNoneBlank(response.getData().getThirdAssert().getOrderUrl()));

    }

}
