package com.timevale.saasbiz.rest;

import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.rest.bean.usercenter.request.GetAppInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.constant.TestAccountConstants.NO_BILL_SUBJECT_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.PROFESSIONAL_SUBJECT_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.PROFESSIONAL_SUBJECT_MEMBER_1;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * 开放平台信息接口单测
 *
 * <AUTHOR>
 * @since 2024-04-02
 */
@Slf4j
@JsonDataIgnore
public class OpenPlatformRestTest extends BaseServiceTest {

    @Test(description = "查询企业下的开放APP列表")
    public void subjectOpenApp() throws Exception {
        // 查询当前企业
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v2/open-platform/org-open-app?subjectId=")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_OPERATOR_ID, PROFESSIONAL_SUBJECT_MEMBER_1)
                        .header(HEADER_TENANT_ID, PROFESSIONAL_SUBJECT_1))
        .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
        .andExpect(MockMvcResultMatchers.jsonPath("$.data.list").isNotEmpty());
        // 查询子企业
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v2/open-platform/org-open-app?subjectId=" + NO_BILL_SUBJECT_1)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_OPERATOR_ID, PROFESSIONAL_SUBJECT_MEMBER_1)
                        .header(HEADER_TENANT_ID, PROFESSIONAL_SUBJECT_1))
        .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "获取app信息")
    public void testGetAppInfo() throws Exception {
        GetAppInfoRequest request = new GetAppInfoRequest();
        request.setAppId("7876705741");
        request.setSubjectOid(PROFESSIONAL_SUBJECT_1);

        // 查询当前企业
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/open-platform/get-app-info")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, PROFESSIONAL_SUBJECT_MEMBER_1)
                                .header(HEADER_TENANT_ID, PROFESSIONAL_SUBJECT_1)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

}
