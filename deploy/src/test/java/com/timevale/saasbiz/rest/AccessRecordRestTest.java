package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.rest.bean.common.request.SaveAccessRecordRequest;
import lombok.extern.slf4j.Slf4j;
import org.hamcrest.Matchers;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;

/**
 * 资源访问历史测试类
 *
 * @author: qianyi
 * @since: 2023-03-13
 */
@Slf4j
@JsonDataIgnore
public class AccessRecordRestTest extends BaseServiceTest {

    private static final String accountId = "*********";

    @Test(description = "获取返回地址")
    public void testSaveAndGetBeforeUrlRest() throws Exception {
        // 正常保存
        SaveAccessRecordRequest request = new SaveAccessRecordRequest();
        String url = "http://localhost:8088/contract-manager/doc.html#";
        request.setUrl(url);
        mockMvc.perform(
                MockMvcRequestBuilders.post(
                        "/v2/access/record/save")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(request))
                        .header(HEADER_OPERATOR_ID, accountId))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Boolean.TRUE));

        // 正常获取
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/access/record/before")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, accountId))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.redirectUrl").value(url));

        // 异常获取，账号不存在的
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v2/access/record/before")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_OPERATOR_ID, "xxxxxx"))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.redirectUrl").isEmpty());

        // 异常获取，账号为空
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/access/record/before")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, ""))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(Matchers.not(0)));
    }
}
