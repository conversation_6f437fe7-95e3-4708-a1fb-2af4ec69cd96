package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.TestJsonObj;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.model.bean.watermark.dto.input.WatermarkTemplatePageInputDTO;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.watermark.request.GenerateWatermarkSnapshotRequest;
import com.timevale.saasbiz.rest.bean.watermark.request.SaveWatermarkTemplateRequest;
import com.timevale.saasbiz.rest.bean.watermark.request.WatermarkTemplatePageRequest;
import com.timevale.saasbiz.rest.bean.watermark.response.*;
import com.timevale.saasbiz.rest.converter.WatermarkRestInOutConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description: 水印管理单测
 * @date Date : 2023年05月17日 14:42
 */
@Slf4j
public class WatermarkTemplateRestTest extends BaseServiceTest {

    static final String TENANT_ID = TestAccountConstants.SENIOR_SUBJECT_1;
    static final String OPERATOR_ID = TestAccountConstants.ACCOUNT_ID_1;
    static final String CLIENT_WEB = "WEB";
    static final String CLIENT_APP_IOS = "APP_IOS";

    @Test(description = "测试分页查询当前租户下的水印模板列表")
    public void listWatermarkTemplatesTest() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("listWatermarkTemplatesTest");
        WatermarkTemplatePageRequest reqData =
                (WatermarkTemplatePageRequest) testJsonObj.getReqData();

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.post("/v2/watermark-template/page-list")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(reqData))
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("listWatermarkTemplatesTest result is :{}", response);
        RestResult<WatermarkTemplatePageResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<WatermarkTemplatePageResponse>>() {});
        Assert.assertTrue(null != restResult && null != restResult.getData());
        Assert.assertTrue(restResult.getData().getTotal() > 0, "用户参与的天印流程列表不应为空");
    }


    @Test(description = "测试保存水印模板")
    public void saveWatermarkTemplateTest() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("saveWatermarkTemplateTest");
        SaveWatermarkTemplateRequest reqData =
                (SaveWatermarkTemplateRequest) testJsonObj.getReqData();

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.post("/v2/watermark-template/save")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(reqData))
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("saveWatermarkTemplateTest result is :{}", response);
        RestResult<SaveWatermarkTemplateResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<SaveWatermarkTemplateResponse>>() {});
        Assert.assertTrue(null != restResult && null != restResult.getData());
        Assert.assertNotNull(restResult.getData().getWatermarkId(), "水印ID没有返回");
    }


    @Test(description = "测试修改水印模板状态 (启/停)")
    public void changeWatermarkTemplateStatusTest() throws Exception {

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.post("/v2/watermark-template/8fc1e6dd09f249f2aa2299d1088c93ca/status-change")
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("changeWatermarkTemplateStatusTest result is :{}", response);
        RestResult restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult>() {});
        Assert.assertNotNull(restResult);
        Assert.assertEquals(restResult.getCode(), 0);
    }

    @Test(description = "测试删除水印模板")
    public void deleteWatermarkTemplateTest() throws Exception {

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.delete("/v2/watermark-template/974b5af056bf4784aa9ddb495219a44e/delete")
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("deleteWatermarkTemplateTest result is :{}", response);
        RestResult restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult>() {});
        Assert.assertNotNull(restResult);
        Assert.assertEquals(restResult.getCode(), 70001400);
    }

    @Test(description = "测试查看水印中的图片")
    public void viewWatermarkImageTest() throws Exception {

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/watermark-template/ddd4e892039a460d92f390e7a1b9378b/view-image")
                        .param("filekey", "$7005ce6e-c77a-4bb4-85db-59ded35224e0$152569301")
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("viewWatermarkImageTest result is :{}", response);
        RestResult<ViewWatermarkImageResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<ViewWatermarkImageResponse>>() {});
        Assert.assertNotNull(restResult);
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertNotNull(restResult.getData());
        Assert.assertNotNull(restResult.getData().getImageUrl());
    }

    @Test(description = "测试预览水印-web")
    public void previewWatermarkTest_WEB() throws Exception {

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/watermark-template/ddd4e892039a460d92f390e7a1b9378b/preview")
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_CLIENT_ID, CLIENT_WEB)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("previewWatermarkTest result is :{}", response);
        RestResult<PreviewWatermarkResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<PreviewWatermarkResponse>>() {});
        Assert.assertNotNull(restResult);
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertNotNull(restResult.getData());
        Assert.assertNotNull(restResult.getData().getPreviewImageUrl());
    }

    @Test(description = "测试预览水印-ios")
    public void previewWatermarkTest_IOS() throws Exception {

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/watermark-template/8fc1e6dd09f249f2aa2299d1088c93ca/preview")
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_CLIENT_ID, CLIENT_APP_IOS)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("previewWatermarkTest result is :{}", response);
        RestResult<PreviewWatermarkResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<PreviewWatermarkResponse>>() {});
        Assert.assertNotNull(restResult);
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertNotNull(restResult.getData());
        Assert.assertNotNull(restResult.getData().getPreviewImageUrl());
    }



    @Test(description = "测试查询水印模板详情")
    public void getWatermarkTemplateDetailTest() throws Exception {
        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/watermark-template/ddd4e892039a460d92f390e7a1b9378b/detail")
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_CLIENT_ID, CLIENT_WEB)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("getWatermarkTemplateDetailTest result is :{}", response);
        RestResult<WatermarkTemplateDetailResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<WatermarkTemplateDetailResponse>>() {});
        Assert.assertNotNull(restResult);
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertNotNull(restResult.getData());
        Assert.assertEquals("ddd4e892039a460d92f390e7a1b9378b", restResult.getData().getWatermarkId());
    }

    @Test(description = "测试生成默认系统水印模板")
    public void sysgenTest() throws Exception {
        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.post("/v2/watermark-template/sysgen")
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_CLIENT_ID, CLIENT_WEB)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("sysgenTest result is :{}", response);
        RestResult restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult>() {});
        Assert.assertNotNull(restResult);
        Assert.assertEquals(restResult.getCode(), 0);
    }

    @Test(description = "测试生成临时水印快照")
    public void snapshotTest() throws Exception {
        GenerateWatermarkSnapshotRequest request = new GenerateWatermarkSnapshotRequest();
        request.setWatermarkId("SS_8d0cd4c82f144ad2a8b8641f9cd32dab");
        request.setContent("单测水印");
        mockMvc.perform(MockMvcRequestBuilders.post("/v2/watermark-template/snapshot")
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .content(JSONObject.toJSONString(request))
                .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                .header(HEADER_CLIENT_ID, CLIENT_WEB)
                .header(HEADER_TENANT_ID, TENANT_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.watermarkSnapshotId").value(request.getWatermarkId()));
    }

    @Test(description = "测试转换类")
    public void watermarkConvertTest() throws Exception {
        WatermarkRestInOutConverter watermarkRestInOutConverter = new WatermarkRestInOutConverter();
        WatermarkTemplatePageRequest request = new WatermarkTemplatePageRequest();
        request.setName("测试水印转换");
        WatermarkTemplatePageInputDTO templatePageInputDTO = WatermarkRestInOutConverter.pageRequest2pageInput(request, TENANT_ID, OPERATOR_ID);
        Assert.assertEquals(TENANT_ID, templatePageInputDTO.getTenantId());
        Assert.assertEquals(OPERATOR_ID, templatePageInputDTO.getOperatorOid());



    }

    @Test(description = "查询水印是否被配置")
    public void watermarkTemplateConfiguredTest() throws Exception {
        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/watermark-template/7fe900de9ada4fac9dd90eaa052cdada/configured")
                        .header(HEADER_OPERATOR_ID, OPERATOR_ID)
                        .header(HEADER_TENANT_ID, TENANT_ID);

        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("watermarkTemplateConfiguredTest result is :{}", response);
        RestResult<ConfiguredWatermarkResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<ConfiguredWatermarkResponse>>() {});
        Assert.assertNotNull(restResult.getData());
        Assert.assertTrue(restResult.getData().getConfigured());
    }

}
