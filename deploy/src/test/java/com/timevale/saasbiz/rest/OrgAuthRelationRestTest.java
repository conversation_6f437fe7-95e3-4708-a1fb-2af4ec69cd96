package com.timevale.saasbiz.rest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.authrelation.request.AuthRelationDoAuthInfoRequest;
import com.timevale.saasbiz.rest.bean.authrelation.response.AuthRelationCanAuthInfoResponse;
import com.timevale.saasbiz.rest.bean.authrelation.response.AuthRelationConfigResponse;
import com.timevale.saasbiz.rest.bean.authrelation.response.AuthRelationDoAuthInfoResponse;
import com.timevale.saasbiz.rest.bean.authrelation.response.AuthRelationGetAuthInfoResponse;
import com.timevale.saasbiz.rest.bean.seal.request.PageQuerySealsRequest;
import com.timevale.saasbiz.rest.bean.seal.response.PageQuerySealsResponse;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

@JsonDataIgnore
public class OrgAuthRelationRestTest extends BaseServiceTest {
    @Test(description = "添加前获取添加条件冒烟测试")
    public void getConfig() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(
                                                "/v2/org-auth-relation/config")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<AuthRelationConfigResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<AuthRelationConfigResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getData().getBatchAddExcelUrl());
    }

    @Test(description = "获取企业可授权信息冒烟测试")
    public void testCanAuthInfos() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(
                                                "/v2/org-auth-relation/can-auth-infos")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<AuthRelationCanAuthInfoResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<AuthRelationCanAuthInfoResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData().getAuthInfoOrder()));
    }


    @Test(description = "获取授权信息冒烟测试")
    public void testGetAuthInfo() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(
                                                "/v2/org-auth-relation/get-auth-info").param("authGid","bef96c98498e4939aaf34db95049c6ce").param("authType","shareAppCount")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<AuthRelationGetAuthInfoResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<AuthRelationGetAuthInfoResponse>>() {});
        Assert.assertNotNull(response);
    }

    @Test(description = "给授权企业分配授权资源冒烟测试")
    public void testDoAuthInfo() throws Exception {
        String requestJson="{\"authGid\":\"bef96c98498e4939aaf34db95049c6ce\",\"orderId\":\"671185a9-b8ad-4fe4-9e4f-bd1029441e2f\",\"shareConfigInfo\":{\"configKey\":\"shareAppCount\",\"configValue\":\"1\"}}";
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v2/org-auth-relation/do-auth-info")
                                        .content(requestJson)
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<AuthRelationDoAuthInfoResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<AuthRelationDoAuthInfoResponse>>() {});
        Assert.assertNotNull(response);
    }
}
