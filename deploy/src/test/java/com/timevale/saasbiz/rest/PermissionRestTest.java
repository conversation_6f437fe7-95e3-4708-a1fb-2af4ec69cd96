package com.timevale.saasbiz.rest;

import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.constant.TestAccountConstants.BASE_SUBJECT_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.BASE_SUBJECT_MEMBER_1;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.exception.SaasBizResultCode.PERMISSION_CODE_MISSING_APPLY_MSG;

/**
 * 权限相关接口单测
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Slf4j
@JsonDataIgnore
public class PermissionRestTest extends BaseServiceTest {

    @Test(description = "获取权限申请文案")
    public void queryPermissionApplyMsg() throws Exception {
        // 权限未配置申请文案
        String permissionCode = "123";
        mockMvc.perform(
                        MockMvcRequestBuilders.get(
                                        "/v2/saas-common/permission/apply/msg/" + permissionCode)
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, BASE_SUBJECT_MEMBER_1)
                                .header(HEADER_TENANT_ID, BASE_SUBJECT_1))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(PERMISSION_CODE_MISSING_APPLY_MSG.getCode()));
        // 权限已配置申请文案
        permissionCode = "CONTRACT-SET_CONTRACT_CONFIDENTIAL";
        mockMvc.perform(
                        MockMvcRequestBuilders.get(
                                        "/v2/saas-common/permission/apply/msg/" + permissionCode)
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, BASE_SUBJECT_MEMBER_1)
                                .header(HEADER_TENANT_ID, BASE_SUBJECT_1))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.copyText").isNotEmpty());
    }
}
