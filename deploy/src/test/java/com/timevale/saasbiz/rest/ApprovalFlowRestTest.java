package com.timevale.saasbiz.rest;

import static com.timevale.saasbiz.constant.TestAccountConstants.*;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_CLIENT_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.timevale.contractapproval.facade.enums.ApprovalQueryTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.enums.approval.ApprovalOperateTypeEnum;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.approval.request.*;
import com.timevale.saasbiz.rest.bean.approval.response.ListApprovalFlowResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ListApprovalGroupResponse;

import com.timevale.saasbiz.rest.bean.approval.vo.ApprovalTableHeadVo;
import lombok.extern.slf4j.Slf4j;

import org.hamcrest.Matchers;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023-04-18 13:56
 */
@Slf4j
@JsonDataIgnore
public class ApprovalFlowRestTest extends BaseServiceTest {

    @Test(description = "保存个人表头配置")
    private void saveHeadConfig() throws Exception {
        String tenantId = SENIOR_SUBJECT_1;
        ApprovalTableHeadConfigRequest request = new ApprovalTableHeadConfigRequest();
        ApprovalTableHeadVo approvalTableHeadVo = new ApprovalTableHeadVo();
        approvalTableHeadVo.setCode("approvalName");
        approvalTableHeadVo.setHasFreeze(false);
        request.setTableHeadConfigList(Arrays.asList(approvalTableHeadVo));

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/approval/flow/table-head")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test(description = "我收到的")
    private void testIReceive() throws Exception {
        String tenantId = SENIOR_SUBJECT_1;

        ListApprovalFlowRequest request = new ListApprovalFlowRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setQueryType(ApprovalQueryTypeEnum.I_RECEIVE.getType());
        request.setSubjectOid(tenantId);

        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/approval/flow/list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, tenantId)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result is null");

        RestResult<ListApprovalFlowResponse> restResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ListApprovalFlowResponse>>() {});
        Assert.assertNotNull(restResult, "restResult isNull");
        Assert.assertNotNull(restResult.getData(), "restResult data isNull");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(restResult.getData().getList()), "I receive isEmpty");
    }

    @Test(description = "我发起的")
    private void testIInitiated() throws Exception {
        String tenantId = SENIOR_SUBJECT_1;

        ListApprovalFlowRequest request = new ListApprovalFlowRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setQueryType(ApprovalQueryTypeEnum.I_INITIATED.getType());
        request.setSubjectOid(tenantId);

        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/approval/flow/list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, tenantId)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_2)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result is null");

        RestResult<ListApprovalFlowResponse> restResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ListApprovalFlowResponse>>() {});
        Assert.assertNotNull(restResult, "restResult isNull");
        Assert.assertNotNull(restResult.getData(), "restResult data isNull");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(restResult.getData().getList()), "I initiated isEmpty");
    }

    @Test(description = "待我审批")
    private void testIPending() throws Exception {
        String tenantId = SENIOR_SUBJECT_1;

        ListApprovalFlowRequest request = new ListApprovalFlowRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setQueryType(ApprovalQueryTypeEnum.I_PENDING.getType());
        request.setSubjectOid(tenantId);

        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/approval/flow/list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, tenantId)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_2)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result isNull");

        RestResult<ListApprovalFlowResponse> restResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ListApprovalFlowResponse>>() {});
        Assert.assertNotNull(restResult, "restResult isNull");
        Assert.assertNotNull(restResult.getData(), "restResult data isNull");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(restResult.getData().getList()), "I pending isEmpty");
    }

    @Test(description = "我已审批")
    private void testIApproved() throws Exception {
        String tenantId = SENIOR_SUBJECT_1;

        ListApprovalFlowRequest request = new ListApprovalFlowRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setQueryType(ApprovalQueryTypeEnum.I_APPROVED.getType());
        request.setSubjectOid(tenantId);

        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/approval/flow/list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, tenantId)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_2)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result isNull");

        RestResult<ListApprovalFlowResponse> restResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ListApprovalFlowResponse>>() {});
        Assert.assertNotNull(restResult, "restResult isNull");
        Assert.assertNotNull(restResult.getData(), "restResult data isNull");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(restResult.getData().getList()), "I approved isEmpty");
    }

    @Test(description = "我发起的-审批组")
    private void testIInitiatedGroup() throws Exception {
        String tenantId = SENIOR_SUBJECT_1;

        ListApprovalFlowRequest request = new ListApprovalFlowRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setQueryType(ApprovalQueryTypeEnum.I_INITIATED.getType());
        request.setSubjectOid(tenantId);

        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/approval/flow/group-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, tenantId)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_3)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result isNull");

        RestResult<ListApprovalGroupResponse> restResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ListApprovalGroupResponse>>() {});
        Assert.assertNotNull(restResult, "restResult isNull");
        Assert.assertNotNull(restResult.getData(), "restResult data isNull");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(restResult.getData().getList()),
                "I initiated group isEmpty");
    }

    @Test(description = "待我审批的-审批组")
    private void testIPendingGroup() throws Exception {
        String tenantId = SENIOR_SUBJECT_1;

        ListApprovalFlowRequest request = new ListApprovalFlowRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setQueryType(ApprovalQueryTypeEnum.I_PENDING.getType());
        request.setSubjectOid(tenantId);

        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/approval/flow/group-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, tenantId)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_3)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result isNull");

        RestResult<ListApprovalGroupResponse> restResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ListApprovalGroupResponse>>() {});
        Assert.assertNotNull(restResult, "restResult isNull");
        Assert.assertNotNull(restResult.getData(), "restResult data isNull");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(restResult.getData().getList()),
                "I pending group isEmpty");
    }

    @Test(description = "我已审批的-审批组")
    private void testIApprovedGroup() throws Exception {
        String tenantId = SENIOR_SUBJECT_1;

        ListApprovalFlowRequest request = new ListApprovalFlowRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setQueryType(ApprovalQueryTypeEnum.I_APPROVED.getType());
        request.setSubjectOid(tenantId);

        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/approval/flow/group-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, tenantId)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_3)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result isNull");

        RestResult<ListApprovalGroupResponse> restResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ListApprovalGroupResponse>>() {});
        Assert.assertNotNull(restResult, "restResult isNull");
        Assert.assertNotNull(restResult.getData(), "restResult data isNull");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(restResult.getData().getList()),
                "I approved group isEmpty");
    }

    @Test(description = "查看参与人列表")
    private void testListParticipant() throws Exception {
        String tenantId = "190d2eef9a9c45f09f4c4d4c2b74937d";
        String accountId = "50ccb183b5df4ec0891006830bf18d32";

        // 设置审批id, 优先基于审批流程鉴权
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/approval/flow/participant")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, accountId)
                                .param("approvalId", "AF-289af9bb5d080d4c")
                                .param("approvalType", "1")
                                .param("processId", "9e7fc565348e45ddbcef69756a7a5227"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.length()").value(Matchers.greaterThan(0)));
    }

    @Test(description = "查看审批列表地址")
    private void testListUrl() throws Exception {
        ListApprovalFlowUrlRequest request = new ListApprovalFlowUrlRequest();
        request.setQueryType(ApprovalQueryTypeEnum.I_APPROVED.getType());
        request.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
        request.setToken("111");
        // 设置审批id, 优先基于审批流程鉴权
        mockMvc.perform(
                MockMvcRequestBuilders.post("/v2/approval/flow/list-url")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_2)
                        .content(JSONObject.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.listUrl").isNotEmpty());
    }

    @Test(description = "审批操作预校验")
    private void testOperatePreCheckByProcess() throws Exception {
        ApprovalOperatePreCheckByProcessRequest request = new ApprovalOperatePreCheckByProcessRequest();
        request.setApprovalCode("AF-600d29df0e004d6ab71ca8bb2e2cb536");
        request.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
        request.setOperateType(ApprovalOperateTypeEnum.AGREE.getType());
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/approval/flow/operate-pre-check-by-process")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, "ae953c4d15514c65b788ae0895b5de34")
                                .header(HEADER_OPERATOR_ID, "551030ad5917474da868ef3aa25a1c3f")
                                .content(JSONObject.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.pass").value(true));
    }

    @Test(description = "审批操作预校验-批量")
    private void testOperatePreCheck() throws Exception {
        ApprovalOperateDataRequest.Data data = new ApprovalOperateDataRequest.Data();
        data.setApprovalId("AF-600d29df0e004d6ab71ca8bb2e2cb536");
        ApprovalOperateDataRequest dataRequest = new ApprovalOperateDataRequest();
        dataRequest.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
        dataRequest.setDatas(Lists.newArrayList(data));
        ApprovalOperatePreCheckRequest request = new ApprovalOperatePreCheckRequest();
        request.setOperateDatas(Lists.newArrayList(dataRequest));
        request.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
        request.setOperateType(ApprovalOperateTypeEnum.AGREE.getType());
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/approval/flow/operate-pre-check")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, "ae953c4d15514c65b788ae0895b5de34")
                                .header(HEADER_OPERATOR_ID, "551030ad5917474da868ef3aa25a1c3f")
                                .content(JSONObject.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.pass").value(true));
    }

    @Test(description = "获取审批意愿认证地址")
    private void testApprovalWillAuthUrl() throws Exception {
        ApprovalAuthUrlByProcessRequest request = new ApprovalAuthUrlByProcessRequest();
        request.setApprovalCode("AF-600d29df0e004d6ab71ca8bb2e2cb536");
        request.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
        request.setRedirectUrl("http://www.baidu.com");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/approval/flow/will-auth-url-by-process")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, "ae953c4d15514c65b788ae0895b5de34")
                                .header(HEADER_OPERATOR_ID, "551030ad5917474da868ef3aa25a1c3f")
                                .content(JSONObject.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.needAuth").value(true));
    }

    @Test(description = "删除审批流")
    private void testApprovalDelete() throws Exception {
        ApprovalOperateDataRequest dataRequest = new ApprovalOperateDataRequest();
        dataRequest.setBizGroupId("a55101b0-bbfb-40e7-a342-9a7e24bc87b9");
        dataRequest.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
        ApprovalOperateRequest request = new ApprovalOperateRequest();
        request.setOperateDatas(Lists.newArrayList(dataRequest));
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/approval/flow/delete")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_CLIENT_ID, "WEB")
                                .header(HEADER_TENANT_ID, "6581ecdcacd44778b2e5274f0ad7470c")
                                .header(HEADER_OPERATOR_ID, "1dd946773d6c414abe0ebcb1efe0dc18")
                                .content(JSONObject.toJSONString(request)))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "查询审批业务流程合同文件列表")
    private void testQueryBizContractFiles() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/approval/flow/biz-contract-files")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .param("approvalCode", "AF-600d29df0e004d6ab71ca8bb2e2cb536")
                                .header(HEADER_CLIENT_ID, "WEB")
                                .header(HEADER_TENANT_ID, "ae953c4d15514c65b788ae0895b5de34")
                                .header(HEADER_OPERATOR_ID, "551030ad5917474da868ef3aa25a1c3f"))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.contractFiles").isNotEmpty());
    }

    @Test(description = "查询审批模板初始化配置")
    private void testApprovalFlowTemplateInitConfig() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/approval/template/init-config")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_CLIENT_ID, "WEB")
                                .header(HEADER_TENANT_ID, "ae953c4d15514c65b788ae0895b5de34")
                                .header(HEADER_OPERATOR_ID, "551030ad5917474da868ef3aa25a1c3f"))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.maxFlowTemplateSize").value(50));
    }

    @Test(description = "审批导出及统计")
    private void testApprovalAsyncExportCount() throws Exception {
        ApprovalExportRequest request = new ApprovalExportRequest();
        request.setQueryType(ApprovalQueryTypeEnum.I_APPROVED.getType());
        request.setApprovalCodes(Lists.newArrayList("AF-5f404e08343344a3a3fac4f33b31c0f5", "73137182cc924ebc8a7da1735245ef13"));
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/approval/flow/async-export/number")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_CLIENT_ID, "WEB")
                                .header(HEADER_TENANT_ID, "fffcc67b09384192af4e06a3b2704a06")
                                .header(HEADER_OPERATOR_ID, "4501308d254a434b90fd9d96f63dc161"))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.totalSize").value(2));

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/approval/flow/async-export")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_CLIENT_ID, "WEB")
                                .header(HEADER_TENANT_ID, "fffcc67b09384192af4e06a3b2704a06")
                                .header(HEADER_OPERATOR_ID, "4501308d254a434b90fd9d96f63dc161"))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "添加审批批注")
    private void testApprovalAddComment() throws Exception {
        ApprovalAddCommentRequest request = new ApprovalAddCommentRequest();
        request.setApprovalCode("20d8a917e2a24a99a96476e67c776d71");
        request.setCommentContent("添加批准单测");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/approval/flow/add-comment")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_CLIENT_ID, "WEB")
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

}
