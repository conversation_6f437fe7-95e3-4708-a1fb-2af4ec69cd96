package com.timevale.saasbiz.rest;

import com.timevale.contractmanager.common.service.bean.PreferenceModel;
import com.timevale.contractmanager.common.service.constant.PreferenceConstant;
import com.timevale.contractmanager.common.service.enums.ProcessPreferenceEnum;
import com.timevale.contractmanager.common.service.result.autoarchive.AutoArchiveRuleResult;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.integration.autoarchive.RpcAutoArchiveInnerServiceClient;
import com.timevale.saasbiz.integration.process.PreferenceClient;
import com.timevale.saasbiz.model.bean.autoarchive.dto.output.ProcessStatusOutputDTO;
import com.timevale.saasbiz.rest.bean.autoarchive.request.AutoArchiveRuleListRequest;
import com.timevale.saasbiz.rest.bean.autoarchive.request.AutoArchiveUpdateStatusRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Collections;
import java.util.List;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * AutoArchiveRestTest
 *
 * <AUTHOR>
 * @since 2023/9/12 4:57 下午
 */
@Slf4j
@JsonDataIgnore
public class AutoArchiveRestTest extends BaseServiceTest  {

    @Autowired
    private RpcAutoArchiveInnerServiceClient rpcAutoArchiveInnerServiceClient;

    @Autowired
    private PreferenceClient preferenceClient;

    @Test()
    public void test() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;

        AutoArchiveRuleListRequest request = new AutoArchiveRuleListRequest();
        request.setPageNum(1);
        request.setPageSize(10);

        MvcResult result =
                mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/auto-archive/rule-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId)
                                .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result is null");

    }

    @Test()
    public void testRuleCondition() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v2/auto-archive/system-rule-config")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .param("bizType", "1"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test()
    public void top() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v2/auto-archive/top-rule")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .param("ruleId", "1f4814f6e7b244c1aa30e0045e2ec0c3"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test()
    public void testSaveAndDelete() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;

        mockMvc.perform(
                MockMvcRequestBuilders.get("/v2/auto-archive/delete-rule")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .param("menuId", "48d9d447096a48eea2b64cd373b56e5a"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        String saveRequestString = "{\"bindingMenuId\":\"48d9d447096a48eea2b64cd373b56e5a\",\"status\":0,\"bindingFormId\":null,\"conditions\":[{\"fieldId\":\"24c65db690c84ed191c72b10b72f4113\",\"key\":\"*************-KNVPPI1V\",\"value\":[\"删除关联\"],\"startNum\":\"\",\"endNum\":\"\",\"childOperatorType\":2,\"matchType\":3,\"prevType\":\"string\",\"errorMessage\":\"\",\"operatorType\":1,\"childOperators\":[{\"operatorType\":2,\"conditionParams\":\"[\\\"删除关联\\\"]\"}],\"fieldType\":3}],\"unityForm\":0,\"bindMenuId\":\"48d9d447096a48eea2b64cd373b56e5a\"}";

        MvcResult mvcResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/auto-archive/save-rule")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId)
                                .content(saveRequestString))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(mvcResult, "result is null");

        AutoArchiveRuleResult result = rpcAutoArchiveInnerServiceClient.getRuleDetailByMenuId("48d9d447096a48eea2b64cd373b56e5a", tenantId);
        String ruleId = result.getUuid();

        MvcResult resultDetail =  mockMvc.perform(
                MockMvcRequestBuilders.get("/v2/auto-archive/get-rule-detail")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .param("ruleId", ruleId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
        Assert.assertNotNull(resultDetail, "result is null");

        mockMvc.perform(
                MockMvcRequestBuilders.get("/v2/auto-archive/get-rule-progress")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .param("ruleId", ruleId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        try {
            mockMvc.perform(
                    MockMvcRequestBuilders.get("/v2/auto-archive/stop-rule")
                            .contentType(MediaType.APPLICATION_JSON_UTF8)
                            .header(HEADER_TENANT_ID, tenantId)
                            .header(HEADER_OPERATOR_ID, operatorId)
                            .param("ruleId", ruleId))
                    .andReturn();
        } catch (Exception ignored) {
        }

        AutoArchiveUpdateStatusRequest updateStatusRequest = new AutoArchiveUpdateStatusRequest();
        updateStatusRequest.setRuleId(ruleId);
        updateStatusRequest.setStatus(1);

        mockMvc.perform(
                MockMvcRequestBuilders.post("/v2/auto-archive/update-rule-status")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .content(JsonUtils.obj2json(updateStatusRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

    }

    @Test()
    public void testExistMenuRuleBind() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/auto-archive/exist-menu-rule-bind")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId)
                                .param("menuId", "bacf32168201499dbce213420f1b469f"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test()
    public void getOptionalStatusList() throws Exception {
        String operatorId = TestAccountConstants.SENIOR_SUBJECT_ADMIN;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
        String tenantGId = TestAccountConstants.SENIOR_SUBJECT_1_GID;
        List<PreferenceModel> preferenceModels = preferenceClient.queryPreference(Collections.singletonList(tenantGId),
                Collections.singletonList(ProcessPreferenceEnum.PROCESS_ARCHIVE_RANGE.getKey()));
        PreferenceModel preferenceModel = preferenceModels.get(0);

        boolean all = StringUtils.equals(preferenceModel.getPreferences().get(0).getPreferenceValue(),
                PreferenceConstant.PROCESS_ARCHIVE_RANGE_ALL);

        if(!all) {
            mockMvc.perform(
                            MockMvcRequestBuilders.get("/v2/auto-archive/get-optional-status")
                                    .contentType(MediaType.APPLICATION_JSON_UTF8)
                                    .header(HEADER_TENANT_ID, tenantId)
                                    .header(HEADER_OPERATOR_ID, operatorId))
                    .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                    .andReturn();
        } else {
            mockMvc.perform(
                            MockMvcRequestBuilders.get("/v2/auto-archive/get-optional-status")
                                    .contentType(MediaType.APPLICATION_JSON_UTF8)
                                    .header(HEADER_TENANT_ID, tenantId)
                                    .header(HEADER_OPERATOR_ID, operatorId))
                    .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                    .andReturn();
        }


    }

}
