package com.timevale.saasbiz.rest;

import com.google.common.collect.Lists;
import com.timevale.clmc.facade.api.model.audit.output.RpcAuditTrialsOutput;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcContractAuditRecordPageQueryOutput;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateAuditResultOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcCreateContractAuditWebOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditListsOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditResultRuleOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.RpcQueryAuditRuleTreeOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.bean.RpcContractAuditRecordDTO;
import com.timevale.contractanalysis.facade.api.dto.contractaudit.bean.RpcContractAuditResultRuleDTO;
import com.timevale.docmanager.service.result.DocInfoResult;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.clmc.ClmcClient;
import com.timevale.saasbiz.integration.contractanalysis.ContractAuditClient;
import com.timevale.saasbiz.integration.doc.DocClient;
import com.timevale.saasbiz.integration.gray.GrayClient;
import com.timevale.saasbiz.integration.saascommon.SaasCommonClient;
import com.timevale.saasbiz.integration.shotlink.ShortLinkClient;
import com.timevale.saasbiz.model.bean.authrelation.bo.FileAuthCheckResultBO;
import com.timevale.saasbiz.model.bean.oauth.dto.output.QueryLatestUserAgreementOutputDTO;
import com.timevale.saasbiz.model.bean.pdftool.bo.SearchResultBO;
import com.timevale.saasbiz.model.bean.pdftool.dto.output.PdfTextSearchOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.model.enums.UserAgreementTypeEnum;
import com.timevale.saasbiz.model.enums.contractaudit.ContractAuditCheckSceneEnum;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.contractaudit.request.ContractAuditKeywordSearchRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.ContractAuditPageQueryRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.CreateAuditResultRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.CreateContractAuditWebRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.GetContractAuditResultRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.GetContractAuditRuleTreeRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.request.QueryAuditRecordExistRequest;
import com.timevale.saasbiz.rest.bean.contractaudit.response.ContractAuditCanCreateResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.ContractAuditKeywordSearchResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.ContractAuditPageQueryResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.ContractAuditTrialResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.CreateAuditResultResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.CreateContractAuditWebResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.GetContractAuditResultResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.GetContractAuditRuleTreeResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.GetWebUrlResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.QueryAuditRecordExistResponse;
import com.timevale.saasbiz.rest.bean.contractaudit.response.QueryRuleListResponse;
import com.timevale.saasbiz.service.contractaudit.ContractAuditService;
import com.timevale.saasbiz.service.contractaudit.impl.ContractAuditServiceImpl;
import com.timevale.saasbiz.service.oauth.UserAgreementService;
import com.timevale.saasbiz.service.pdftool.PdfToolService;
import com.timevale.saasbiz.service.process.ProcessFileAuthCheckService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.testng.Assert;
import org.testng.annotations.Test;

import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.verify;

/**
 *
 * <AUTHOR>
 * @since 2024-09-11
 */
@JsonDataIgnore
public class ContractAuditRestMockTest extends BaseMockTest {

    private ContractAuditRest contractAuditRest = new ContractAuditRest();

    private ContractAuditService contractAuditService = new ContractAuditServiceImpl();

    private GrayClient grayClient = Mockito.mock(GrayClient.class);

    private UserCenterService userCenterService = Mockito.mock(UserCenterService.class);

    private DocClient docClient = Mockito.mock(DocClient.class);

    private SaasCommonClient saasCommonClient = Mockito.mock(SaasCommonClient.class);

    private UserAgreementService userAgreementService = Mockito.mock(UserAgreementService.class);

    private ContractAuditClient contractAuditClient = Mockito.mock(ContractAuditClient.class);

    private ShortLinkClient shortLinkClient = Mockito.mock(ShortLinkClient.class);

    private ClmcClient clmcClient = Mockito.mock(ClmcClient.class);

    private ProcessFileAuthCheckService processFileAuthCheckService = Mockito.mock(ProcessFileAuthCheckService.class);

    private PdfToolService pdfToolService = Mockito.mock(PdfToolService.class);

    @Test
    public void testCreateContractAuditWeb() {
        init();

        //非灰度名单
        mockGray(false);
        CreateContractAuditWebRequest request = new CreateContractAuditWebRequest();
        request.setFileId("test_file_id");
        Assert.assertThrows(SaasBizException.class, () -> contractAuditRest.createContractAuditWeb(request, "test_tenant_oid", "test_oid"));
        //恢复
        mockGray(true);

        //文件上传人不匹配
        DocInfoResult docInfoResult = new DocInfoResult();
        docInfoResult.setOperatorId("xxx");
        mockDocInfo(docInfoResult);
        Assert.assertThrows(SaasBizException.class, () -> contractAuditRest.createContractAuditWeb(request, "test_tenant_oid", "test_oid"));
        verify(grayClient, atLeast(1)).inGrayOid(Mockito.anyString(), Mockito.anyString());
        //恢复
        docInfoResult.setOperatorId("test_oid");
        mockDocInfo(docInfoResult);

        //会员功能校验不通过
        mockFunctionCheck(false);
        Assert.assertThrows(SaasBizException.class, () -> contractAuditRest.createContractAuditWeb(request, "test_tenant_oid", "test_oid"));
        verify(saasCommonClient, atLeast(1)).supportFunction("test_tenant_oid", null, "contract_audit");
        //恢复
        mockFunctionCheck(true);

        //用户未授权
        mockUserAgreement(false);
        Assert.assertThrows(SaasBizException.class, () -> contractAuditRest.createContractAuditWeb(request, "test_tenant_oid", "test_oid"));
        verify(userAgreementService, atLeast(1)).queryLatestUserAgreement(null, "test_tenant_oid", UserAgreementTypeEnum.ENABLE_AI.getType());
        //恢复
        mockUserAgreement(true);

        //创建合同审计记录
        mockUser();
        RpcCreateContractAuditWebOutputDTO webOutputDTO = new RpcCreateContractAuditWebOutputDTO();
        webOutputDTO.setWebUrl("test_web_url");
        webOutputDTO.setRecordId("test_record_id");
        Mockito.when(contractAuditClient.createContractAudit(Mockito.any())).thenReturn(webOutputDTO);
        mockShortLink();
        RestResult<CreateContractAuditWebResponse> response = contractAuditRest.createContractAuditWeb(request, "test_tenant_oid", "test_oid");

        Assert.assertEquals(response.getData().getRecordId(), "test_record_id");
        Assert.assertEquals(response.getData().getUrl(), "test_short_url");
    }

    @Test
    public void testGetWebUrl() {
        init();

        mockShortLink();
        Mockito.when(clmcClient.getTrialUrl(Mockito.anyString())).thenReturn("test_trial_url");
        RestResult<GetWebUrlResponse> result = contractAuditRest.getWebUrl("1", true, "test_tenant_oid", "test_oid");
        Assert.assertEquals(result.getCode(), 0);
        Assert.assertEquals(result.getData().getUrl(), "test_short_url");

        Mockito.when(contractAuditClient.getContractAuditWebUrl("test_record_id", "test_gid", "test_tenant_gid")).thenReturn("test_web_url");
        mockUser();
        result = contractAuditRest.getWebUrl("test_record_id", false, "test_tenant_oid", "test_oid");
        Assert.assertEquals(result.getCode(), 0);
        Assert.assertEquals(result.getData().getUrl(), "test_short_url");
    }

    @Test
    public void testPageQuery() {
        init();

        RpcContractAuditRecordPageQueryOutput pageQueryOutput = new RpcContractAuditRecordPageQueryOutput();
        pageQueryOutput.setTotal(1L);
        RpcContractAuditRecordDTO recordDTO = new RpcContractAuditRecordDTO();
        recordDTO.setRecordId("test_record_id");
        pageQueryOutput.setRecords(Lists.newArrayList(recordDTO));
        Mockito.when(contractAuditClient.contractAuditPageRecord(Mockito.any())).thenReturn(pageQueryOutput);
        mockUser();
        ContractAuditPageQueryRequest request = new ContractAuditPageQueryRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        RestResult<ContractAuditPageQueryResponse> restResult = contractAuditRest.pageQuery(request, "test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertEquals(restResult.getData().getTotal(), new Long(1));
    }

    @Test
    public void testDeleteRecord() {
        init();

        mockUser();
        Mockito.doNothing().when(contractAuditClient).deleteContractAuditRecord(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());
        RestResult<Void> restResult = contractAuditRest.deleteRecord("test_record_id", "test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertNull(restResult.getData());
    }

    @Test
    public void testTrial() {
        init();

        RpcAuditTrialsOutput output = new RpcAuditTrialsOutput();
        RpcAuditTrialsOutput.FileInfo fileInfo = new RpcAuditTrialsOutput.FileInfo();
        fileInfo.setId("1");
        fileInfo.setFileName("销售合同");
        output.setTrialFiles(Lists.newArrayList(fileInfo));
        Mockito.when(clmcClient.trails()).thenReturn(output);
        RestResult<ContractAuditTrialResponse> restResult = contractAuditRest.trial();
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertEquals(restResult.getData().getFiles().size(), 1);
    }

    @Test
    public void testQueryRuleLists() {
        init();

        mockUser();
        RpcQueryAuditListsOutputDTO output = new RpcQueryAuditListsOutputDTO();
        RpcQueryAuditListsOutputDTO.RuleList ruleList = new RpcQueryAuditListsOutputDTO.RuleList();
        ruleList.setRuleListId("1");
        ruleList.setRuleListName("test_rule_name");
        output.setMyRuleList(Lists.newArrayList(ruleList));
        Mockito.when(contractAuditClient.queryAuditRuleLists(Mockito.anyString(), Mockito.anyString())).thenReturn(output);
        RestResult<QueryRuleListResponse> restResult = contractAuditRest.queryRuleLists("test_tenant_oid", "test_oid");

        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertEquals(restResult.getData().getCategory().get(0).getRuleLists().size(), 1);
    }

    @Test
    public void testQueryAuditRecordExist() {
        init();

        mockUser();
        Mockito.when(contractAuditClient.queryEmbedContractAuditRecord(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        QueryAuditRecordExistRequest request = new QueryAuditRecordExistRequest();
        request.setProcessId("test_process_id");
        request.setFileId("test_file_id");
        RestResult<QueryAuditRecordExistResponse> restResult = contractAuditRest.queryAuditRecordExist(request, "test_tenant_oid", "test_oid");
        Assert.assertFalse(restResult.getData().isExists());

        RpcContractAuditRecordDTO recordDTO = new RpcContractAuditRecordDTO();
        recordDTO.setRecordId("test_record_id");
        Mockito.when(contractAuditClient.queryEmbedContractAuditRecord(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(recordDTO);
        restResult = contractAuditRest.queryAuditRecordExist(request, "test_tenant_oid", "test_oid");
        Assert.assertTrue(restResult.getData().isExists());
        Assert.assertEquals(restResult.getData().getRecordId(), "test_record_id");
    }

    @Test
    public void canCreateTest() {
        init();

        mockGray(false);
        RestResult<ContractAuditCanCreateResponse> restResult = contractAuditRest.canCreate("test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getData().getScene(), ContractAuditCheckSceneEnum.GRAY_NOT_OPEN.getCode());
        mockGray(true);

        mockFunctionCheck(false);
        restResult = contractAuditRest.canCreate("test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getData().getScene(), ContractAuditCheckSceneEnum.VIP_NOT_SUPPORT.getCode());
        mockFunctionCheck(true);

        mockUserAgreement(false);
        restResult = contractAuditRest.canCreate("test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getData().getScene(), ContractAuditCheckSceneEnum.USER_NOT_AGREEMENT.getCode());
        mockUserAgreement(true);

        restResult = contractAuditRest.canCreate("test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getData().getScene(), ContractAuditCheckSceneEnum.NORMAL.getCode());
    }

    @Test
    public void testCreateAuditResult() {
        init();

        mockUser();
        mockGray(true);
        mockFunctionCheck(true);
        mockUserAgreement(true);
        mockFileAuth(true);
        RpcCreateAuditResultOutputDTO outputDTO = new RpcCreateAuditResultOutputDTO();
        outputDTO.setRecordId("test_record_id");
        Mockito.when(contractAuditClient.createAuditResult(Mockito.any())).thenReturn(outputDTO);
        CreateAuditResultRequest request = new CreateAuditResultRequest();
        request.setProcessId("test_process_id");
        request.setFileId("test_file_id");
        request.setRuleListId("test_rule_list_id");
        RestResult<CreateAuditResultResponse> restResult = contractAuditRest.createAuditResult(request, "test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertEquals(restResult.getData().getRecordId(), "test_record_id");
    }

    @Test
    public void testGetContractAuditRuleTree() {
        init();

        mockUser();
        RpcQueryAuditRuleTreeOutputDTO outputDTO = new RpcQueryAuditRuleTreeOutputDTO();
        RpcQueryAuditRuleTreeOutputDTO.AuditRuleBO auditRuleBO = new RpcQueryAuditRuleTreeOutputDTO.AuditRuleBO();
        auditRuleBO.setRuleSeq("1.1");
        outputDTO.setTree(Lists.newArrayList(auditRuleBO));
        Mockito.when(contractAuditClient.queryAuditRuleTree(Mockito.any())).thenReturn(outputDTO);

        GetContractAuditRuleTreeRequest request = new GetContractAuditRuleTreeRequest();
        request.setProcessId("test_process_id");
        request.setFileId("test_file_id");
        request.setRecordId("test_record_id");
        RestResult<GetContractAuditRuleTreeResponse> restResult = contractAuditRest.getContractAuditRuleTree(request, "test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertEquals(restResult.getData().getTree().size(), 1);
    }

    @Test
    public void testGetContractAuditResult() {
        init();

        mockUser();
        RpcQueryAuditResultRuleOutputDTO outputDTO = new RpcQueryAuditResultRuleOutputDTO();
        outputDTO.setStatus("running");
        RpcContractAuditResultRuleDTO resultRuleDTO = new RpcContractAuditResultRuleDTO();
        resultRuleDTO.setRuleSeq("1.1");
        outputDTO.setResultRules(Lists.newArrayList(resultRuleDTO));
        Mockito.when(contractAuditClient.queryAuditRuleResult(Mockito.any())).thenReturn(outputDTO);
        GetContractAuditResultRequest request = new GetContractAuditResultRequest();
        request.setRecordId("test_record_id");
        RestResult<GetContractAuditResultResponse> restResult = contractAuditRest.getContractAuditResult(request, "test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertEquals(restResult.getData().getStatus(), "running");
        Assert.assertEquals(restResult.getData().getResult().size(), 1);
    }

    @Test
    public void testKeywordSearch() {
        init();

        mockUser();
        mockFileAuth(true);
        PdfTextSearchOutputDTO outputDTO = new PdfTextSearchOutputDTO();
        SearchResultBO searchResultBO = new SearchResultBO();
        searchResultBO.setKeyword("test_keyword");
        outputDTO.setSearchResults(Lists.newArrayList(searchResultBO));
        Mockito.when(pdfToolService.pdfTextSearch(Mockito.anyString(), Mockito.any(), Mockito.anyString())).thenReturn(outputDTO);
        ContractAuditKeywordSearchRequest request = new ContractAuditKeywordSearchRequest();
        request.setProcessId("test_process_id");
        request.setFileId("test_file_id");
        request.setKeywords(Lists.newArrayList("test_keyword"));
        request.setAcceptPosFormat("center");
        RestResult<ContractAuditKeywordSearchResponse> restResult = contractAuditRest.keywordSearch(request, "test_tenant_oid", "test_oid");
        Assert.assertEquals(restResult.getCode(), 0);
        Assert.assertEquals(restResult.getData().getSearchResults().size(), 1);
    }

    private void init() {
        Whitebox.setInternalState(contractAuditRest, "contractAuditService", contractAuditService);
        Whitebox.setInternalState(contractAuditService, "grayClient", grayClient);
        Whitebox.setInternalState(contractAuditService, "userCenterService", userCenterService);
        Whitebox.setInternalState(contractAuditService, "docClient", docClient);
        Whitebox.setInternalState(contractAuditService, "saasCommonClient", saasCommonClient);
        Whitebox.setInternalState(contractAuditService, "contractAuditClient", contractAuditClient);
        Whitebox.setInternalState(contractAuditService, "shortLinkClient", shortLinkClient);
        Whitebox.setInternalState(contractAuditService, "userAgreementService", userAgreementService);
        Whitebox.setInternalState(contractAuditService, "clmcClient", clmcClient);
        Whitebox.setInternalState(contractAuditService, "processFileAuthCheckService", processFileAuthCheckService);
        Whitebox.setInternalState(contractAuditService, "pdfToolService", pdfToolService);
    }

    private void mockFileAuth(boolean pass) {
        FileAuthCheckResultBO resultBO = new FileAuthCheckResultBO();
        resultBO.setSuccess(pass);
        Mockito.when(processFileAuthCheckService.checkFileAuth(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(resultBO);
    }

    private void mockShortLink() {
        Mockito.when(shortLinkClient.convertShortUrl(Mockito.anyString(), Mockito.anyLong())).thenReturn("test_short_url");
    }

    private void mockUserAgreement(boolean pass) {
        QueryLatestUserAgreementOutputDTO agreementOutputDTO = new QueryLatestUserAgreementOutputDTO();
        if (pass) {
            agreementOutputDTO.setLatestAgreeTime(System.currentTimeMillis() - 1000);
        }
        Mockito.when(userAgreementService.queryLatestUserAgreement(null, "test_tenant_oid", UserAgreementTypeEnum.ENABLE_AI.getType())).thenReturn(agreementOutputDTO);
    }

    private void mockFunctionCheck(boolean pass) {
        Mockito.when(saasCommonClient.supportFunction("test_tenant_oid", null, FunctionCodeConstants.CONTRACT_AUDIT)).thenReturn(pass);
    }

    private void mockGray(boolean pass) {
        Mockito.when(grayClient.inGrayOid(Mockito.anyString(), Mockito.anyString())).thenReturn(pass);
    }

    private void mockDocInfo(DocInfoResult docInfoResult) {
        Mockito.when(docClient.queryDocInfo(Mockito.anyString())).thenReturn(docInfoResult);
    }

    private void mockUser() {
        AccountInfoDTO userAccount = new AccountInfoDTO();
        userAccount.setGid("test_gid");
        Mockito.when(userCenterService.queryAccountInfoByOid("test_oid")).thenReturn(userAccount);
        AccountInfoDTO tenantAccount = new AccountInfoDTO();
        tenantAccount.setGid("test_tenant_gid");
        Mockito.when(userCenterService.queryAccountInfoByOid("test_tenant_oid")).thenReturn(tenantAccount);
    }
}
