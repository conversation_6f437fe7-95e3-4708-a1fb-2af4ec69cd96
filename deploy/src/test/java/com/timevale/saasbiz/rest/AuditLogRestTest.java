package com.timevale.saasbiz.rest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saas.common.manage.common.service.model.base.BaseResult;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogConfigQueryRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogDownloadRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogListRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogRecordQueryRequest;
import com.timevale.saasbiz.rest.bean.auditlog.request.AuditLogSubscriberSaveRequest;
import com.timevale.saasbiz.rest.bean.auditlog.response.*;

import com.timevale.saasbiz.rest.bean.auditlog.vo.AuditLogRecordVO;
import com.timevale.saasbiz.rest.bean.auditlog.vo.AuditLogSubscriberVO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.test.web.servlet.MvcResult;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.timevale.saasbiz.constant.TestAccountConstants.SENIOR_SUBJECT_1;

/**
 * 设计日志单测
 *
 * <AUTHOR>
 * @since 2023-10-24 17:14
 */
@Slf4j
@JsonDataIgnore
public class AuditLogRestTest extends BaseServiceTest {

    @Test(description = "获取操作日志列表")
    private void testPageList() throws Exception {
        AuditLogListRequest request = new AuditLogListRequest();
        request.setEndTime(new Date());
        request.setStartTime(DateUtils.addDays(request.getEndTime(), -365));

        // 在高级版企业用测试账号1查询审计日志列表
        MvcResult mvcResult =
                seniorAccount1ExpectPost("/v1/saas-common/audit-log/page-list", request, 0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<AuditLogListResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogListResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(baseResult.getData().getList()), "list is empty");
    }

    @Test(description = "获取业务模块列表")
    private void testListFirstModule() throws Exception {
        // 在高级版企业用测试账号1查询业务模块列表
        MvcResult mvcResult =
                seniorAccount1ExpectGet("/v1/saas-common/audit-log/first-module-list", null, 0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<AuditLogListResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogListResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(baseResult.getData().getList()), "list is empty");
    }

    @Test(description = "查询事件类型列表")
    private void testListEvent() throws Exception {
        // 在高级版企业用测试账号1查询事件类型
        MvcResult mvcResult =
                seniorAccount1ExpectGet(
                        "/v1/saas-common/audit-log/event",
                        ImmutableMap.of("module", "saas_contract_template"),
                        0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<QueryEventListResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<QueryEventListResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(baseResult.getData().getEventList()), "list is empty");
    }

    @Test(description = "下载审计日志")
    private void testDownloadAuditLog() throws Exception {
        AuditLogDownloadRequest request = new AuditLogDownloadRequest();
        request.setEndTime(new Date());
        request.setStartTime(DateUtils.addDays(request.getEndTime(), -7));
        // 请求下载
        MvcResult mvcResult =
                seniorAccount1ExpectPost("/v1/saas-common/audit-log/async-download", request, 0);
        BaseResult<AuditLogDownloadResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogDownloadResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(baseResult.getData().getTaskId()), "list is empty");

        // 获取下载结果
        String downloadUrl = "";
        for (int i = 0; i < 10; i++) {
            MvcResult result =
                    seniorAccount1ExpectGet(
                            "/v1/saas-common/audit-log/download-url",
                            ImmutableMap.of("taskId", baseResult.getData().getTaskId()),
                            0);
            BaseResult<QueryDownloadStatusResponse> statusResult =
                    JsonUtilsPlus.parseObject(
                            result.getResponse().getContentAsString(),
                            new TypeReference<BaseResult<QueryDownloadStatusResponse>>() {});
            // 获取到结果直接返回
            if (StringUtils.isNotBlank(statusResult.getData().getDownloadUrl())) {
                downloadUrl = statusResult.getData().getDownloadUrl();
                break;
            }

            // 轮询间隔1秒
            TimeUnit.SECONDS.sleep(1);
        }

        Assert.assertTrue(StringUtils.isNotBlank(downloadUrl), "download url is blank");
    }

    @Test(description = "审计日志导出列表")
    private void testQueryExportList() throws Exception {
        MvcResult mvcResult =
                seniorAccount1ExpectGet(
                        "/v1/saas-common/audit-log/export-record",
                        ImmutableMap.of("pageNo", "1", "pageSize", "10"),
                        0);

        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<QueryAuditLogExportListResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<QueryAuditLogExportListResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
        Assert.assertNotNull(baseResult.getData().getData(), "data list is empty");
    }


    @Test(description = "获取推送记录列表")
    private void testSubscribePageList() throws Exception {
        AuditLogRecordQueryRequest request = new AuditLogRecordQueryRequest();
        request.setEndTime(new Date());
        request.setStartTime(DateUtils.addDays(request.getEndTime(), -365));

        // 在高级版企业用测试账号1查询审计日志列表
        MvcResult mvcResult =
                seniorAccount1ExpectPost("/v1/saas-common/audit-log/subscription/record-list", request, 0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<AuditLogListResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogListResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(baseResult.getData().getList()), "list is empty");
    }

    @Test(description = "获取推送配置列表")
    private void testSubscribeConfigPageList() throws Exception {
        AuditLogConfigQueryRequest request = new AuditLogConfigQueryRequest();
        request.setPageNo(1);
        request.setPageSize(10);

        // 在高级版企业用测试账号1查询审计日志列表
        MvcResult mvcResult =
                seniorAccount1ExpectPost("/v1/saas-common/audit-log/subscription/config-list", request, 0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<AuditLogConfigListResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogConfigListResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(baseResult.getData().getList()), "list is empty");
    }


    @Test(description = "保存订阅人")
    private void testSaveSubscriber() throws Exception {
        List<AuditLogSubscriberVO> accountList = new ArrayList<>();
        AuditLogSubscriberVO subscriberVO = new AuditLogSubscriberVO();
        subscriberVO.setAccountId("b7f5864286cf48538e7cc7a0d510e539");
        subscriberVO.setAccountType("role");
        accountList.add(subscriberVO);
        AuditLogSubscriberSaveRequest request = new AuditLogSubscriberSaveRequest();
        request.setConfigId("98084cb85ea74661a7e5a5ab073c89ba");
        request.setAccountList(accountList);

        // 在高级版企业用测试账号1查询审计日志列表
        MvcResult mvcResult =
                seniorAccount1ExpectPost("/v1/saas-common/audit-log/subscription/save-subscriber", request, 0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<AuditLogConfigUpdateResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogConfigUpdateResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");

    }

    @Test(description = "获取推送配置订阅人")
    private void testSubscribeList() throws Exception {

        MvcResult mvcResult =
                seniorAccount1ExpectGet("/v1/saas-common/audit-log/subscription/subscriber-list",   ImmutableMap.of("configId", "3ce98e994dcb41be950cf7b4b3d63e21"), 0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<AuditLogSubscriberListResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogSubscriberListResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(baseResult.getData().getList()), "list is empty");
    }

    @Test(description = "修改订阅状态")
    private void testChangeStatus() throws Exception {

        MvcResult mvcResult =
                seniorAccount1ExpectGet("/v1/saas-common/audit-log/subscription/change-status",   ImmutableMap.of("configId", "98084cb85ea74661a7e5a5ab073c89ba"), 0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<AuditLogConfigUpdateResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogConfigUpdateResponse>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
    }

    @Test(description = "查询推送记录详情")
    private void testRecordDetail() throws Exception {

        MvcResult mvcResult =
                seniorAccount1ExpectGet("/v1/saas-common/audit-log/subscription/record-detail",   ImmutableMap.of("recordId", "6313911310102591561", "resourceTenantId", SENIOR_SUBJECT_1), 0);
        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");
        Assert.assertTrue(
                StringUtils.isNotBlank(mvcResult.getResponse().getContentAsString()),
                "response content is blank");

        BaseResult<AuditLogRecordVO> baseResult =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<BaseResult<AuditLogRecordVO>>() {});
        Assert.assertNotNull(baseResult.getData(), "data is null");
    }
}
