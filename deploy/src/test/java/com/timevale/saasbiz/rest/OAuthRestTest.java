package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.rest.bean.oauth.request.CancelAuthRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * 授权管理相关接口单测
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Slf4j
@JsonDataIgnore
public class OAuthRestTest extends BaseServiceTest {

    private static final String APP_AUTH_RECORD_QUERY_APP_ID = "**********";
    private static final String APP_AUTH_RECORD_CANCEL_APP_ID = "**********";

    @Test(description = "测试分页查询应用Scope授权关系列表")
    public void testQueryScopeAuthMappings() throws Exception {
        // 未设置TENANT_ID或OPERATOR_ID请求头
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v3/oauth/mappings/list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, "")
                                .param("pageNum", "1")
                                .param("pageSize", "10"))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.SAAS_ILLEGAL_PARAM.getCode()));
        // 设置TENANT_ID、OPERATOR_ID请求头
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v3/oauth/mappings/list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_ADMIN)
                                .param("pageNum", "1")
                                .param("pageSize", "10"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "测试分页查询应用Scope授权日志列表")
    public void testQueryScopeAuthLogs() throws Exception {
        // 未设置TENANT_ID或OPERATOR_ID请求头
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v3/oauth/mappings/log-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, "")
                                .param("pageNum", "1")
                                .param("pageSize", "10"))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.SAAS_ILLEGAL_PARAM.getCode()));
        // 未设置appId参数
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v3/oauth/mappings/log-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_ADMIN)
                                .param("pageNum", "1")
                                .param("pageSize", "10"))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.SAAS_ILLEGAL_PARAM.getCode()));
        // 设置TENANT_ID、OPERATOR_ID请求头及appId参数
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v3/oauth/mappings/log-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_ADMIN)
                                .param("appId", APP_AUTH_RECORD_QUERY_APP_ID)
                                .param("pageNum", "1")
                                .param("pageSize", "10"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "测试取消应用Scope授权")
    public void testCancelScopeAuth() throws Exception {
        CancelAuthRequest request = new CancelAuthRequest();
        // 未设置TENANT_ID或OPERATOR_ID请求头
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/oauth/cancel-auth")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_TENANT_ID, "")
                                .header(HEADER_OPERATOR_ID, ""))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.SAAS_ILLEGAL_PARAM.getCode()));
        // 未设置appId参数
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/oauth/cancel-auth")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_TENANT_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_ADMIN))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.SAAS_ILLEGAL_PARAM.getCode()));
        // 设置TENANT_ID、OPERATOR_ID请求头及appId参数
        request.setAppId(APP_AUTH_RECORD_CANCEL_APP_ID);
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/oauth/cancel-auth")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_TENANT_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.PROFESSIONAL_SUBJECT_ADMIN))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }
}
