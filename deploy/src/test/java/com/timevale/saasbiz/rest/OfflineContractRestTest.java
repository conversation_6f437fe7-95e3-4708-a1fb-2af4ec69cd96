package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.timevale.contractmanager.common.service.enums.ProcessFileType;
import com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractExtractWayEnum;
import com.timevale.contractmanager.common.service.enums.offlinecontract.OfflineContractImportWayEnum;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.integration.contractanalysis.ContractCategoryClient;
import com.timevale.saasbiz.rest.bean.offlinecontract.request.*;
import com.timevale.saasbiz.rest.bean.offlinecontract.response.*;
import com.timevale.saasbiz.rest.bean.offlinecontract.vo.*;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Date;
import java.util.List;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * 线下合同单测
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
@JsonDataIgnore
public class OfflineContractRestTest extends BaseServiceTest {

    private static final String TENANT_ID = TestAccountConstants.SENIOR_SUBJECT_1;
    private static final String TENANT_GID = TestAccountConstants.SENIOR_SUBJECT_1_GID;
    private static final String ACCOUNT_ID = TestAccountConstants.SENIOR_SUBJECT_ADMIN;

    @Autowired ContractCategoryClient contractCategoryClient;

    @Test(description = "测试下载合同信息导入excel")
    public void testDownloadExcel() throws Exception {
        DownloadOfflineContractExcelRequest request = new DownloadOfflineContractExcelRequest();
        request.setContractFileNames(Lists.newArrayList("简单文档1", "简单文档2"));
        OfflineContractSignerConfigVO signerConfigVO = new OfflineContractSignerConfigVO();
        signerConfigVO.setSignerKeyword("甲方");
        signerConfigVO.setSignerSubjectType(SubjectTypeEnum.ORG.getType());
        request.setSignerConfigs(Lists.newArrayList(signerConfigVO));
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.POST,
                                        "/v2/offline-contracts/download-contract-excel")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID)
                                .header(HEADER_TENANT_ID, TENANT_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.downloadUrl").isNotEmpty());
    }

    @Test(description = "测试解析合同信息导入excel")
    public void testParseExcel() throws Exception {
        ParseOfflineContractExcelRequest request = new ParseOfflineContractExcelRequest();
        request.setFileKey("$62b5987d-83b2-4a92-96b2-98e847db7be8$**********");
        OfflineContractSignerConfigVO signerConfigVO = new OfflineContractSignerConfigVO();
        signerConfigVO.setSignerKeyword("甲方");
        signerConfigVO.setSignerSubjectType(SubjectTypeEnum.ORG.getType());
        request.setSignerConfigs(Lists.newArrayList(signerConfigVO));
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.POST,
                                        "/v2/offline-contracts/parse-contract-excel")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID)
                                .header(HEADER_TENANT_ID, TENANT_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.processInfos").isNotEmpty());
    }

    @Test(description = "测试导入线下合同-手动录入")
    public void testImportOfflineContract_Input() throws Exception {
        // 线下合同文件列表
        OfflineContractVO offlineContractVO = new OfflineContractVO();
        OfflineContractFileVO offlineContractFileVO = new OfflineContractFileVO();
        offlineContractFileVO.setFileId("4b2d5d4983374a9992dff8999fb6cad6");
        offlineContractFileVO.setFileName("电梯销售合同.pdf");
        offlineContractFileVO.setFileType(ProcessFileType.CONTRACT_FILE.getCode());
        offlineContractVO.setContractFiles(Lists.newArrayList(offlineContractFileVO));
        // 线下合同用户信息
        OfflineContractAccountVO offlineContractAccountVO = new OfflineContractAccountVO();
        offlineContractAccountVO.setAccount(TestAccountConstants.ACCOUNT_4);
        offlineContractAccountVO.setAccountName(TestAccountConstants.ACCOUNT_ID_4_NAME);
        offlineContractAccountVO.setSubjectName(TestAccountConstants.ACCOUNT_ID_4_NAME);
        offlineContractAccountVO.setSubjectType(SubjectTypeEnum.PERSON.getType());
        // 组装线下合同信息
        OfflineContractProcessInfoVO processInfoVO = new OfflineContractProcessInfoVO();
        processInfoVO.setTitle("单测-手动录入纸质合同" + System.currentTimeMillis());
        processInfoVO.setContractValidity(DateUtils.getDayEnd(DateUtils.addDays(new Date(), 30)).getTime());
        processInfoVO.setSigners(Lists.newArrayList(offlineContractAccountVO));
        offlineContractVO.setProcessInfo(processInfoVO);
        // 组装导入参数
        ImportOfflineContractRequest importRequest = new ImportOfflineContractRequest();

        importRequest.setMenuId("e3d2fb7d6d634eadad7e16859e9f8eaf");
        importRequest.setImportWay(OfflineContractImportWayEnum.BATCH_PDF.getWay());
        importRequest.setExtractWay(OfflineContractExtractWayEnum.MANUAL_INPUT.getWay());
        importRequest.setContracts(Lists.newArrayList(offlineContractVO));
        MvcResult mvcResult = mockMvc.perform(
                        MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/offline-contracts/import")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(importRequest))
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID)
                                .header(HEADER_TENANT_ID, TENANT_ID)
                                .header(HEADER_CLIENT_ID, "WEB"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.recordId").isNotEmpty())
                .andReturn();
        ImportOfflineContractResponse importResponse =
                parseMvcResultResponse(mvcResult, ImportOfflineContractResponse.class);

        String recordId = importResponse.getRecordId();

        // 查询导入记录列表
        PageQueryOfflineContractRecordsResponse listResponse =
                pageQueryOfflineContractRecords(ACCOUNT_ID, TENANT_ID);
        Assert.assertTrue(listResponse.getTotal() > 0);

        // 查询导入记录基本信息
        QueryOfflineContractRecordInfoResponse recordInfoResponse =
                queryOfflineContractRecordInfo(recordId, ACCOUNT_ID, TENANT_ID);
        Assert.assertTrue(recordInfoResponse.getContractSize() == 1);

        // 批量查询导入记录基本信息
        BatchQueryOfflineContractRecordInfoResponse batchResponse =
                batchQueryOfflineContractRecordInfo(
                        Lists.newArrayList(recordId), ACCOUNT_ID, TENANT_ID);
        Assert.assertTrue(CollectionUtils.isNotEmpty(batchResponse.getRecords()));
        Assert.assertTrue(batchResponse.getRecords().size() == 1);

        // 查询导入记录合同文件列表
        QueryOfflineContractRecordContractsResponse contractsResponse =
                queryOfflineContractRecordContracts(recordId, ACCOUNT_ID, TENANT_ID);
        Assert.assertTrue(CollectionUtils.isNotEmpty(contractsResponse.getContracts()));
        Assert.assertTrue(contractsResponse.getContracts().size() == 1);

        Thread.sleep(2000L);
        // 停止导入
        stopOfflineContractImport(Lists.newArrayList(recordId), ACCOUNT_ID, TENANT_ID);
        // 恢复导入
        recoverOfflineContractImport(Lists.newArrayList(recordId), ACCOUNT_ID, TENANT_ID, ********);
        // 失败重试导入
        restartFailedOfflineContractImport(recordId, ACCOUNT_ID, TENANT_ID, ********);
        // 删除导入记录
        deleteOfflineContractRecord(Lists.newArrayList(recordId), ACCOUNT_ID, TENANT_ID);
    }

    /**
     * 查询线下合同导入记录列表
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private PageQueryOfflineContractRecordsResponse pageQueryOfflineContractRecords(
            String accountId, String subjectId) throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/offline-contracts/records")
                                .param("pageNum", "1")
                                .param("pageSize", "10")
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        return parseMvcResultResponse(mvcResult, PageQueryOfflineContractRecordsResponse.class);
    }

    /**
     * 批量查询线下合同导入记录基本信息
     * @param recordIds
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private BatchQueryOfflineContractRecordInfoResponse batchQueryOfflineContractRecordInfo(
            List<String> recordIds, String accountId, String subjectId) throws Exception {
        BatchQueryOfflineContractRecordInfoRequest request = new BatchQueryOfflineContractRecordInfoRequest();
        request.setRecordIds(recordIds);
        request.setWithMenuPath(true);
        MvcResult mvcResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/offline-contracts/batch-record-info")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        return parseMvcResultResponse(mvcResult, BatchQueryOfflineContractRecordInfoResponse.class);
    }

    /**
     * 查询线下合同导入记录基本信息
     * @param recordId
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private QueryOfflineContractRecordInfoResponse queryOfflineContractRecordInfo(
            String recordId, String accountId, String subjectId) throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/offline-contracts/record-info")
                                .param("recordId", recordId)
                                .param("withMenuPath", "true")
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        return parseMvcResultResponse(mvcResult, QueryOfflineContractRecordInfoResponse.class);
    }

    /**
     * 查询线下合同导入记录基本信息
     * @param recordId
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private QueryOfflineContractRecordContractsResponse queryOfflineContractRecordContracts(
            String recordId, String accountId, String subjectId) throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/offline-contracts/record-contract-files")
                                .param("recordId", recordId)
                                .param("pageNum", "1")
                                .param("pageSize", "10")
                                .param("withExtract", "true")
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        return parseMvcResultResponse(mvcResult, QueryOfflineContractRecordContractsResponse.class);
    }

    /**
     * 停止导入
     * @param recordIds
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private void stopOfflineContractImport(
            List<String> recordIds, String accountId, String subjectId) throws Exception {
        BatchOfflineContractRecordsRequest request = new BatchOfflineContractRecordsRequest();
        request.setRecordIds(recordIds);
        mockMvc.perform(
            MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/offline-contracts/stop-import")
                    .contentType(MediaType.APPLICATION_JSON_UTF8)
                    .content(JSONObject.toJSONString(request))
                    .header(HEADER_OPERATOR_ID, accountId)
                    .header(HEADER_TENANT_ID, subjectId))
            .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    /**
     * 恢复导入
     * @param recordIds
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private void recoverOfflineContractImport(
            List<String> recordIds, String accountId, String subjectId, int code) throws Exception {
        BatchOfflineContractRecordsRequest request = new BatchOfflineContractRecordsRequest();
        request.setRecordIds(recordIds);
        mockMvc.perform(
            MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/offline-contracts/recover-import")
                    .contentType(MediaType.APPLICATION_JSON_UTF8)
                    .content(JSONObject.toJSONString(request))
                    .header(HEADER_OPERATOR_ID, accountId)
                    .header(HEADER_TENANT_ID, subjectId))
            .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(code));
    }

    /**
     * 失败重试
     * @param recordId
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private void restartFailedOfflineContractImport(
            String recordId, String accountId, String subjectId, int code) throws Exception {
        ReimportFailedOfflineContractRequest request = new ReimportFailedOfflineContractRequest();
        request.setRecordId(recordId);
        mockMvc.perform(
            MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/offline-contracts/failed-reimport")
                    .contentType(MediaType.APPLICATION_JSON_UTF8)
                    .content(JSONObject.toJSONString(request))
                    .header(HEADER_OPERATOR_ID, accountId)
                    .header(HEADER_TENANT_ID, subjectId))
            .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(code));
    }

    /**
     * 删除导入记录
     * @param recordIds
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private void deleteOfflineContractRecord(
            List<String> recordIds, String accountId, String subjectId) throws Exception {
        BatchOfflineContractRecordsRequest request = new BatchOfflineContractRecordsRequest();
        request.setRecordIds(recordIds);
        mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/offline-contracts/delete-records")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(request))
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }
}
