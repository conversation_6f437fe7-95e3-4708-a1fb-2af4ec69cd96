package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.approval.response.ListApprovalFlowResponse;
import com.timevale.saasbiz.rest.bean.flowtemplate.request.FlowTemplateParticipantDataSourceFieldRequest;
import com.timevale.saasbiz.rest.bean.flowtemplate.response.FlowTemplateChannelDataSourceListResponse;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectDownloadImportTemplateRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormSaveRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormUpdateRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskBatchEditAuthRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskEditAuthRequest;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskResultRequest;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfCollectQueryResourceAuthResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectFormDetailResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectFormSaveResponse;
import com.timevale.saasbiz.rest.bean.process.request.ProcessStartRequest;
import lombok.extern.slf4j.Slf4j;
import org.hamcrest.Matchers;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Arrays;
import java.util.Objects;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.SENIOR_SUBJECT_1;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_APP_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2023/9/18 14:45
 */
@Slf4j
public class InfoCollectRestTest extends BaseServiceTest {



    @Test
    public void authTest() throws Exception {

        //  查询可授权角色
        MvcResult formSearchFieldResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/info-collect/task-auth-role")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        // 编辑授权
        InfoCollectTaskEditAuthRequest editAuthRequest =
                copyJsonData("editAuthReq", InfoCollectTaskEditAuthRequest.class);
        MvcResult editAuthResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/info-collect/task-edit-auth")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                .content(JSON.toJSONString(editAuthRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        //  查询授权列表
        MvcResult authListResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/info-collect/task-auth-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                .param("formId", "form650800f5e4b0aae76a743330")
                                .param("taskId", "1a5987f387854dda9108a5ff66d3c83a")
                )
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
        RestResult<InfCollectQueryResourceAuthResponse> authListResultResult = JsonUtilsPlus.parseObject(authListResult.getResponse().getContentAsString(),
                new TypeReference<RestResult<InfCollectQueryResourceAuthResponse>>() {
                });
        Assert.assertTrue(CollectionUtils.isNotEmpty(authListResultResult.getData().getAuthData()));
        Assert.assertTrue(authListResultResult.getData().getAuthData().stream()
                .anyMatch(elm -> elm.getAuthorizedEntityId().equals("f73027705d2243d58d065b33ec91643c")));



    }

    @Test
    public void taskBatchEditAuth() throws Exception {
        // 批量编辑授权
        InfoCollectTaskBatchEditAuthRequest editBatchAuthRequest =
                copyJsonData("editBatchAuthReq", InfoCollectTaskBatchEditAuthRequest.class);
        MvcResult editAuthResult = mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/info-collect/task-batch-edit-auth")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                .content(JSON.toJSONString(editBatchAuthRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test(description = "数据源查询测试")
    public void infoCollectDataSource() throws Exception {
        String subjectOid = SENIOR_SUBJECT_1;
        String operatorOid = ACCOUNT_ID_1;

        mockMvc.perform(MockMvcRequestBuilders.get("/v1/info-collect/start-data-list")
                        .param("pageNum", "1")
                        .param("pageSize","1")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, subjectOid)
                        .header(HEADER_OPERATOR_ID, operatorOid)
                        .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));

//        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/v2/flow-templates/channel-data-source-list")
//                        .contentType(MediaType.APPLICATION_JSON_UTF8)
//                        .header(HEADER_TENANT_ID, subjectOid)
//                        .header(HEADER_OPERATOR_ID, operatorOid)
//                        .header(HEADER_APP_ID, APP_ID))
//                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
//                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
//                .andReturn();
//
//        FlowTemplateChannelDataSourceListResponse listResponse = JSONObject.parseObject(
//                        mvcResult.getResponse().getContentAsString(),
//                        new com.alibaba.fastjson.TypeReference<BaseResult<FlowTemplateChannelDataSourceListResponse>>() {})
//                .getData();
//        Assert.assertTrue(CollectionUtils.isNotEmpty(listResponse.getList()), "表单不女主奶");

    }

    @Test(description = "获取表单结果页")
    public void infoCollectTaskResult() throws Exception {
//        String subjectOid = SENIOR_SUBJECT_1;
        String operatorOid = ACCOUNT_ID_1;
        InfoCollectTaskResultRequest request = new InfoCollectTaskResultRequest();
        request.setDataId("933738145627607040");
        request.setTaskKey("task933668260675080192");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/info-collect/task-result")
                        .content(JSON.toJSONString(request))
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, operatorOid)
                        .header(HEADER_OPERATOR_ID, operatorOid)
                        .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.btnList").isNotEmpty());
    }
}
