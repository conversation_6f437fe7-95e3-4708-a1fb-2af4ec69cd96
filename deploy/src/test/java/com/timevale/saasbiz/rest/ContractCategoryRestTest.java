package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import com.timevale.contractanalysis.facade.api.dto.contractcategory.CheckContractCategoryNameExistedResultDTO;
import com.timevale.contractanalysis.facade.api.enums.FieldValueTypeEnum;
import com.timevale.contractanalysis.facade.api.request.contractcategory.CheckContractCategoryNameExistedRequest;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.integration.contractanalysis.ContractCategoryClient;
import com.timevale.saasbiz.rest.bean.contractcategory.request.*;
import com.timevale.saasbiz.rest.bean.contractcategory.response.*;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.ContractCategoryVO;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.ExtractFieldVO;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.RelateFlowTemplateFileVO;
import com.timevale.saasbiz.rest.bean.contractcategory.vo.SceneContractCategoryVO;
import org.assertj.core.util.Lists;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import java.util.List;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * 合同类型单测
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@JsonDataIgnore
public class ContractCategoryRestTest extends BaseServiceTest {

    private static final String TENANT_ID = TestAccountConstants.SENIOR_SUBJECT_1;
    private static final String TENANT_GID = TestAccountConstants.SENIOR_SUBJECT_1_GID;
    private static final String ACCOUNT_ID = TestAccountConstants.SENIOR_SUBJECT_ADMIN;

    @Autowired ContractCategoryClient contractCategoryClient;

    @Test(description = "测试保存、编辑及查询合同类型")
    public void testContractCategories() throws Exception {
        // 查询系统合同类型
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/contract-categories/system-list")
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID)
                                        .header(HEADER_TENANT_ID, TENANT_ID))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        QuerySysContractCategoriesResponse response =
                parseMvcResultResponse(mvcResult, QuerySysContractCategoriesResponse.class);
        String refSysCategoryId = null;
        if (CollectionUtils.isNotEmpty(response.getCategories())) {
            for (SceneContractCategoryVO category : response.getCategories()) {
                ContractCategoryVO contractCategoryVO =
                        category.getCategories().stream()
                                .filter(i -> "劳动合同".equals(i.getCategoryName()))
                                .findFirst()
                                .orElse(null);
                if (null != contractCategoryVO) {
                    refSysCategoryId = contractCategoryVO.getCategoryId();
                    break;
                }
            }
        }
        String categoryId = null;
        String categoryName = "单测-劳动合同";
        boolean existed = checkContractCategoryNameExisted(categoryName, ACCOUNT_ID, TENANT_ID);
        // 查询已存在的合同类型
        if (existed) {
            CheckContractCategoryNameExistedRequest existedRequest =
                    new CheckContractCategoryNameExistedRequest();
            existedRequest.setSubjectGid(TENANT_GID);
            existedRequest.setCategoryName(categoryName);
            CheckContractCategoryNameExistedResultDTO resultDTO =
                    contractCategoryClient.checkContractCategoryNameExisted(existedRequest);
            categoryId = resultDTO.getExistedCategoryId();
        }
        // 如果不存在， 创建合同类型
        if (StringUtils.isBlank(categoryId)) {
            // 新增合同类型
            SaveContractCategoryRequest request = new SaveContractCategoryRequest();
            request.setCategoryName(categoryName);
            request.setRefSysCategoryId(refSysCategoryId);
            ExtractFieldVO extractFieldVO = new ExtractFieldVO();
            extractFieldVO.setFieldName("员工姓名");
            extractFieldVO.setFieldType(FieldValueTypeEnum.STRING.getType());
            request.setExtractFields(Lists.newArrayList(extractFieldVO));
            SaveContractCategoryResponse saveResponse =
                    saveContractCategory(request, ACCOUNT_ID, TENANT_ID);
            categoryId = saveResponse.getCategoryId();
        }
        // 查询合同类型列表
        mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/contract-categories/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID)
                        .header(HEADER_TENANT_ID, TENANT_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.total").value(Matchers.greaterThan(0)));

        // 修改合同类型
        UpdateContractCategoryRequest updateRequest = new UpdateContractCategoryRequest();
        updateRequest.setCategoryId(categoryId);
        updateRequest.setCategoryName(categoryName);
        updateRequest.setRefSysCategoryId(refSysCategoryId);
        ExtractFieldVO extractFieldVO = new ExtractFieldVO();
        extractFieldVO.setFieldName("员工姓名");
        extractFieldVO.setFieldType(FieldValueTypeEnum.STRING.getType());
        ExtractFieldVO newExtractFieldVO = new ExtractFieldVO();
        newExtractFieldVO.setFieldName("员工身份证");
        newExtractFieldVO.setFieldType(FieldValueTypeEnum.STRING.getType());
        updateRequest.setExtractFields(Lists.newArrayList(extractFieldVO, newExtractFieldVO));
        updateContractCategory(updateRequest, ACCOUNT_ID, TENANT_ID);

        // 查询合同类型详情
        QueryContractCategoriesDetailResponse detailResponse =
                queryContractCategoryDetail(categoryId, ACCOUNT_ID, TENANT_ID);
        Assert.assertTrue(CollectionUtils.isNotEmpty(detailResponse.getExtractFields()));
        Assert.assertTrue(detailResponse.getExtractFields().size() == 2);

        // 关联流程模板
        String flowTemplateId = "dfd6863a656d4e9e88364f390e7ba20b";
        String flowTemplateName = "单测设置及修改文件合同类型专用";
        String fileId = "d882550d79ab4efe9dbd957404472566";
        RelateFlowTemplateFileVO relateFlowTemplateFileVO = new RelateFlowTemplateFileVO();
        relateFlowTemplateFileVO.setFlowTemplateId(flowTemplateId);
        relateFlowTemplateFileVO.setFlowTemplateName(flowTemplateName);
        relateFlowTemplateFileVO.setRelateFileIds(Lists.newArrayList(fileId));
        relateFlowTemplateMappingByCategoryId(categoryId, ACCOUNT_ID, TENANT_ID, relateFlowTemplateFileVO);

        // 查询合同类型关联的流程模板
        QueryRelatedFlowTemplatesResponse relateResponse =
                queryRelateFlowTemplateMappingByCategoryId(categoryId, ACCOUNT_ID, TENANT_ID);
        Assert.assertTrue(CollectionUtils.isNotEmpty(relateResponse.getRelateFlowTemplates()));
        Assert.assertTrue(relateResponse.getRelateFlowTemplates().size() == 1);
        Assert.assertTrue(relateResponse.getRelateFlowTemplates().get(0).getFlowTemplateId().equals(flowTemplateId));

        // 启用合同类型
        updateContractCategoryStatus(categoryId, ACCOUNT_ID, TENANT_ID, true);

        // 查询可用的合同类型列表
        mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/contract-categories/usable-list")
                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID)
                        .header(HEADER_TENANT_ID, TENANT_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.categories").isNotEmpty());

        // 查询流程模板关联的合同类型
        QueryRelatedCategoriesResponse relateCategoryResponse =
                queryRelateContractCategoryByFlowTemplateId(flowTemplateId, ACCOUNT_ID, TENANT_ID);
        Assert.assertTrue(CollectionUtils.isNotEmpty(relateCategoryResponse.getCategories()));

        // 批量查询合同类型详情
        BatchQueryContractCategoriesDetailResponse batchDetails =
                batchQueryContractCategoryDetail(
                        Lists.newArrayList(categoryId), ACCOUNT_ID, TENANT_ID);
        Assert.assertTrue(CollectionUtils.isNotEmpty(batchDetails.getCategories()));
        Assert.assertTrue(batchDetails.getCategories().size() == 1);

        // 停用合同类型
        updateContractCategoryStatus(categoryId, ACCOUNT_ID, TENANT_ID, false);

        // 删除合同类型
        deleteContractCategory(categoryId, ACCOUNT_ID, TENANT_ID);
    }

    /**
     * 校验合同类型名称是否已存在
     *
     * @param categoryName
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private boolean checkContractCategoryNameExisted(
            String categoryName, String accountId, String subjectId)
            throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/contract-categories/check-name-existed")
                                .param("categoryName", categoryName)
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
                        .andReturn();
        return parseMvcResultResponse(mvcResult, Boolean.class).booleanValue();
    }

    /**
     * 保存合同类型
     *
     * @param request
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private SaveContractCategoryResponse saveContractCategory(
            SaveContractCategoryRequest request, String accountId, String subjectId)
            throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/contract-categories/save")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
                        .andReturn();
        return parseMvcResultResponse(mvcResult, SaveContractCategoryResponse.class);
    }

    /**
     * 修改合同类型
     *
     * @param request
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private void updateContractCategory(
            UpdateContractCategoryRequest request, String accountId, String subjectId)
            throws Exception {
        mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/contract-categories/update")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(request))
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    /**
     * 查询合同类型详情
     *
     * @param categoryId
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private QueryContractCategoriesDetailResponse queryContractCategoryDetail(
            String categoryId, String accountId, String subjectId) throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                    MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/contract-categories/detail")
                            .param("categoryId", categoryId)
                            .header(HEADER_OPERATOR_ID, accountId)
                            .header(HEADER_TENANT_ID, subjectId))
                    .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                    .andReturn();
        return parseMvcResultResponse(mvcResult, QueryContractCategoriesDetailResponse.class);
    }

    /**
     * 批量查询合同类型详情
     *
     * @param categoryIds
     * @param accountId
     * @param subjectId
     * @return
     * @throws Exception
     */
    private BatchQueryContractCategoriesDetailResponse batchQueryContractCategoryDetail(
            List<String> categoryIds, String accountId, String subjectId) throws Exception {
        BatchQueryContractCategoryDetailRequest request = new BatchQueryContractCategoryDetailRequest();
        request.setCategoryIds(categoryIds);
        request.setWithFields(true);
        MvcResult mvcResult =
                mockMvc.perform(
                    MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/contract-categories/batch-detail")
                            .contentType(MediaType.APPLICATION_JSON_UTF8)
                            .content(JSONObject.toJSONString(request))
                            .header(HEADER_OPERATOR_ID, accountId)
                            .header(HEADER_TENANT_ID, subjectId))
                    .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                    .andReturn();
        return parseMvcResultResponse(mvcResult, BatchQueryContractCategoriesDetailResponse.class);
    }

    /**
     * 修改合同类型状态
     * @param categoryId
     * @param accountId
     * @param subjectId
     * @param enable
     * @throws Exception
     */
    private void updateContractCategoryStatus(
            String categoryId, String accountId, String subjectId, boolean enable)
            throws Exception {
        UpdateContractCategoryStatusRequest statusRequest =
                new UpdateContractCategoryStatusRequest();
        statusRequest.setCategoryIds(Lists.newArrayList(categoryId));
        statusRequest.setEnable(enable);
        mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/contract-categories/update-status")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(statusRequest))
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    /**
     * 关联流程模板
     * @param categoryId
     * @param accountId
     * @param subjectId
     * @throws Exception
     */
    private void relateFlowTemplateMappingByCategoryId(
            String categoryId, String accountId, String subjectId, RelateFlowTemplateFileVO fileVO)
            throws Exception {
        RelateContractCategoryFlowTemplatesRequest request = new RelateContractCategoryFlowTemplatesRequest();
        request.setCategoryId(categoryId);
        request.setRelateFlowTemplates(Lists.newArrayList(fileVO));
        mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/contract-categories/relate-flow-templates")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(request))
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    /**
     * 查询合同类型关联的流程模板
     * @param categoryId
     * @param accountId
     * @param subjectId
     * @throws Exception
     */
    private QueryRelatedFlowTemplatesResponse queryRelateFlowTemplateMappingByCategoryId(
            String categoryId, String accountId, String subjectId)
            throws Exception {
        MvcResult mvcResult = mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/contract-categories/query-related-flow-templates")
                        .param("categoryId", categoryId)
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
        return parseMvcResultResponse(mvcResult, QueryRelatedFlowTemplatesResponse.class);
    }

    /**
     * 查询流程模板关联的合同类型
     * @param flowTemplateId
     * @param accountId
     * @param subjectId
     * @throws Exception
     */
    private QueryRelatedCategoriesResponse queryRelateContractCategoryByFlowTemplateId(
            String flowTemplateId, String accountId, String subjectId)
            throws Exception {
        MvcResult mvcResult = mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.GET, "/v2/contract-categories/query-related-categories")
                        .param("flowTemplateId", flowTemplateId)
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
        return parseMvcResultResponse(mvcResult, QueryRelatedCategoriesResponse.class);
    }

    /**
     * 删除合同类型
     * @param categoryId
     * @param accountId
     * @param subjectId
     * @throws Exception
     */
    private void deleteContractCategory(String categoryId, String accountId, String subjectId) throws Exception {
        DeleteContractCategoryRequest deleteRequest = new DeleteContractCategoryRequest();
        deleteRequest.setCategoryIds(Lists.newArrayList(categoryId));
        mockMvc.perform(
                MockMvcRequestBuilders.request(HttpMethod.POST, "/v2/contract-categories/delete")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(deleteRequest))
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }
}
