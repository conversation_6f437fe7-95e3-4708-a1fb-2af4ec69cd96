package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.model.bean.vip.dto.input.QueryApplyLogInputDTO;
import com.timevale.saasbiz.model.bean.vip.dto.output.QueryApplyLogOutputDTO;
import com.timevale.saasbiz.model.enums.vip.IllustrateCodeEnum;
import com.timevale.saasbiz.rest.bean.vip.GetFunctionIllustrateRequest;
import com.timevale.saasbiz.service.vip.impl.TrialFunctionCodeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.hamcrest.Matchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR>
 * @since 2024-01-11 14:32
 */
@Slf4j
@JsonDataIgnore
public class SaasVipFunctionRestTest extends BaseServiceTest {

    @Autowired
    private TrialFunctionCodeServiceImpl trialFunctionCodeService;
    
    @Test(description = "功能引导")
    public void guideFunctionIllustrateTest() throws Exception {
        GetFunctionIllustrateRequest request = new GetFunctionIllustrateRequest();
        request.setFunctionCode("info_collect");
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/function-illustrate")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_1)
                                .header(HEADER_TENANT_ID, TestAccountConstants.SENIOR_SUBJECT_1)
                                .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.needGuide").value(false));
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/function-illustrate")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_3)
                                .header(HEADER_TENANT_ID, TestAccountConstants.SENIOR_SUBJECT_1)
                                .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.needGuide").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.illustrateControl.code")
                        .value(IllustrateCodeEnum.NO_FUNCTION_AUTH.getCode()));
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/function-illustrate")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_1)
                                .header(HEADER_TENANT_ID, TestAccountConstants.BASE_SUBJECT_1)
                                .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.needGuide").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.illustrateControl.code")
                        .value(IllustrateCodeEnum.NO_BUTTON.getCode()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.wx").value(Matchers.notNullValue()));

        // 支持试用的case-管理员
        GetFunctionIllustrateRequest trialRequest = new GetFunctionIllustrateRequest();
        trialRequest.setFunctionCode("set_process_secret");
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/function-illustrate")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_1)
                                .header(HEADER_TENANT_ID, TestAccountConstants.BASE_SUBJECT_1)
                                .header(HEADER_CLIENT_ID,"WEB")
                                .content(JSON.toJSONString(trialRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.supportTrial").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.trialInfo").exists())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.needGuide").value(true));

        // 支持试用的case-非管理员
        GetFunctionIllustrateRequest notAdminTrialRequest = new GetFunctionIllustrateRequest();
        notAdminTrialRequest.setFunctionCode("set_process_secret");
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/function-illustrate")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_3)
                                .header(HEADER_TENANT_ID, TestAccountConstants.BASE_SUBJECT_1)
                                .header(HEADER_CLIENT_ID,"WEB")
                                .content(JSON.toJSONString(notAdminTrialRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.supportTrial").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.trialInfo").exists())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.needGuide").value(true));
        GetFunctionIllustrateRequest LimitRequest=new GetFunctionIllustrateRequest();
        LimitRequest.setFunctionCode("member_manage");
        LimitRequest.setFunctionLimitIllustrate(true);
        LimitRequest.setFunctionLimitScene("DEFAULT");
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/function-illustrate")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_3)
                                .header(HEADER_TENANT_ID, TestAccountConstants.BASE_SUBJECT_1)
                                .header(HEADER_CLIENT_ID,"WEB")
                                .content(JSON.toJSONString(LimitRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.illustrate.docTitle").value("企业成员数量超出限制"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.illustrate.docContent").value("当前企业版本为基础版,企业成员限制最多20人,可升级版本后继续添加人员"));
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/function-illustrate")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_1)
                                .header(HEADER_TENANT_ID, TestAccountConstants.BASE_SUBJECT_1)
                                .header(HEADER_CLIENT_ID,"WEB")
                                .content(JSON.toJSONString(LimitRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.illustrate.docTitle").value("企业成员数量超出限制"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.illustrate.docContent").value("当前企业版本为基础版,企业成员限制最多20人,可升级版本后继续添加人员"));
    }

    @Test(description = "版本功能信息列表")
    public void testQueryVersionInfos() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/version/function-infos")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_1)
                                .header(HEADER_TENANT_ID, TestAccountConstants.SENIOR_SUBJECT_1))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }


    @Test(description = "获取升级引导信息")
    public void testGetUpgradeIllustrate() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/saas-common/vipmanage/upgrade-illustrate")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, TestAccountConstants.ACCOUNT_ID_1)
                                .header(HEADER_TENANT_ID, TestAccountConstants.SENIOR_SUBJECT_1)
                                .header(HEADER_CLIENT_ID,"FEI_SHU"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.qrCodeType").isString());
    }


    @Test(description = "查询试用记录")
    public void testQueryApplyLog() throws Exception {
        QueryApplyLogInputDTO queryApplyLogInputDTO = new QueryApplyLogInputDTO();
        queryApplyLogInputDTO.setFuncCode("set_process_secret");
        queryApplyLogInputDTO.setSubjectOid(TestAccountConstants.BASE_SUBJECT_1);
        queryApplyLogInputDTO.setLanguage("zh-CN");
        queryApplyLogInputDTO.setOperatorId(TestAccountConstants.ACCOUNT_ID_1);
        queryApplyLogInputDTO.setApplicantOid(TestAccountConstants.ACCOUNT_ID_4);
        queryApplyLogInputDTO.setApplicantSubjectOid(TestAccountConstants.BASE_SUBJECT_1);
        QueryApplyLogOutputDTO logOutputDTO = trialFunctionCodeService.queryApplyLog(queryApplyLogInputDTO);
        Assert.assertNotNull(logOutputDTO);
    }
}
