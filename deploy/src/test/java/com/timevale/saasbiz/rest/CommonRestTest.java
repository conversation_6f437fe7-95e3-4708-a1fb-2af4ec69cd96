package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.rest.bean.common.request.CommonTenantQueryListRequest;
import com.timevale.saasbiz.rest.bean.common.request.FunctionUsedRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

import static com.timevale.saasbiz.constant.TestAccountConstants.*;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR>
 * @since 2023-03-15 09:54
 */
@Slf4j
@JsonDataIgnore
public class CommonRestTest extends BaseServiceTest {

    @Test(description = "根据租户id查询租户信息,包含已注销的")
    public void testTenantList() throws Exception {
        CommonTenantQueryListRequest request = new CommonTenantQueryListRequest();
        request.setOidList(Lists.newArrayList("eaaaaa547b42426db6a6e423a5cf1f95"));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/saas-common/tenant-list")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(request))
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").isNotEmpty());
    }

    @Test(description = "测试功能开关")
    public void testFunctionSwitch() throws Exception {
        MvcResult result =
                mockMvc.perform(MockMvcRequestBuilders.get("/v2/function/switch"))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();

        // appid黑名单
        mockMvc.perform(MockMvcRequestBuilders.get("/v2/function/switch")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_APP_ID,"3438757421"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Boolean.FALSE));

        // 企业gid黑名单
        mockMvc.perform(MockMvcRequestBuilders.get("/v2/function/switch")
                        .header(HEADER_TENANT_ID, AI_SUBJECT_1)
                        .header(HEADER_APP_ID,"3438757422"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Boolean.FALSE));

        mockMvc.perform(MockMvcRequestBuilders.get("/v2/function/switch")
                        .header(HEADER_APP_ID,"3438757422")
                        .param("functionKey","h5_auto_open_app"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Boolean.TRUE));

        Assert.assertNotNull(result);
    }

    @Test(description = "测试用户保活")
    public void testKeepAlive() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.put("/v1/saas-common/user/keep-alive"))
                .andExpect(result -> log.info("response:{}", result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "查询功能在当前企业是否有使用记录")
    public void testQueryUsed() throws Exception {
        FunctionUsedRequest request = new FunctionUsedRequest();
        // 自动归档规则
        request.setFunctionCode("intelligent_auto_archive");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 合同审批模板
        request.setFunctionCode("org_approve_template_manage");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 部门管理
        request.setFunctionCode("org_department_manage");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 专属云是否在合同偏好可见
        request.setFunctionCode("dedicated_cloud");
        Map<String, Object> dedicatedCloudMap = new HashMap<>();
        dedicatedCloudMap.put("checkPreferenceShow", true);
        request.setExtendParam(dedicatedCloudMap);
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 信息采集器
        request.setFunctionCode("info_collect");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 智能台账 · 字段库
        request.setFunctionCode("field_management");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 智能台账
        request.setFunctionCode("intelligent_standbook");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 纸质文件
        request.setFunctionCode("offline_contract_management");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 关联企业管理
        request.setFunctionCode("affiliated_enterprises");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 模板授权
        request.setFunctionCode("template_grant");
        Map<String, Object> map = new HashMap<>();
        map.put("templateId", "62d381ce26d249359b952a7ba0dfb384");
        request.setExtendParam(map);
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        map.put("templateId", "a0e2c5bb86c24f24a322fb29672dad41");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));

        // 用印审批模板
        request.setFunctionCode("use_seal_approve");
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, SENIOR_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(true));
        mockMvc.perform(MockMvcRequestBuilders.post("/v1/common/function/used")
                        .header(HEADER_TENANT_ID, BASE_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, BASE_SUBJECT_ADMIN)
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.used").value(false));
    }
}
