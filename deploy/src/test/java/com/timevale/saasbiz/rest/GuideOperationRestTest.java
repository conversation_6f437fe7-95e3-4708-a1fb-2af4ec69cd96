package com.timevale.saasbiz.rest;

import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.operation.QueryWecomGuideAccountDTO;
import com.timevale.saasbiz.operation.GuideOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 *
 * @date 2023/4/7
 */
@Slf4j
@JsonDataIgnore
public class GuideOperationRestTest extends BaseServiceTest {
    @Autowired GuideOperationService guideOperationService;

    @Test(description = "获取企微运营账号")
    public void queryWecomGuideAccount() {
        QueryWecomGuideAccountDTO accountDTO =
                guideOperationService.queryWecomGuideAccount(
                        "d9ec3169a6e04d148e5a8cc08ab3c13d",
                        "afa54c4fc26644ddb66b255467ef2325",
                        true);
        Assert.assertNotNull(accountDTO, "获取引导微信号失败");
    }

    @Test(description = "获取企微运营账号(非CRM)")
    public void queryWecomGuideAccountApply() {
        QueryWecomGuideAccountDTO accountDTO =
                guideOperationService.queryWecomGuideAccount(
                        "d9ec3169a6e04d148e5a8cc08ab3c13d",
                        "b27913606bfd4f74925c36367e2cc985",
                        false);
        Assert.assertNotNull(accountDTO, "获取引导微信号失败");
    }

    @Test(description = "获取企微二维码")
    public void getQrcode() {
        String url =
                guideOperationService.queryWecomContactWay(
                        "b27913606bfd4f74925c36367e2cc985", "", "MingXiu-XieJia");
        Assert.assertNull(url);
    }
}
