package com.timevale.saasbiz.rest;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.SENIOR_SUBJECT_1;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;

import com.timevale.saasbiz.rest.bean.RestResult;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-05-08 17:11
 */
@Deprecated
@Slf4j
@JsonDataIgnore
public class ApprovalFlowAdaptRestTest extends BaseServiceTest {

    @Test
    public void testCountApproval() throws Exception {
        MockHttpServletRequestBuilder requestBuilder =
                MockMvcRequestBuilders.get("/v1/approval/adapt-count")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                        .header(HEADER_CLIENT_ID, CLIENT_ID_WEB);

        MvcResult mvcResult =
                mockMvc.perform(requestBuilder)
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();

        Assert.assertNotNull(mvcResult, "mvcResult is null");
        Assert.assertNotNull(mvcResult.getResponse(), "response is null");

        String content = mvcResult.getResponse().getContentAsString();
        Assert.assertTrue(StringUtils.isNotBlank(content), "content is blank");
        //
        RestResult<ApprovalFlowAdaptRest.CountApprovalFlowListResponse> baseResult =
                JsonUtilsPlus.parseObject(
                        content,
                        new TypeReference<
                                RestResult<
                                        ApprovalFlowAdaptRest.CountApprovalFlowListResponse>>() {});
        Assert.assertTrue(
                Objects.nonNull(baseResult) && Objects.nonNull(baseResult.getData()),
                "data is null");
    }
}
