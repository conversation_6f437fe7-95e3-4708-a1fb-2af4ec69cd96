package com.timevale.saasbiz.rest;

import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.rest.bean.cooperation.request.CooperationBehaviorRequest;
import org.hamcrest.Matchers;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.constant.TestAccountConstants.EPAAS_TEMPLATE_ADMIN_001_OID;
import static com.timevale.saasbiz.constant.TestAccountConstants.EPAAS_TEMPLATE_SUBJECT_001_OID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-biz
 * @Description:
 * @date Date : 2024年05月27日 18:59
 */
public class CooperationRestTest extends BaseServiceTest {

    @Test(description = "获取填写页按钮")
    public void testCooperationBehavior() throws Exception {
        CooperationBehaviorRequest request = new CooperationBehaviorRequest();
        request.setProcessId("790caee31f0a49ad9790990d9de280c1");
        request.setTaskId(1419897L);

        mockMvc.perform(MockMvcRequestBuilders.post("/v1/cooperation/ecd3ff0eb5024d608a8b6150a0852996/behavior-buttons")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JsonUtils.obj2json(request))
                        .header(HEADER_TENANT_ID, EPAAS_TEMPLATE_SUBJECT_001_OID)
                        .header(HEADER_OPERATOR_ID, EPAAS_TEMPLATE_ADMIN_001_OID)
                        .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));
    }


}
