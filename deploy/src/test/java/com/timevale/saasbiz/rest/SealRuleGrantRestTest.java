package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.timevale.footstone.seal.facade.enums.RuleGrantStatusEnum;
import com.timevale.footstone.seal.facade.enums.SealGrantTypeEnum;
import com.timevale.footstone.seal.facade.saas.input.AddRulesInput;
import com.timevale.footstone.seal.facade.saas.input.SecondSealGrantAddInput;
import com.timevale.footstone.seal.facade.saas.output.AddRulesOutput;
import com.timevale.footstone.seal.facade.saas.output.SecondSealGrantBatchAddOutput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.TestJsonObj;
import com.timevale.saas.common.manage.common.service.model.base.BaseResult;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.integration.seal.SealRuleGrantClient;
import com.timevale.saasbiz.integration.seal.SecondSealGrantClient;
import com.timevale.saasbiz.model.bean.seal.dto.SealGrantDTO;
import com.timevale.saasbiz.model.bean.seal.dto.input.DeleteBatchSealGrantInputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.input.ListRuleGrantInputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.output.ListRuleGrantOutputDTO;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.seal.request.*;
import com.timevale.saasbiz.rest.bean.seal.response.*;
import com.timevale.saasbiz.service.seal.SealRuleGrantService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * <AUTHOR>
 * @since 2023-05-31 17:04
 */
@Slf4j
public class SealRuleGrantRestTest extends BaseServiceTest {
    private static final String ESIGN_TEST_APP_ID = "3438757422";
    private static final String NOT_DELETE_SEAL_ID = "323c14d9-ff46-4150-8e93-d36297237db3";

    @Autowired private SealRuleGrantClient sealRuleGrantClient;
    @Autowired private SecondSealGrantClient secondSealGrantClient;
    @Autowired private SealRuleGrantService sealRuleGrantService;
    @Test(description = "授权企业/成员列表冒烟")
    public void testGetRuleGrantList() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(
                                                "/v1/saas-common/rules-grant/seals/rule-grant-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .param("orgId", ORG_OID)
                                        .param("ruleGrantStatus", "ALL")
                                        .param("type", "1")
                                        .param("resourceId", NOT_DELETE_SEAL_ID)
                                        .param("offset", "0")
                                        .param("size", "10"))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<GetRuleGrantListResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<GetRuleGrantListResponse>>() {});

        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getData());
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData().getGrantList()));
    }

    @Test(description = "获取企业外授权信息")
    public void testGetRuleGrantListOutOrg() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(
                                                "/v1/saas-common/rules-grant/seals/rule-grant-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .param("orgId", ORG_OID)
                                        .param("ruleGrantStatus", "ALL")
                                        .param("type", "2")
                                        .param("resourceId", NOT_DELETE_SEAL_ID)
                                        .param("offset", "0")
                                        .param("size", "10"))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<GetRuleGrantListResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<GetRuleGrantListResponse>>() {});

        Assert.assertNotNull(response);
        Assert.assertNotNull(response.getData());
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData().getGrantList()));
    }

    @Test(description = "批量删除印章授权冒烟")
    public void testDeleteBatchRuleGrant() throws Exception {
        // 印章授权
        AddRulesInput input = new AddRulesInput();
        input.setAccountOid(ACCOUNT_OID);
        input.setOrgOid(ORG_OID);
        input.setAppId(ESIGN_TEST_APP_ID);
        input.setResourceId(NOT_DELETE_SEAL_ID);
        input.setRoleKey("SEAL_USER");
        input.setScopeList(Collections.singleton("ALL"));
        input.setNotifySetting(true);
        input.setGrantedAccountIds(Collections.singletonList("100476ad9a72425fad13e06ddaf1d218"));
        input.setEffectiveTime(System.currentTimeMillis());
        input.setExpireTime(DateUtils.addMonths(new Date(System.currentTimeMillis()), 3).getTime());
        input.setGrantType(1);
        input.setOperatorOid("307a45c17d394563885f861dd961d7e8");
        input.setOperatorTime(System.currentTimeMillis());
        input.setTerminal("WEB");
        input.setSealId("323c14d9-ff46-4150-8e93-d36297237db3");
        AddRulesOutput addRulesOutput = sealRuleGrantClient.addSealGrant(input);

        Assert.assertNotNull(addRulesOutput, "addRulesOutput is null");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(addRulesOutput.getRuleGrantIds()), "grant list empty");

        // 批量删除授权
        DeleteBatchSealGrantRequest request = new DeleteBatchSealGrantRequest();
        request.setRuleGrantIds(addRulesOutput.getRuleGrantIds());
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v1/saas-common/rules-grant/seals/delete-batch-grant")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                .header(HEADER_TENANT_ID, ORG_OID)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test(description = "批量删除二级印章授权冒烟")
    public void testDeleteBatchSecondRuleGrant() throws Exception {
        // 二级印章授权
        SecondSealGrantAddInput input = new SecondSealGrantAddInput();
        input.setSealGrantBizId("ea6a66a2-9a91-4e53-8878-e8e8fa7589d2");
        input.setGrantedOids(Collections.singleton("01ba9af20c9247d1a1214eaf32335f28"));
        input.setGranterOid(ACCOUNT_OID);
        input.setGrantRoleKey("SEAL_USER");
        input.setGrantOrgId(ORG_OID);
        input.setEffectiveTime(new Date(1722268900200L));
        input.setExpireTime(new Date(1785427189000L));
        input.setAppId(APP_ID);
        input.setOrgOid(ORG_OID);
        input.setOperatorOid(ACCOUNT_OID);
        input.setOperatorTime(System.currentTimeMillis());
        input.setTerminal("WEB");
        input.setSealId("c60ecc4c-d0e7-4172-bbe1-29ac390ad29e");
        input.setTemplateIds(Collections.singleton("ALL"));

        SecondSealGrantBatchAddOutput addOutput = secondSealGrantClient.addGrantOfInternal(input);
        Assert.assertNotNull(addOutput, "addRulesOutput is null");
        Assert.assertTrue(
                CollectionUtils.isNotEmpty(addOutput.getSecSealGrantBizIds()), "grant list empty");

        // 批量删除二级授权
        DeleteBatchSealSecondGrantRequest request = new DeleteBatchSealSecondGrantRequest();
        request.setSecSealGrantBizIds(addOutput.getSecSealGrantBizIds());
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v1/saas-common/rules-grant/seals/delete-batch-second-grant")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                .header(HEADER_TENANT_ID, ORG_OID)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }

    @Test(description = "分页查询印章列表冒烟测试")
    public void testPageQuerySeals() throws Exception {
        PageQuerySealsRequest request = new PageQuerySealsRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setSealOwnerOid(ORG_OID);
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/page-seals")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<PageQuerySealsResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<PageQuerySealsResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getData().getSealList().size() > 0);
    }

    @Test(description = "查看二次授权列表冒烟")
    public void testGetSecondList() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get(
                                        "/v1/saas-common/rules-grant/seals/get-second-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                .header(HEADER_TENANT_ID, ORG_OID)
                                .param("pageNum", "1")
                                .param("sealGrantBizId", "dd103e64-d36e-4aa1-a7d3-472a1f8d49e1")
                                .param("pageSize", "10"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.result").isNotEmpty());
    }

    @Test(description = "分页查询集团印章列表冒烟测试")
    public void testPageQueryGroupSeals() throws Exception {

        PageQuerySealsRequest request = new PageQuerySealsRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setQueryAllGroupSeals(true);
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/page-seals")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<PageQuerySealsResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<PageQuerySealsResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getData().getSealList().size() > 0);
    }

    @Test(description = "分页查询子企业印章列表冒烟测试")
    public void testPageQuerySubSeals() throws Exception {

        PageQuerySealsRequest request = new PageQuerySealsRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setSealOwnerOid("ccb284fadd34405ba68a0c710cccc74f");
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/page-seals")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<PageQuerySealsResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<PageQuerySealsResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getData().getSealList().size() > 0);
    }

    @Test(description = "批量印章查询剩余可用授权数量冒烟测试")
    public void testGetBatchSealGrantNum() throws Exception {

        BatchSealGrantNumRequest request = new BatchSealGrantNumRequest();
        request.setGrantLevel(1);
        request.setResourceOwnerOid("ccb284fadd34405ba68a0c710cccc74f");
        request.setResourceBizIds(Lists.newArrayList("68de1396-e3a1-4c2a-a875-52345f63a948"));
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/get-batch-grant-num")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<BatchSealGrantNumResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<BatchSealGrantNumResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(StringUtils.isNotBlank(response.getData().getLimitSealId()));
    }

    @Test(description = "批量印章查询剩余可用授权数量（二次授权的）冒烟测试")
    public void testGetBatchSealGrantNumSecond() throws Exception {
        BatchSealGrantNumRequest request = new BatchSealGrantNumRequest();
        request.setGrantLevel(2);
        request.setResourceOwnerOid("97298c2243c14ee792e54492058f0391");
        request.setResourceBizIds(Lists.newArrayList("ea6a66a2-9a91-4e53-8878-e8e8fa7589d2"));
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/get-batch-grant-num")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<BatchSealGrantNumResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<BatchSealGrantNumResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(StringUtils.isNotBlank(response.getData().getLimitSealId()));
    }

    @Test(description = "分页查询被授权印章列表冒烟测试")
    public void testPageQueryGrantedSeals() throws Exception {

        PageQueryGrantedSealsRequest request = new PageQueryGrantedSealsRequest();
        request.setGrantedOid("97298c2243c14ee792e54492058f0391");
        request.setDownloadFlag(true);
        request.setPageNo(1);
        request.setPageSize(10);
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/page-granted-seals")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<PageQueryGrantedSealsResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<PageQueryGrantedSealsResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getData().getGrantedSealList().size() > 0);
    }

    @Test(description = "分页查询被授权印章列表冒烟测试--过滤待生效状态")
    public void testPageQueryGrantedSeals_ByWaitValid() throws Exception {

        PageQueryGrantedSealsRequest request = new PageQueryGrantedSealsRequest();
        request.setGrantedOid("97298c2243c14ee792e54492058f0391");
        request.setDownloadFlag(true);
        request.setExistValidFlag(3);
        request.setPageNo(1);
        request.setPageSize(10);
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/page-granted-seals")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<PageQueryGrantedSealsResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<PageQueryGrantedSealsResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(response.getData().getGrantedSealList().size() > 0);
    }

    @Test
    public void batchSealGrantTestByHeader() throws Exception {
        String operatorOid = "0e23abffd0c14d0c8bd17a0332c60469";
        String tenantOid = "d9ec3169a6e04d148e5a8cc08ab3c13d";
        TestJsonObj testJsonObj = jsonDataMap.get("batchSealGrantRequestByHeader");
        BatchSealGrantRequest request =
                (BatchSealGrantRequest) testJsonObj.getReqData();
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/batch-grant")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, operatorOid)
                                        .header(HEADER_TENANT_ID, tenantOid)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<BatchSealGrantResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<BatchSealGrantResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertNull(response.getData().getRedirectUrl());
        // 清理数据
        deleteSealRuleGrant(operatorOid, tenantOid, request);

        // 集团批量印章授权
        GroupBatchSealGrantRequest groupRequest = new GroupBatchSealGrantRequest();
        BeanUtils.copyProperties(groupRequest, request);
        groupRequest.setGrantedResourceIds(request.getGrantedResourceIds());
        groupRequest.setGrantType(SealGrantTypeEnum.ORG_EXTERNAL.getCode());
        groupRequest.setGrantedAccountIds(Lists.newArrayList("29d712fce8cc415398b1dc32c5b4281b"));
        mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/rules-grant/seals/group-batch-grant")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, operatorOid)
                                        .header(HEADER_TENANT_ID, tenantOid)
                                        .content(JsonUtils.obj2json(groupRequest)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<BatchSealGrantResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertNull(response.getData().getRedirectUrl());

        //清理数据
        deleteSealRuleGrant(operatorOid, tenantOid, request);
    }

    /**
     * 清除印章授权数据
     * @param operatorOid
     * @param tenantOid
     * @param request
     */
    private void deleteSealRuleGrant(String operatorOid, String tenantOid, BatchSealGrantRequest request) {
        ListRuleGrantInputDTO ruleGrantInputDTO = new ListRuleGrantInputDTO();
        ruleGrantInputDTO.setOrgId(request.getResourceOwnerOid());
        ruleGrantInputDTO.setResourceId(request.getGrantedResourceIds().get(0));
        ruleGrantInputDTO.setRuleGrantStatus(RuleGrantStatusEnum.INVALID.name());
        ruleGrantInputDTO.setOffset(0);
        ruleGrantInputDTO.setSize(10);
        ruleGrantInputDTO.setAppId(request.getAppId());
        ruleGrantInputDTO.setAccountOid(request.getResourceOwnerAdminOid());
        ruleGrantInputDTO.setOperatorOid(operatorOid);
        ListRuleGrantOutputDTO listRuleGrantOutputDTO1 = sealRuleGrantService.listRuleGrant(ruleGrantInputDTO);
        Assert.assertNotNull(listRuleGrantOutputDTO1);
        Assert.assertNotNull(listRuleGrantOutputDTO1.getGrantList());

        List<String> ruleGrantList = Stream.of(listRuleGrantOutputDTO1.getGrantList()).flatMap(List::stream).map(SealGrantDTO::getRuleGrantId).collect(Collectors.toList());
        DeleteBatchSealGrantInputDTO dto = new DeleteBatchSealGrantInputDTO();
        dto.setOrgOid(request.getResourceOwnerOid());
        dto.setRuleGrantIds(ruleGrantList);
        dto.setAppId(request.getAppId());
        dto.setOperatorTenantOid(tenantOid);
        dto.setOperatorOid(operatorOid);
        sealRuleGrantService.deleteBatchGrant(dto);
    }

//    @Test
//    public void downloadOfflineLegalAuthTest() throws Exception {
//        DownloadOfflineLegalAuthLetterRequest request = new DownloadOfflineLegalAuthLetterRequest();
//        request.setLegalName("黄振衡");
//        request.setLegalNo("P1232345657");
//        request.setLegalNoType("CRED_PSN_PASSPORT");
//        mockMvc.perform(
//                        MockMvcRequestBuilders.post(
//                                        "/v1/saas-common/rules-grant/seals/download-offline-legal-auth")
//                                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                                .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
//                                .header(HEADER_OPERATOR_ID, "72526616536d40b9864c79cd1954dbd1")
//                                .header(HEADER_TENANT_ID, "c4a8d111eecd4786815e0a94c3bce354")
//                                .content(JsonUtils.obj2json(request)))
//                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
//                .andExpect(MockMvcResultMatchers.jsonPath("$.data.downloadUrl").isNotEmpty());
//    }

    @Test(description = "获取立即授权地址")
    public void getGrantUrlTest() throws Exception {
        // 印章授权升级单测勿动 印章
        DoGrantRequest request = new DoGrantRequest();
        request.setRuleGrantId("828460aa-c5c1-4b65-ae20-b122d3177515");
        request.setSealOwnerOid("97298c2243c14ee792e54492058f0391");

        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v1/saas-common/rules-grant/seals/get-grant-url")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                .header(HEADER_TENANT_ID, ORG_OID)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.url").isNotEmpty()).andReturn();
    }

    @Test(description = "根据分组id查询印章授权详情")
    public void querySealGrantGroupDetailTest() throws Exception {
        QuerySealGrantGroupDetailRequest request = new QuerySealGrantGroupDetailRequest();
        request.setGroupId("e305eb75c24644c4bc4b6021786d61a8");
        request.setOffset(0);
        request.setSize(10);

        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v1/saas-common/rules-grant/seals/query-seal-grant-group-detail")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.details").isNotEmpty());
    }

    @Test(description = "中间页 扫码或立即签署")
    public void createSealGrantConfirmUrlTest() throws Exception {
        CreateSealGrantConfirmUrlRequest request = new CreateSealGrantConfirmUrlRequest();
        // groupId 来源于 querySealGrantGroupDetailTest context解析出的groupId
        request.setGroupId("e305eb75c24644c4bc4b6021786d61a8");
        request.setAuthConfirmMethod(0);

        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v1/saas-common/rules-grant/seals/create-seal-grant-confirm-url")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.url").isNotEmpty());
    }

    @Test(description = "中间页 轮询查询印章授权结果")
    public void querySealGrantResultTest() throws Exception {
        QuerySealGrantResultRequest request = new QuerySealGrantResultRequest();
        request.setGroupId("e305eb75c24644c4bc4b6021786d61a8");

        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v1/saas-common/rules-grant/seals/query-seal-grant-result")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.groupStatus").isNotEmpty());
    }


    @Test(description = "创建企业章前置校验印章名字不重复")
    public void testCreateSealPreCheck() throws Exception {
        String sealName = "靖安单元测试印章";

        CreateOfficialSealPreCheckRequest request = new CreateOfficialSealPreCheckRequest();
        request.setSealName(sealName);
        request.setSealOwnerOid(ORG_OID);

        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/organizations/seals/create-seal-pre-check")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JSONObject.toJSONString(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();

        BaseResult<CreateOfficialSealPreCheckResponse> result =
                JSON.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new com.alibaba.fastjson.TypeReference<BaseResult<CreateOfficialSealPreCheckResponse>>() {
                        });
        boolean nameExist = result.getData().isNameExist();
        Assert.assertFalse(nameExist);
    }

    @Test(description = "被授权企业列表")
    public void testGetRuleGrantedList() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get("/v1/saas-common/rules-grant/seals/get-rule-granted-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .param("orgId", "bfcd509c78214873aacbf3329c595176")
                                        .param("sealId", "9ef83c60-567f-4ead-9345-5d69bfd13d01")
                                        .param("sealOwnerOid", "bfcd509c78214873aacbf3329c595176")
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, "72526616536d40b9864c79cd1954dbd1")
                                        .header(HEADER_TENANT_ID, "bfcd509c78214873aacbf3329c595176"))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();

        BaseResult<GetRuleGrantedListResponse> result =
                JSON.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new com.alibaba.fastjson.TypeReference<BaseResult<GetRuleGrantedListResponse>>() {
                        });
        Integer total = result.getData().getTotal();
        Assert.assertTrue(total > 0);
    }

}
