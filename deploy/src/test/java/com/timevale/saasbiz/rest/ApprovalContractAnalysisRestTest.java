package com.timevale.saasbiz.rest;


import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisDetailResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisFileUrlResponse;
import com.timevale.saasbiz.rest.bean.approval.response.ApprovalContractAnalysisPageResponse;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * TODO 功能说明
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@JsonDataIgnore
public class ApprovalContractAnalysisRestTest extends BaseServiceTest {
    private static final String BASE_URL = "/v2/approval/contract-analysis/";

    @Test
   public void detail() throws Exception {
        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(BASE_URL + "detail")
                                        .param("compareId", "c21552cf54f64306829e9cab61ea119f")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(
                                                HEADER_TENANT_ID,
                                                "849b1e1fce2b4568b0aa2b709daabea4")
                                        .header(
                                                HEADER_OPERATOR_ID,
                                                "dfb118e404794f5fba158c6e97de5518"))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<ApprovalContractAnalysisDetailResponse> saveRestResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ApprovalContractAnalysisDetailResponse>>() {});
        Assert.assertNotNull(saveRestResult.getData());
    }

    @Test
    public  void getCompareDownloadUrl() throws Exception {
        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(BASE_URL + "file-url")
                                        .param("compareId", "c21552cf54f64306829e9cab61ea119f")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(
                                                HEADER_TENANT_ID,
                                                "849b1e1fce2b4568b0aa2b709daabea4")
                                        .header(
                                                HEADER_OPERATOR_ID,
                                                "dfb118e404794f5fba158c6e97de5518"))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<ApprovalContractAnalysisFileUrlResponse> saveRestResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<
                                RestResult<ApprovalContractAnalysisFileUrlResponse>>() {});
        Assert.assertNotNull(saveRestResult.getData());
    }

    @Test
    public  void list() throws Exception {
        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(BASE_URL + "list")
                                        .param("analysisBizId", "ce36fd02301b433cadc7f7eb99c87756")
                                        .param("analysisBizType", "CONTRACT")
                                        .param("pageNum", "1")
                                        .param("pageSize", "10")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(
                                                HEADER_TENANT_ID,
                                                "849b1e1fce2b4568b0aa2b709daabea4")
                                        .header(
                                                HEADER_OPERATOR_ID,
                                                "dfb118e404794f5fba158c6e97de5518"))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<ApprovalContractAnalysisPageResponse> saveRestResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<
                                RestResult<ApprovalContractAnalysisPageResponse>>() {});
        Assert.assertNotNull(saveRestResult.getData());
        Assert.assertTrue(CollectionUtils.isNotEmpty(saveRestResult.getData().getCompareRecordList()));
    }

    @Test
    public  void queryResult() throws Exception {

    }
}
