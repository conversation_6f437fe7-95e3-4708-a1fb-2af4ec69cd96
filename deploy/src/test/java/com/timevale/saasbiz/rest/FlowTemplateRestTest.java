package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.timevale.doccooperation.service.enums.FlowTemplatePreviewSceneEnum;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saas.common.base.util.RequestContextExtUtils;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.enums.flowtemplate.FlowTemplateAuthQueryEnum;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.rest.bean.flowtemplate.request.*;
import com.timevale.saasbiz.rest.bean.flowtemplate.response.FlowTemplateChannelDataSourceListResponse;
import lombok.extern.slf4j.Slf4j;
import org.hamcrest.Matchers;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.timevale.privilege.service.enums.BuiltinRoleType.TEMP_UPDATE;
import static com.timevale.privilege.service.enums.BuiltinRoleType.TEMP_USE;
import static com.timevale.saasbiz.constant.TestAccountConstants.*;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;
import static com.timevale.saasbiz.model.exception.SaasBizResultCode.FLOW_TEMPLATE_NOT_EXIST;
import static com.timevale.saasbiz.model.exception.SaasBizResultCode.SAAS_BIZ_ERROR;

/**
 * <AUTHOR>
 * @since 2023-08-04 18:05
 */
@Slf4j
@JsonDataIgnore
public class FlowTemplateRestTest extends BaseServiceTest {

    private static final String FLOW_TEMPLATE_ID1 = "4d7ad056b731473f90746b7fd9d37122";

    private static final String FLOW_TEMPLATE_ID3 = "4d5d5b7b0ce84d7dad1f6e023e371a30";

    private static final String FLOW_TEMPLATE_DELETE = "f7ed74c788624fcb98b3b1232c28ac0e";

    private static final String FLOW_TEMPLATE_DOC1 = "b91558b11b584cacb517edd15444b917";

    private static final String APP_ID = "**********";

    private static final String TENANT_ID1 = SENIOR_SUBJECT_1;

    private static final String TENANT_ID2 = NO_BILL_SUBJECT_1;

    private static final String OPERATOR_ID1 = ACCOUNT_ID_1;

    private static final String OPERATOR_ID2 = ACCOUNT_ID_4;

    private static final String ROLE_ID1 = "e0d7abd0635344b6858dac9fcaff9229";

    private static final String ROLE_ID2 = "de378d97947c475fa4870093b5c84066";

    @Test(description = "测试流程模板的文件列表")
    public void testListFlowTemplateFile() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.GET,
                                        "/v2/processes/flowTemplates/files/" + FLOW_TEMPLATE_ID1)
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.data.files")
                                .value(Matchers.notNullValue()));
    }

    @Test(description = "测试删除流程模板")
    public void testDeleteFlowTemplate() throws Exception {
        /** 已删除的流程模板 */
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.DELETE,
                                        "/v2/processes/flowTemplates/" + FLOW_TEMPLATE_DELETE)
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(result -> Assert.assertTrue(200 == result.getResponse().getStatus()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(FLOW_TEMPLATE_NOT_EXIST.getCode()));
    }

    @Test(description = "测试启用流程模板状态")
    public void testEnableFlowTemplate() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.PUT,
                                        "/v2/processes/flowTemplates/"
                                                + FLOW_TEMPLATE_ID1
                                                + "/enable")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.PUT,
                                        "/v2/processes/flowTemplates/"
                                                + FLOW_TEMPLATE_ID1
                                                + "/enable")
                                .header(HEADER_TENANT_ID, TENANT_ID2)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.FLOW_TEMPLATE_OWNER_FAIL.getCode()));
    }

    @Test(description = "测试禁用流程模板状态")
    public void testDisableFlowTemplate() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.PUT,
                                        "/v2/processes/flowTemplates/"
                                                + FLOW_TEMPLATE_ID1
                                                + "/disable")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.PUT,
                                        "/v2/processes/flowTemplates/"
                                                + FLOW_TEMPLATE_ID1
                                                + "/disable")
                                .header(HEADER_TENANT_ID, TENANT_ID2)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.FLOW_TEMPLATE_OWNER_FAIL.getCode()));
    }

    @Test(description = "测试流程模板批量授权")
    public void testBatchAuthFlowTemplate() throws Exception {
        // 1.多个流程模板授权
        FlowTemplateBatchAuthRequest request = new FlowTemplateBatchAuthRequest();
        List<FlowTemplateAuthRequest> flowTemplateAuthList = new ArrayList<>();

        // 2.授权单个流程模板
        FlowTemplateAuthRequest flowTemplateAuthRequest = new FlowTemplateAuthRequest();

        // 3.准备授权对象列表

        // 4.准备授权单个流程模板
        flowTemplateAuthRequest.setAuthList(getAuthList());
        flowTemplateAuthRequest.setFlowTemplateId(FLOW_TEMPLATE_ID1);
        // 5.准备多个授权流程模板
        flowTemplateAuthList.add(flowTemplateAuthRequest);
        request.setFlowTemplateAuthList(flowTemplateAuthList);
        mockMvc.perform(
                        MockMvcRequestBuilders.request(
                                        HttpMethod.POST, "/v2/flowTemplates/batchAuth")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.nullValue()));
    }

    private List<FlowTemplateAuthObjectRequest> getAuthList() {
        List<FlowTemplateAuthObjectRequest> authList = new ArrayList<>();
        FlowTemplateAuthObjectRequest obj1 = new FlowTemplateAuthObjectRequest();
        obj1.setAuthId("f73027705d2243d58d065b33ec91643c");
        obj1.setAccountOid(OPERATOR_ID1);
        obj1.setRoleId(ROLE_ID1);
        obj1.setRoleKey(TEMP_UPDATE);
        obj1.setType(2);
        FlowTemplateAuthObjectRequest obj2 = new FlowTemplateAuthObjectRequest();
        obj2.setAuthId("2539334030d74d8ca46a1e779fc6fd82");
        obj2.setAccountOid(OPERATOR_ID2);
        obj2.setRoleId(ROLE_ID2);
        obj2.setRoleKey(TEMP_USE);
        obj2.setType(2);
        FlowTemplateAuthObjectRequest obj3 = new FlowTemplateAuthObjectRequest();
        obj3.setAuthId("fcb23c197e8d47cea7a4bf6742a4af88");
        obj3.setRoleId(ROLE_ID2);
        obj3.setRoleKey(TEMP_USE);
        obj3.setType(3);
        authList.add(obj1);
        authList.add(obj2);
        authList.add(obj3);
        return authList;
    }

    @Test(description = "测试流程模板列表")
    public void testListFlowTemplate() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flowTemplates")
                                .param("pageNum", "1")
                                .param("pageSize", "10")
                                .param("flowTemplateName", "悔桑")
                                .param("containShared", "true")
                                .param("containsDynamic", "true")
                                .param("status", "")
                                .param("categoryId", "")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.data.list")
                                .value(Matchers.notNullValue()));
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flowTemplates")
                                .param("pageNum", "1")
                                .param("pageSize", "10")
                                .param("flowTemplateName", "")
                                .param("containShared", "true")
                                .param("containsDynamic", "true")
                                .param("status", "1")
                                .param("categoryId", "be5373ae644a47febe28ffddbcbc7e54")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.data.list")
                                .value(Matchers.notNullValue()));
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flowTemplates")
                                .param("pageNum", "1")
                                .param("pageSize", "10")
                                .param("flowTemplateName", "")
                                .param("containShared", "true")
                                .param("containsDynamic", "true")
                                .param("status", "")
                                .param("categoryId", "")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID2)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.data.list")
                                .value(Matchers.notNullValue()));
    }

    @Test(description = "测试流程模板权限列表")
    public void testGetPermissionList() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flowTemplates/getPermissionList")
                                .param("flowTemplateId", FLOW_TEMPLATE_ID1)
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flowTemplates/getPermissionList")
                                .param("flowTemplateId", FLOW_TEMPLATE_ID3)
                                .header(HEADER_TENANT_ID, TENANT_ID2)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.FLOW_TEMPLATE_OWNER_FAIL.getCode()));
    }

    @Test(description = "测试AI识别流程模板中文件的控件")
    public void testAiParseStructComponent() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get(
                                        "/v2/flowTemplates/"
                                                + FLOW_TEMPLATE_ID1
                                                + "/files/"
                                                + FLOW_TEMPLATE_DOC1
                                                + "/parseStructComponents")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.data.structComponents")
                                .value(Matchers.notNullValue()));
        mockMvc.perform(
                        MockMvcRequestBuilders.get(
                                        "/v2/flowTemplates/"
                                                + FLOW_TEMPLATE_ID1
                                                + "/files/"
                                                + FLOW_TEMPLATE_DOC1
                                                + "/parseStructComponents")
                                .header(HEADER_TENANT_ID, TENANT_ID2)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(120000403));
    }

    @Test(description = "流程模板批量开启-异步")
    public void testBatchEnableAsync() throws Exception {
        FlowTemplateBatchOperateBaseRequest request = new FlowTemplateBatchAuthAsyncRequest();
        request.setFlowTemplateIds(Lists.newArrayList(FLOW_TEMPLATE_ID1));
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/batch-enable-async")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.nullValue()));
    }

    @Test(description = "流程模板批量禁用-异步")
    public void testBatchDisableAsync() throws Exception {
        FlowTemplateBatchOperateBaseRequest request = new FlowTemplateBatchAuthAsyncRequest();
        request.setFlowTemplateIds(Lists.newArrayList(FLOW_TEMPLATE_ID1));
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/batch-disable-async")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.nullValue()));
    }

    @Test(description = "流程模板批量删除-异步")
    public void testBatchDeleteAsync() throws Exception {
        FlowTemplateBatchOperateBaseRequest request = new FlowTemplateBatchAuthAsyncRequest();
        request.setFlowTemplateIds(Lists.newArrayList(FLOW_TEMPLATE_DELETE));
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/batch-delete-async")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.nullValue()));
    }

    @Test(description = "流程模板批量删除-异步")
    public void testBatchAuthAsync() throws Exception {
        FlowTemplateBatchAuthAsyncRequest request = new FlowTemplateBatchAuthAsyncRequest();
        request.setAuthList(getAuthList());
        request.setFlowTemplateIds(Lists.newArrayList(FLOW_TEMPLATE_ID1));
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/batch-auth-async")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.nullValue()));
    }

    @Test(description = "流程模板批量删除-异步")
    public void testSingleTemplateAuth() throws Exception {
        FlowTemplateDetailQueryRequest request = new FlowTemplateDetailQueryRequest();
        request.setAuthType(FlowTemplateAuthQueryEnum.ROLE.getType());
        request.setFlowTemplateId("3a654eeb8ba44f24a0f6510a294e13d8");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flowTemplates/single-template-auth-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, "5a3de2b6835d4d37afdcabbc69fc232b")
                                .header(HEADER_OPERATOR_ID, "4fdeb4e427ac4d73a695b0ec176e0318")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));

        request.setAuthType("other");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flowTemplates/single-template-auth-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, "5a3de2b6835d4d37afdcabbc69fc232b")
                                .header(HEADER_OPERATOR_ID, "4fdeb4e427ac4d73a695b0ec176e0318")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "流程模板批量删除-异步")
    public void testTemplateAuthConfig() throws Exception {
        TemplateAuthConfigSaveRequest request = new TemplateAuthConfigSaveRequest();
        request.setAuthId("2a1760db7b4741fda801eea517cf6bb6");
        request.setConfigValue("force");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flowTemplates/save-template-auth-config")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, "5a3de2b6835d4d37afdcabbc69fc232b")
                                .header(HEADER_OPERATOR_ID, "4fdeb4e427ac4d73a695b0ec176e0318")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));

        TemplateAuthConfigQueryRequest queryRequest = new TemplateAuthConfigQueryRequest();
        queryRequest.setAuthId("2a1760db7b4741fda801eea517cf6bb6");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flowTemplates/get-template-auth-config")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(queryRequest))
                                .header(HEADER_TENANT_ID, "5a3de2b6835d4d37afdcabbc69fc232b")
                                .header(HEADER_OPERATOR_ID, "4fdeb4e427ac4d73a695b0ec176e0318")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));

        queryRequest = new TemplateAuthConfigQueryRequest();
        queryRequest.setAuthId("b541f1fada934ae5ad2cdbe220d87b9b");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flowTemplates/get-template-auth-config")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(queryRequest))
                                .header(HEADER_TENANT_ID, "5a3de2b6835d4d37afdcabbc69fc232b")
                                .header(HEADER_OPERATOR_ID, "4fdeb4e427ac4d73a695b0ec176e0318")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));

        // 特殊case，模板管理员禁止编辑
        queryRequest = new TemplateAuthConfigQueryRequest();
        queryRequest.setAuthId("9cdf1e3c95ca4001886c888f0ac76bf3");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flowTemplates/get-template-auth-config")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(queryRequest))
                                .header(HEADER_TENANT_ID, "21a38928f32245658cb858ad2a4ee711")
                                .header(HEADER_OPERATOR_ID, "0daa0a5bb81846a0823964cd8762a958")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.modifyConfig").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.modifyTemplate").value(false));
    }

    @Test(description = "流程模板预览-发起时预览")
    public void testTemplatePreview_start() throws Exception {
        FlowTemplatePreviewRequest request = new FlowTemplatePreviewRequest();
        request.setPreviewScene(FlowTemplatePreviewSceneEnum.START_PREVIEW.getPreviewScene());
        request.setFlowTemplateIds(Lists.newArrayList("32b6a7bc7ebe48a5a32d543174c8bf4c"));

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flowTemplates/preview-url")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, EPAAS_TEMPLATE_SUBJECT_001_OID)
                                .header(HEADER_OPERATOR_ID, EPAAS_TEMPLATE_ADMIN_001_OID)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));
    }

    // @Test(description = "创建epaas文件模板")
    @Ignore("epaas问题，先忽略掉")
    public void createDocsTest() throws Exception {
        FlowTemplateCreateTemplateRequest request = new FlowTemplateCreateTemplateRequest();
        FlowTemplateFileIdRequest request1 = new FlowTemplateFileIdRequest();
        request1.setFileId("589a823d121b45ed841d5110afd44921");
        request1.setTemplateType(0);
        FlowTemplateFileIdRequest request2 = new FlowTemplateFileIdRequest();
        request2.setFileId("bf65d2bc270c403a998d66fec5c058fa");
        request2.setTemplateType(0);
        request.getResourceList().add(request1);
        request.getResourceList().add(request2);

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/create-doc")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JsonUtils.obj2json(request))
                                .header(HEADER_TENANT_ID, EPAAS_TEMPLATE_SUBJECT_001_OID)
                                .header(HEADER_OPERATOR_ID, EPAAS_TEMPLATE_ADMIN_001_OID)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));
    }

    @Test(description = "查询流程模板基础信息")
    public void testGetFlowTemplateBaseInfo() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/flow-templates/32b6a7bc7ebe48a5a32d543174c8bf4c/base-info")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, EPAAS_TEMPLATE_SUBJECT_001_OID)
                                .header(HEADER_OPERATOR_ID, EPAAS_TEMPLATE_ADMIN_001_OID)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));
    }

    @Test(description = "数据源查询测试")
    public void flowTemplateDataSource() throws Exception {
        String subjectOid = SENIOR_SUBJECT_1;
        String operatorOid = ACCOUNT_ID_1;

        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flow-templates/data-source-channel")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, subjectOid)
                                .header(HEADER_OPERATOR_ID, operatorOid)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));

        FlowTemplateChannelDataSourceListRequest dataSourceListRequest =
                new FlowTemplateChannelDataSourceListRequest();
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v2/flow-templates/channel-data-source-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .content(JSON.toJSONString(dataSourceListRequest))
                                        .header(HEADER_TENANT_ID, subjectOid)
                                        .header(HEADER_OPERATOR_ID, operatorOid)
                                        .header(HEADER_APP_ID, APP_ID))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andExpect(
                                MockMvcResultMatchers.jsonPath("$.data")
                                        .value(Matchers.notNullValue()))
                        .andReturn();

        FlowTemplateChannelDataSourceListResponse listResponse =
                JSONObject.parseObject(
                                mvcResult.getResponse().getContentAsString(),
                                new TypeReference<
                                        BaseResult<FlowTemplateChannelDataSourceListResponse>>() {})
                        .getData();
        Assert.assertTrue(CollectionUtils.isNotEmpty(listResponse.getList()), "表单不女主奶");

        // 列表
        FlowTemplateParticipantDataSourceFieldRequest dataSourceFieldRequest =
                new FlowTemplateParticipantDataSourceFieldRequest();
        dataSourceFieldRequest.setDataSourceIds(
                Arrays.asList(listResponse.getList().get(0).getDataSourceId()));
        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/flow-templates/participant-data-source-field")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(dataSourceFieldRequest))
                                .header(HEADER_TENANT_ID, subjectOid)
                                .header(HEADER_OPERATOR_ID, operatorOid)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));

        ///
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flow-templates/data-source-detail")
                                .param(
                                        "dataSourceId",
                                        listResponse.getList().get(0).getDataSourceId())
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(dataSourceFieldRequest))
                                .header(HEADER_TENANT_ID, subjectOid)
                                .header(HEADER_OPERATOR_ID, operatorOid)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Matchers.notNullValue()));
    }

    @Test(description = "测试主子企业流程模板列表")
    public void testParentAndChildListFlowTemplate() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flowTemplates")
                                .param("pageNum", "1")
                                .param("pageSize", "10")
                                .param("flowTemplateName", "")
                                .param("containShared", "true")
                                .param("containsDynamic", "true")
                                .param("status", "")
                                .param("categoryId", "")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(
                                        RequestContextExtUtils.HEADER_TSIGN_OPEN_RESOURCE_TENANT_ID,
                                        "fcb23c197e8d47cea7a4bf6742a4af88")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(120000432));
    }

    @Test(description = "测试获取流程模板参与方节点数据")
    public void testFlowTemplateParticipantNodes() throws Exception {
        // 草稿模板
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flow-templates/participant-nodes")
                                .param("flowTemplateId", "a357bb0508634c87a872010246d0b158")
                                .param("withOperation", "true")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.signerAccounts").isNotEmpty());
        // 普通模板
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flow-templates/participant-nodes")
                                .param("flowTemplateId", "d214a7819e384f43bfd4c2219acbb2ac")
                                .param("withOperation", "false")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.signerAccounts").isNotEmpty());
    }

    @Test(description = "测试获取流程模板参与方节点数据-流程模板不存在")
    public void testFlowTemplateParticipantNodes_flowTemplateNotExist() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flow-templates/participant-nodes")
                                .param("flowTemplateId", "1")
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(
                        mvcResult ->
                                log.info(
                                        "response: {}",
                                        mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(Matchers.not(0)));
    }

    @Test(description = "测试提交定时发起")
    public void testFlowTemplateScheduledStart() throws Exception {
        FlowTemplateScheduledStartRequest request = new FlowTemplateScheduledStartRequest();
        request.setFlowTemplateId("06ab9f03ccbe4029813198bdd82d7fe2");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/scheduled-start")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(request))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_CLIENT_ID, CLIENT_ID_WEB)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(
                        mvcResult ->
                                log.info(
                                        "response: {}",
                                        mvcResult.getResponse().getContentAsString()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code").value(SAAS_BIZ_ERROR.getCode()));
    }

    @Test(description = "测试流程模板参与方印章查询")
    public void testFlowTemplateParticipantSeals() throws Exception {
        // 已指定印章列表， 指定印章ID
        FlowTemplateParticipantSealsRequest assignedSealRequest =
                JSONObject.parseObject(
                        "{\"flowTemplateId\":\"bca1d2cbfc8c4fee836c25f5dbd27bdb\",\"queryParam\":{\"participants\":[{\"participantId\":\"ba8777e82adb42a3b0e24cbcf86e8a1a\",\"seals\":[{\"signRequirement\":\"1\",\"sealIds\":[]}]}]},\"queryType\":\"ASSIGNED_SEAL\"}",
                        FlowTemplateParticipantSealsRequest.class);
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/participant-seals")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(assignedSealRequest))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_CLIENT_ID, CLIENT_ID_WEB)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(
                        mvcResult ->
                                log.info(
                                        "response: {}",
                                        mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.participantSeals").isNotEmpty());

        // 已指定印章列表, 未指定印章类型
        assignedSealRequest =
                JSONObject.parseObject(
                        "{\"flowTemplateId\":\"bca1d2cbfc8c4fee836c25f5dbd27bdb\",\"queryParam\":{\"participants\":[{\"participantId\":\"f45602552ac44a4d9df9c1fed9e6f7ea\",\"seals\":[{\"signRequirement\":\"1\",\"sealIds\":[\"b12e7c2c-e18b-40c4-81b4-05f9e7f1c407\"]}]}]},\"queryType\":\"ASSIGNED_SEAL\"}",
                        FlowTemplateParticipantSealsRequest.class);
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/participant-seals")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(assignedSealRequest))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_CLIENT_ID, CLIENT_ID_WEB)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(
                        mvcResult ->
                                log.info(
                                        "response: {}",
                                        mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.participantSeals").isNotEmpty());

        // 可指定印章列表
        FlowTemplateParticipantSealsRequest assignableSealRequest =
                JSONObject.parseObject(
                        "{\"flowTemplateId\":\"bca1d2cbfc8c4fee836c25f5dbd27bdb\",\"queryParam\":{\"participantId\":\"a17a49bd97ec429199b4e8fedfd86633\",\"signRequirement\":\"1\"},\"queryType\":\"ASSIGNABLE_SEAL\"}",
                        FlowTemplateParticipantSealsRequest.class);
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/participant-seals")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(assignableSealRequest))
                                .header(HEADER_TENANT_ID, TENANT_ID1)
                                .header(HEADER_OPERATOR_ID, OPERATOR_ID1)
                                .header(HEADER_CLIENT_ID, CLIENT_ID_WEB)
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(
                        mvcResult ->
                                log.info(
                                        "response: {}",
                                        mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.participantSeals").isNotEmpty())
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.data.participantSeals[0].seals")
                                .isNotEmpty());
    }

    @Test(description = "测试校验同步数据源表单")
    public void testCheckAndSyncForm() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flow-templates/check-data-source-form")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .param("formId", "D1E22F9B-5683-4A1F-87D1-2CBEB37219AF")
                                .param("channel", "FEISHU")
                                .header(HEADER_TENANT_ID, "3d71616d4218443ba660ead8c0fb47a8")
                                .header(HEADER_OPERATOR_ID, "34b6bc2b6b8343fb9a3cef2828081a52")
                                .header(HEADER_CLIENT_ID, "FEI_SHU")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "测试按单个模板查询授权enterprise列表")
    public void testSingleFlowTemplateAuthListEnterprise() throws Exception {
        FlowTemplateDetailQueryRequest request = new FlowTemplateDetailQueryRequest();
        request.setFlowTemplateId("1ba2346b789b4f339179a60312ed712b");
        request.setQuerySelf(true);
        request.setOwnerTenantId("3d71616d4218443ba660ead8c0fb47a8");
        request.setAuthType("enterprise");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flowTemplates/single-template-auth-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(request))
                                .header(HEADER_TENANT_ID, "3d71616d4218443ba660ead8c0fb47a8")
                                .header(HEADER_OPERATOR_ID, "34b6bc2b6b8343fb9a3cef2828081a52")
                                .header(HEADER_CLIENT_ID, "FEI_SHU")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "测试获取数据源字段和关联规则")
    public void testDataSourceField() throws Exception {
        FlowTemplateDataSourceFieldRequest request=new FlowTemplateDataSourceFieldRequest();
        request.setDataSourceIds(Lists.newArrayList("D1E22F9B-5683-4A1F-87D1-2CBEB37219AF"));
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/data-source-field")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(request))
                                .header(HEADER_TENANT_ID, "3d71616d4218443ba660ead8c0fb47a8")
                                .header(HEADER_OPERATOR_ID, "34b6bc2b6b8343fb9a3cef2828081a52")
                                .header(HEADER_CLIENT_ID, "FEI_SHU")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "企业模版数据源渠道")
   public void testDataSourceChannel() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/flow-templates/data-source-channel")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, "9c6b87f1e2004a37bb5a3a9220d6e103")
                                .header(HEADER_OPERATOR_ID, "116a184fb65646309c0fc5ed1d9eb4b6")
                                .header(HEADER_CLIENT_ID, "FEI_SHU")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "基于文件/模板创建ePaas文档")
    public void testCreateDoc() throws Exception {
        FlowTemplateCreateTemplateRequest request = new FlowTemplateCreateTemplateRequest();
        FlowTemplateFileIdRequest fileIdRequest = new FlowTemplateFileIdRequest();
        fileIdRequest.setFileId("81cd23f817b0456cbdd16c5b7a9da53d");
        fileIdRequest.setTemplateType(0);
        request.setResourceList(Lists.newArrayList(fileIdRequest));
        request.setResourceTenantId("3d71616d4218443ba660ead8c0fb47a8");
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/flow-templates/create-doc")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSON.toJSONString(request))
                                .header(HEADER_TENANT_ID, "347472f5dd36482c9cdd3c81ada70684")
                                .header(HEADER_OPERATOR_ID, "dce0491fcff345d48e31deb89e29d903")
                                .header(HEADER_CLIENT_ID, "FEI_SHU")
                                .header(HEADER_APP_ID, APP_ID))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test
    public void epaasStructChoiceListConfigTest() throws Exception {
        MvcResult mvcResult = mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v2/flowTemplates/epaas-struct-choice-list-config")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header("X-Tsign-Open-Operator-Id", ACCOUNT_ID_1)
                                .header("X-Tsign-Open-Tenant-Id", AI_SUBJECT_1)
                                .header("X-Tsign-Client-Id", "WEB")
                                .header("X-Tsign-Open-App-Id", APP_ID))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
    }
}
