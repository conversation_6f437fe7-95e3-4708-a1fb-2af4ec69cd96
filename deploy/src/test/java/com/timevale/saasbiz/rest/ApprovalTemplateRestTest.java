package com.timevale.saasbiz.rest;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.SENIOR_SUBJECT_1;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.approval.request.ListApprovalTemplateRequest;
import com.timevale.saasbiz.rest.bean.approval.response.ListApprovalTemplateResponse;

import lombok.extern.slf4j.Slf4j;

import org.hamcrest.Matchers;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2023-03-15 09:54
 */
@Slf4j
@JsonDataIgnore
public class ApprovalTemplateRestTest extends BaseServiceTest {

    @Test(description = "审批模板列表查询")
    public void testListApprovalTemplate() throws Exception {
        ListApprovalTemplateRequest request = new ListApprovalTemplateRequest();

        MvcResult result =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/approval/template/list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result为空");

        RestResult<ListApprovalTemplateResponse> restResult =
                JsonUtilsPlus.parseObject(
                        result.getResponse().getContentAsString(),
                        new TypeReference<RestResult<ListApprovalTemplateResponse>>() {});
        Assert.assertNotNull(restResult, "restResult为空");
        Assert.assertNotNull(restResult.getData(), "restResult data为空");
        Assert.assertTrue(CollectionUtils.isNotEmpty(restResult.getData().getItems()), "审批模板列表为空");
    }

    @Test(description = "审批模板印章列表查询")
    public void testApprovalTemplateSealList() throws Exception{
        // 分页查询跨企业授权章
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/approval/template/seal-list")
                                .param("pageNum", "1")
                                .param("pageSize", "10")
                                .param("downloadFlag", "true")
                                .param("grantedSeal", "true")
                                .header("X-Tsign-Open-Operator-Id", "551030ad5917474da868ef3aa25a1c3f")
                                .header("X-Tsign-Open-Tenant-Id", "71973bb34af94b8688b54fcf9437e2de"))
                .andExpect(s -> System.out.println(s.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        // 查询企业全部印章
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/approval/template/seal-list-all")
                                .param("downloadFlag", "true")
                                .header("X-Tsign-Open-Operator-Id", "551030ad5917474da868ef3aa25a1c3f")
                                .header("X-Tsign-Open-Tenant-Id", "71973bb34af94b8688b54fcf9437e2de"))
                .andExpect(s -> System.out.println(s.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.total").value(Matchers.greaterThan(0)))
                .andReturn();
    }
}
