package com.timevale.saasbiz.rest;

import static com.timevale.saasbiz.constant.TestAccountConstants.*;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.timevale.doccooperation.service.util.LambdaUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.usercenter.dto.CategoryDTO;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.flowtemplate.request.*;

import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 流程模板分类单测
 *
 * <AUTHOR>
 * @since 2023-03-16 11:27
 */
@Slf4j
@JsonDataIgnore
public class FlowTemplateCategoryRestTest extends BaseServiceTest {

    private static final String FLOW_TEMPLATE_1 = "b154fdd6d1424b4b88766cd5d3ffa26d";
    private static final String FLOW_TEMPLATE_2 = "05cb99a75e294e02ba628f9c0e35527a";

    @Test(description = "正常保存及名称重复")
    public void testSaveCategoryNameRepeat() throws Exception {
        // 保存分类成功
        String categoryId = seniorSaveCategory("bizC1", SaasBizResultCode.SUCCESS.getCode());
        Assert.assertTrue(StringUtils.isNotBlank(categoryId), "保存分类失败");

        // 分类名称重复
        seniorSaveCategory("bizC1", SaasBizResultCode.SAAS_BIZ_ERROR.getCode());

        // 删除刚创建的分类
        seniorBatchDeleteCategory(categoryId);
    }

    @Test(description = "名称长度及字符不符合要求")
    public void testSaveCategoryNameError() throws Exception {
        // 分类名称超长
        seniorSaveCategory("分类名称超长", SaasBizResultCode.SAAS_BIZ_ERROR.getCode());

        // 分类名称字符不合法
        seniorSaveCategory("-123-！@", SaasBizResultCode.SAAS_ILLEGAL_PARAM.getCode());
    }

    @Test(description = "添加到分类")
    public void testAddToCategory() throws Exception {
        // 保存分类成功
        String categoryId = seniorSaveCategory("addTo1", SaasBizResultCode.SUCCESS.getCode());
        Assert.assertTrue(StringUtils.isNotBlank(categoryId), "保存分类失败");

        // 添加模板到分类下
        AddToCategoryRequest request = new AddToCategoryRequest();
        request.setCategoryId(categoryId);
        request.setFlowTemplateId(FLOW_TEMPLATE_1);
        seniorAccount1ExpectPost(
                "/v2/flow-templates/add-to-category", request, SaasBizResultCode.SUCCESS.getCode());

        // 删除刚创建的分类
        seniorBatchDeleteCategory(categoryId);
    }

    @Test(description = "批量添加到分类")
    public void testBatchToCategory() throws Exception {
        // 保存分类成功
        String categoryId = seniorSaveCategory("batchTo1", SaasBizResultCode.SUCCESS.getCode());
        Assert.assertTrue(StringUtils.isNotBlank(categoryId), "保存分类失败");

        // 批量添加模板到分类下
        BatchToCategoryRequest request = new BatchToCategoryRequest();
        request.setCategoryId(categoryId);
        request.setFlowTemplateIds(Arrays.asList(FLOW_TEMPLATE_1, FLOW_TEMPLATE_2));
        seniorAccount1ExpectPost(
                "/v2/flow-templates/batch-to-category",
                request,
                SaasBizResultCode.SUCCESS.getCode());

        // 删除刚创建的分类
        seniorBatchDeleteCategory(categoryId);
    }

    @Test(description = "流程模板分类列表")
    public void testListCategory() throws Exception {
        // 保存分类成功
        String categoryId = seniorSaveCategory("listC1", SaasBizResultCode.SUCCESS.getCode());
        Assert.assertTrue(StringUtils.isNotBlank(categoryId), "保存分类失败");

        // 分类列表
        List<CategoryDTO> list = seniorListCategory();
        Assert.assertTrue(CollectionUtils.isNotEmpty(list), "分类列表为空");
        Assert.assertTrue(
                list.stream().map(CategoryDTO::getCategoryId).anyMatch(categoryId::equals),
                "列表中未包含创建的分类");

        // 删除刚创建的分类
        seniorBatchDeleteCategory(categoryId);
    }

    @Test(description = "从分类下删除流程模板")
    public void testRemoveFromCategory() throws Exception {
        // 保存分类成功
        String categoryId = seniorSaveCategory("rmFtFromC1", SaasBizResultCode.SUCCESS.getCode());
        Assert.assertTrue(StringUtils.isNotBlank(categoryId), "保存分类失败");

        // 批量添加模板到分类下
        BatchToCategoryRequest request = new BatchToCategoryRequest();
        request.setCategoryId(categoryId);
        request.setFlowTemplateIds(Arrays.asList(FLOW_TEMPLATE_1, FLOW_TEMPLATE_2));
        seniorAccount1ExpectPost(
                "/v2/flow-templates/batch-to-category",
                request,
                SaasBizResultCode.SUCCESS.getCode());
        // 添加模板是否成功
        List<CategoryDTO> list = seniorListCategory();
        CategoryDTO category =
                list.stream().filter(c -> c.getCategoryId().equals(categoryId)).findFirst().get();
        Assert.assertEquals((int) category.getNumber(), 2, "分类添加模板失败");

        // 删除分类下模板
        RemoveFromCategoryRequest removeReq = new RemoveFromCategoryRequest();
        removeReq.setCategoryId(categoryId);
        removeReq.setFlowTemplateId(FLOW_TEMPLATE_1);
        seniorAccount1ExpectPost(
                "/v2/flow-templates/remove-from-category",
                removeReq,
                SaasBizResultCode.SUCCESS.getCode());
        // 删除分类下模板是否成功
        List<CategoryDTO> list2 = seniorListCategory();
        category =
                list2.stream().filter(c -> c.getCategoryId().equals(categoryId)).findFirst().get();
        Assert.assertEquals((int) category.getNumber(), 1, "分类删除模板失败");

        // 删除刚创建的分类
        seniorBatchDeleteCategory(categoryId);
    }

    @Test(description = "添加流程模板到分类下")
    public void testAddToCategories() throws Exception {
        // 保存分类成功
        String categoryId1 = seniorSaveCategory("toCate1", SaasBizResultCode.SUCCESS.getCode());
        String categoryId2 = seniorSaveCategory("toCate2", SaasBizResultCode.SUCCESS.getCode());
        String categoryId3 = seniorSaveCategory("toCate3", SaasBizResultCode.SUCCESS.getCode());
        List<String> categoryIds = Arrays.asList(categoryId1, categoryId2, categoryId3);

        // 给一个模板添加多个分类
        AddToCategoriesRequest request = new AddToCategoriesRequest();
        request.setCategoryIds(categoryIds);
        request.setFlowTemplateId(FLOW_TEMPLATE_1);
        seniorAccount1ExpectPost(
                "/v2/flow-templates/add-to-categories",
                request,
                SaasBizResultCode.SUCCESS.getCode());

        // 检查是否成功添加到了分类下
        List<CategoryDTO> categories = seniorListCategory();
        for (CategoryDTO category : categories) {
            if (!categoryIds.contains(category.getCategoryId())) {
                continue;
            }
            Assert.assertEquals((int) category.getNumber(), 1, "模板批量添加分类失败");
        }

        // 删除刚创建的分类(3个)
        seniorBatchDeleteCategory(categoryId1, categoryId2, categoryId3);
    }

    @Test(description = "测试频繁修改模板分类")
    public void testManyUpdateCategories() throws Exception {
        // 保存分类成功
        String categoryId1 = seniorSaveCategory("toCate1", SaasBizResultCode.SUCCESS.getCode());
        String categoryId2 = seniorSaveCategory("toCate2", SaasBizResultCode.SUCCESS.getCode());
        String categoryId3 = seniorSaveCategory("toCate3", SaasBizResultCode.SUCCESS.getCode());
        List<String> categoryIds = Arrays.asList(categoryId1, categoryId2, categoryId3);

        // 批量添加模板1、2到分类1，分类1=2，分类2=0，分类3=0
        BatchToCategoryRequest batchReq = new BatchToCategoryRequest();
        batchReq.setCategoryId(categoryId1);
        batchReq.setFlowTemplateIds(Arrays.asList(FLOW_TEMPLATE_1, FLOW_TEMPLATE_2));
        seniorAccount1ExpectPost(
                "/v2/flow-templates/batch-to-category",
                batchReq,
                SaasBizResultCode.SUCCESS.getCode());

        // 添加模板1到分类2，分类1=2，分类2=1，分类3=0
        AddToCategoryRequest addReq = new AddToCategoryRequest();
        addReq.setCategoryId(categoryId2);
        addReq.setFlowTemplateId(FLOW_TEMPLATE_1);
        seniorAccount1ExpectPost(
                "/v2/flow-templates/add-to-category", addReq, SaasBizResultCode.SUCCESS.getCode());

        // 批量添加模板2分类2、3，分类1=1，分类2=2，分类3=1 (覆盖)
        AddToCategoriesRequest request = new AddToCategoriesRequest();
        request.setFlowTemplateId(FLOW_TEMPLATE_2);
        request.setCategoryIds(Arrays.asList(categoryId2, categoryId3));
        seniorAccount1ExpectPost(
                "/v2/flow-templates/add-to-categories",
                request,
                SaasBizResultCode.SUCCESS.getCode());

        // 移除分类1下的模板1，分类1=0，分类2=2，分类3=1
        RemoveFromCategoryRequest removeReq = new RemoveFromCategoryRequest();
        removeReq.setCategoryId(categoryId1);
        removeReq.setFlowTemplateId(FLOW_TEMPLATE_1);
        seniorAccount1ExpectPost(
                "/v2/flow-templates/remove-from-category",
                removeReq,
                SaasBizResultCode.SUCCESS.getCode());

        // 最终数据是否正确
        List<CategoryDTO> list = seniorListCategory();
        Map<String, Integer> countMap =
                LambdaUtil.toMap(list, CategoryDTO::getCategoryId, CategoryDTO::getNumber);
        Assert.assertEquals((int) countMap.get(categoryId1), 0, "分类1数量错误");
        Assert.assertEquals((int) countMap.get(categoryId2), 2, "分类2数量错误");
        Assert.assertEquals((int) countMap.get(categoryId3), 1, "分类3数量错误");
        // 删除刚创建的分类
        seniorBatchDeleteCategory(categoryId1, categoryId2, categoryId3);
    }

    @Test(description = "置顶流程模板")
    public void testTopFlowTemplate() throws Exception {
        BaseFlowTemplateRequest request = new BaseFlowTemplateRequest();
        request.setFlowTemplateId(FLOW_TEMPLATE_1);

        // 置顶
        seniorAccount1ExpectPost(
                "/v2/flow-templates/top", request, SaasBizResultCode.SUCCESS.getCode());

        // 取消置顶
        seniorAccount1ExpectPost(
                "/v2/flow-templates/top", request, SaasBizResultCode.SUCCESS.getCode());
    }

    /**
     * 批量删除模板
     *
     * @param categoryIds
     * @throws Exception
     */
    private void seniorBatchDeleteCategory(String... categoryIds) throws Exception {
        DeleteCategoryRequest deleteReq = new DeleteCategoryRequest();
        for (String categoryId : categoryIds) {
            deleteReq.setCategoryId(categoryId);
            seniorAccount1ExpectPost(
                    "/v2/flow-templates/delete-category",
                    deleteReq,
                    SaasBizResultCode.SUCCESS.getCode());
        }
    }

    /**
     * 在高级版企业下用Account1账号保存分类
     *
     * @param categoryName
     * @return
     * @throws Exception
     */
    private String seniorSaveCategory(String categoryName, int expectCode) throws Exception {
        SaveCategoryRequest saveReq = new SaveCategoryRequest();
        saveReq.setCategoryName(categoryName);

        // 保存分类成功
        MvcResult mvcResult =
                seniorAccount1ExpectPost("/v2/flow-templates/save-category", saveReq, expectCode);

        return JSON.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<String>>() {})
                .getData();
    }

    /**
     * 高级企业分类列表
     *
     * @return
     * @throws Exception
     */
    private List<CategoryDTO> seniorListCategory() throws Exception {
        // 保存分类成功
        MvcResult mvcResult =
                seniorAccount1ExpectGet(
                        "/v2/flow-templates/category-list",
                        null,
                        SaasBizResultCode.SUCCESS.getCode());

        return JSON.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<List<CategoryDTO>>>() {})
                .getData();
    }
}
