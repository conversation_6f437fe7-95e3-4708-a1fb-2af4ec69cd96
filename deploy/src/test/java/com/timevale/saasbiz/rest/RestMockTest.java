package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.contractapproval.facade.dto.ApprovalContractAnalysisRelationDTO;
import com.timevale.footstone.base.model.response.BaseResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.approval.ApprovalContractAnalysisClient;
import com.timevale.saasbiz.integration.approval.impl.ApprovalContractAnalysisClientImpl;
import com.timevale.saasbiz.integration.contractanalysis.FileCompareClient;
import com.timevale.saasbiz.integration.contractanalysis.impl.FileCompareClientImpl;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalContractAnalysisCreateInputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.approval.request.ApprovalContractAnalysisCreateRequest;
import com.timevale.saasbiz.rest.bean.approval.request.ListApprovalFlowRequest;
import com.timevale.saasbiz.rest.bean.approval.response.ListApprovalFlowResponse;
import com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormSaveRequest;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectFormSaveResponse;
import com.timevale.saasbiz.service.approval.ApprovalContractAnalysisService;
import com.timevale.saasbiz.service.approval.impl.ApprovalContractAnalysisServiceImpl;
import com.timevale.saasbiz.service.usercenter.impl.UserCenterServiceImpl;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.SENIOR_SUBJECT_1;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @since 2023/8/8 10:41
 */
public class RestMockTest extends BaseMockTest {

    @InjectMocks private ApprovalFlowRest approvalFlowRest;
    @InjectMocks private ApprovalContractAnalysisServiceImpl approvalContractAnalysisService;
    @Mock private UserCenterServiceImpl userCenterService;

    @Mock private ApprovalContractAnalysisClientImpl approvalContractAnalysisClient;
    @Mock private FileCompareClientImpl fileCompareClient;

    @Test(testName = "当前用户无gid,直接返回空")
    public void flowListOperatorNoGidTest() {
        String operatorGid = "accountId";
        Mockito.when(userCenterService.queryAccountInfoByOid(operatorGid))
                .thenReturn(new AccountInfoDTO());
        RestResult<ListApprovalFlowResponse> restResult =
                approvalFlowRest.list(operatorGid, new ListApprovalFlowRequest());
        Assert.assertTrue(null != restResult && null == restResult.getData());
    }

    @Test(testName = "合同比对创建mock测试")
    public void contractAnalysisCreateTest()  {
        Mockito.when(userCenterService.queryAccountInfoByOid(any()))
                .thenReturn(new AccountInfoDTO());
        Mockito.when(fileCompareClient.addCompare(any()))
                .thenReturn("testId");
        Mockito.doNothing().when(approvalContractAnalysisClient).createContractAnalysis(any());
        String result= approvalContractAnalysisService.createApprovalAnalysis(new ApprovalContractAnalysisCreateInputDTO());
        Assert.assertTrue(StringUtils.isNotBlank(result));
    }

    @Test(testName = "合同比对重试mock测试")
    public void contractAnalysisRetryTest()  {
        Mockito.when(approvalContractAnalysisClient.queryContractAnalysisDetail(any()))
                .thenReturn(new ApprovalContractAnalysisRelationDTO());
        Mockito.doNothing().when(approvalContractAnalysisClient).updateContractAnalysis(any());
        Mockito.doNothing().when(fileCompareClient).retryCompare(any());
        try{
            approvalContractAnalysisService.retryCompare("testid");
        }catch (Exception e){
            Assert.fail();
        }
    }
}
