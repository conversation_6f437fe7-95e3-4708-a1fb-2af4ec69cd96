package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.contractmanager.common.service.enums.fulfillment.FulfillmentRuleStatusEnum;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.autoarchive.request.AutoArchiveRuleListRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRecordListRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRuleListRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRuleSaveRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRuleUpdateRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.request.ContractFulfillmentRuleUpdateStatusRequest;
import com.timevale.saasbiz.rest.bean.fulfillment.response.ContractFulfillmentRuleTypeResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectFormDetailResponse;
import com.timevale.saasbiz.rest.bean.infocollect.response.InfoCollectFormSaveResponse;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * ContractFulfillmentRestTest
 *
 * <AUTHOR>
 * @since 2023/11/16 3:39 下午
 */
@Slf4j
@JsonDataIgnore
public class ContractFulfillmentRestTest extends BaseServiceTest {

    @Autowired
    private MapperFactory mapperFactory;

    @Test()
    public void test() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;

        //保存
        ContractFulfillmentRuleSaveRequest saveRequest = JSON.parseObject("{\"name\":\"单测规则保存\",\"scopeConditions\":[{\"fieldId\":\"24c65db690c84ed191c72b10b72f4113\",\"value\":[{\"val\":\"123\",\"key\":\"*************-298\"}],\"fieldType\":3,\"matchType\":3,\"operatorType\":2,\"childOperators\":[{\"conditionParams\":\"[\\\"123\\\"]\",\"operatorType\":2}]}],\"noticeRule\":{\"condition\":[{\"key\":\"createTime\",\"value\":3,\"middleType\":\"after\"}],\"reminder\":{\"accounts\":[],\"type\":[\"initiator\",\"cc\",\"signer\"]}},\"status\":\"enable\",\"type\":\"expire\",\"typeName\":\"\",\"scopeType\":\"force\",\"noticeChannels\":[\"INMAIL\"]}\n", ContractFulfillmentRuleSaveRequest.class);

        MvcResult saveResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/contract-fulfillment/save-rule")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId)
                                .content(JsonUtils.obj2json(saveRequest)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<String> saveRestResult = JsonUtilsPlus.parseObject(saveResult.getResponse().getContentAsString(), new TypeReference<RestResult<String>>() {});
        String ruleId = saveRestResult.getData();
        Assert.assertNotNull(ruleId, "ruleId is null");

        ContractFulfillmentRuleUpdateRequest updateRequest = mapperFactory.getMapperFacade().map(saveRequest, ContractFulfillmentRuleUpdateRequest.class);
        updateRequest.setRuleId(ruleId);
        mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/contract-fulfillment/update-rule")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .content(JsonUtils.obj2json(updateRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        ContractFulfillmentRuleUpdateStatusRequest updateStatusRequest = new ContractFulfillmentRuleUpdateStatusRequest();
        updateStatusRequest.setRuleId(ruleId);
        updateStatusRequest.setStatus(FulfillmentRuleStatusEnum.DISABLE.getStatus());
        mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/contract-fulfillment/update-rule-status")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .content(JsonUtils.obj2json(updateStatusRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();


        mockMvc.perform(
                MockMvcRequestBuilders.get("/v1/contract-fulfillment/get-rule-detail")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .param("ruleId", ruleId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        ContractFulfillmentRecordListRequest recordListRequest = new ContractFulfillmentRecordListRequest();
        recordListRequest.setPageNum(1);
        recordListRequest.setPageSize(10);
        mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/contract-fulfillment/record-list")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .content(JsonUtils.obj2json(recordListRequest)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        ContractFulfillmentRuleListRequest request = new ContractFulfillmentRuleListRequest();
        request.setPageNum(1);
        request.setPageSize(10);

        MvcResult listResult =
                mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/contract-fulfillment/rule-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId)
                                .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(listResult, "result is null");

        mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/contract-fulfillment/type-list")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .content(JsonUtils.obj2json(new ContractFulfillmentRecordListRequest())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        mockMvc.perform(
                MockMvcRequestBuilders.get("/v1/contract-fulfillment/delete-rule")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId)
                        .param("ruleId", ruleId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

    }



}
