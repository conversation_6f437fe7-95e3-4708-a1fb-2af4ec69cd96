package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.timevale.framework.mq.client.consumer.ReceiveResult;
import com.timevale.framework.mq.client.producer.Msg;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.TestJsonObj;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.mq.consumer.WitnessFlowChangeMqConsumer;
import com.timevale.saasbiz.mq.consumer.WitnessFlowSyncMqConsumer;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.witness.request.*;
import com.timevale.saasbiz.rest.bean.witness.response.GetWitnessFlowBatchSignListResponse;
import com.timevale.saasbiz.rest.bean.witness.response.GetWitnessFlowBatchSignUrlResponse;
import com.timevale.saasbiz.rest.bean.witness.response.QueryWitnessFlowsResponse;
import com.timevale.saasbiz.rest.bean.witness.vo.WitnessFlowVO;
import com.timevale.saasbiz.service.witness.WitnessFlowService;
import lombok.extern.slf4j.Slf4j;
import org.hamcrest.Matchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_3;
import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_4;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

/**
 * @author: qianyi
 * @since: 2023-03-13
 */
@Slf4j
public class WitnessFlowRestTest extends BaseServiceTest {
    @Autowired WitnessFlowService witnessService;
    @Autowired WitnessFlowSyncMqConsumer witnessFlowSyncMqConsumer;
    @Autowired WitnessFlowChangeMqConsumer witnessFlowChangeMqConsumer;

    @Test(description = "天印流程变更消息消费")
    public void testWitnessFlowChangeMqConsumer() {
        String jsonMsg =
                "{\"eventCode\":\"BUTLER_END_FLOW\",\"flowInfo\":{\"flowId\":\"6a359fc337b3c8ef5c6e62f2873cb901\",\"businessScene\":\"轩辕外部个人全流程上链002\",\"appId\":\"**********\",\"appName\":\"玉瓒-天印专用-轩辕\",\"signValidity\":*************,\"flowStartTime\":*************,\"flowUpdateTime\":*************,\"flowEndTime\":*************,\"flowStatus\":2,\"initiatorAccountName\":\"宁缺\",\"initiatorAuthorizedAccountName\":\"测试中心\",\"initiatorAccountType\":1,\"initiatorSignKeyInfo\":{\"oauthCallbackUrl\":\"http://**************:8086/esign/rest/callback/auth/applets/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mInL2MrFtt-CWS-ccnegYi2hhCFkwON9gBR5loUt5vc?accountId=f1cfbf3d-cc46-4152-acf9-76e8e0b055bb&processId=9191\",\"willingCallbackUrl\":\"http://**************:8086/esign/rest/willing/applets/f1cfbf3d-cc46-4152-acf9-76e8e0b055bb/result?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mInL2MrFtt-CWS-ccnegYi2hhCFkwON9gBR5loUt5vc&flowId=9191&provider=xuanyuan\",\"userName\":\"宁缺\",\"licenseNo\":\"330326198906053014\"},\"signers\":[{\"signStatus\":2,\"signTime\":*************,\"accountId\":\"f0b5f6c1703f42709f6d636b8e88d81f\",\"accountName\":\"罗杰\",\"identityAccountId\":\"f0b5f6c1703f42709f6d636b8e88d81f\",\"identityAccountName\":\"罗杰\",\"identityAccountType\":0,\"signKeyInfo\":{\"oauthCallbackUrl\":\"http://**************:8086/esign/rest/callback/auth/applets/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mInL2MrFtt-CWS-ccnegYi2hhCFkwON9gBR5loUt5vc?accountId=dc1bd8a2-2394-4a3a-8592-1635eea20da7&processId=9191\",\"willingCallbackUrl\":\"http://**************:8086/esign/rest/willing/applets/dc1bd8a2-2394-4a3a-8592-1635eea20da7/result?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mInL2MrFtt-CWS-ccnegYi2hhCFkwON9gBR5loUt5vc&flowId=9191&provider=xuanyuan\",\"userName\":\"罗杰\",\"licenseNo\":\"330326198906053014\"}}],\"signDocs\":[{\"fileName\":\"轩辕外部个人全流程上链002.pdf\",\"totalPage\":1}]},\"timestamp\":*************}";
        Msg msg = new Msg(jsonMsg.getBytes(StandardCharsets.UTF_8));
        msg.setTopic("contract_butler_platform_ty_flow_change_topic");
        msg.setTags("BUTLER_END_FLOW");
        try {
            ReceiveResult receiveResult =
                    witnessFlowChangeMqConsumer.receive(Lists.newArrayList(msg));
            Assert.assertTrue(receiveResult.isReceiveSuccess());
        } catch (Exception e) {
            Assert.assertTrue(false, "天印流程消费不应报错");
        }
    }

    @Test(description = "天印流程同步消息消费")
    public void testWitnessFlowSyncMqConsumer() {
        String jsonMsg =
                "{\"eventCode\":\"BUTLER_SYNC_FLOW\",\"flowInfo\":{\"flowId\":\"6ad1c57ecfcdc182cd2f6a463e88563d\",\"businessScene\":\"386537-3A_用印审批\",\"appId\":\"**********\",\"appName\":\"玉瓒-天印专用-轩辕\",\"signValidity\":*************,\"flowStartTime\":*************,\"flowUpdateTime\":*************,\"flowStatus\":1,\"initiatorAccountName\":\"文敏\",\"initiatorAuthorizedAccountName\":\"测试中心\",\"initiatorAccountType\":1,\"initiatorSignKeyInfo\":{\"oauthCallbackUrl\":\"http://**************:8086/esign/rest/callback/auth/applets/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XGdtOwwLqQdemA6tixMFimAlzGuLDgWGysScbQWIdk0?accountId=cc56c5a5-6878-419b-a758-dfd034788704&processId=9499\",\"willingCallbackUrl\":\"http://**************:8086/esign/rest/willing/applets/cc56c5a5-6878-419b-a758-dfd034788704/result?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.18hOwafDwwqyVvePD7my8lv4Tf_dgxBzpN3MaP8TolM&flowId=9499&provider=xuanyuan\",\"userName\":\"文敏\",\"licenseNo\":\"220301199006090014\"},\"signers\":[{\"signStatus\":1,\"accountId\":\"280359ee52634f53b66f6df154a8c89d\",\"accountName\":\"姜娇\",\"identityAccountId\":\"d30c633dd10740cf934560dbb5880334\",\"identityAccountName\":\"外部文敏测试（196）有限公司\",\"identityAccountType\":1,\"signKeyInfo\":{\"oauthCallbackUrl\":\"http://**************:8086/esign/rest/callback/auth/applets/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GW387MX3IReekVMs4w8o5Hyca_B2OVvhitehTPnPq-4?accountId=58f4ff50-9531-467e-860c-fbe49ea612ae&processId=9499\",\"willingCallbackUrl\":\"http://**************:8086/esign/rest/willing/applets/58f4ff50-9531-467e-860c-fbe49ea612ae/result?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.wii4XRd2qyo6HEDvPF5VTyUFgSXj-8OPwpxyGWwxGcI&flowId=9499&provider=xuanyuan\",\"userName\":\"姜娇\",\"licenseNo\":\"420624199305255542\"}}],\"signDocs\":[{\"fileName\":\"A-30498\",\"totalPage\":7}]},\"timestamp\":*************}";
        Msg msg = new Msg(jsonMsg.getBytes(StandardCharsets.UTF_8));
        msg.setTopic("contract_butler_platform_ty_flow_sync_topic");
        msg.setTags("BUTLER_SYNC_FLOW");
        try {
            ReceiveResult receiveResult =
                    witnessFlowSyncMqConsumer.receive(Lists.newArrayList(msg));
            Assert.assertTrue(receiveResult.isReceiveSuccess());
        } catch (Exception e) {
            Assert.assertTrue(false, "天印流程消费不应报错");
        }
    }

    @Test(description = "测试获取用户天印流程列表")
    public void testQueryUserWitnessFlows() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("QueryUserWitnessFlows");
        QueryUserWitnessFlowRequest reqData =
                (QueryUserWitnessFlowRequest) testJsonObj.getReqData();

        String accountId = reqData.getAccountId();
        String subjectId = reqData.getSubjectId();

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/witness/flows/user/list")
                        .param("accountId", accountId)
                        .param("subjectId", subjectId)
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId);
        if (null != reqData.getTimeRangeType()) {
            builder.param("timeRangeType", reqData.getTimeRangeType().toString());
        }
        if (null != reqData.getBeginTimeInMillSec()) {
            builder.param("beginTimeInMillSec", reqData.getBeginTimeInMillSec().toString());
        }
        if (null != reqData.getEndTimeInMillSec()) {
            builder.param("endTimeInMillSec", reqData.getEndTimeInMillSec().toString());
        }
        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("testQueryUserWitnessFlows result is :{}", response);
        RestResult<QueryWitnessFlowsResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<QueryWitnessFlowsResponse>>() {});
        Assert.assertTrue(null != restResult && null != restResult.getData());
        Assert.assertTrue(restResult.getData().getTotal() > 0, "用户参与的天印流程列表不应为空");
    }

    @Test(description = "测试通过身份证获取用户天印流程列表")
    public void testQueryUserWitnessFlowsByPersonIdentity() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("QueryUserWitnessFlowsByPersonIdentity");
        QueryUserWitnessFlowRequest reqData =
                (QueryUserWitnessFlowRequest) testJsonObj.getReqData();

        String accountId = reqData.getAccountId();
        String subjectId = reqData.getSubjectId();
        String personIdentity = reqData.getPersonIdentity();
        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/witness/flows/user/list")
                        .param("accountId", accountId)
                        .param("subjectId", subjectId)
                        .param("personIdentity",personIdentity)
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId);
        if (null != reqData.getTimeRangeType()) {
            builder.param("timeRangeType", reqData.getTimeRangeType().toString());
        }
        if (null != reqData.getBeginTimeInMillSec()) {
            builder.param("beginTimeInMillSec", reqData.getBeginTimeInMillSec().toString());
        }
        if (null != reqData.getEndTimeInMillSec()) {
            builder.param("endTimeInMillSec", reqData.getEndTimeInMillSec().toString());
        }
        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("testQueryUserWitnessFlows result is :{}", response);
        RestResult<QueryWitnessFlowsResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<QueryWitnessFlowsResponse>>() {});
        Assert.assertTrue(null != restResult && null != restResult.getData());
        Assert.assertTrue(restResult.getData().getTotal() > 0, "用户参与的天印流程列表不应为空");
    }

    @Test(description = "测试获取用户可查看的企业天印流程列表")
    public void testQueryViewWitnessFlows() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("QueryViewWitnessFlows");
        QueryViewableWitnessFlowRequest reqData =
                (QueryViewableWitnessFlowRequest) testJsonObj.getReqData();

        String accountId = reqData.getAccountId();
        String subjectId = reqData.getSubjectId();
        boolean sortAsc = reqData.isSortAsc();

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/witness/flows/view/list")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId)
                        .param("accountId", accountId)
                        .param("subjectId", subjectId)
                        .param("sortAsc", String.valueOf(sortAsc));
        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("testQueryViewWitnessFlows result is :{}", response);
        RestResult<QueryWitnessFlowsResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<QueryWitnessFlowsResponse>>() {});
        Assert.assertTrue(null != restResult && null != restResult.getData());
        Assert.assertTrue(restResult.getData().getTotal() > 0, "用户可查看的天印流程列表不应为空");
    }

    @Test(description = "测试获取用户可出证的天印流程列表")
    public void testQueryIssueWitnessFlows() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("QueryIssueWitnessFlows");
        QueryIssueWitnessFlowRequest reqData =
                (QueryIssueWitnessFlowRequest) testJsonObj.getReqData();

        String accountId = reqData.getAccountId();
        String subjectId = reqData.getSubjectId();

        MockHttpServletRequestBuilder builder =
                MockMvcRequestBuilders.get("/v2/witness/flows/issue/list")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .param("accountId", accountId)
                        .param("subjectId", subjectId)
                        .header(HEADER_OPERATOR_ID, accountId)
                        .header(HEADER_TENANT_ID, subjectId);
        String response = mockMvc.perform(builder).andReturn().getResponse().getContentAsString();
        log.info("testQueryIssueWitnessFlows result is :{}", response);
        RestResult<QueryWitnessFlowsResponse> restResult =
                JSONObject.parseObject(
                        response, new TypeReference<RestResult<QueryWitnessFlowsResponse>>() {});
        Assert.assertTrue(null != restResult && null != restResult.getData());
        Assert.assertTrue(restResult.getData().getTotal() > 0, "用户可出证的天印流程列表不应为空");
    }

    @Test(description = "测试获取用户可出证的天印流程列表 --- 企业账号不存在或已注销")
    public void testQueryIssueWitnessFlows_orgNotExist() throws Exception {
        String accountId = ACCOUNT_ID_3;
        String subjectId = ACCOUNT_ID_4;
        // 预期错误信息
        String expectMessage = SaasBizResultCode.ORG_NOT_EXIST.getMessage();
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/witness/flows/issue/list")
                                .param("accountId", accountId)
                                .param("subjectId", subjectId)
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_APP_ID, APP_ID)
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value(expectMessage));
    }

    @Test(description = "测试判断用户是否有天印流程,用户参与的")
    public void testCheckHasWitnessFlowsUserJoin() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("CheckHasWitnessFlowsUserJoin");
        CheckHasWitnessFlowRequest reqData = (CheckHasWitnessFlowRequest) testJsonObj.getReqData();

        String accountId = reqData.getAccountId();
        String subjectId = reqData.getSubjectId();
        Integer type = reqData.getType();

        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/witness/flows/user/check")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .param("accountId", accountId)
                                .param("subjectId", subjectId)
                                .param("type", null == type ? "" : type.toString())
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.existed").value(true));
    }

    @Test(description = "测试判断用户是否有天印流程,用户查看的")
    public void testCheckHasWitnessFlowsUserQuery() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("CheckHasWitnessFlowsUserQuery");
        CheckHasWitnessFlowRequest reqData = (CheckHasWitnessFlowRequest) testJsonObj.getReqData();

        String accountId = reqData.getAccountId();
        String subjectId = reqData.getSubjectId();
        Integer type = reqData.getType();

        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/witness/flows/user/check")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .param("accountId", accountId)
                                .param("subjectId", subjectId)
                                .param("type", null == type ? "" : type.toString())
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, subjectId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.existed").value(true));
    }

    @Test(description = "测试获取签署任务概要")
    public void testQuerySignTaskOutlineWitness() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("QuerySignTaskOutlineWitness");
        QueryWitnessFlowOutlineRequest reqData =
                (QueryWitnessFlowOutlineRequest) testJsonObj.getReqData();

        String accountId = reqData.getAccountId();
        String flowId = reqData.getFlowId();

        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/witness/flows/outline")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .param("accountId", accountId)
                                .param("flowId", flowId)
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, accountId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.signDocs").isNotEmpty())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.signers").isNotEmpty());
    }

    @Test(description = "测试获取天印流程批量签署列表及地址")
    public void testWitnessFlowsBatchSignList() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("QueryUserWitnessFlows");
        QueryUserWitnessFlowRequest reqData =
                (QueryUserWitnessFlowRequest) testJsonObj.getReqData();

        String accountId = reqData.getAccountId();
        String subjectId = reqData.getSubjectId();

        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get("/v2/witness/flows/user/list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .param("accountId", accountId)
                                        .param("subjectId", subjectId)
                                        .param("operable", "true")
                                        .header(HEADER_OPERATOR_ID, accountId)
                                        .header(HEADER_TENANT_ID, subjectId))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        String response = mvcResult.getResponse().getContentAsString();
        log.info("query user flow list result : " + response);
        QueryWitnessFlowsResponse queryResponse =
                JSONObject.parseObject(
                                response,
                                new TypeReference<RestResult<QueryWitnessFlowsResponse>>() {})
                        .getData();
        List<String> flowIds = Lists.newArrayList();
        for (WitnessFlowVO flow : queryResponse.getFlows()) {
            flowIds.add(flow.getFlowId());
        }

        GetWitnessFlowBatchSignListRequest listRequest = new GetWitnessFlowBatchSignListRequest();
        listRequest.setAccountId(accountId);
        listRequest.setFlowIds(flowIds);
        // 首次获取批量签署流程列表
        mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v2/witness/flows/batchsign/list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .content(JSONObject.toJSONString(listRequest))
                                        .header(HEADER_OPERATOR_ID, accountId)
                                        .header(HEADER_TENANT_ID, subjectId))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        String firstTimeBatchSignListResp = mvcResult.getResponse().getContentAsString();
        log.info("query batch sign list result : " + firstTimeBatchSignListResp);

        GetWitnessFlowBatchSignListResponse firstTimeBatchSignListResponse =
                JSONObject.parseObject(
                                firstTimeBatchSignListResp,
                                new TypeReference<
                                        RestResult<GetWitnessFlowBatchSignListResponse>>() {})
                        .getData();

        // 第二次获取批量签署流程列表
        listRequest.setBatchSerialId(firstTimeBatchSignListResponse.getBatchSerialId());
        String secondTimeBatchSignListResp = mvcResult.getResponse().getContentAsString();
        log.info("query batch sign list result : " + secondTimeBatchSignListResp);

        GetWitnessFlowBatchSignListResponse secondTimeBatchSignListResponse =
                JSONObject.parseObject(
                                secondTimeBatchSignListResp,
                                new TypeReference<
                                        RestResult<GetWitnessFlowBatchSignListResponse>>() {})
                        .getData();
        Assert.assertTrue(
                firstTimeBatchSignListResponse
                        .getBatchSerialId()
                        .equals(secondTimeBatchSignListResponse.getBatchSerialId()),
                "两次查询返回的序列号id应相同");
        Assert.assertTrue(
                firstTimeBatchSignListResponse.getNotSupportBatchSignList().size()
                        == secondTimeBatchSignListResponse.getNotSupportBatchSignList().size(),
                "两次查询返回的不可批量签署列表应相同");

        if (CollectionUtils.isNotEmpty(secondTimeBatchSignListResponse.getBatchSignList())) {
            GetWitnessFlowBatchSignListResponse.WitnessAppFlowsVO appFlows =
                    secondTimeBatchSignListResponse.getBatchSignList().get(0);
            GetWitnessFlowBatchSignUrlRequest urlRequest = new GetWitnessFlowBatchSignUrlRequest();
            urlRequest.setAccountId(accountId);
            urlRequest.setAppId(appFlows.getAppId());
            urlRequest.setFlowIds(flowIds);
            // 获取批量签署地址
            mvcResult =
                    mockMvc.perform(
                                    MockMvcRequestBuilders.post("/v2/witness/flows/batchsign/url")
                                            .contentType(MediaType.APPLICATION_JSON_UTF8)
                                            .content(JSONObject.toJSONString(urlRequest))
                                            .header(HEADER_OPERATOR_ID, accountId)
                                            .header(HEADER_TENANT_ID, subjectId))
                            .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                            .andReturn();
            String batchSignUrlResp = mvcResult.getResponse().getContentAsString();
            GetWitnessFlowBatchSignUrlResponse batchSignUrlResponse =
                    JSONObject.parseObject(
                                    batchSignUrlResp,
                                    new TypeReference<
                                            RestResult<GetWitnessFlowBatchSignUrlResponse>>() {})
                            .getData();
            String signSerialId = batchSignUrlResponse.getSignSerialId();
            Assert.assertTrue(StringUtils.isNotBlank(signSerialId));

            String urlTemplate = "/v2/witness/flows/batchsign/" + signSerialId + "/info";
            mvcResult =
                    mockMvc.perform(
                                    MockMvcRequestBuilders.get(urlTemplate)
                                            .contentType(MediaType.APPLICATION_JSON_UTF8)
                                            .param("appId", appFlows.getAppId())
                                            .header(HEADER_OPERATOR_ID, accountId)
                                            .header(HEADER_TENANT_ID, subjectId))
                            .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                            .andReturn();
            log.info("sign serial info : " + mvcResult.getResponse().getContentAsString());
        }
    }
}
