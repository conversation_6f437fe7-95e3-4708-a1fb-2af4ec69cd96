package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerCloseRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerFormSaveRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerListRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerMatchStructRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerUpdateFormDataRequest;
import com.timevale.saasbiz.rest.bean.ledger.request.LedgerUpdateStatusRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;


import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

/**
 * LedgerRestTest
 *
 * <AUTHOR>
 * @since 2023/9/11 2:10 下午
 */
@Slf4j
@JsonDataIgnore
public class LedgerRestTest extends BaseServiceTest {

    @Test()
    public void test() throws Exception {
        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;

        LedgerListRequest request = new LedgerListRequest();
        request.setPageNum(1);
        request.setPageSize(10);

        MvcResult result =
                mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/contract-ledger/form-list")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, tenantId)
                                .header(HEADER_OPERATOR_ID, operatorId)
                                .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        Assert.assertNotNull(result, "result is null");

    }

    private void waitAndCloseLedger(String formId, String tenantId, String operatorId) throws Exception {
        int count =0;
        while (count < 3) {
            LedgerCloseRequest closeRequest = new LedgerCloseRequest();
            closeRequest.setFormId(formId);
            MvcResult mvcResult1 = mockMvc.perform(
                            MockMvcRequestBuilders.post("/v3/contract-ledger/close")
                                    .contentType(MediaType.APPLICATION_JSON_UTF8)
                                    .header(HEADER_TENANT_ID, tenantId)
                                    .header(HEADER_OPERATOR_ID, operatorId)
                                    .content(JsonUtils.obj2json(closeRequest)))
                    .andReturn();
            RestResult<Void> restResult = JSON.parseObject(mvcResult1.getResponse().getContentAsString(), RestResult.class);
            if (restResult.getCode() == 0) {
                return;
            }
            Thread.sleep(5000);
            count++;
        }
        Assert.fail("ledger close timeout");
    }

    @Test()
    public void testGetExtractConfig() throws Exception {

        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;

        MvcResult resultDetail =  mockMvc.perform(
                MockMvcRequestBuilders.get("/v3/contract-ledger/get-extract-config")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, tenantId)
                        .header(HEADER_OPERATOR_ID, operatorId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();
        Assert.assertNotNull(resultDetail, "result is null");

    }

    @Test
    public void updateFormData() throws Exception {

//        String operatorId = TestAccountConstants.ACCOUNT_ID_1;
//        String tenantId = TestAccountConstants.SENIOR_SUBJECT_1;
//        LedgerUpdateFormDataRequest request = new LedgerUpdateFormDataRequest();
//        request.setMenuId();
//        request.setFieldDataList();
//        MvcResult resultDetail =  mockMvc.perform(
//                        MockMvcRequestBuilders.post("/v3/contract-ledger/update-form-data")
//                                .contentType(MediaType.APPLICATION_JSON_UTF8)
//                                .content(JSON.toJSONString(request))
//                                .header(HEADER_TENANT_ID, tenantId)
//                                .header(HEADER_OPERATOR_ID, operatorId))
//                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
//                .andReturn();
    }
}
