package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSON;
import com.timevale.footstone.base.model.enums.ClientEnum;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.rest.bean.packagewarn.request.PackageWarnIgnoreRequest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.constant.TestAccountConstants.SPECIAL_CUSTOMER_SUBJECT_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.SPECIAL_CUSTOMER_SUBJECT_MEMBER_1;
import static com.timevale.saasbiz.model.exception.SaasBizResultCode.SAAS_ILLEGAL_PARAM;

/**
 * <AUTHOR>
 * @since 2023-06-14
 */
@JsonDataIgnore
public class PackageWarnOperateRestTest extends BaseServiceTest {

    /** 7天内不提示 */
    @Test
    public void testShortIgnoreWarn() throws Exception {

        PackageWarnIgnoreRequest request = new PackageWarnIgnoreRequest();
        request.setAccountId(SPECIAL_CUSTOMER_SUBJECT_MEMBER_1);
        request.setOrgId(SPECIAL_CUSTOMER_SUBJECT_1);
        request.setSourceCode(ClientEnum.WEB.getClientId());

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/package/warn/short/ignore")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header("X-Tsign-Open-App-Id", "**********")
                                .content(JSON.toJSONString(request)))
                .andExpect(
                        mvcResult -> System.out.print(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));

        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/package/warn/ignored")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header("X-Tsign-Open-App-Id", "**********")
                                .param("orgId", SPECIAL_CUSTOMER_SUBJECT_1)
                                .param("accountId", SPECIAL_CUSTOMER_SUBJECT_MEMBER_1)
                                .param("sourceCode", ClientEnum.WEB.getClientId()))
                .andExpect(
                        mvcResult -> System.out.print(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    /** 本次订单未续购前不提示 */
    @Test
    public void testLastIgnoreWarn() throws Exception {
        PackageWarnIgnoreRequest request = new PackageWarnIgnoreRequest();
        request.setAccountId(SPECIAL_CUSTOMER_SUBJECT_MEMBER_1);
        request.setOrgId(SPECIAL_CUSTOMER_SUBJECT_1);
        request.setSourceCode(ClientEnum.WEB.getClientId());

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/package/warn/last/ignore")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header("X-Tsign-Open-App-Id", "**********")
                                .content(JSON.toJSONString(request)))
                .andExpect(
                        mvcResult -> System.out.print(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));

        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/package/warn/ignored")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header("X-Tsign-Open-App-Id", "**********")
                                .param("orgId", SPECIAL_CUSTOMER_SUBJECT_1)
                                .param("accountId", SPECIAL_CUSTOMER_SUBJECT_MEMBER_1)
                                .param("sourceCode", ClientEnum.WEB.getClientId()))
                .andExpect(
                        mvcResult -> System.out.print(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    /** 参数校验覆盖 */
    @Test
    public void testCheckParams() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/package/warn/short/ignore")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header("X-Tsign-Open-App-Id", "**********")
                                .content(JSON.toJSONString(new PackageWarnIgnoreRequest())))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(SAAS_ILLEGAL_PARAM.getCode()));

        PackageWarnIgnoreRequest request = new PackageWarnIgnoreRequest();
        request.setAccountId("testUser");
        request.setOrgId("testOrg");
        request.setSourceCode(ClientEnum.WEB.getClientId());
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v1/package/warn/last/ignore")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header("X-Tsign-Open-App-Id", "**********")
                                .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));

        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/package/warn/ignored")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header("X-Tsign-Open-App-Id", "**********")
                                .param("orgId", "")
                                .param("accountId", "")
                                .param("sourceCode", ""))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }
}
