package com.timevale.saasbiz.rest;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.SENIOR_SUBJECT_1;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.*;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.seal.dto.SealMemberVisibleDTO;
import com.timevale.saasbiz.model.bean.seal.dto.input.GetSealVisibleScopeInputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.input.SealDetailInputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.input.SealPartVisibleDetailInputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.output.GetSealVisibleScopeOutputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.output.SealDetailOutputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.output.SealPartVisibleDetailOutputDTO;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.seal.request.*;
import com.timevale.saasbiz.rest.bean.seal.response.*;
import com.timevale.saasbiz.service.seal.SealOrganizationService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023-05-31 14:29
 */
@Slf4j
public class SealOrganizationRestTest extends BaseServiceTest {

    private static final String ESIGN_TEST_APP_ID = "**********";
    private static final String NOT_DELETE_SEAL_ID = "323c14d9-ff46-4150-8e93-d36297237db3";
    // 默认给saas-common-manager使用的单测数据
    // esigntest测试saascommon单测用
    private static final String CUSTOM_ORG_OID = "97298c2243c14ee792e54492058f0391";
    // 账号：测试陈是是 （管理员及法人）
    private static final String CUSTOM_ACCOUNT_OID = "307a45c17d394563885f861dd961d7e8";

    private static final String USAGE_SEAL_ID = "6f1e94a5-401b-4213-bd32-c444d7bced54";

    @Autowired private SealOrganizationService sealOrganizationService;

    @Test(description = "根据印章id查询印章详情冒烟测试")
    public void testSetDefaultSeal() throws Exception {
        SetOrgDefaultSealRequest request = new SetOrgDefaultSealRequest();
        request.setSealId(NOT_DELETE_SEAL_ID);
        mockMvc.perform(
                        MockMvcRequestBuilders.put(
                                        "/v1/saas-common/organizations/seals/set-default-seal")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                .header(HEADER_OPERATOR_ID, CUSTOM_ACCOUNT_OID)
                                .header(HEADER_TENANT_ID, CUSTOM_ORG_OID)
                                .content(JSON.toJSONString(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        SealDetailInputDTO dto = new SealDetailInputDTO();
        dto.setAppId(ESIGN_TEST_APP_ID);
        dto.setOrgOid(CUSTOM_ORG_OID);
        dto.setSealId(NOT_DELETE_SEAL_ID);
        dto.setOperatorOid(CUSTOM_ACCOUNT_OID);
        SealDetailOutputDTO outputDTO = sealOrganizationService.getOrgSealDetail(dto);

        Assert.assertTrue(Objects.nonNull(outputDTO), "outputDTO is null");
        Assert.assertTrue(outputDTO.isDefaultFlag(), "set default error");
    }

    @Test(description = "获取用印统计列表查询")
    public void testGetUsageList() throws Exception {
        Date date = DateUtils.parseDate("2023-06-30 00:00:00", "yyyy-MM-dd HH:mm:ss");

        UsageListRequest request = new UsageListRequest();
        request.setSealId(USAGE_SEAL_ID);
        request.setLogTimeStart(DateUtils.addDays(date, -30).getTime());
        request.setLogTimeEnd(date.getTime());

        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/organizations/seals/usage-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<UsageListResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<UsageListResponse>>() {});

        Assert.assertNotNull(response.getData(), "data is null");
        Assert.assertNotNull(response.getData().getTotal(), "total is null");
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData().getList()), "list empty");
    }

    @Test(description = "获取用印统计列表查询-时间范围")
    public void testGetUsageListDuration() throws Exception {
        Date date = DateUtils.parseDate("2023-06-30 00:00:00", "yyyy-MM-dd HH:mm:ss");
        UsageListRequest request = new UsageListRequest();
        request.setSealId(USAGE_SEAL_ID);
        request.setLogTimeStart(DateUtils.addDays(date, -30).getTime());
        request.setLogTimeEnd(date.getTime());

        // 三个月临界值
        Date threeMonthAgo = DateUtils.addMonths(date, -3);
        request.setLogTimeStart(threeMonthAgo.getTime());
        MvcResult mvcResult1 =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/organizations/seals/usage-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<UsageListResponse> response1 =
                JsonUtilsPlus.parseObject(
                        mvcResult1.getResponse().getContentAsString(),
                        new TypeReference<RestResult<UsageListResponse>>() {});
        Assert.assertEquals(response1.getCode(), 0, "threshold block");

        // 查询范围超过三个月
        request.setLogTimeStart(DateUtils.addDays(threeMonthAgo, -1).getTime());
        MvcResult mvcResult2 =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/organizations/seals/usage-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(*********))
                        .andReturn();
        RestResult<UsageListResponse> response2 =
                JsonUtilsPlus.parseObject(
                        mvcResult2.getResponse().getContentAsString(),
                        new TypeReference<RestResult<UsageListResponse>>() {});
        Assert.assertEquals(response2.getCode(), *********, "over duration not block");
    }

    @Test(description = "查询印章详情")
    public void testGetSealDetail() throws Exception {
        SealDetailRequest request = new SealDetailRequest();
        request.setSealId(USAGE_SEAL_ID);

        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post(
                                                "/v1/saas-common/organizations/seals/seal-detail")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, APP_ID)
                                        .header(HEADER_CLIENT_ID, CLIENT_ID_WEB)
                                        .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<SealDetailResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<SealDetailResponse>>() {});

        Assert.assertNotNull(response.getData(), "data is null");
        Assert.assertNotNull(response.getData().getSealId(), "sealId is null");
    }

    @Test(description = "查询印章可见范围详情——查询成员列表")
    public void testSealPartVisibleScopeDetailForDept() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(
                                                "/v1/saas-common/organizations/seals/part-visible-detail")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, "97298c2243c14ee792e54492058f0391")
                                        .header(HEADER_OPERATOR_ID, "307a45c17d394563885f861dd961d7e8")
                                        .param("sealId", "db935a12-c30f-4494-9fd7-e6d6cdc571cc")
                                        .param("type", "ORGAN_DEPT")
                                        .param("pageNo", "1")
                                        .param("pageSize", "3")
                        )
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<SealPartVisibleDetailResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<SealPartVisibleDetailResponse>>() {});

        Assert.assertNotNull(response.getData(), "data is null");
        Assert.assertNotNull(response.getData().getTotal(), "total is null");
    }

    @Test(description = "查询印章可见范围详情——查询部门列表")
    public void testSealPartVisibleScopeDetailForMember() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(
                                                "/v1/saas-common/organizations/seals/part-visible-detail")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, "97298c2243c14ee792e54492058f0391")
                                        .header(HEADER_OPERATOR_ID, "307a45c17d394563885f861dd961d7e8")
                                        .param("sealId", "db935a12-c30f-4494-9fd7-e6d6cdc571cc")
                                        .param("type", "ORGAN_MEMBER")
                                        .param("pageNo", "1")
                                        .param("pageSize", "3")
                        )
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<SealPartVisibleDetailResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<SealPartVisibleDetailResponse>>() {});

        Assert.assertNotNull(response.getData(), "data is null");
        Assert.assertNotNull(response.getData().getTotal(), "total is null");
    }

    @Test(description = "设置印章可见范围详情,设置一个部门一个成员")
    public void testSetSealVisibleScope() throws Exception {
        SetSealVisibleScopeRequest request = new SetSealVisibleScopeRequest();
        request.setSealId("db935a12-c30f-4494-9fd7-e6d6cdc571cc");
        request.setVisibleScope("PART_MEMBER");
        request.setAssignedDeptIdList(
                Collections.singletonList("2ad4555184db4801b32279d8e4094775"));
        request.setAssignedMemberIdList(
                Collections.singletonList("100476ad9a72425fad13e06ddaf1d218"));

        mockMvc.perform(
                MockMvcRequestBuilders.put(
                        "/v1/saas-common/organizations/seals/visible-scope")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_TENANT_ID, "97298c2243c14ee792e54492058f0391")
                        .header(HEADER_OPERATOR_ID, "307a45c17d394563885f861dd961d7e8")
                        .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        GetSealVisibleScopeInputDTO dto = new GetSealVisibleScopeInputDTO();
        dto.setOrgOid("97298c2243c14ee792e54492058f0391");
        dto.setSealId("db935a12-c30f-4494-9fd7-e6d6cdc571cc");
        dto.setOperatorOid("307a45c17d394563885f861dd961d7e8");
        GetSealVisibleScopeOutputDTO output = sealOrganizationService.getVisibleScope(dto);

        Assert.assertTrue(Objects.nonNull(output), "outputDTO is null");
        Assert.assertNotNull(output.getVisibleScope(), "visibleScope is null");
    }

    @Test(description = "设置印章可见范围详情")
    public void testSetSealVisibleScopeForPartVisible() throws Exception {
        SetSealVisibleScopeRequest request = new SetSealVisibleScopeRequest();
        request.setSealId(USAGE_SEAL_ID);
        request.setVisibleScope("ALL");

        mockMvc.perform(
                        MockMvcRequestBuilders.put(
                                        "/v1/saas-common/organizations/seals/visible-scope")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, SENIOR_SUBJECT_1)
                                .header(HEADER_OPERATOR_ID, ACCOUNT_ID_1)
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        GetSealVisibleScopeInputDTO dto = new GetSealVisibleScopeInputDTO();
        dto.setOrgOid(SENIOR_SUBJECT_1);
        dto.setSealId(USAGE_SEAL_ID);
        dto.setOperatorOid(ACCOUNT_ID_1);
        GetSealVisibleScopeOutputDTO output = sealOrganizationService.getVisibleScope(dto);

        Assert.assertTrue(Objects.nonNull(output), "outputDTO is null");
        Assert.assertNotNull(output.getVisibleScope(), "visibleScope is null");
    }

    @Test(description = "查询印章可见范围")
    public void testSealVisibleScope() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get(
                                                "/v1/saas-common/organizations/seals/visible-scope")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_TENANT_ID, "97298c2243c14ee792e54492058f0391")
                                        .header(HEADER_OPERATOR_ID, "307a45c17d394563885f861dd961d7e8")
                                        .param("sealId", "db935a12-c30f-4494-9fd7-e6d6cdc571cc")
                        )
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<GetSealVisibleScopeResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<GetSealVisibleScopeResponse>>() {});

        Assert.assertNotNull(response.getData(), "data is null");
        Assert.assertNotNull(response.getData().getVisibleScope(), "visibleScope is null");
    }

    @Test(description = "设置印章可见范围详情")
    public void testDeleteSealVisibleScope() throws Exception {
        DeleteSealVisibleScopeRequest request = new DeleteSealVisibleScopeRequest();
        request.setSealId("db935a12-c30f-4494-9fd7-e6d6cdc571cc");
        request.setMemberOidList(Arrays.asList("100476ad9a72425fad13e06ddaf1d218"));

        mockMvc.perform(
                        MockMvcRequestBuilders.post(
                                        "/v1/saas-common/organizations/seals/delete-visible-scope")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_TENANT_ID, "97298c2243c14ee792e54492058f0391")
                                .header(HEADER_OPERATOR_ID, "307a45c17d394563885f861dd961d7e8")
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        SealPartVisibleDetailInputDTO input = new SealPartVisibleDetailInputDTO();
        input.setOrgOid("97298c2243c14ee792e54492058f0391");
        input.setSealId("db935a12-c30f-4494-9fd7-e6d6cdc571cc");
        input.setOperatorOid("307a45c17d394563885f861dd961d7e8");
        input.setType("ORGAN_MEMBER");
        input.setPageNo(1);
        input.setPageSize(10);

        SealPartVisibleDetailOutputDTO output = sealOrganizationService.getSealPartVisibleDetail(input);

        Assert.assertTrue(Objects.nonNull(output), "outputDTO is null");
        Assert.assertNotNull(output.getVisibleMember(), "visibleMember is null");

        boolean deleteSuccess = true;
        for (SealMemberVisibleDTO dto : output.getVisibleMember().getPartMemberList()) {
            if(dto.getMemberId().equals("db935a12-c30f-4494-9fd7-e6d6cdc571cc")) {
                deleteSuccess = false;
                break;
            }
        }

        Assert.assertTrue(deleteSuccess, "delete failed");
    }

    @Test(description = "分页查询印章列表冒烟测试")
    public void testPageSimpleSealList() throws Exception {
        // 授权章
        PageSimpleSealListRequest request = new PageSimpleSealListRequest();
        request.setGrantedSeal(true);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setSealOwnerOid(ORG_OID);
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v1/saas-common/organizations/seals/simple-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<PageSimpleSealListResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<PageSimpleSealListResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData().getSeals()));
        // 本企业章
        request.setGrantedSeal(false);
        mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.post("/v1/saas-common/organizations/seals/simple-list")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(HEADER_APP_ID, ESIGN_TEST_APP_ID)
                                        .header(HEADER_OPERATOR_ID, ACCOUNT_OID)
                                        .header(HEADER_TENANT_ID, ORG_OID)
                                        .content(JsonUtils.obj2json(request)))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
       response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<PageSimpleSealListResponse>>() {});
        Assert.assertNotNull(response);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData().getSeals()));
    }
}
