package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.dal.bean.AccountNoviceOperateDO;
import com.timevale.saasbiz.dal.dao.AccountNoviceOperateDAO;
import com.timevale.saasbiz.model.enums.novice.NoviceOperateTypeEnum;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.rest.bean.process.request.ProcessStartRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_4;

/**
 * 流程发起rest单测
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Slf4j
public class ProcessStartRestTest extends BaseServiceTest {

    @Autowired AccountNoviceOperateDAO accountNoviceOperateDAO;

    @Test(description = "流程体验签署发起")
    public void processExperienceStart() throws Exception {
        String accountId = ACCOUNT_ID_4;
        ProcessStartRequest request =
                copyJsonData("ExperienceProcessStart", ProcessStartRequest.class);
        request.setTaskName("单测-流程体验签署发起");
        request.setInitiatorAccountId(accountId);

        // 体验签署流程已发起
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/processes/experience/start")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header("X-Tsign-Open-Operator-Id", accountId)
                                .header("X-Tsign-Open-Tenant-Id", accountId)
                                .header("X-Tsign-Client-Id", CLIENT_ID_WEB)
                                .header("X-Tsign-Open-App-Id", APP_ID))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SaasBizResultCode.EXPERIENCE_START_ALREADY_DONE.getCode()));
        // 清除体验签署发起记录
        String operateName = NoviceOperateTypeEnum.COMPLETE_EXPERIENCE_START.getName();
        AccountNoviceOperateDO doneOperate = accountNoviceOperateDAO.queryByAccountOidName(accountId, operateName);
        if (null != doneOperate && BooleanUtils.toBoolean(doneOperate.getStatus())) {
            doneOperate.setStatus(BooleanUtils.toInteger(false));
            accountNoviceOperateDAO.updateStatus(doneOperate);
        }
        // 发起体验签署流程
        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/processes/experience/start")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header("X-Tsign-Open-Operator-Id", accountId)
                                .header("X-Tsign-Open-Tenant-Id", accountId)
                                .header("X-Tsign-Client-Id", CLIENT_ID_WEB)
                                .header("X-Tsign-Open-App-Id", APP_ID))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.processId").isNotEmpty());
    }
}
