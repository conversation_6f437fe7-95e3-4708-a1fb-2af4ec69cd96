package com.timevale.saasbiz.rest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.account.dto.output.QueryOrgTransferConfigsOutputDTO;
import com.timevale.saasbiz.model.enums.transfer.TransferConfigTypeEnum;
import com.timevale.saasbiz.model.utils.JsonUtilsPlus;
import com.timevale.saasbiz.rest.bean.RestResult;
import com.timevale.saasbiz.rest.bean.account.OrgTransferConfigVO;
import com.timevale.saasbiz.rest.bean.account.request.UpdateOrgTransferConfigsRequest;
import com.timevale.saasbiz.rest.bean.account.response.QueryOrgTransferConfigsResponse;
import com.timevale.saasbiz.service.account.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

import static com.timevale.saasbiz.constant.TestAccountConstants.*;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.exception.SaasBizResultCode.SAAS_ILLEGAL_PARAM;

/**
 * 用户信息接口单测
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Slf4j
@JsonDataIgnore
public class AccountRestTest extends BaseServiceTest {

    @Autowired private AccountService accountService;
    @Test(description = "获取用户基本信息")
    public void queryAccountInfo() throws Exception {
        // 用户id未传
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/common/user/accountInfo")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, ""))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(
                        MockMvcResultMatchers.jsonPath("$.code")
                                .value(SAAS_ILLEGAL_PARAM.getCode()));
        // 正常获取
        String accountId = BASE_SUBJECT_MEMBER_1;
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v2/common/user/accountInfo")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, accountId)
                                .header(HEADER_TENANT_ID, BASE_SUBJECT_1))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.accountId").value(accountId));
    }

    @Test(description = "用户权限校验-直接发起权限")
    public void userPermission() throws Exception {
        // 有直接发起权限
        mockMvc.perform(
                        MockMvcRequestBuilders.get(
                                        "/v2/common/user/permission?resource=CONTRACT&operation=DIRECT_START")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, BASE_SUBJECT_MEMBER_1)
                                .header(HEADER_TENANT_ID, BASE_SUBJECT_1))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Boolean.TRUE));

        // 无直接发起权限
        mockMvc.perform(
                        MockMvcRequestBuilders.get(
                                        "/v2/common/user/permission?resource=CONTRACT&operation=DIRECT_START")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, BASE_SUBJECT_MEMBER_3)
                                .header(HEADER_TENANT_ID, BASE_SUBJECT_1))
                .andExpect(mvcResult -> log.info(mvcResult.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(Boolean.FALSE));
    }

    @Test
    public void testQueryOrgConfig() throws Exception {
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get("/v2/common/user/org-transfer-configs-get")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(
                                                HEADER_OPERATOR_ID,
                                                "307a45c17d394563885f861dd961d7e8")
                                        .header(
                                                HEADER_TENANT_ID,
                                                "ccb284fadd34405ba68a0c710cccc74f"))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<QueryOrgTransferConfigsResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<QueryOrgTransferConfigsResponse>>() {});
        Assert.assertNotNull(response, "request is null");
        Assert.assertNotNull(response.getData(), "data is null");
    }

    @Test
    public void testUpdateOrgConfig() throws Exception {
        UpdateOrgTransferConfigsRequest request = new UpdateOrgTransferConfigsRequest();
        List<OrgTransferConfigVO> list = new ArrayList<>();
        OrgTransferConfigVO vo = new OrgTransferConfigVO();
        vo.setType("contract_approval_transfer");
        vo.setTransferToUserType("admin");
        vo.setTransferToUserOid("haha");
        list.add(vo);
        request.setTransferConfigList(list);

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v2/common/user/org-transfer-configs-save")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, "307a45c17d394563885f861dd961d7e8")
                                .header(HEADER_TENANT_ID, "ccb284fadd34405ba68a0c710cccc74f")
                                .content(JsonUtils.obj2json(request)))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn();

        QueryOrgTransferConfigsOutputDTO output =
                accountService.queryOrgTransferConfigs(
                        TransferConfigTypeEnum.getEnumList(), "ccb284fadd34405ba68a0c710cccc74f");
        Assert.assertNotNull(output, "query failed");
        Assert.assertNotNull(output.getOrgTransferConfigList(), "list is null");
    }

    @Test
    public void testQueryOrgConfigNoAdmin() throws Exception {
        //无管理员的企业
        String orgOid="d37eb9ebc35e492da9496fa46e36c82b";
        MvcResult mvcResult =
                mockMvc.perform(
                                MockMvcRequestBuilders.get("/v2/common/user/org-transfer-configs-get")
                                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                                        .header(
                                                HEADER_OPERATOR_ID,
                                                "a946a47e27994d45ba6f9af128a30638")
                                        .header(
                                                HEADER_TENANT_ID,
                                                orgOid))
                        .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                        .andReturn();
        RestResult<QueryOrgTransferConfigsResponse> response =
                JsonUtilsPlus.parseObject(
                        mvcResult.getResponse().getContentAsString(),
                        new TypeReference<RestResult<QueryOrgTransferConfigsResponse>>() {});
        Assert.assertNotNull(response, "request is null");
        Assert.assertNotNull(response.getData(), "data is null");
    }

}
