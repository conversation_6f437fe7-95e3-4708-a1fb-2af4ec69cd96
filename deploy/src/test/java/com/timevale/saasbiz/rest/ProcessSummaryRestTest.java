package com.timevale.saasbiz.rest;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.model.RestResult;
import com.google.common.collect.Lists;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.enums.process.ProcessSummaryCheckSceneEnum;
import com.timevale.saasbiz.rest.bean.process.request.*;
import com.timevale.saasbiz.rest.bean.process.response.ProcessSummaryCreateCheckResponse;
import com.timevale.saasbiz.rest.bean.process.response.ProcessSummaryKeywordSearchResponse;

import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;

@Slf4j
@JsonDataIgnore
public class ProcessSummaryRestTest extends BaseServiceTest {

    private static final String accountId = "66e4287f478a446a80aa63f258b7b8fb";
    private static final String subjectId = "fb06392c6da34001a3f74d9abc854407";

    @Test(description = "测试获取合同摘要")
    public void testGetProcessSummary() throws Exception {
        String processId = "5b82457404114320afd660859d7cb341";
        String fileId = "0a07b49909754076a4973e29a3ea4cff";

        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v3/processes/get-summary-info")
                                .param("processId", processId)
                                .param("fileId", fileId)
                                .header("X-Tsign-Open-Operator-Id", accountId)
                                .header("X-Tsign-Open-Tenant-Id", subjectId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "测试修改合同摘要")
    public void testUpdateProcessSummary() throws Exception {
        String summary = "房屋租赁合同主要内容是将M78星云-奥特之星的房屋出租给王二麻子作为生活用途，租赁期限为自2023年07月28日至2023年07月31日。";
        ProcessSummaryKeyInfoUpdateRequest keyInfoModel = new ProcessSummaryKeyInfoUpdateRequest();
        keyInfoModel.setSeq(1);
        keyInfoModel.setName("合同名称");
        keyInfoModel.setValue("房屋租赁合同");
        ArrayList<ProcessSummaryKeyInfoUpdateRequest> keyInfo = Lists.newArrayList(keyInfoModel);
        String processId = "4ae1089e61fa4eb3bedfc1eeac5c70b2";
        String fileId = "1593112ba16b40a0b5995d410f627b65";
        ProcessSummaryUpdateRequest request = new ProcessSummaryUpdateRequest();
        request.setProcessId(processId);
        request.setFileId(fileId);
        request.setSummary(summary);
        request.setKeyInfo(keyInfo);

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/processes/update-summary-info")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header("X-Tsign-Open-Operator-Id", accountId)
                                .header("X-Tsign-Open-Tenant-Id", subjectId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "测试创建合同摘要")
    public void testCreateProcessSummary() throws Exception {
        String processId = "5b82457404114320afd660859d7cb341";
        String fileId = "0a07b49909754076a4973e29a3ea4cff";
        ProcessSummaryCreateRequest request = new ProcessSummaryCreateRequest();
        request.setProcessId(processId);
        request.setFileId(fileId);

        mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/processes/create-summary-info")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header("X-Tsign-Open-Operator-Id", accountId)
                                .header("X-Tsign-Open-Tenant-Id", subjectId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0));
    }

    @Test(description = "测试校验创建合同摘要")
    public void testCheckCreateProcessSummary() throws Exception {
        String processId = "5b82457404114320afd660859d7cb341";
        ProcessSummaryCreateCheckRequest request = new ProcessSummaryCreateCheckRequest();
        request.setProcessId(processId);
        MvcResult response= mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/processes/check-create-summary")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header("X-Tsign-Open-Operator-Id", accountId)
                                .header("X-Tsign-Open-Tenant-Id", subjectId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0)).andReturn();
        RestResult<ProcessSummaryCreateCheckResponse> restResult = JSONObject.parseObject(response.getResponse().getContentAsString(), new TypeReference<RestResult<ProcessSummaryCreateCheckResponse>>(){});
        Assert.assertEquals(restResult.getData().getCheckScene(), ProcessSummaryCheckSceneEnum.NORMAL.getCode());
        Assert.assertNull(restResult.getData().getNotCreateReason());
    }

    @Test(description = "测试校验创建合同摘要异常")
    public void testCheckCreateProcessSummaryFalse() throws Exception {
        String processId = "5b82457404114320afd660859d7cb341";
        ProcessSummaryCreateCheckRequest request = new ProcessSummaryCreateCheckRequest();
        request.setProcessId(processId);
        MvcResult response= mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/processes/check-create-summary")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header("X-Tsign-Open-Operator-Id", "test")
                                .header("X-Tsign-Open-Tenant-Id", subjectId))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0)).andReturn();
        RestResult<ProcessSummaryCreateCheckResponse> restResult = JSONObject.parseObject(response.getResponse().getContentAsString(), new TypeReference<RestResult<ProcessSummaryCreateCheckResponse>>(){});
        Assert.assertNotNull(restResult.getData().getCheckScene());
        Assert.assertNotNull(restResult.getData().getNotCreateReason());
    }

    @Test(description = "测试校验创建合同摘要非saas流程")
    public void testCheckCreateProcessSummaryNoSaasFlow() throws Exception {
        String processId = "867bedba0a4c40f19c35a91bbafebd21";
        ProcessSummaryCreateCheckRequest request = new ProcessSummaryCreateCheckRequest();
        request.setProcessId(processId);
        MvcResult response= mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/processes/check-create-summary")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .content(JSONObject.toJSONString(request))
                                .header("X-Tsign-Open-Operator-Id", "8af82bef9d7141c2b20c6e8b44800e22")
                                .header("X-Tsign-Open-Tenant-Id", "8af82bef9d7141c2b20c6e8b44800e22"))
                .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0)).andReturn();
        RestResult<ProcessSummaryCreateCheckResponse> restResult = JSONObject.parseObject(response.getResponse().getContentAsString(), new TypeReference<RestResult<ProcessSummaryCreateCheckResponse>>(){});
        Assert.assertEquals(restResult.getData().getCheckScene(),ProcessSummaryCheckSceneEnum.APPID_CONFIG_NOT_SUPPORT.getCode());
        Assert.assertNotNull(restResult.getData().getNotCreateReason());
    }

    @Test(description = "测试创建合同摘要，异常合同状态")
    public void testCreateProcessSummaryErrorCase() throws Exception {
        String processId = "870973dd01e045c48da62f9b39783882";
        String fileId = "02ebf036430e4104a0189b60b6e7cdcd";

        ProcessSummaryCreateRequest request = new ProcessSummaryCreateRequest();
        request.setProcessId(processId);
        request.setFileId(fileId);

        mockMvc.perform(
                MockMvcRequestBuilders.post("/v3/processes/create-summary-info")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .content(JSONObject.toJSONString(request))
                        .header("X-Tsign-Open-Operator-Id", accountId)
                        .header("X-Tsign-Open-Tenant-Id", subjectId))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(*********));
    }

    @Test(description = "测试关键词搜索")
    public void testKeywordSearch() throws Exception {
        String processId = "5b82457404114320afd660859d7cb341";
        String fileId = "0a07b49909754076a4973e29a3ea4cff";
        ProcessSummaryKeywordSearchRequest.KeyInfo keyInfo = new ProcessSummaryKeywordSearchRequest.KeyInfo();
        keyInfo.setSeq(1);
        keyInfo.setAiValue("房屋租赁合同");
        ProcessSummaryKeywordSearchRequest request = new ProcessSummaryKeywordSearchRequest();
        request.setProcessId(processId);
        request.setFileId(fileId);
        request.setKeyInfoList(Lists.newArrayList(keyInfo));
        String jsonStr = mockMvc.perform(
                        MockMvcRequestBuilders.post("/v3/processes/extract-keyword-pos")
                               .contentType(MediaType.APPLICATION_JSON_UTF8)
                               .content(JSONObject.toJSONString(request))
                               .header("X-Tsign-Open-Operator-Id", accountId)
                               .header("X-Tsign-Open-Tenant-Id", subjectId))
                        .andExpect(result -> log.info(result.getResponse().getContentAsString()))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andReturn().getResponse().getContentAsString();
        
        RestResult<ProcessSummaryKeywordSearchResponse> restResult = JSONObject.parseObject(jsonStr, new TypeReference<RestResult<ProcessSummaryKeywordSearchResponse>>(){});
        Assert.assertTrue(restResult.getData().getKeywordPositions().size() >= 1);
    }
}
