package com.timevale.saasbiz.rest;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessRemarkOutputDTO;

import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * 合同备注单测
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@JsonDataIgnore
public class ProcessRemarkRestTest extends BaseServiceTest {

    @Test(description = "测试添加备注")
    public void testAddRemark() throws Exception {
        String processId = "f4be63f626ec40bda003219768a29700";

        String requestJson = "{\"processId\": \""+ processId +"\", \"remark\": \"test remark\"}";

        mockMvc.perform(MockMvcRequestBuilders.post("/v2/processes/add-remark")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(HEADER_OPERATOR_ID, "66e4287f478a446a80aa63f258b7b8fb")
                        .header(HEADER_TENANT_ID, "fb06392c6da34001a3f74d9abc854407")
                        .content(requestJson))
                .andExpect(MockMvcResultMatchers.status().isOk());

        String contentAsString = mockMvc.perform(MockMvcRequestBuilders.get("/v2/processes/get-remark?processId=" + processId)
                        .header(HEADER_OPERATOR_ID, "66e4287f478a446a80aa63f258b7b8fb")
                        .header(HEADER_TENANT_ID, "fb06392c6da34001a3f74d9abc854407"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn().getResponse().getContentAsString();

        JSONObject jsonObject = JSON.parseObject(contentAsString);
        JSONArray data = jsonObject.getJSONArray("data");
        ProcessRemarkOutputDTO remarkOutputDTO = data.getObject(0, ProcessRemarkOutputDTO.class);
        Assert.assertEquals(remarkOutputDTO.getRemark(), "test remark");
    }
}
