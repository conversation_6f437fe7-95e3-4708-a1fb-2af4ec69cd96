package com.timevale.saasbiz.rest;

import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import org.hamcrest.Matchers;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_OPERATOR_ID;
import static com.timevale.saasbiz.model.constants.RequestHeaderConstants.HEADER_TENANT_ID;
import static com.timevale.saasbiz.model.exception.SaasBizResultCode.CERT_TYPE_ERROR;

/**
 * <AUTHOR>
 * @since 2023-05-10
 */
@JsonDataIgnore
public class CertRestTest extends BaseServiceTest {

    @Test
    public void testGetPersonCert() throws Exception {
        mockMvc.perform(
                        MockMvcRequestBuilders.get("/v1/saas-common/certs/person-cert")
                                .contentType(MediaType.APPLICATION_JSON_UTF8)
                                .header(HEADER_OPERATOR_ID, "0ae7dbb9da3d465399e413866ce793a1")
                                .header(HEADER_TENANT_ID, "0ae7dbb9da3d465399e413866ce793a1"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").exists());
    }

    @Test
    public void testGetPersonCertTypeNotMatch() throws Exception {
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v1/saas-common/certs/person-cert")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_OPERATOR_ID, "e23768d069c34d18a2e241efb291f3e9")
                        .header(HEADER_TENANT_ID, "e23768d069c34d18a2e241efb291f3e9"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(CERT_TYPE_ERROR.getCode()));
    }

    @Test
    public void testGetOrgCert() throws Exception{
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v1/saas-common/certs/org-cert")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_OPERATOR_ID, "307a45c17d394563885f861dd961d7e8")
                        .header(HEADER_TENANT_ID, "e23768d069c34d18a2e241efb291f3e9"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(0))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data").exists());
    }

    @Test
    public void testGetOrgCertTypeNotMatch() throws Exception {
        mockMvc.perform(
                MockMvcRequestBuilders.get("/v1/saas-common/certs/org-cert")
                        .contentType(MediaType.APPLICATION_JSON_UTF8)
                        .header(HEADER_OPERATOR_ID, "307a45c17d394563885f861dd961d7e8")
                        .header(HEADER_TENANT_ID, "307a45c17d394563885f861dd961d7e8"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(CERT_TYPE_ERROR.getCode()));
    }
}
