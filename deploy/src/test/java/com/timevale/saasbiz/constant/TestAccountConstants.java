package com.timevale.saasbiz.constant;

/** 单测账号信息常量类 */
public class TestAccountConstants {

    // 未实名账号， 无姓名，账号：<EMAIL> 登录密码：test123456
    public static final String ACCOUNT_ID_0 = "3c11b3069dbe4217a3d5bb777a48705d";
    // 测试前缀单测账号一, 账号：<EMAIL> 实名手机号：*********** 登录密码：test123456 签署密码：sign123456  admin
    public static final String ACCOUNT_ID_1 = "e2368fdcd51a48c5bcc5b5da2277e8c0";
    public static final String ACCOUNT_ID_1_GID = "f73027705d2243d58d065b33ec91643c";
    public static final String ACCOUNT_ID_1_NAME = "测试前缀单测账号一";

    // 测试前缀单测账号二, 账号：<EMAIL> 登录密码：test123456 签署密码：sign123456
    public static final String ACCOUNT_ID_2 = "d915e74e9d2d428082a2f760e1b7bde5";
    public static final String ACCOUNT_ID_2_NAME = "测试前缀单测账号二";
    // 测试前缀单测账号三, 账号：*********** 登录密码：test123456 签署密码：sign123456
    public static final String ACCOUNT_ID_3 = "374fbae8502d4e679d91ff83cf1fa85d";
    public static final String ACCOUNT_ID_3_NAME = "测试前缀单测账号三";
    // 测试前缀单测账号四, 账号：*********** 登录密码：test123456 签署密码：sign123456
    public static final String ACCOUNT_4 = "***********";
    public static final String ACCOUNT_ID_4 = "878d296d0b404854a34645d058857a77";
    public static final String ACCOUNT_ID_4_NAME = "测试前缀单测账号四";
    // 测试前缀单测账号五, 账号：<EMAIL> 登录密码：test123456 签署密码：sign123456
    public static final String ACCOUNT_ID_5 = "3d119cd1f40945b4b5165e4afe018cf0";
    public static final String ACCOUNT_ID_5_NAME = "测试前缀单测账号五";

    // esigntest单测未实名无证件号企业一, 未实名无证件号
    public static final String NO_CERT_SUBJECT_1 = "1a2aae16adb741128022a6f9393c1e14";

    // esigntest单测余额不足企业一，esigntest单测专业版企业一的关联企业， 未开启流量共享
    public static final String NO_BILL_SUBJECT_1 = "6c1793be2fe74048b8f93e65402f25c1";
    // esigntest单测余额不足企业一的管理员
    public static final String NO_BILL_SUBJECT_ADMIN = ACCOUNT_ID_2;

    // esigntest单测高级版企业一， 会员版本：高级版
    public static final String SENIOR_SUBJECT_1 = "554027011f28412f8b0ecd518dbaa008";
    public static final String SENIOR_SUBJECT_1_GID = "d585b186914f48d4b189156194d71a11";
    // esigntest单测高级版企业一的管理员
    public static final String SENIOR_SUBJECT_ADMIN = ACCOUNT_ID_1;
    // esigntest单测高级版企业一的普通成员, 可设置合同保密，可用印章：b12e7c2c-e18b-40c4-81b4-05f9e7f1c407（公章）
    public static final String SENIOR_SUBJECT_MEMBER_1 = ACCOUNT_ID_2;
    // esigntest单测高级版企业一的普通成员，发起时需要合同审批，可用印章：3947f82f-c6bb-4051-9277-a5945d4e0fb1（合同专用章）
    public static final String SENIOR_SUBJECT_MEMBER_2 = ACCOUNT_ID_3;
    // esigntest单测高级版企业一的普通成员，无模板（模板名称：单测模板使用鉴权）使用权限
    public static final String SENIOR_SUBJECT_MEMBER_3 = ACCOUNT_ID_4;

    // esigntest单测基础版企业一，会员版本：基础版
    public static final String BASE_SUBJECT_1 = "472a4ff2a183491497566d50a4883a5a";
    // esigntest单测基础版企业一的可用文件id
    public static final String BASE_SUBJECT_FILE_ID_1 = "3c8cd77e9e744faea8e49f588396c29a";
    // esigntest单测基础版企业一的可用文件名称
    public static final String BASE_SUBJECT_FILE_NAME_1 = "合同模板.pdf";
    // esigntest单测基础版企业一的管理员
    public static final String BASE_SUBJECT_ADMIN = ACCOUNT_ID_1;
    // esigntest单测基础版企业一的普通成员, 只有一个菜单（名称：单测部分菜单）有权限，可直接发起/导出明细
    // 可用印章：14b25db6-c0b7-4d2b-8b06-b5f5a64b6f28（公章）
    public static final String BASE_SUBJECT_MEMBER_1 = ACCOUNT_ID_2;
    // esigntest单测基础版企业一的普通成员，所有菜单均无权限，可直接发起
    // 可审批印章：ba8b97af-07f4-407e-a1d0-3b8a2e0c31d0（合同专用章）
    public static final String BASE_SUBJECT_MEMBER_2 = ACCOUNT_ID_3;
    // esigntest单测基础版企业一的普通成员，不可直接发起，无印章使用权限
    public static final String BASE_SUBJECT_MEMBER_3 = ACCOUNT_ID_4;

    // esigntest单测智能版企业一，会员版本：智能版，参与方数据来源：企业相对方，可归档全部合同
    public static final String AI_SUBJECT_1 = "fe9ca45170b849198ff520fa43717d1b";
    public static final String AI_SUBJECT_GID_1 = "8f16c164f81743dfbc05f229ff7eec5f";
    // esigntest单测智能版企业一的应用id
    public static final String AI_SUBJECT_1_APP_ID = "**********";
    // esigntest单测智能版企业一的管理员
    public static final String AI_SUBJECT_ADMIN = ACCOUNT_ID_1;
    // esigntest单测智能版企业一的归档管理员/台账管理员, 可用印章：50b931f1-e125-4460-abf0-4c6f389abc72（公章）
    public static final String AI_SUBJECT_MEMBER_1 = ACCOUNT_ID_2;
    // esigntest单测智能版企业一的普通成员，发起时需要合同审批，可审批印章：c2ae1f01-90a8-4d56-9bf9-ea894e4a6bc8（合同专用章）
    public static final String AI_SUBJECT_MEMBER_2 = ACCOUNT_ID_3;


    // esigntest单测专业版企业一，会员版本：专业版，参与方数据来源：发起人联系人，只能归档已完成合同
    public static final String PROFESSIONAL_SUBJECT_1 = "babac1ab42a643708b867e271c9c9513";
    public static final String PROFESSIONAL_SUBJECT_1_GID = "1341f97dbb244c13bbba048715c14c9b";

    // esigntest单测专业版企业一的管理员
    public static final String PROFESSIONAL_SUBJECT_ADMIN = ACCOUNT_ID_1;
    // esigntest单测专业版企业一的归档管理员，可导出明细，可用印章：bc681cb1-53be-4083-9d4b-fe2c08244144（公章）
    public static final String PROFESSIONAL_SUBJECT_MEMBER_1 = ACCOUNT_ID_2;
    // esigntest单测专业版企业一的普通成员，可经办合同下载，可审批印章：3cbb36e7-8796-497e-bea9-4c1ec6566760（合同专用章）
    public static final String PROFESSIONAL_SUBJECT_MEMBER_2 = ACCOUNT_ID_3;
    // esigntest单测专业版企业一的模板管理员
    public static final String PROFESSIONAL_SUBJECT_MEMBER_3 = ACCOUNT_ID_4;


    // esigntest单测尊享版企业一，会员版本：尊享版
    public static final String SPECIAL_CUSTOMER_SUBJECT_1 = "5d71ebb586ab40ae8276b03e8a3fc54b";
    // esigntest单测尊享版企业一的管理员
    public static final String SPECIAL_CUSTOMER_SUBJECT_ADMIN = ACCOUNT_ID_1;
    // esigntest单测尊享版企业一的普通成员, 可用印章：007d80f2-efda-4394-bba8-62cd0ab9173e（公章）
    public static final String SPECIAL_CUSTOMER_SUBJECT_MEMBER_1 = ACCOUNT_ID_2;
    // esigntest单测尊享版企业一的出证管理员，可设置合同保密，可审批印章：79d7352a-9cab-4d1c-b2fa-7c5f68e59bab（合同专用章）
    public static final String SPECIAL_CUSTOMER_SUBJECT_MEMBER_2 = ACCOUNT_ID_3;
    // esigntest单测尊享版企业一的法人
    public static final String SPECIAL_CUSTOMER_SUBJECT_LEGAL = ACCOUNT_ID_5;

    // esigntest单测新基础版企业一，会员版本：新基础版
    public static final String NEW_BASE_SUBJECT_1 = "2fe4f445b1804f11ae66f039ab06eaea";
    // esigntest单测新基础版企业一的管理员
    public static final String NEW_BASE_SUBJECT_ADMIN = ACCOUNT_ID_1;
    // esigntest单测新基础版企业一的普通成员，有经办合同下载/导出明细权限，有企业合同解约权限，可用印章：8b37762c-0f05-4ddc-a9c6-a423ecb98d1c（公章）
    public static final String NEW_BASE_SUBJECT_MEMBER_1 = ACCOUNT_ID_2;
    // esigntest单测新基础版企业一的普通成员，无下载权限，可审批印章：4ea01e18-c101-44e8-9508-abfc9790d6d1（合同专用章）
    public static final String NEW_BASE_SUBJECT_MEMBER_2 = ACCOUNT_ID_3;

    // esigntest单测转交专用企业一
    public static final String TRANSFER_SUBJECT_1 = "edaac24a78dc49a480775d00babf0a73";
    // esigntest单测转交专用企业一的管理员
    public static final String TRANSFER_SUBJECT_ADMIN = ACCOUNT_ID_1;
    // esigntest单测转交专用企业一的离职成员， 钉钉账号且未实名
    public static final String TRANSFER_SUBJECT_RETIRE_MEMBER_1 = "1630c3760ef840cc98858508e3138de1";
    // esigntest单测转交专用企业一的离职成员，已实名，用于全部流程转交单测
    public static final String TRANSFER_SUBJECT_RETIRE_MEMBER_2 = ACCOUNT_ID_3;
    // esigntest单测转交专用企业一的离职成员，已实名, 用于单个流程转交单测
    public static final String TRANSFER_SUBJECT_RETIRE_MEMBER_3 = ACCOUNT_ID_5;
    // esigntest单测转交专用企业一的转交接收成员，已实名
    public static final String TRANSFER_SUBJECT_TO_MEMBER_1 = ACCOUNT_ID_4;
    // esigntest单测转交专用企业一的普通成员，已实名
    public static final String TRANSFER_SUBJECT_MEMBER_1 = ACCOUNT_ID_2;

    /*********************************************************epaas相关账号******************************/
    // esigntest单测高级版epaas专用账号001，会员版本：高级版
    public static final String EPAAS_TEMPLATE_SUBJECT_001_OID = "44c9b60f551e479b8eff3dd22e554197";
    public static final String EPAAS_TEMPLATE_SUBJECT_001_GID = "f66dd8da83c1476ab02fa562c4ac99df";
    // esigntest单测高级版epaas专用账号001, 子管理员账号：<EMAIL> 登录密码：test123456 签署密码：sign123456  admin
    public static final String EPAAS_TEMPLATE_ADMIN_001_OID = "e2368fdcd51a48c5bcc5b5da2277e8c0";
}
