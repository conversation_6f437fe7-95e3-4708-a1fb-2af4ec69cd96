package com.timevale.saasbiz.tracking;

import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saas.tracking.bean.TrackingCollectBean;
import com.timevale.saas.tracking.service.ITrackingService;
import com.timevale.saas.tracking.service.custom.ICustomTrackingService;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.tracking.consts.TrackingKeyConstant;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.model.bean.authrelation.dto.input.AuthRelationBatchAddDTO;
import com.timevale.saasbiz.tracking.data.WatermarkTemplateTrackingData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.testng.Assert;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.tracking.consts.TrackingServiceConstant.AUTH_RELATION_AI_PAGE_ADD_SHARE;
import static com.timevale.saasbiz.tracking.consts.TrackingServiceConstant.WATERMARK_TEMPLATE_TRACKING;

/**
 * 埋点功能单测
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
@JsonDataIgnore
public class TrackingServiceTest extends BaseServiceTest {

    @Autowired ITrackingService iTrackingService;


    @Autowired
    @Qualifier(WATERMARK_TEMPLATE_TRACKING)
    ICustomTrackingService watermarkTrackingService;

    @Autowired
    @Qualifier(AUTH_RELATION_AI_PAGE_ADD_SHARE)
    ICustomTrackingService authRelationAiPageAddShareTrackingService;

    @Test(description = "测试通用埋点方法")
    public void testGlobalTracking() {
        TrackingCollectBean trackingData = new TrackingCollectBean();
        trackingData.setAppId(APP_ID);
        trackingData.setOperatorId(TestAccountConstants.ACCOUNT_ID_1);
        trackingData.setPlatform(CLIENT_ID_WEB);
        trackingData.setTrackingKey(TrackingKeyConstant.TRACKING_TEST);
        try {
            iTrackingService.tracking(trackingData);
        } catch (Exception e) {
            Assert.assertTrue(false, "埋点失败");
        }
    }


    @Test(description = "测试水印埋点方法")
    public void testWatermarkTracking() {
        TrackingCollectBean trackingData = new TrackingCollectBean();
        trackingData.setAppId(APP_ID);
        trackingData.setOperatorId(TestAccountConstants.ACCOUNT_ID_1);
        trackingData.setPlatform(CLIENT_ID_WEB);
        trackingData.setTrackingKey(TrackingKeyConstant.TRACKING_TEST);

        WatermarkTemplateTrackingData watermarkTemplateTrackingData = new WatermarkTemplateTrackingData();
        watermarkTemplateTrackingData.setWatermarkType("wenjian");

        trackingData.setTrackingData(watermarkTemplateTrackingData);
        try {
            watermarkTrackingService.tracking(trackingData);
        } catch (Exception e) {
            Assert.assertTrue(false, "埋点失败");
        }
    }

    @Test(description = "关联企业共享算力埋点")
    public void testAuthRelationAiPageAddShareTracking() {
        TrackingCollectBean trackingCollectBean = new TrackingCollectBean();
        trackingCollectBean.setAppId(APP_ID);
        trackingCollectBean.setOperatorId(TestAccountConstants.ACCOUNT_ID_1);
        trackingCollectBean.setPlatform(CLIENT_ID_WEB);
        trackingCollectBean.setTrackingKey(TrackingKeyConstant.TRACKING_TEST);

        AuthRelationBatchAddDTO trackingData = new AuthRelationBatchAddDTO();
        trackingCollectBean.setTrackingData(trackingData);
        try {
            authRelationAiPageAddShareTrackingService.tracking(trackingCollectBean);
        } catch (Exception e) {
            Assert.assertTrue(false, "埋点失败");
        }
    }
}
