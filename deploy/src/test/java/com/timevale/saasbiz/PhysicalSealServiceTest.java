package com.timevale.saasbiz;

import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.physicalseal.dto.input.QueryPhysicalListInputDTO;
import com.timevale.saasbiz.model.bean.physicalseal.dto.input.QueryPhysicalSealFlagInputDTO;
import com.timevale.saasbiz.model.bean.physicalseal.dto.output.QueryPhysicalListOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.rest.converter.QueryPhysicalSealListResponseConverter;
import com.timevale.saasbiz.service.physicalseal.PhysicalSealService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

@JsonDataIgnore
@Slf4j
public class PhysicalSealServiceTest extends BaseServiceTest {

    @Autowired
    private PhysicalSealService physicalSealService;

    @Autowired
    UserCenterService userCenterService;

    @Test
    public void testGetAccountBeanByOid() {
        String oid = "b27913606bfd4f74925c36367e2cc985";
        AccountInfoDTO result = userCenterService.queryAccountInfoByOid(oid);
        Assert.assertNotNull(result);
        oid = "cd2942c9fb6041f2b6e223b14ffb019f";
        AccountInfoDTO result1 = userCenterService.queryAccountInfoByOid(oid);
        Assert.assertNotNull(result1);
    }

    @Test
    public void testQueryPhysicalSealFlag() {
        QueryPhysicalSealFlagInputDTO queryPhysicalSealFlagInputDTO = new QueryPhysicalSealFlagInputDTO();
        queryPhysicalSealFlagInputDTO.setOid("b27913606bfd4f74925c36367e2cc985");
        Boolean flag = physicalSealService.queryPhysicalSealFlag(queryPhysicalSealFlagInputDTO);
        Assert.assertTrue(!flag);
        queryPhysicalSealFlagInputDTO.setOid("cd2942c9fb6041f2b6e223b14ffb019f");
        Boolean flag1 = physicalSealService.queryPhysicalSealFlag(queryPhysicalSealFlagInputDTO);
        Assert.assertTrue(!flag1);
    }

    @Test
    public void testQueryPhysicalSealList() {
        QueryPhysicalListInputDTO queryPhysicalSealFlagBO = new QueryPhysicalListInputDTO();
        queryPhysicalSealFlagBO.setOid("cd2942c9fb6041f2b6e223b14ffb019f");
        queryPhysicalSealFlagBO.setCommonSearch("流程");
        queryPhysicalSealFlagBO.setPage(1);
        queryPhysicalSealFlagBO.setPageSize(10);
        QueryPhysicalListOutputDTO queryPhysicalListOutputDTO = physicalSealService.queryPhysicalSealList(queryPhysicalSealFlagBO);
        Assert.assertNotNull(queryPhysicalListOutputDTO);
        QueryPhysicalSealListResponseConverter.convertPhysicalSealListResponse(queryPhysicalListOutputDTO);
        queryPhysicalSealFlagBO.setOid("b27913606bfd4f74925c36367e2cc985");
        queryPhysicalSealFlagBO.setPhySealStatus(1);
        queryPhysicalListOutputDTO = physicalSealService.queryPhysicalSealList(queryPhysicalSealFlagBO);
        Assert.assertNotNull(queryPhysicalListOutputDTO);
    }


}
