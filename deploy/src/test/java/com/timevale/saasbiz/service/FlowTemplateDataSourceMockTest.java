package com.timevale.saasbiz.service;

import com.timevale.contractanalysis.facade.api.dto.infocollect.InfoCollectTemplateFormRelationDTO;
import com.timevale.contractanalysis.facade.api.dto.infocollect.TemplateFieldDataSourceChangeErrorDTO;
import com.timevale.contractanalysis.facade.api.enums.InfoCollectDataSourceStructureChangeTypeEnum;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.infocollect.InfoCollectClient;
import com.timevale.saasbiz.service.flowtemplate.impl.FlowTemplateDataSourceServiceImpl;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/9/23 20:25
 */
@JsonDataIgnore
public class FlowTemplateDataSourceMockTest extends BaseMockTest {

    @InjectMocks
    private FlowTemplateDataSourceServiceImpl flowTemplateDataSourceService;
    @Mock
    private InfoCollectClient infoCollectClient;

    @Test
    public void dataSourceErrorTest() {
        InfoCollectTemplateFormRelationDTO relationDTO = new InfoCollectTemplateFormRelationDTO();
        relationDTO.setFormId("dataSource");
        relationDTO.setDataSourceStructureChangeTypes(Arrays.asList(InfoCollectDataSourceStructureChangeTypeEnum.PARTICIPANT_FIELD_CHANGE.getCode(), InfoCollectDataSourceStructureChangeTypeEnum.TEMPLATE_FIELD_CHANGE.getCode()));
        TemplateFieldDataSourceChangeErrorDTO errorDTO = new TemplateFieldDataSourceChangeErrorDTO();
        errorDTO.setFieldName("F");
        errorDTO.setDataSourceFieldName("dF");
        relationDTO.setTemplateFieldDataSourceChangeError(Arrays.asList(errorDTO));
        Mockito.when(infoCollectClient.queryTemplateFormRelationByTemplate(Mockito.any(),
                Mockito.any())).thenReturn(Arrays.asList(relationDTO));

        // 错误
        String error = flowTemplateDataSourceService.dataSourceError( "subjectOid",  "flowTemplateId",  "dataSource");
        Assert.assertTrue(StringUtils.isNotBlank(error));

    }
}
