package com.timevale.saasbiz.service;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saas.integration.service.api.RpcDedicatedProjectService;
import com.timevale.saas.integration.service.model.input.DeleteDedicatedProjectInput;
import com.timevale.saasbiz.base.BaseTransactionalServiceTest;
import com.timevale.saasbiz.model.bean.dedicatedcloud.dto.DedicatedCloudDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.dto.DedicatedCloudHowGuideDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.input.DedicatedCloudAuthAppIdInputDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.input.DedicatedCloudCreateInputDTO;
import com.timevale.saasbiz.model.bean.dedicatedcloud.input.DedicatedCloudUpdateInputDTO;
import com.timevale.saasbiz.model.enums.dedicatedcloud.DedicatedCloudDisplayTypeEnum;
import com.timevale.saasbiz.service.dedicatedcloud.DedicatedCloudService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.PROFESSIONAL_SUBJECT_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.PROFESSIONAL_SUBJECT_1_GID;

/**
 * <AUTHOR>
 * @since 2024/3/6 19:33
 */

public class DedicatedCloudServiceTest extends BaseTransactionalServiceTest {


    private String subjectOid = PROFESSIONAL_SUBJECT_1;
    private String subjectGid = PROFESSIONAL_SUBJECT_1_GID;
    private String operatorOid = ACCOUNT_ID_1;

    @Autowired
    private DedicatedCloudService dedicatedCloudService;
    @Autowired
    private RpcDedicatedProjectService rpcDedicatedProjectService;
    @Autowired
    private UserCenterService userCenterService;

    @Test
    public void dedicatedCloudFlow() {

        // 专属云项目列表
        List<DedicatedCloudDTO> existDedicatedCloudDTOS = dedicatedCloudService.list(subjectOid);
        if (CollectionUtils.isNotEmpty(existDedicatedCloudDTOS)) {
            // 删除
            DeleteDedicatedProjectInput deleteDedicatedProjectInput = new DeleteDedicatedProjectInput();
            deleteDedicatedProjectInput.setDedicatedCloudId(existDedicatedCloudDTOS.get(0).getDedicatedCloudId());
            deleteDedicatedProjectInput.setModifyBy(operatorOid);
            rpcDedicatedProjectService.deleteDedicatedProject(deleteDedicatedProjectInput);
        }

        // 创建
        DedicatedCloudCreateInputDTO dedicatedCloudCreateInputDTO = new DedicatedCloudCreateInputDTO();
        dedicatedCloudCreateInputDTO.setSubjectOid(subjectOid);
        dedicatedCloudCreateInputDTO.setOperatorOid(operatorOid);
        dedicatedCloudCreateInputDTO.setProjectName("create-projectName");
        dedicatedCloudCreateInputDTO.setAppId("7876705724");
        dedicatedCloudCreateInputDTO.setServerUrl("http://lp-zsyyqxm-projectk8s.tsign.cn/dedicated-sign");
        DedicatedCloudAuthAppIdInputDTO dedicatedCloudAuthAppIdInputDTO = new DedicatedCloudAuthAppIdInputDTO();
        dedicatedCloudAuthAppIdInputDTO.setAppId("7876705741");
        dedicatedCloudAuthAppIdInputDTO.setSubjectGid(subjectGid);
        dedicatedCloudCreateInputDTO.setAuthAppIds(Arrays.asList(dedicatedCloudAuthAppIdInputDTO));
        String dedicatedCloudId = dedicatedCloudService.create(dedicatedCloudCreateInputDTO);

        // 更新
        DedicatedCloudUpdateInputDTO dedicatedCloudUpdateInputDTO = new DedicatedCloudUpdateInputDTO();
        dedicatedCloudUpdateInputDTO.setSubjectOid(subjectOid);
        dedicatedCloudUpdateInputDTO.setOperatorOid(operatorOid);
        dedicatedCloudUpdateInputDTO.setDedicatedCloudId(dedicatedCloudId);
        dedicatedCloudUpdateInputDTO.setProjectName("update-projectName");
        dedicatedCloudUpdateInputDTO.setAuthAppIds(Arrays.asList(dedicatedCloudAuthAppIdInputDTO));
        dedicatedCloudUpdateInputDTO.setServerUrl(dedicatedCloudCreateInputDTO.getServerUrl());
        dedicatedCloudService.update(dedicatedCloudUpdateInputDTO);

        // 更新状态
        Assert.assertThrows(() -> {
            dedicatedCloudService.updateStatus(subjectOid, operatorOid, dedicatedCloudId, 1);
        });


        // 专属云项目详情
        DedicatedCloudDTO dedicatedCloudDTO = dedicatedCloudService.detail(subjectOid, dedicatedCloudId);
        Assert.assertTrue(null != dedicatedCloudDTO && Objects.equals(dedicatedCloudDTO.getDedicatedCloudId(), dedicatedCloudId));

        // 专属云项目列表
        List<DedicatedCloudDTO>  dedicatedCloudDTOS = dedicatedCloudService.list(subjectOid);
        Assert.assertTrue(CollectionUtils.isNotEmpty(dedicatedCloudDTOS) &&
                Objects.equals(dedicatedCloudDTOS.get(0).getDedicatedCloudId(), dedicatedCloudId));

        // 删除
        DeleteDedicatedProjectInput deleteDedicatedProjectInput = new DeleteDedicatedProjectInput();
        deleteDedicatedProjectInput.setDedicatedCloudId(dedicatedCloudId);
        deleteDedicatedProjectInput.setModifyBy(operatorOid);
        rpcDedicatedProjectService.deleteDedicatedProject(deleteDedicatedProjectInput);



        DedicatedCloudHowGuideDTO dedicatedCloudHowGuideDTO =
                dedicatedCloudService.guidData(userCenterService.queryAccountInfoByOid(subjectOid));
        Assert.assertTrue(dedicatedCloudHowGuideDTO.getDedicatedCloudDisplayType().equals(DedicatedCloudDisplayTypeEnum.CREATE.getType()));

    }

    @Test
    public void dedicatedAuthRelationListTest() {
       Assert.assertTrue(CollectionUtils.isNotEmpty(dedicatedCloudService.authRelationList(subjectOid)));
    }

    @Test
    public void dedicatedGetConfigTest() {
        Assert.assertNotNull(dedicatedCloudService.getConfig("15a002e1332e42619557bfb00ffce845").getDedicatedCloudId());
    }

}
