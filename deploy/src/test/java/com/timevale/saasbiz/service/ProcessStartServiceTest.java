package com.timevale.saasbiz.service;


import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessStartFileChangeOutputDTO;
import com.timevale.saasbiz.service.process.ProcessStartService;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@JsonDataIgnore
public class ProcessStartServiceTest extends BaseServiceTest {
    
    @Autowired
    private ProcessStartService processStartService;
    
    private static final String PROCESS_ID = "6d7d29777e3f446fa9452d5230e83df3";
    private static final String SUBJECT_ID = "9f105772a03f46b2a3bb249bfad5c239";
    
    @Test(description = "获取非标模板文件变化")
    public void getProcessStartFileChangeInfo() {
        ProcessStartFileChangeOutputDTO processStartFileChangeInfo = 
                processStartService.getProcessStartFileChangeInfo(PROCESS_ID, SUBJECT_ID);
        Assert.assertTrue(MapUtils.isNotEmpty(processStartFileChangeInfo.getChangeFile()), "结果应该是true");
        Assert.assertTrue(CollectionUtils.isNotEmpty(processStartFileChangeInfo.getAddFile()), "结果应该是true");
        Assert.assertTrue(CollectionUtils.isNotEmpty(processStartFileChangeInfo.getDeleteFile()), "结果应该是true");
    }
    
}
