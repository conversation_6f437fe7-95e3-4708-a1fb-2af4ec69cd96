package com.timevale.saasbiz.service;

import com.timevale.mandarin.base.util.BooleanUtils;
import com.timevale.qa.apitest.model.TestJsonObj;
import com.timevale.saasbiz.base.BaseTransactionalServiceTest;
import com.timevale.saasbiz.dal.bean.AccountNoviceOperateDO;
import com.timevale.saasbiz.dal.dao.AccountNoviceOperateDAO;
import com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceOperateInputDTO;
import com.timevale.saasbiz.model.bean.novice.dto.output.CheckNoviceOperateOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.service.novice.NoviceOperateService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2023-05-24
 */
@Slf4j
public class NoviceOperateServiceTest extends BaseTransactionalServiceTest {

    @Autowired NoviceOperateService noviceOperateService;

    @Autowired AccountNoviceOperateDAO accountNoviceOperateDAO;

    @Autowired UserCenterService userCenterService;

    /** 新手操作验证是否完成测试 */
    @Test(description = "操作类型异常情况")
    public void testCheckNoviceOperateWhenOperateNameException() {
        TestJsonObj testJsonObj = jsonDataMap.get("OperateNameException");
        CheckNoviceOperateInputDTO requestData =
                (CheckNoviceOperateInputDTO) testJsonObj.getReqData();
        try {
            CheckNoviceOperateOutputDTO response =
                    noviceOperateService.checkNoviceOperate(
                            requestData.getAccountId(), requestData.getOperateName());
        } catch (Exception e) {
            Assert.assertTrue(true, "操作参数格式异常");
        }
    }

    @Test(description = "为空的情况，且操作类型为notExistAsDone为true")
    public void testCheckNoviceOperateWhenNotExitsAndDoneIsTrue() {
        TestJsonObj testJsonObj = jsonDataMap.get("NotExitsAndDoneIsTrue");
        CheckNoviceOperateInputDTO reqData = (CheckNoviceOperateInputDTO) testJsonObj.getReqData();
        CheckNoviceOperateOutputDTO response =
                noviceOperateService.checkNoviceOperate(
                        reqData.getAccountId(), reqData.getOperateName());
        Assert.assertTrue(response.isDone() == true, "结果应该是true");
    }

    @Test(description = "为空的情况，且操作类型为notExistAsDone不为true")
    public void testCheckNoviceOperateWhenNotExitsAndDoneNotTrue() throws Exception {
        TestJsonObj testJsonObj = jsonDataMap.get("NotExitsAndDoneNotTrue");
        CheckNoviceOperateInputDTO requestData =
                (CheckNoviceOperateInputDTO) testJsonObj.getReqData();
        CheckNoviceOperateOutputDTO response =
                noviceOperateService.checkNoviceOperate(
                        requestData.getAccountId(), requestData.getOperateName());
        Assert.assertTrue(response.isDone() == false, "结果应该是false");
    }

    @Test(description = "查询结果不为空，且状态为未完成")
    public void testCheckNoviceOperateWhenExitsAndNotComplete() {
        TestJsonObj testJsonObj = jsonDataMap.get("ExitsAndNotComplete");
        CheckNoviceOperateInputDTO reqData = (CheckNoviceOperateInputDTO) testJsonObj.getReqData();

        insertData(reqData.getAccountId(), reqData.getOperateName(), BooleanUtils.toInteger(false));

        CheckNoviceOperateOutputDTO response =
                noviceOperateService.checkNoviceOperate(
                        reqData.getAccountId(), reqData.getOperateName());
        Assert.assertTrue(response.isDone() == false, "false");
    }

    @Test(description = "查询结果不为空，且状态为已完成")
    public void testCheckNoviceOperateWhenExitsAndComplete() {
        TestJsonObj testJsonObj = jsonDataMap.get("ExitsAndComplete");
        CheckNoviceOperateInputDTO reqData = (CheckNoviceOperateInputDTO) testJsonObj.getReqData();
        insertData(reqData.getAccountId(), reqData.getOperateName(), BooleanUtils.toInteger(true));
        CheckNoviceOperateOutputDTO response =
                noviceOperateService.checkNoviceOperate(
                        reqData.getAccountId(), reqData.getOperateName());
        Assert.assertTrue(response.isDone() == true, "结果应该是true");
    }

    /** 新手完成任务操作测试 */
    @Test(description = "操作类型异常情况")
    public void testCompleteNoviceOperateWhenOperateNameException() {
        TestJsonObj testJsonObj = jsonDataMap.get("OperateNameException");
        CheckNoviceOperateInputDTO requestData =
                (CheckNoviceOperateInputDTO) testJsonObj.getReqData();
        try {
            noviceOperateService.completeNoviceOperate(
                    requestData.getAccountId(), requestData.getOperateName());
        } catch (Exception e) {
            Assert.assertTrue(true, "操作参数格式异常");
        }
    }

    @Test(description = "存在且任务完成")
    public void testCompleteNoviceOperateWhenExitsAndNoviceTaskComplete() {
        TestJsonObj testJsonObj = jsonDataMap.get("ExitsAndNoviceTaskComplete");
        CheckNoviceOperateInputDTO reqData = (CheckNoviceOperateInputDTO) testJsonObj.getReqData();
        insertData(reqData.getAccountId(), reqData.getOperateName(), BooleanUtils.toInteger(false));

        noviceOperateService.completeNoviceOperate(
                reqData.getAccountId(), reqData.getOperateName());
    }

    @Test(description = "不存在且任务未完成")
    public void testCompleteNoviceOperateWhenNotExitsAndNoviceTaskNotComplete() {
        TestJsonObj testJsonObj = jsonDataMap.get("NotExitsAndNoviceTaskNotComplete");
        CheckNoviceOperateInputDTO requestData =
                (CheckNoviceOperateInputDTO) testJsonObj.getReqData();
        noviceOperateService.completeNoviceOperate(
                requestData.getAccountId(), requestData.getOperateName());
    }

    public void insertData(String accountOid, String operateName, Integer status) {
        AccountInfoDTO userAccount = userCenterService.queryAccountInfoByOid(accountOid);
        AccountNoviceOperateDO accountNoviceOperateDO = new AccountNoviceOperateDO();
        accountNoviceOperateDO.setAccountOid(accountOid);
        accountNoviceOperateDO.setAccountGid(userAccount.getGid());
        accountNoviceOperateDO.setName(operateName);
        accountNoviceOperateDO.setStatus(status);

        accountNoviceOperateDAO.insert(accountNoviceOperateDO);
    }
}
