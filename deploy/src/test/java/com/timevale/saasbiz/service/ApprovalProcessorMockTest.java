package com.timevale.saasbiz.service;

import com.timevale.contractapproval.facade.dto.ApprovalDTO;
import com.timevale.contractapproval.facade.output.ApprovalAddOutput;
import com.timevale.contractapproval.facade.output.ApprovalReduceOutput;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.approval.ProcessApprovalClient;
import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalOperateSupport;
import com.timevale.saasbiz.service.approval.approvaloperate.operate.ApprovalAddProcessor;
import com.timevale.saasbiz.service.approval.approvaloperate.operate.ApprovalOperateData;
import com.timevale.saasbiz.service.approval.approvaloperate.operate.ApprovalReduceProcessor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.annotations.Test;

/**
 * ApprovalProcessorMockTest
 *
 * <AUTHOR>
 * @since 2024/8/13 上午10:02
 */
public class ApprovalProcessorMockTest  extends BaseMockTest {

    @InjectMocks
    private ApprovalAddProcessor approvalAddProcessor;

    @InjectMocks
    private ApprovalReduceProcessor approvalReduceProcessor;

    @Mock
    private ProcessApprovalClient processApprovalClient;

    @Mock
    private ApprovalOperateSupport operateSupport;

    @Test
    public void flowListNoGidTest() {

        Mockito.when(processApprovalClient.addApproval(Mockito.any())).thenReturn(new ApprovalAddOutput());
        Mockito.when(processApprovalClient.reduceApproval(Mockito.any())).thenReturn(new ApprovalReduceOutput());
        Mockito.when(operateSupport.checkContractMemberInSubject(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new ApprovalDTO());

        ApprovalOperateData approvalOperateData = new ApprovalOperateData();
        approvalAddProcessor.process(approvalOperateData);
        approvalReduceProcessor.process(approvalOperateData);
    }

}
