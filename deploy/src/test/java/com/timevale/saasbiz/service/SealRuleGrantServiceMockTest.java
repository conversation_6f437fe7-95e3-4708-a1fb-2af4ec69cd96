package com.timevale.saasbiz.service;

import com.timevale.footstone.seal.facade.saas.output.BatchUpdateRuleGrantOutput;
import com.timevale.footstone.seal.facade.saas.output.BindRuleOutput;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.seal.SealRuleGrantClient;
import com.timevale.saasbiz.model.bean.seal.dto.input.UpdateRuleGrantInputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.input.UpdateRuleGrantsInputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.output.UpdateRuleGrantOutputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.output.UpdateRuleGrantsOutputDTO;
import com.timevale.saasbiz.service.seal.impl.SealRuleGrantServiceImpl;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * @ClassName SealRuleGrantServiceMockTest
 * @Description
 * <AUTHOR>
 * @Date 2025/8/5 14:31
 **/
@JsonDataIgnore
public class SealRuleGrantServiceMockTest extends BaseMockTest {

    @InjectMocks
    private SealRuleGrantServiceImpl sealRuleGrantService;
    @Mock
    private SealRuleGrantClient sealRuleGrantClient;

    @Test
    private void updateRuleGrant(){
        Mockito.when(sealRuleGrantClient.updateRuleGrant(Mockito.any())).thenReturn(new BindRuleOutput());
        UpdateRuleGrantOutputDTO outputDTO = sealRuleGrantService.updateRuleGrant("", "", "", new UpdateRuleGrantInputDTO());
        Assert.assertNotNull(outputDTO);
    }

    @Test
    private void batchUpdateRuleGrant(){
        Mockito.when(sealRuleGrantClient.batchUpdateRuleGrant(Mockito.any())).thenReturn(new BatchUpdateRuleGrantOutput());
        UpdateRuleGrantsOutputDTO outputDTO = sealRuleGrantService.batchUpdateRuleGrant("", "", "", new UpdateRuleGrantsInputDTO());
        Assert.assertNotNull(outputDTO);
    }
}
