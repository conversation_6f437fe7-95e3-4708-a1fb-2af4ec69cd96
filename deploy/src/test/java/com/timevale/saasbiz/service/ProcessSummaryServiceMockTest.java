package com.timevale.saasbiz.service;

import com.timevale.saas.common.manage.common.service.model.output.VipFunctionQueryOutput;
import com.timevale.saasbiz.integration.gray.GrayClient;
import com.timevale.saasbiz.integration.saascommon.SaasCommonClient;
import com.timevale.saasbiz.model.bean.oauth.dto.output.QueryLatestUserAgreementOutputDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.model.enums.UserAgreementTypeEnum;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.service.oauth.UserAgreementService;
import com.timevale.saasbiz.service.process.ProcessBizRuleConfigService;
import com.timevale.saasbiz.service.process.impl.ProcessSummaryServiceImpl;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertTrue;

public class ProcessSummaryServiceMockTest {

    @InjectMocks
    private ProcessSummaryServiceImpl processSummaryService;

    @Mock
    private GrayClient grayClient;

    @Mock
    private ProcessBizRuleConfigService processBizRuleConfigService;

    @Mock
    private SaasCommonClient saasCommonClient;

    @Mock
    private UserAgreementService userAgreementService;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test(description = "测试不在灰度范围内")
    public void testCheckAiAgentCanUse_NotInGray() {
        // 准备测试数据
        String subjectId = "subject123";
        String subjectGid = "gid123";
        String accountId = "account123";
        ContractProcessDTO processDTO = new ContractProcessDTO();
        processDTO.setProcessId("process123");

        // mock灰度检查返回false
        when(grayClient.inGrayGid(anyString(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT))).thenReturn(false);

        // 执行测试
        ProcessSummaryServiceImpl.CheckResult result = processSummaryService.checkAiAgentCanUse(subjectId, subjectGid, accountId, processDTO);

        // 验证结果
        assertFalse(result.isCanCreate());
        assertEquals(result.getException().getCode(), String.valueOf(SaasBizResultCode.AIAGENT_NOT_IN_SUPPORT.getCode()));
    }

    @Test(description = "测试个人空间不支持")
    public void testCheckAiAgentCanUse_PersonalSpace() {
        // 准备测试数据
        String subjectId = "account123";
        String subjectGid = "gid123";
        String accountId = "account123";
        ContractProcessDTO processDTO = new ContractProcessDTO();
        processDTO.setProcessId("process123");

        // mock灰度检查返回true
        when(grayClient.inGrayGid(anyString(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT))).thenReturn(true);

        // 执行测试
        ProcessSummaryServiceImpl.CheckResult result = processSummaryService.checkAiAgentCanUse(subjectId, subjectGid, accountId, processDTO);

        // 验证结果
        assertFalse(result.isCanCreate());
        assertEquals(result.getException().getCode(), String.valueOf(SaasBizResultCode.AIAGENT_NOT_IN_SUPPORT.getCode()));
    }

    @Test(description = "测试专有云不支持")
    public void testCheckAiAgentCanUse_DedicatedCloudNotSupport() {
        // 准备测试数据
        String subjectId = "subject123";
        String subjectGid = "gid123";
        String accountId = "account123";
        ContractProcessDTO processDTO = new ContractProcessDTO();
        processDTO.setProcessId("process123");
        processDTO.setDedicatedCloudId("cloud123");

        // mock相关调用
        when(grayClient.inGrayGid(anyString(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT))).thenReturn(true);
        when(processBizRuleConfigService.supportAiSummary(anyString())).thenReturn(false);

        // 执行测试
        ProcessSummaryServiceImpl.CheckResult result = processSummaryService.checkAiAgentCanUse(subjectId, subjectGid, accountId, processDTO);

        // 验证结果
        assertFalse(result.isCanCreate());
        assertEquals(result.getException().getCode(), String.valueOf(SaasBizResultCode.DEDICATED_CLOUD_NOT_SUPPORT.getCode()));
    }

    @Test(description = "测试会员功能不支持")
    public void testCheckAiAgentCanUse_VipFunctionNotSupport() {
        // 准备测试数据
        String subjectId = "subject123";
        String subjectGid = "gid123";
        String accountId = "account123";
        ContractProcessDTO processDTO = new ContractProcessDTO();
        processDTO.setProcessId("process123");

        // mock相关调用
        when(grayClient.inGrayGid(anyString(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT))).thenReturn(true);
        when(processBizRuleConfigService.supportAiSummary(any())).thenReturn(true);

        VipFunctionQueryOutput vipFunctionQueryOutput = new VipFunctionQueryOutput();
        vipFunctionQueryOutput.setEnable(false);
        when(saasCommonClient.queryVipFunction(anyString(), any(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT)))
                .thenReturn(vipFunctionQueryOutput);

        // 执行测试
        ProcessSummaryServiceImpl.CheckResult result = processSummaryService.checkAiAgentCanUse(subjectId, subjectGid, accountId, processDTO);

        // 验证结果
        assertFalse(result.isCanCreate());
        assertEquals(result.getException().getCode(), String.valueOf(SaasBizResultCode.PROCESS_SUMMARY_FUNC_NOT_SUPPORT.getCode()));
    }

    @Test(description = "测试未同意AI协议")
    public void testCheckAiAgentCanUse_NoAiAgreement() {
        // 准备测试数据
        String subjectId = "subject123";
        String subjectGid = "gid123";
        String accountId = "account123";
        ContractProcessDTO processDTO = new ContractProcessDTO();
        processDTO.setProcessId("process123");

        // mock相关调用
        when(grayClient.inGrayGid(anyString(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT))).thenReturn(true);
        when(processBizRuleConfigService.supportAiSummary(any())).thenReturn(true);

        VipFunctionQueryOutput vipFunctionQueryOutput = new VipFunctionQueryOutput();
        vipFunctionQueryOutput.setEnable(true);
        when(saasCommonClient.queryVipFunction(anyString(), any(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT)))
                .thenReturn(vipFunctionQueryOutput);

        when(userAgreementService.queryLatestUserAgreement(any(), anyString(), eq(UserAgreementTypeEnum.ENABLE_AI.getType())))
                .thenReturn(null);

        // 执行测试
        ProcessSummaryServiceImpl.CheckResult result = processSummaryService.checkAiAgentCanUse(subjectId, subjectGid, accountId, processDTO);

        // 验证结果
        assertFalse(result.isCanCreate());
        assertEquals(result.getException().getCode(), String.valueOf(SaasBizResultCode.PROCESS_SUMMARY_AI_NOT_AGGREMENT.getCode()));
    }

    @Test(description = "测试正常场景")
    public void testCheckAiAgentCanUse_Normal() {
        // 准备测试数据
        String subjectId = "subject123";
        String subjectGid = "gid123";
        String accountId = "account123";
        ContractProcessDTO processDTO = new ContractProcessDTO();
        processDTO.setProcessId("process123");

        // mock相关调用
        when(grayClient.inGrayGid(anyString(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT))).thenReturn(true);
        when(processBizRuleConfigService.supportAiSummary(any())).thenReturn(true);

        VipFunctionQueryOutput vipFunctionQueryOutput = new VipFunctionQueryOutput();
        vipFunctionQueryOutput.setEnable(true);
        when(saasCommonClient.queryVipFunction(anyString(), any(), eq(FunctionCodeConstants.CONTRACT_DETAIL_AGENT)))
                .thenReturn(vipFunctionQueryOutput);

        QueryLatestUserAgreementOutputDTO agreementOutput = new QueryLatestUserAgreementOutputDTO();
        agreementOutput.setLatestAgreeTime(System.currentTimeMillis());
        when(userAgreementService.queryLatestUserAgreement(any(), anyString(), eq(UserAgreementTypeEnum.ENABLE_AI.getType())))
                .thenReturn(agreementOutput);

        // 执行测试
        ProcessSummaryServiceImpl.CheckResult result = processSummaryService.checkAiAgentCanUse(subjectId, subjectGid, accountId, processDTO);

        // 验证结果
        assertTrue(result.isCanCreate());
    }
}
