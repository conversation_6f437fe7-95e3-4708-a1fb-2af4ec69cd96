package com.timevale.saasbiz.service;

import com.timevale.aiagent.generic.rpc.dto.input.BizContextDTO;
import com.timevale.aiagent.generic.rpc.dto.input.UserBaseInfo;
import com.timevale.aiagent.generic.rpc.dto.output.AssembleParmsResult;
import com.timevale.aiagent.generic.rpc.dto.output.UserLoginInfo;
import com.timevale.clmc.facade.api.enums.RagSplitTableBizType;
import com.timevale.saasbiz.aiagent.ProcessDetailStrategyImpl;
import com.timevale.saasbiz.base.BasePowerMockTest;
import com.timevale.saasbiz.config.BizConfigCenter;
import com.timevale.saasbiz.integration.clmc.ClmcClient;
import com.timevale.saasbiz.integration.filesystem.FileSystemClient;
import com.timevale.saasbiz.integration.process.ProcessClient;
import com.timevale.saasbiz.model.bean.ai.agent.dto.output.ReadFilesByProcessIdOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.model.exception.SaasBizRuntimeException;
import com.timevale.saasbiz.service.ai.agent.AiAgentService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@PrepareForTest({BizConfigCenter.class})
public class ProcessDetailStrategyMockTest extends BasePowerMockTest {

    @InjectMocks
    private ProcessDetailStrategyImpl processDetailStrategy;

    @Mock
    private ProcessClient processClient;

    @Mock
    private UserCenterService userCenterService;

    @Mock
    private ClmcClient clmcClient;

    @Mock
    private FileSystemClient fileSystemClient;

    @Mock
    private AiAgentService aiAgentService;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testAssembleParams_Success() {
        // 准备测试数据
        String processId = "test_process_id";
        String operatorId = "test_operator_id";
        String tenantId = "test_tenant_id";
        String menuId = "test_menu_id";
        Integer flowType = 1;
        String orgName = "test_org";
        String userName = "test_user";
        String userHead = "test_head";
        String splitTableName = "test_table";
        String fileId = "test_file_id";

        // 准备BizContextDTO
        BizContextDTO bizContextDTO = new BizContextDTO();
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setOperatorId(operatorId);
        userBaseInfo.setTenantId(tenantId);
        bizContextDTO.setUserBaseInfo(userBaseInfo);
        Map<String, Object> bizContext = new HashMap<>();
        bizContext.put("processId", processId);
        bizContext.put("menuId", menuId);
        bizContext.put("flowType", flowType);
        bizContextDTO.setBizContext(bizContext);

        // Mock用户和组织账户信息
        AccountInfoDTO userAccount = new AccountInfoDTO();
        userAccount.setOid(operatorId);
        userAccount.setName(userName);
        userAccount.setUserHead(userHead);
        AccountInfoDTO orgAccount = new AccountInfoDTO();
        orgAccount.setOid(tenantId);
        orgAccount.setName(orgName);
        orgAccount.setGid("test_gid");

        when(userCenterService.queryAccountInfoByOid(operatorId)).thenReturn(userAccount);
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(orgAccount);

        // Mock文件系统客户端
        when(fileSystemClient.getDownloadUrl(userHead, false, 24 * 60 * 60 * 1000)).thenReturn("http://test.com/head.jpg");

        // Mock合同流程信息
        ContractProcessDTO processDTO = new ContractProcessDTO();
        processDTO.setProcessCreateTime(new Date());
        when(processClient.getByProcessId(processId)).thenReturn(processDTO);

        // Mock分表名称
        when(clmcClient.getSplitTableName(anyString(), any())).thenReturn(splitTableName);

        // Mock权限文件列表
        List<ReadFilesByProcessIdOutputDTO.FileInfo> permissionFiles = new ArrayList<>();
        ReadFilesByProcessIdOutputDTO.FileInfo fileInfo = new ReadFilesByProcessIdOutputDTO.FileInfo();
        fileInfo.setFileId(fileId);
        permissionFiles.add(fileInfo);
        when(aiAgentService.getPermissionFiles(anyString(), any(), anyString(), anyString())).thenReturn(permissionFiles);

        // Mock开场白
        Map<String, String> introductionInfo = new HashMap<>();
        introductionInfo.put(RagSplitTableBizType.PROCESS_DETAIL.getBizCode(), "test_introduction");
        PowerMockito.mockStatic(BizConfigCenter.class);
        when(BizConfigCenter.getAiAgentIntroductionInfo()).thenReturn(introductionInfo);

        // 执行测试
        AssembleParmsResult result = processDetailStrategy.assembleParams(bizContextDTO);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(result.getUserId(), String.format("%s_%s_%s", operatorId, tenantId, processId));
        
        UserLoginInfo userLoginInfo = result.getUserLoginInfo();
        Assert.assertNotNull(userLoginInfo);
        Assert.assertEquals(userLoginInfo.getUserName(), userName);
        Assert.assertEquals(userLoginInfo.getUserAvatarUrl(), "http://test.com/head.jpg");

        Map<String, Object> fixedInputs = result.getFixedInputs();
        Assert.assertNotNull(fixedInputs);
        Assert.assertEquals(fixedInputs.get("process_id"), processId);
        Assert.assertEquals(fixedInputs.get("org_name"), orgName);
        Assert.assertEquals(fixedInputs.get("collection_name"), splitTableName);
        Assert.assertEquals(fixedInputs.get("user_id"), operatorId);
        Assert.assertEquals(fixedInputs.get("org_id"), tenantId);
        Assert.assertEquals(fixedInputs.get("menu_id"), menuId);
        Assert.assertEquals(fixedInputs.get("flow_type"), flowType);
        
        Assert.assertEquals(result.getIntroduction(), "test_introduction");
    }

    @Test
    public void testAssembleParams_MissingProcessId() {
        // 准备测试数据
        BizContextDTO bizContextDTO = new BizContextDTO();
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setOperatorId("test_operator_id");
        userBaseInfo.setTenantId("test_tenant_id");
        bizContextDTO.setUserBaseInfo(userBaseInfo);
        Map<String, Object> bizContext = new HashMap<>();
        bizContext.put("flowType", 1);
        bizContextDTO.setBizContext(bizContext);

        // 执行测试
        try {
            processDetailStrategy.assembleParams(bizContextDTO);
        } catch (SaasBizRuntimeException e) {
            Assert.assertEquals(e.getCode(), String.valueOf(SaasBizResultCode.AGENT_PARAM_PROCESS_ID_MISS.getCode()));
            return;
        }
        Assert.fail("should throw SaasBizRuntimeException");
    }

    @Test
    public void testAssembleParams_MissingFlowType() {
        // 准备测试数据
        BizContextDTO bizContextDTO = new BizContextDTO();
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setOperatorId("test_operator_id");
        userBaseInfo.setTenantId("test_tenant_id");
        bizContextDTO.setUserBaseInfo(userBaseInfo);
        Map<String, Object> bizContext = new HashMap<>();
        bizContext.put("processId", "test_process_id");
        bizContextDTO.setBizContext(bizContext);

        // 执行测试
        try {
            processDetailStrategy.assembleParams(bizContextDTO);
        } catch (SaasBizRuntimeException e) {
            Assert.assertEquals(e.getCode(), String.valueOf(SaasBizResultCode.AGENT_PARAM_FLOW_TYPE_MISS.getCode()));
            return;
        }
        Assert.fail("should throw SaasBizRuntimeException");
    }
}
