package com.timevale.saasbiz.service;

import com.alibaba.fastjson.JSONObject;
import com.timevale.saas.common.manage.common.service.enums.TaskStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.TaskTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationAuthReasonEnum;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskAddInput;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskUpdateInput;
import com.timevale.saas.common.manage.common.service.model.output.SaasTaskInfoOutput;
import com.timevale.saasbiz.base.BasePowerMockTest;
import com.timevale.saasbiz.integration.saascommon.AuthRelationInnerClient;
import com.timevale.saasbiz.integration.saascommon.SaasCommonClient;
import com.timevale.saasbiz.model.bean.authrelation.bo.AuthRelationBatchAddContextBO;
import com.timevale.saasbiz.model.bean.authrelation.bo.AuthRelationChildTenantDetailBO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationBatchAddResultDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationEffectiveProductDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationGetBatchAddTaskDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.enums.authrelation.AuthRelationAddSceneEnum;
import com.timevale.saasbiz.model.enums.authrelation.AuthRelationAddTaskStatusEnum;
import com.timevale.saasbiz.model.enums.authrelation.AuthRelationBatchAddTaskStatusEnum;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.utils.AppConfigUtil;
import com.timevale.saasbiz.model.utils.CacheUtil;
import com.timevale.saasbiz.mq.bean.AuthRelationAddMsgEntity;
import com.timevale.saasbiz.mq.producer.AuthRelationAddProducer;
import com.timevale.saasbiz.service.authrelation.AuthRelationBizService;
import com.timevale.saasbiz.service.authrelation.AuthRelationCoreService;
import com.timevale.saasbiz.service.authrelation.processor.AuthRelationBatchAddProcessor;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.*;
import static com.timevale.saas.common.manage.common.service.exception.ResultEnum.TASK_NOT_EXIST;
import static com.timevale.saasbiz.model.enums.AppConfigEnum.BATCH_ADD_AUTH_RELATION_NEW_PROGRESS_MAX_SIZE;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthRelationBatchAddProcessor的Mock测试类
 * 覆盖批量添加关联企业处理器的所有方法，确保高分支覆盖率
 */
@PrepareForTest({CacheUtil.class, AppConfigUtil.class})
public class AuthRelationBatchAddProcessorMockTest extends BasePowerMockTest {

    @InjectMocks
    private AuthRelationBatchAddProcessor authRelationBatchAddProcessor;

    @Mock
    private SaasCommonClient saasCommonClient;

    @Mock
    private UserCenterService userCenterService;

    @Mock
    private AuthRelationCoreService authRelationCoreService;

    @Mock
    private AuthRelationInnerClient authRelationInnerClient;

    @Mock
    private AuthRelationBizService authRelationBizService;

    @Mock
    private AuthRelationAddProducer authRelationAddProducer;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试checkExist方法 - 无缓存数据
     */
    @Test(description = "测试检查存在进行中任务 - 无缓存数据")
    public void testCheckExistNoCache() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");

        // mock缓存返回空
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.get(anyString())).thenReturn(null);

        // 执行方法，不应抛出异常
        authRelationBatchAddProcessor.checkExist(context);
    }

    /**
     * 测试checkExist方法 - 任务不存在
     */
    @Test(description = "测试检查存在进行中任务 - 任务不存在")
    public void testCheckExistTaskNotExist() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        cacheData.setSerialId("serialId");
        cacheData.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());
        String cacheJson = JSONObject.toJSONString(cacheData);

        // mock缓存和服务调用
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.get(anyString())).thenReturn(cacheJson);
        when(saasCommonClient.querySaasTask(anyString(), anyInt())).thenThrow(new SaasCommonBizException(TASK_NOT_EXIST.getCode(), "任务不存在"));

        // 执行方法，不应抛出异常
        authRelationBatchAddProcessor.checkExist(context);

        // 验证调用
        verify(saasCommonClient).querySaasTask(anyString(), anyInt());
    }

    /**
     * 测试checkExist方法 - 任务已结束
     */
    @Test(description = "测试检查存在进行中任务 - 任务已结束")
    public void testCheckExistTaskFinished() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        cacheData.setSerialId("serialId");
        cacheData.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());
        String cacheJson = JSONObject.toJSONString(cacheData);

        // 准备任务输出
        SaasTaskInfoOutput taskOutput = new SaasTaskInfoOutput();
        taskOutput.setTaskStatus(TaskStatusEnum.FINISH.getType());

        // mock缓存和服务调用
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.get(anyString())).thenReturn(cacheJson);
        when(saasCommonClient.querySaasTask(anyString(), anyInt())).thenReturn(taskOutput);

        // 执行方法，不应抛出异常
        authRelationBatchAddProcessor.checkExist(context);

        // 验证调用
        verify(saasCommonClient).querySaasTask(anyString(), anyInt());
    }

    /**
     * 测试checkExist方法 - 当前用户有进行中任务
     */
    @Test(description = "测试检查存在进行中任务 - 当前用户有进行中任务", expectedExceptions = SaasBizException.class)
    public void testCheckExistCurrentUserTask() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");
        context.setOperatorOid("operatorOid");

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        cacheData.setSerialId("serialId");
        cacheData.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());
        cacheData.setAccountId("operatorOid");
        String cacheJson = JSONObject.toJSONString(cacheData);

        // 准备任务输出
        SaasTaskInfoOutput taskOutput = new SaasTaskInfoOutput();
        taskOutput.setTaskStatus(TaskStatusEnum.EXECUTING.getType());

        // mock缓存和服务调用
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.get(anyString())).thenReturn(cacheJson);
        when(saasCommonClient.querySaasTask(anyString(), anyInt())).thenReturn(taskOutput);

        // 执行方法，应抛出异常
        authRelationBatchAddProcessor.checkExist(context);
    }

    /**
     * 测试checkExist方法 - 其他用户有进行中任务
     */
    @Test(description = "测试检查存在进行中任务 - 其他用户有进行中任务", expectedExceptions = SaasBizException.class)
    public void testCheckExistOtherUserTask() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");
        context.setOperatorOid("currentOperator");

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        cacheData.setSerialId("serialId");
        cacheData.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());
        cacheData.setAccountId("otherOperator");
        String cacheJson = JSONObject.toJSONString(cacheData);

        // 准备任务输出
        SaasTaskInfoOutput taskOutput = new SaasTaskInfoOutput();
        taskOutput.setTaskStatus(TaskStatusEnum.EXECUTING.getType());

        // 准备用户信息
        AccountInfoDTO accountInfo = new AccountInfoDTO();
        accountInfo.setName("测试用户");

        // mock缓存、服务调用和用户查询
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.get(anyString())).thenReturn(cacheJson);
        when(saasCommonClient.querySaasTask(anyString(), anyInt())).thenReturn(taskOutput);
        when(userCenterService.queryAccountInfoByOid("otherOperator")).thenReturn(accountInfo);

        // 执行方法，应抛出异常
        authRelationBatchAddProcessor.checkExist(context);

        // 验证用户查询被调用
        verify(userCenterService).queryAccountInfoByOid("otherOperator");
    }

    /**
     * 测试checkParam方法 - 参数有效
     */
    @Test(description = "测试参数校验 - 参数有效")
    public void testCheckParamValid() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");
        context.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());

        AuthRelationChildTenantDetailBO childTenant = new AuthRelationChildTenantDetailBO();
        childTenant.setChildTenantOid("childOid");
        childTenant.setChildTenantName("子企业名称");
        childTenant.setChildUsccCode("123456789012345678");
        childTenant.setParentTenantGid("parentGid");
        childTenant.setAuthReason(AuthRelationAuthReasonEnum.PARENT_CHILD.getCode());

        context.setChildTenantList(Collections.singletonList(childTenant));

        // 执行方法，不应抛出异常
        authRelationBatchAddProcessor.checkParam(context);
    }

    /**
     * 测试checkParam方法 - 缺少授权企业Gid
     */
    @Test(description = "测试参数校验 - 缺少授权企业Gid", expectedExceptions = SaasBizException.class)
    public void testCheckParamMissingAuthTenantGid() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());

        AuthRelationChildTenantDetailBO childTenant = new AuthRelationChildTenantDetailBO();
        context.setChildTenantList(Collections.singletonList(childTenant));

        // 执行方法，应抛出异常
        authRelationBatchAddProcessor.checkParam(context);
    }

    /**
     * 测试checkParam方法 - 缺少子企业列表
     */
    @Test(description = "测试参数校验 - 缺少子企业列表", expectedExceptions = SaasBizException.class)
    public void testCheckParamEmptyChildTenantList() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");
        context.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());
        context.setChildTenantList(Collections.emptyList());

        // 执行方法，应抛出异常
        authRelationBatchAddProcessor.checkParam(context);
    }

    /**
     * 测试checkParam方法 - 子企业重复
     */
    @Test(description = "测试参数校验 - 子企业重复", expectedExceptions = SaasBizException.class)
    public void testCheckParamDuplicateChildTenant() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");
        context.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());

        AuthRelationChildTenantDetailBO childTenant1 = new AuthRelationChildTenantDetailBO();
        childTenant1.setChildTenantOid("childOid");
        childTenant1.setChildTenantName("子企业名称1");
        childTenant1.setChildUsccCode("123456789012345678");
        childTenant1.setParentTenantGid("parentGid");
        childTenant1.setAuthReason(AuthRelationAuthReasonEnum.PARENT_CHILD.getCode());

        AuthRelationChildTenantDetailBO childTenant2 = new AuthRelationChildTenantDetailBO();
        childTenant2.setChildTenantOid("childOid");
        childTenant2.setChildTenantName("子企业名称2");
        childTenant2.setChildUsccCode("876543210987654321");
        childTenant2.setParentTenantGid("parentGid");
        childTenant2.setAuthReason(AuthRelationAuthReasonEnum.PARENT_CHILD.getCode());

        context.setChildTenantList(Arrays.asList(childTenant1, childTenant2));

        // 执行方法，应抛出异常
        authRelationBatchAddProcessor.checkParam(context);
    }

    /**
     * 测试checkSpecialLogic方法 - 正常情况
     */
    @Test(description = "测试特殊逻辑校验 - 正常情况")
    public void testCheckSpecialLogicNormal() {
        // 准备数据
        AuthRelationChildTenantDetailBO childTenantDetailBO = new AuthRelationChildTenantDetailBO();
        childTenantDetailBO.setChildTenantOid("childTenantOid");
        childTenantDetailBO.setChildTenantName("childTenantName");
        childTenantDetailBO.setOrderId("orderId");
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantGid("testGid");
        context.setChildTenantList(Collections.singletonList(childTenantDetailBO));

        // mock依赖
        when(userCenterService.getAdminOidByOrgGid("testGid")).thenReturn("adminOid");
        when(authRelationBizService.doExistParentAuthRelation("testGid")).thenReturn(true);
        when(authRelationInnerClient.checkOneLevelMaxCount(anyString(), anyInt())).thenReturn(true);

        // 模拟产品数据
        AuthRelationEffectiveProductDTO productDTO = new AuthRelationEffectiveProductDTO();
        productDTO.setOrderId("orderId");
        productDTO.setMargin(1);
        productDTO.setEffectiveStartTime(System.currentTimeMillis() - 86400000); // 昨天
        productDTO.setEffectiveEndTime(System.currentTimeMillis() + 86400000); // 明天
        when(authRelationCoreService.doQueryEffectiveProducts("testGid")).thenReturn(Collections.singletonList(productDTO));

        // 执行方法
        authRelationBatchAddProcessor.checkSpecialLogic(context);

        // 验证调用
        verify(userCenterService).getAdminOidByOrgGid("testGid");
        verify(authRelationBizService).doExistParentAuthRelation("testGid");
        verify(authRelationCoreService).doQueryEffectiveProducts("testGid");
        verify(authRelationInnerClient).checkOneLevelMaxCount(anyString(), anyInt());
    }

    /**
     * 测试checkOrderMargin方法 - 余额充足
     */
    @Test(description = "测试订单余额校验 - 余额充足")
    public void testCheckOrderMarginSufficient() {
        // 准备数据
        AuthRelationChildTenantDetailBO childTenant = new AuthRelationChildTenantDetailBO();
        childTenant.setOrderId("orderId");
        List<AuthRelationChildTenantDetailBO> childTenantList = Collections.singletonList(childTenant);

        AuthRelationEffectiveProductDTO productDTO = new AuthRelationEffectiveProductDTO();
        productDTO.setMargin(10);
        Map<String, AuthRelationEffectiveProductDTO> orderMap = Collections.singletonMap("orderId", productDTO);

        // 执行方法
        authRelationBatchAddProcessor.checkOrderMargin(childTenantList, orderMap);
    }

    /**
     * 测试checkOrderMargin方法 - 余额不足
     */
    @Test(description = "测试订单余额校验 - 余额不足", expectedExceptions = SaasBizException.class)
    public void testCheckOrderMarginInsufficient() {
        // 准备数据
        AuthRelationChildTenantDetailBO childTenant1 = new AuthRelationChildTenantDetailBO();
        childTenant1.setOrderId("orderId");

        AuthRelationChildTenantDetailBO childTenant2 = new AuthRelationChildTenantDetailBO();
        childTenant2.setOrderId("orderId");

        List<AuthRelationChildTenantDetailBO> childTenantList = Arrays.asList(childTenant1, childTenant2);

        AuthRelationEffectiveProductDTO productDTO = new AuthRelationEffectiveProductDTO();
        productDTO.setMargin(1);
        Map<String, AuthRelationEffectiveProductDTO> orderMap = Collections.singletonMap("orderId", productDTO);

        // 执行方法
        authRelationBatchAddProcessor.checkOrderMargin(childTenantList, orderMap);
    }

    /**
     * 测试batchSaveAuthRelation方法 - 无需跳转任务中心
     * @throws Exception 
     */
    @Test(description = "测试批量添加关联企业 - 无需跳转任务中心")
    public void testBatchSaveAuthRelationNoJobCenter() throws Exception {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantOid("authTenantOid");
        context.setAuthTenantGid("authTenantGid");
        context.setOperatorOid("operatorOid");
        context.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());

        AuthRelationChildTenantDetailBO childTenant = new AuthRelationChildTenantDetailBO();
        childTenant.setChildTenantOid("childOid");
        childTenant.setChildTenantName("子企业名称");
        childTenant.setOrderId("orderId");
        childTenant.setParentTenantGid("parentGid");
        childTenant.setAuthReason(AuthRelationAuthReasonEnum.PARENT_CHILD.getCode());

        context.setChildTenantList(Collections.singletonList(childTenant));

        // mock依赖
        AccountInfoDTO operator = new AccountInfoDTO();
        operator.setOid("operatorOid");
        operator.setGid("operatorGid");
        when(userCenterService.queryAccountInfoByOid("operatorOid")).thenReturn(operator);

        // 模拟配置
        PowerMockito.mockStatic(AppConfigUtil.class);
        PowerMockito.when(AppConfigUtil.getInteger(BATCH_ADD_AUTH_RELATION_NEW_PROGRESS_MAX_SIZE)).thenReturn(10);

        // mock缓存方法
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.doNothing().when(CacheUtil.class, "degradeSet", anyString(), anyString(), anyLong(), any());

        // 执行方法
        AuthRelationBatchAddResultDTO result = authRelationBatchAddProcessor.batchSaveAuthRelation(context);

        // 验证结果
        Assert.assertFalse(result.isGoToJobCenter());

        // 验证调用
        verify(userCenterService).queryAccountInfoByOid("operatorOid");
        verify(saasCommonClient).addTasks(any(SaasTaskAddInput.class));
        verify(authRelationAddProducer).sendMessage(any(AuthRelationAddMsgEntity.class));
    }

    /**
     * 测试batchSaveAuthRelation方法 - 需要跳转任务中心
     */
    @Test(description = "测试批量添加关联企业 - 需要跳转任务中心")
    public void testBatchSaveAuthRelationWithJobCenter() {
        // 准备数据
        AuthRelationBatchAddContextBO context = new AuthRelationBatchAddContextBO();
        context.setAuthTenantOid("authTenantOid");
        context.setAuthTenantGid("authTenantGid");
        context.setOperatorOid("operatorOid");
        context.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());

        // 创建11个子企业，超过配置的10个
        List<AuthRelationChildTenantDetailBO> childTenantList = new ArrayList<>();
        for (int i = 0; i < 11; i++) {
            AuthRelationChildTenantDetailBO childTenant = new AuthRelationChildTenantDetailBO();
            childTenant.setChildTenantOid("childOid" + i);
            childTenant.setChildTenantName("子企业名称" + i);
            childTenant.setOrderId("orderId");
            childTenant.setParentTenantGid("parentGid");
            childTenant.setAuthReason(AuthRelationAuthReasonEnum.PARENT_CHILD.getCode());
            childTenantList.add(childTenant);
        }
        context.setChildTenantList(childTenantList);

        // mock依赖
        AccountInfoDTO operator = new AccountInfoDTO();
        operator.setOid("operatorOid");
        operator.setGid("operatorGid");
        when(userCenterService.queryAccountInfoByOid("operatorOid")).thenReturn(operator);

        // 模拟配置
        PowerMockito.mockStatic(AppConfigUtil.class);
        PowerMockito.when(AppConfigUtil.getInteger(BATCH_ADD_AUTH_RELATION_NEW_PROGRESS_MAX_SIZE)).thenReturn(10);

        // 执行方法
        AuthRelationBatchAddResultDTO result = authRelationBatchAddProcessor.batchSaveAuthRelation(context);

        // 验证结果
        Assert.assertTrue(result.isGoToJobCenter());

        // 验证调用
        verify(userCenterService).queryAccountInfoByOid("operatorOid");
        verify(saasCommonClient).addTasks(any(SaasTaskAddInput.class));
        verify(authRelationAddProducer, times(11)).sendMessage(any(AuthRelationAddMsgEntity.class));
    }

    /**
     * 测试queryBatchAddProgress方法 - 无缓存数据
     */
    @Test(description = "测试查询批量添加进度 - 无缓存数据")
    public void testQueryBatchAddProgressNoCache() {
        // 准备数据
        AccountInfoDTO tenantAccount = new AccountInfoDTO();
        tenantAccount.setGid("tenantGid");
        when(userCenterService.queryAccountInfoByOid("tenantOid")).thenReturn(tenantAccount);

        // mock缓存返回空
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.degradeGet(anyString())).thenReturn(null);

        // 执行方法
        AuthRelationGetBatchAddTaskDTO result = authRelationBatchAddProcessor.queryBatchAddProgress("tenantOid");

        // 验证结果
        Assert.assertNull(result);

        // 验证调用
        verify(userCenterService).queryAccountInfoByOid("tenantOid");
    }

    /**
     * 测试queryBatchAddProgress方法 - 任务不存在
     */
    @Test(description = "测试查询批量添加进度 - 任务不存在")
    public void testQueryBatchAddProgressTaskNotExist() {
        // 准备数据
        AccountInfoDTO tenantAccount = new AccountInfoDTO();
        tenantAccount.setGid("tenantGid");
        when(userCenterService.queryAccountInfoByOid("tenantOid")).thenReturn(tenantAccount);

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        cacheData.setSerialId("serialId");
        cacheData.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());
        cacheData.setAccountId("accountId");
        String cacheJson = JSONObject.toJSONString(cacheData);

        // mock缓存和服务调用
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.degradeGet(anyString())).thenReturn(cacheJson);
        when(saasCommonClient.querySaasTask(anyString(), anyInt())).thenThrow(new SaasCommonBizException(TASK_NOT_EXIST.getCode(), "任务不存在"));

        // 执行方法
        AuthRelationGetBatchAddTaskDTO result = authRelationBatchAddProcessor.queryBatchAddProgress("tenantOid");

        // 验证结果
        Assert.assertNull(result);

        // 验证调用
        verify(userCenterService).queryAccountInfoByOid("tenantOid");
        verify(saasCommonClient).querySaasTask(anyString(), anyInt());
    }

    /**
     * 测试queryBatchAddProgress方法 - 进行中任务
     */
    @Test(description = "测试查询批量添加进度 - 进行中任务")
    public void testQueryBatchAddProgressTaskExecuting() {
        // 准备数据
        AccountInfoDTO tenantAccount = new AccountInfoDTO();
        tenantAccount.setGid("tenantGid");
        when(userCenterService.queryAccountInfoByOid("tenantOid")).thenReturn(tenantAccount);

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        cacheData.setSerialId("serialId");
        cacheData.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());
        cacheData.setAccountId("accountId");

        AuthRelationBatchAddProcessor.BatchAddChildTenantCacheData childData = new AuthRelationBatchAddProcessor.BatchAddChildTenantCacheData();
        childData.setChildTenantName("子企业名称");
        childData.setAddTaskStatus(AuthRelationAddTaskStatusEnum.UN_DO.getCode());
        cacheData.getChildTenantMap().put("taskBizId", childData);

        String cacheJson = JSONObject.toJSONString(cacheData);

        // 准备任务输出
        SaasTaskInfoOutput taskOutput = new SaasTaskInfoOutput();
        taskOutput.setTaskStatus(TaskStatusEnum.EXECUTING.getType());
        taskOutput.setTaskCreatOr("accountId");

        // mock缓存和服务调用
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.degradeGet(anyString())).thenReturn(cacheJson);
        when(saasCommonClient.querySaasTask(anyString(), anyInt())).thenReturn(taskOutput);

        // 执行方法
        AuthRelationGetBatchAddTaskDTO result = authRelationBatchAddProcessor.queryBatchAddProgress("tenantOid");

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(result.getAuthRelationBatchAddTaskStatus(), AuthRelationBatchAddTaskStatusEnum.DOING.getCode());
        Assert.assertTrue(result.getTotalCount() == 1);
        Assert.assertTrue(result.getSuccessCount() == 0);
        Assert.assertTrue(result.getHasHandleCount() == 0);
        Assert.assertEquals(result.getResultList().size(), 1);
        Assert.assertEquals(result.getResultList().get(0).getChildTenantName(), "子企业名称");
        Assert.assertEquals(result.getResultList().get(0).getAddTaskStatus(), AuthRelationAddTaskStatusEnum.UN_DO.getCode());

        // 验证调用
        verify(userCenterService).queryAccountInfoByOid("tenantOid");
        verify(saasCommonClient).querySaasTask(anyString(), anyInt());
    }

    /**
     * 测试queryBatchAddProgress方法 - 任务完成全部成功
     * @throws Exception 
     */
    @Test(description = "测试查询批量添加进度 - 任务完成全部成功")
    public void testQueryBatchAddProgressTaskFinishedAllSuccess() throws Exception {
        // 准备数据
        AccountInfoDTO tenantAccount = new AccountInfoDTO();
        tenantAccount.setGid("tenantGid");
        when(userCenterService.queryAccountInfoByOid("tenantOid")).thenReturn(tenantAccount);

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        cacheData.setSerialId("serialId");
        cacheData.setAuthScene(AuthRelationAddSceneEnum.ADD.getScene());
        cacheData.setAccountId("accountId");

        AuthRelationBatchAddProcessor.BatchAddChildTenantCacheData childData = new AuthRelationBatchAddProcessor.BatchAddChildTenantCacheData();
        childData.setChildTenantName("子企业名称");
        childData.setAddTaskStatus(AuthRelationAddTaskStatusEnum.SUCCESS.getCode());
        cacheData.getChildTenantMap().put("taskBizId", childData);

        String cacheJson = JSONObject.toJSONString(cacheData);

        // 准备任务输出
        SaasTaskInfoOutput taskOutput = new SaasTaskInfoOutput();
        taskOutput.setTaskStatus(TaskStatusEnum.FINISH.getType());
        taskOutput.setTaskCreatOr("accountId");

        // mock缓存和服务调用
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.degradeGet(anyString())).thenReturn(cacheJson);
        when(saasCommonClient.querySaasTask(anyString(), anyInt())).thenReturn(taskOutput);
        PowerMockito.doNothing().when(CacheUtil.class, "degradeDelete", anyString());

        // 执行方法
        AuthRelationGetBatchAddTaskDTO result = authRelationBatchAddProcessor.queryBatchAddProgress("tenantOid");

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(result.getAuthRelationBatchAddTaskStatus(), AuthRelationBatchAddTaskStatusEnum.COMPLETED_ALL_SUCCESS.getCode());
        Assert.assertTrue(result.getTotalCount() == 1);
        Assert.assertTrue(result.getSuccessCount() == 1);
        Assert.assertTrue(result.getHasHandleCount() == 1);

        // 验证调用
        verify(userCenterService).queryAccountInfoByOid("tenantOid");
        verify(saasCommonClient).querySaasTask(anyString(), anyInt());
    }

    /**
     * 测试updateBatchAddProgress方法 - 任务成功
     * @throws Exception 
     */
    @Test(description = "测试更新批量添加进度 - 任务成功")
    public void testUpdateBatchAddProgressSuccess() throws Exception {
        // 准备数据
        String tenantGid = "tenantGid";
        String taskBizId = "taskBizId";
        int taskType = TaskTypeEnum.BATCH_ADD_AUTH_RELATION.getType();

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        AuthRelationBatchAddProcessor.BatchAddChildTenantCacheData childData = new AuthRelationBatchAddProcessor.BatchAddChildTenantCacheData();
        cacheData.getChildTenantMap().put(taskBizId, childData);

        // mock缓存和服务调用
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.degradeGet(anyString())).thenReturn(JSONObject.toJSONString(cacheData));
        PowerMockito.doNothing().when(CacheUtil.class, "degradeSet", anyString(), anyString(), anyLong(), any());

        // 执行方法
        authRelationBatchAddProcessor.updateBatchAddProgress(tenantGid, taskBizId, taskType, true, null);

        // 验证调用
        verify(saasCommonClient).updateTask(any(SaasTaskUpdateInput.class));
    }

    /**
     * 测试updateBatchAddProgress方法 - 任务失败
     * @throws Exception 
     */
    @Test(description = "测试更新批量添加进度 - 任务失败")
    public void testUpdateBatchAddProgressFailure() throws Exception {
        // 准备数据
        String tenantGid = "tenantGid";
        String taskBizId = "taskBizId";
        int taskType = TaskTypeEnum.BATCH_ADD_AUTH_RELATION.getType();
        String failReason = "失败原因";

        // 准备缓存数据
        AuthRelationBatchAddProcessor.BatchAddCacheData cacheData = new AuthRelationBatchAddProcessor.BatchAddCacheData();
        AuthRelationBatchAddProcessor.BatchAddChildTenantCacheData childData = new AuthRelationBatchAddProcessor.BatchAddChildTenantCacheData();
        cacheData.getChildTenantMap().put(taskBizId, childData);

        // mock缓存和服务调用
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.degradeGet(anyString())).thenReturn(JSONObject.toJSONString(cacheData));
        PowerMockito.doNothing().when(CacheUtil.class, "degradeSet", anyString(), anyString(), anyLong(), any());

        // 执行方法
        authRelationBatchAddProcessor.updateBatchAddProgress(tenantGid, taskBizId, taskType, false, failReason);

        // 验证调用
        verify(saasCommonClient).updateTask(any(SaasTaskUpdateInput.class));
    }
}