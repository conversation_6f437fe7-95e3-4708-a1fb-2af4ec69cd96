package com.timevale.saasbiz.service;

import com.google.common.collect.Lists;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecDetailOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerExecLastTrialDoneOutputDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerFieldDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerTemplateDTO;
import com.timevale.contractanalysis.facade.api.dto.ledger.LedgerTemplateStructMatchResultDTO;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecResultStatusEnum;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecRunModelEnum;
import com.timevale.contractanalysis.facade.api.enums.LedgerExecRunStatusEnum;
import com.timevale.contractanalysis.facade.api.enums.LedgerExtractTypeEnum;
import com.timevale.contractanalysis.facade.api.enums.LedgerFieldTypeEnum;
import com.timevale.contractmanager.common.service.enums.FlowTypeEnum;
import com.timevale.contractmanager.common.service.result.QueryProcessFilesResult;
import com.timevale.contractmanager.common.service.result.ledger.LedgerFormDetailResult;
import com.timevale.docmanager.service.input.DocListResult;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.GetAuthRelationShareConfigOutput;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.bill.BillClient;
import com.timevale.saasbiz.integration.clmc.ClmcClient;
import com.timevale.saasbiz.integration.datarefresh.RpcJobProgressServiceClient;
import com.timevale.saasbiz.integration.ledger.RpcLedgerInnerClient;
import com.timevale.saasbiz.integration.process.DocServiceClient;
import com.timevale.saasbiz.integration.process.ProcessClient;
import com.timevale.saasbiz.integration.saascommon.AuthRelationClient;
import com.timevale.saasbiz.model.bean.common.dto.TaskProgressDTO;
import com.timevale.saasbiz.model.bean.ledger.bo.LedgerFieldStructMappingBO;
import com.timevale.saasbiz.model.bean.ledger.bo.LedgerFileStructMappingBO;
import com.timevale.saasbiz.model.bean.ledger.bo.LedgerStructDetailBO;
import com.timevale.saasbiz.model.bean.ledger.bo.LedgerTemplateBO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerFormSaveInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerFormUpdateAiFieldInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.input.LedgerFormUpdateInputDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerAiPageBalanceResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerExecLastTrialDoneResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerExecTrialProcessFileResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerLastRunInfoResultDTO;
import com.timevale.saasbiz.model.bean.ledger.dto.output.LedgerTaskProgressResultDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.constants.BizConstants;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.mq.producer.LedgerHistoryExtractProducer;
import com.timevale.saasbiz.service.common.TaskProgressService;
import com.timevale.saasbiz.service.ledger.impl.LedgerFormServiceImpl;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
@Slf4j
@JsonDataIgnore
public class LedgerFormServiceMockTest extends BaseMockTest {

    @Mock
    private RpcLedgerInnerClient rpcLedgerInnerClient;

    @Mock
    private UserCenterService userCenterService;

    @Mock
    private ClmcClient clmcClient;

    @Mock
    private MapperFactory mapperFactory;

    @Mock
    private AuthRelationClient authRelationClient;

    @Mock
    private BillClient billClient;

    @Mock
    private ProcessClient processClient;

    @Mock
    private LedgerHistoryExtractProducer ledgerHistoryExtractProducer;

    @Mock
    private RpcJobProgressServiceClient rpcJobProgressServiceClient;

    @Mock
    private TaskProgressService taskProgressService;

    @Mock
    private DocServiceClient docServiceClient;

    @InjectMocks
    private LedgerFormServiceImpl ledgerFormServiceImpl;

    public void init() {
        MapperFactory factory = new DefaultMapperFactory.Builder().build();
        when(mapperFactory.getMapperFacade()).thenReturn(factory.getMapperFacade());
    }

    @Test
    public void rerun() {
        // given
        String formId = "formId";
        String tenantId = "tenantId";
        String accountId = "accountId";
        Boolean removeHistory = true;
        String runModel = "runModel";

        LedgerFormDetailResult resultDTO = new LedgerFormDetailResult();
        resultDTO.setExtractType(LedgerExtractTypeEnum.CATEGORY.getType());
        LedgerExecDetailOutputDTO execDetail = new LedgerExecDetailOutputDTO();
        execDetail.setRunStatus(LedgerExecRunStatusEnum.RUNNING.getCode());
        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("tenantGid");

        init();
        when(rpcLedgerInnerClient.detail(formId, tenantId)).thenReturn(resultDTO);
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(clmcClient.queryAIBalanceEnough(anyString(), anyInt())).thenReturn(true);

        // when
        ledgerFormServiceImpl.rerun(formId, tenantId, accountId, removeHistory, runModel);

        // then
        verify(rpcLedgerInnerClient, atLeast(1)).detail(formId, tenantId);
        verify(userCenterService, atLeast(1)).queryAccountInfoByOid(tenantId);
        verify(clmcClient, atLeast(1)).queryAIBalanceEnough(anyString(), anyInt());

        // running
        when(rpcLedgerInnerClient.queryExecDetail(formId)).thenReturn(execDetail);
        try{
            ledgerFormServiceImpl.rerun(formId, tenantId, accountId, removeHistory, runModel);
        }catch (SaasBizException e){
            Assert.assertEquals(SaasBizResultCode.LEDGER_RUNNING.getCode(), e.getErrCode());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testContinueRun() {
        String formId = "formId";
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";
        String accountId = "accountId";

        LedgerFormDetailResult resultDTO = new LedgerFormDetailResult();
        resultDTO.setExtractType(LedgerExtractTypeEnum.CATALOG.getType());
        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);


        init();
        when(rpcLedgerInnerClient.detail(formId, tenantId)).thenReturn(resultDTO);
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(clmcClient.queryAIBalanceEnough(anyString(), anyInt())).thenReturn(true);
        when(rpcLedgerInnerClient.queryExecDetail(formId)).thenReturn(null);

        try{
            ledgerFormServiceImpl.continueRun(formId, tenantId, accountId, false);
        }catch (SaasBizException e){
            Assert.assertEquals(SaasBizResultCode.LEDGER_NOT_PAUSE.getCode(), e.getErrCode());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testSaveForm() {
        String formId = "formId";
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";
        String accountId = "accountId";
        List<String> conditionIds = Lists.newArrayList("1", "2", "3");

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);

        init();
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(rpcLedgerInnerClient.saveForm(any())).thenReturn(formId);
        when(clmcClient.queryAIBalanceEnough(anyString(), anyInt())).thenReturn(true);

        LedgerFormSaveInputDTO inputDTO = new LedgerFormSaveInputDTO();
        inputDTO.setExtractType(LedgerExtractTypeEnum.CATEGORY.getType());
        inputDTO.setConditionIds(conditionIds);
        inputDTO.setOpenLedger(true);
        inputDTO.setFieldIds(Lists.newArrayList("1", "2", "3"));
        ledgerFormServiceImpl.saveForm(inputDTO, tenantId, accountId);

        verify(userCenterService, atLeast(1)).queryAccountInfoByOid(tenantId);

        when(clmcClient.queryAIBalanceEnough(anyString(), anyInt())).thenReturn(false);
        try{
            ledgerFormServiceImpl.saveForm(inputDTO, tenantId, accountId);
        }catch (SaasBizException e){
            Assert.assertEquals(SaasBizResultCode.BALANCE_NOT_ENOUGH.getCode(), e.getErrCode());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testSaveFormWithTemplate() {
        String formId = "formId";
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";
        String accountId = "accountId";
        String fieldId = "fieldId";
        String fieldName = "fieldName";
        Integer fieldType = LedgerFieldTypeEnum.SINGLE_LINE_TEXT.getType();

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);
        LedgerFieldDTO ledgerFieldDTO = new LedgerFieldDTO();
        ledgerFieldDTO.setFieldId(fieldId);
        ledgerFieldDTO.setFieldType(fieldType);
        ledgerFieldDTO.setFieldName(fieldName);
        List<LedgerFieldDTO> ledgerFieldDTOS = Lists.newArrayList(ledgerFieldDTO);


        init();
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(rpcLedgerInnerClient.saveForm(any())).thenReturn(formId);
        when(rpcLedgerInnerClient.batchSaveField(any())).thenReturn(ledgerFieldDTOS);

        LedgerFormSaveInputDTO inputDTO = new LedgerFormSaveInputDTO();
        inputDTO.setExtractType(LedgerExtractTypeEnum.TEMPLATE.getType());
        inputDTO.setOpenLedger(true);
        LedgerTemplateBO ledgerTemplateBO = new LedgerTemplateBO();

        List<LedgerTemplateBO> ledgerTemplateMapping = Lists.newArrayList(ledgerTemplateBO);
        LedgerFileStructMappingBO fileStructMappingBO = new LedgerFileStructMappingBO();
        LedgerFieldStructMappingBO fieldStructMappingBO = new LedgerFieldStructMappingBO();
        fieldStructMappingBO.setFieldName(fieldName);
        LedgerStructDetailBO ledgerStructDetailBO = new LedgerStructDetailBO();
        ledgerStructDetailBO.setTemplateStructType(fieldType);
        fieldStructMappingBO.setStruct(ledgerStructDetailBO);
        List<LedgerFieldStructMappingBO> fieldStructMapping = Lists.newArrayList(fieldStructMappingBO);
        fileStructMappingBO.setFieldStructMapping(fieldStructMapping);
        List<LedgerFileStructMappingBO> fileStructMappingList = Lists.newArrayList(fileStructMappingBO);
        ledgerTemplateBO.setFileStructMappingList(fileStructMappingList);
        inputDTO.setLedgerTemplateMapping(ledgerTemplateMapping);

        String aformId = ledgerFormServiceImpl.saveForm(inputDTO, tenantId, accountId);
        Assert.assertNotNull(aformId);
        Assert.assertEquals(formId, aformId);
    }

    @Test
    public void testUpdateForm() {
        String formId = "formId";
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";
        String accountId = "accountId";
        List<String> conditionIds = Lists.newArrayList("1", "2", "3");

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);

        init();
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(rpcLedgerInnerClient.saveForm(any())).thenReturn(formId);
        when(clmcClient.queryAIBalanceEnough(anyString(), anyInt())).thenReturn(true);

        LedgerFormUpdateInputDTO inputDTO = new LedgerFormUpdateInputDTO();
        inputDTO.setExtractType(LedgerExtractTypeEnum.CATEGORY.getType());
        inputDTO.setConditionIds(conditionIds);
        inputDTO.setOpenLedger(true);
        inputDTO.setFieldIds(Lists.newArrayList("1", "2", "3"));
        ledgerFormServiceImpl.updateForm(inputDTO, tenantId, accountId);

        verify(userCenterService, atLeast(1)).queryAccountInfoByOid(tenantId);

        when(clmcClient.queryAIBalanceEnough(anyString(), anyInt())).thenReturn(false);
        try{
            ledgerFormServiceImpl.updateForm(inputDTO, tenantId, accountId);
        }catch (SaasBizException e){
            Assert.assertEquals(SaasBizResultCode.BALANCE_NOT_ENOUGH.getCode(), e.getErrCode());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testUpdateFormAiField() {
        String formId = "formId";
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";
        String accountId = "accountId";

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);

        init();
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(rpcLedgerInnerClient.saveForm(any())).thenReturn(formId);
        when(clmcClient.queryAIBalanceEnough(anyString(), anyInt())).thenReturn(true);

        LedgerFormUpdateAiFieldInputDTO inputDTO = new LedgerFormUpdateAiFieldInputDTO();
        inputDTO.setOpenLedger(true);
        inputDTO.setFieldIds(Lists.newArrayList("1", "2", "3"));
        ledgerFormServiceImpl.updateFormAiField(inputDTO, tenantId, accountId);

        verify(userCenterService, atLeast(1)).queryAccountInfoByOid(tenantId);

        when(clmcClient.queryAIBalanceEnough(anyString(), anyInt())).thenReturn(false);
        try{
            ledgerFormServiceImpl.updateFormAiField(inputDTO, tenantId, accountId);
        }catch (SaasBizException e){
            Assert.assertEquals(SaasBizResultCode.BALANCE_NOT_ENOUGH.getCode(), e.getErrCode());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testStopForm() {
        String formId = "formId";
        String tenantId = "tenantId";
        String formName = "formName";
        String accountId = "accountId";

        LedgerFormDetailResult detailResult = new LedgerFormDetailResult();
        detailResult.setFormName(formName);
        LedgerExecDetailOutputDTO outputDTO = new LedgerExecDetailOutputDTO();
        outputDTO.setFormId(formId);
        outputDTO.setRunStatus(LedgerExecRunStatusEnum.RUNNING.getCode());

        init();
        when(rpcLedgerInnerClient.detail(formId, tenantId)).thenReturn(detailResult);
        when(rpcLedgerInnerClient.queryExecDetail(formId)).thenReturn(outputDTO);
        when(rpcJobProgressServiceClient.stopJob(formId)).thenReturn(false);

        ledgerFormServiceImpl.stopForm(formId, tenantId, accountId);
        verify(rpcLedgerInnerClient, atLeast(1)).detail(formId, tenantId);
        verify(rpcLedgerInnerClient, atLeast(1)).updateExecResult(formId, tenantId, accountId, LedgerExecResultStatusEnum.FAIL);

        outputDTO.setRunStatus(LedgerExecRunStatusEnum.DONE.getCode());
        try{
            ledgerFormServiceImpl.stopForm(formId, tenantId, accountId);
        } catch (SaasBizException e){
            Assert.assertEquals(SaasBizResultCode.SAAS_ILLEGAL_PARAM.getCode(), e.getErrCode());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testQueryExecLastTrialDone() {
        //mock
        String formId = "formId";
        String formName = "formName";
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);
        LedgerExecLastTrialDoneOutputDTO doneOutputDTO = new LedgerExecLastTrialDoneOutputDTO();
        doneOutputDTO.setFormId(formId);
        doneOutputDTO.setFormName(formName);

        init();
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(rpcLedgerInnerClient.queryExecLastTrialDone(tenantGid)).thenReturn(doneOutputDTO);
        LedgerExecLastTrialDoneResultDTO resultDTO = ledgerFormServiceImpl.queryExecLastTrialDone(tenantId);
        Assert.assertEquals(resultDTO.getFormId(), formId);
        Assert.assertEquals(resultDTO.getFormName(), formName);
    }

    @Test
    public void testAiPageBalance() {
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";
        String shareTenantId = "shareTenantId";
        String shareTenantGid = "shareTenantGid";
        String shareTenantName = "shareTenantName";
        Long balance = 100L;

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);
        AccountInfoDTO shareAccountInfoDTO = new AccountInfoDTO();
        shareAccountInfoDTO.setGid(shareTenantGid);
        shareAccountInfoDTO.setName(shareTenantName);
        GetAuthRelationShareConfigOutput configOutput = new GetAuthRelationShareConfigOutput();
        configOutput.setEffective(true);
        configOutput.setParentTenantOid(shareTenantId);
        configOutput.setParentTenantGid(shareTenantGid);


        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(userCenterService.queryAccountInfoByOid(shareTenantId)).thenReturn(shareAccountInfoDTO);
        when(authRelationClient.getAuthRelationShareConfig(tenantGid, BizConstants.RELATION_SHARE_KEY_SIGN)).thenReturn(configOutput);
        when(billClient.searchBalance(shareTenantGid, 1783L)).thenReturn(balance);

        LedgerAiPageBalanceResultDTO resultDTO = ledgerFormServiceImpl.aiPageBalance(tenantId);
        Assert.assertEquals(resultDTO.getCount(), balance);
        Assert.assertEquals(resultDTO.getShowCount(), "100页");
        Assert.assertNotNull(resultDTO.getEstimateList());

        when(billClient.searchBalance(shareTenantGid, 1783L)).thenReturn(10001L);
        resultDTO = ledgerFormServiceImpl.aiPageBalance(tenantId);
        Assert.assertEquals(resultDTO.getCount().longValue(), 10001L);
        Assert.assertEquals(resultDTO.getShowCount(), "1万页");

        when(billClient.searchBalance(shareTenantGid, 1783L)).thenReturn(1_0000_0003L);
        resultDTO = ledgerFormServiceImpl.aiPageBalance(tenantId);
        Assert.assertEquals(resultDTO.getCount().longValue(), 1_0000_0003L);
        Assert.assertEquals(resultDTO.getShowCount(), "1亿页");
    }

    @Test
    public void testQueryLastRunInfo() {
        String formId = "formId";
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";
        Date subStartTime = new Date();
        Integer subExtractNum = 3;
        Integer extractNum = 100;
        Date startTime = new Date();

        LedgerExecDetailOutputDTO outputDTO = new LedgerExecDetailOutputDTO();
        outputDTO.setRunModel(LedgerExecRunModelEnum.WAIT_SUB.getCode());
        outputDTO.setSubExtractNum(subExtractNum);
        outputDTO.setSubStartTime(subStartTime);
        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);

        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(rpcLedgerInnerClient.queryExecDetail(formId)).thenReturn(null);
        when(rpcLedgerInnerClient.queryFormExists(formId, tenantGid)).thenReturn(true);
        LedgerLastRunInfoResultDTO resultDTO = ledgerFormServiceImpl.queryLastRunInfo(formId, tenantId);
        Assert.assertNull(resultDTO.getStartTime());
        Assert.assertNull(resultDTO.getExtractNumber());

        when(rpcLedgerInnerClient.queryExecDetail(formId)).thenReturn(outputDTO);
        resultDTO = ledgerFormServiceImpl.queryLastRunInfo(formId, tenantId);
        Assert.assertNotNull(resultDTO);
        Assert.assertEquals(resultDTO.getStartTime().longValue(), subStartTime.getTime());
        Assert.assertEquals(resultDTO.getExtractNumber().longValue(), new Long(subExtractNum).longValue());

        outputDTO.setRunModel(LedgerExecRunModelEnum.ALL.getCode());
        outputDTO.setExtractNum(extractNum);
        outputDTO.setStartTime(startTime);
        when(rpcLedgerInnerClient.queryExecDetail(formId)).thenReturn(outputDTO);
        resultDTO = ledgerFormServiceImpl.queryLastRunInfo(formId, tenantId);
        Assert.assertNotNull(resultDTO);
        Assert.assertEquals(resultDTO.getStartTime().longValue(), startTime.getTime());
        Assert.assertEquals(resultDTO.getExtractNumber().longValue(), new Long(extractNum).longValue());

        when(rpcLedgerInnerClient.queryFormExists(formId, tenantGid)).thenReturn(false);
        try {
            ledgerFormServiceImpl.queryLastRunInfo(formId, tenantId);
        }catch (SaasBizException e) {
            Assert.assertEquals(e.getErrCode(), SaasBizResultCode.LEDGER_NOT_EXIST.getCode());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testQueryExecTrialProcessFile(){
        String formId = "formId";
        String tenantId = "tenantId";
        String formName = "formName";
        String processId = "processId";
        String fileId = "fileId";
        String fileName = "fileName";
        String fileKey = "fileKey";
        List<String> limitProcessIds = Lists.newArrayList(processId);
        String processTitle = "processTitle";
        ContractProcessDTO processDTO = new ContractProcessDTO();
        processDTO.setTitle(processTitle);
        processDTO.setProcessId(processId);

        LedgerFormDetailResult detailResult = new LedgerFormDetailResult();
        detailResult.setFormName(formName);
        LedgerExecDetailOutputDTO execDetailOutputDTO = new LedgerExecDetailOutputDTO();
        execDetailOutputDTO.setLimitProcessIds(limitProcessIds);
        List<ContractProcessDTO> processDTOS = Lists.newArrayList(processDTO);
        QueryProcessFilesResult.ProcessFile processFile = new QueryProcessFilesResult.ProcessFile();
        processFile.setFileId(fileId);
        processFile.setFileKey(fileKey);
        processFile.setFlowType(FlowTypeEnum.SIGN_FLOW.getType());
        List<QueryProcessFilesResult.ProcessFile> processFiles = Lists.newArrayList(processFile);
        DocListResult docListResult = new DocListResult();
        docListResult.setDocId(fileId);
        docListResult.setFileName(fileName);
        List<DocListResult> docLists = Lists.newArrayList(docListResult);

        when(rpcLedgerInnerClient.detail(formId, tenantId)).thenReturn(detailResult);
        when(rpcLedgerInnerClient.queryExecDetail(formId)).thenReturn(execDetailOutputDTO);
        when(processClient.listByProcessIds(limitProcessIds)).thenReturn(processDTOS);
        when(processClient.getProcessFiles(processId)).thenReturn(processFiles);
        when(docServiceClient.queryInternalDocList(Lists.newArrayList(fileId))).thenReturn(docLists);

        LedgerExecTrialProcessFileResultDTO resultDTO = ledgerFormServiceImpl.queryExecTrialProcessFile(formId, tenantId);
        Assert.assertEquals(resultDTO.getLedgerName(), formName);
        Assert.assertEquals(resultDTO.getList().get(0).getProcessId(), processId);
        Assert.assertEquals(resultDTO.getList().get(0).getProcessName(), processTitle);
        Assert.assertEquals(resultDTO.getList().get(0).getFiles().get(0).getFileId(), fileId);

        processFile.setFlowType(FlowTypeEnum.CONTRACT_APPROVAL_FLOW.getType());
        execDetailOutputDTO.setRunModel(LedgerExecRunModelEnum.WAIT_SUB.getCode());
        execDetailOutputDTO.setSubDoneProcessIds(limitProcessIds);
        resultDTO = ledgerFormServiceImpl.queryExecTrialProcessFile(formId, tenantId);
        Assert.assertEquals(resultDTO.getLedgerName(), formName);
        Assert.assertEquals(resultDTO.getList().get(0).getProcessId(), processId);
        Assert.assertEquals(resultDTO.getList().get(0).getProcessName(), processTitle);
        Assert.assertEquals(resultDTO.getList().get(0).getFiles().get(0).getFileId(), fileId);
    }

    @Test
    public void testQueryTaskProgress() {
        String formId = "formId";
        String tenantId = "tenantId";
        long completed = 50L;
        long total = 100L;

        LedgerExecDetailOutputDTO execDetailOutputDTO = new LedgerExecDetailOutputDTO();
        execDetailOutputDTO.setRunStatus(LedgerExecRunStatusEnum.DONE.getCode());
        TaskProgressDTO taskProgressDTO = new TaskProgressDTO();
        taskProgressDTO.setCompleted(completed);
        taskProgressDTO.setTotal(total);

        when(rpcLedgerInnerClient.queryExecDetail(formId)).thenReturn(execDetailOutputDTO);
        when(taskProgressService.getTaskProgress(formId, tenantId)).thenReturn(taskProgressDTO);

        LedgerTaskProgressResultDTO resultDTO = ledgerFormServiceImpl.queryTaskProgress(formId, tenantId);
        Assert.assertTrue(resultDTO.isSuspend());

        execDetailOutputDTO.setRunStatus(LedgerExecRunStatusEnum.RUNNING.getCode());
        resultDTO = ledgerFormServiceImpl.queryTaskProgress(formId, tenantId);
        Assert.assertFalse(resultDTO.isSuspend());
        Assert.assertEquals(resultDTO.getCompleted(), completed);
        Assert.assertEquals(resultDTO.getTotal(), total);
    }

    @Test
    public void testNextFormProcessId() {
        String formId = "formId";
        String tenantId = "tenantId";
        String processId = "processId";
        String nextProcessId = "nextProcessId";
        String tenantGid = "tenantGid";

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);

        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(rpcLedgerInnerClient.queryFormExists(formId, tenantGid)).thenReturn(true);
        when(rpcLedgerInnerClient.queryNextFormProcessId(formId, processId)).thenReturn(nextProcessId);
       
        String result = ledgerFormServiceImpl.nextFormProcessId(formId, processId, tenantId);
        Assert.assertEquals(result, nextProcessId);

        when(rpcLedgerInnerClient.queryFormExists(formId, tenantGid)).thenReturn(false);
        try {
            ledgerFormServiceImpl.nextFormProcessId(formId, processId, tenantId);
        }catch (SaasBizException e) {
            Assert.assertEquals(e.getErrCode(), SaasBizResultCode.LEDGER_NOT_EXIST.getCode());
            return;
        }
        Assert.fail();
    }

    @Test
    public void testMatchTemplateStruct() {
        String tenantId = "tenantId";
        String templateId = "templateId";

        List<String> templateIds = Lists.newArrayList(templateId);
        LedgerTemplateStructMatchResultDTO resultDTO = new LedgerTemplateStructMatchResultDTO();
        LedgerTemplateDTO ledgerTemplateDTO = new LedgerTemplateDTO();
        ledgerTemplateDTO.setTemplateId(templateId);
        resultDTO.setTemplates(Lists.newArrayList(ledgerTemplateDTO));

        when(rpcLedgerInnerClient.matchTemplateStruct(tenantId, templateIds)).thenReturn(resultDTO);

        LedgerTemplateStructMatchResultDTO matchTemplateStruct = ledgerFormServiceImpl.matchTemplateStruct(tenantId, templateIds);
        Assert.assertNotNull(matchTemplateStruct);
        Assert.assertEquals(matchTemplateStruct.getTemplates().get(0).getTemplateId(), templateId);
    }

    @Test
    public void testUpdateStatus(){
        String formId = "formId";
        String tenantId = "tenantId";
        String tenantGid = "tenantGid";
        String accountId = "accountId";

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid(tenantGid);

        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(rpcLedgerInnerClient.queryFormExists(formId, tenantGid)).thenReturn(true);

        ledgerFormServiceImpl.updateStatus(formId, tenantId, accountId, true);

        verify(rpcLedgerInnerClient, atLeast(1)).updateFormStatus(tenantId, accountId, formId, true);

        when(rpcLedgerInnerClient.queryFormExists(formId, tenantGid)).thenReturn(false);
        try {
            ledgerFormServiceImpl.updateStatus(formId, tenantId, accountId, true);
        }catch (SaasBizException e) {
            Assert.assertEquals(e.getErrCode(), SaasBizResultCode.LEDGER_NOT_EXIST.getCode());
            return;
        }
        Assert.fail();
    }
}
