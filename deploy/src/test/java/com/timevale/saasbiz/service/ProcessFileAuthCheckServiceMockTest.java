package com.timevale.saasbiz.service;

import com.google.common.collect.Lists;
import com.timevale.contractmanager.common.service.bean.ProcessConfigBean;
import com.timevale.contractmanager.common.service.bean.ProcessFileAuthSimpleBean;
import com.timevale.contractmanager.common.service.enums.ProcessAuthFilePermissionEnum;
import com.timevale.contractmanager.common.service.enums.ProcessSecretEnum;
import com.timevale.contractmanager.common.service.result.ProcessConfigResult;
import com.timevale.contractmanager.common.service.result.ProcessFileAuthResult;
import com.timevale.contractmanager.common.service.result.processpermission.CheckUserProcessViewableResult;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.process.ProcessClient;
import com.timevale.saasbiz.integration.process.impl.ProcessClientImpl;
import com.timevale.saasbiz.model.bean.authrelation.bo.FileAuthCheckResultBO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.service.process.impl.ProcessFileAuthCheckServiceImpl;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 *
 * <AUTHOR>
 * @since 2024-09-1
 */
@JsonDataIgnore
public class ProcessFileAuthCheckServiceMockTest extends BaseMockTest {

    @InjectMocks private ProcessFileAuthCheckServiceImpl processFileAuthCheckService;

    private ProcessClient processClient = Mockito.mock(ProcessClientImpl.class);

    @Test
    public void testCheckFileAuth() {
        init();

        AccountInfoDTO userAccount = new AccountInfoDTO();
        userAccount.setOid("test_oid");
        userAccount.setGid("test_gid");
        AccountInfoDTO tenantAccount = new AccountInfoDTO();
        tenantAccount.setOid("test_tenant_oid");
        tenantAccount.setGid("test_tenant_gid");

        //专属云
        mockDedicatedCloud(true);
        FileAuthCheckResultBO checkResultBO = processFileAuthCheckService.checkFileAuth("test_process_id", userAccount, tenantAccount, Lists.newArrayList("test_file_id"), "test_file_path");
        Assert.assertFalse(checkResultBO.isSuccess());
        Assert.assertEquals(checkResultBO.getException().getErrCode(), SaasBizResultCode.DEDICATED_CLOUD_NOT_SUPPORT_BIZ.getCode());
        //恢复
        mockDedicatedCloud(false);

        //无流程查看权限
        mockProcessViewable(false);
        checkResultBO = processFileAuthCheckService.checkFileAuth("test_process_id", userAccount, tenantAccount, Lists.newArrayList("test_file_id"), "test_file_path");
        Assert.assertFalse(checkResultBO.isSuccess());
        Assert.assertEquals(checkResultBO.getException().getErrCode(), SaasBizResultCode.CAN_NOT_CREATE_BIZ.getCode());
        //恢复
        mockProcessViewable(true);

        //文件查看权限
        ProcessFileAuthResult authResult = new ProcessFileAuthResult();
        authResult.setVisibleType(ProcessSecretEnum.ALL_SECRET.getType());
        Mockito.when(processClient.queryProcessFileAuth(Mockito.any())).thenReturn(authResult);
        checkResultBO = processFileAuthCheckService.checkFileAuth("test_process_id", userAccount, tenantAccount, Lists.newArrayList(), "test_file_path");
        Assert.assertFalse(checkResultBO.isSuccess());
        Assert.assertEquals(checkResultBO.getException().getErrCode(), SaasBizResultCode.CAN_NOT_CREATE_BIZ.getCode());

        authResult.setVisibleType(ProcessSecretEnum.NONE_SECRET.getType());
        Mockito.when(processClient.queryProcessFileAuth(Mockito.any())).thenReturn(authResult);
        checkResultBO = processFileAuthCheckService.checkFileAuth("test_process_id", userAccount, tenantAccount, Lists.newArrayList(), "test_file_path");
        Assert.assertTrue(checkResultBO.isSuccess());

        ProcessFileAuthSimpleBean fileAuthSimpleBean = new ProcessFileAuthSimpleBean();
        fileAuthSimpleBean.setFileId("test_file_id");
        authResult.setFiles(Lists.newArrayList(fileAuthSimpleBean));
        Mockito.when(processClient.queryProcessFileAuth(Mockito.any())).thenReturn(authResult);
        checkResultBO = processFileAuthCheckService.checkFileAuth("test_process_id", userAccount, tenantAccount, Lists.newArrayList("test_file_id"), "test_file_path");
        Assert.assertTrue(checkResultBO.isSuccess());

        authResult.setVisibleType(ProcessSecretEnum.ALL_SECRET.getType());
        Mockito.when(processClient.queryProcessFileAuth(Mockito.any())).thenReturn(authResult);
        checkResultBO = processFileAuthCheckService.checkFileAuth("test_process_id", userAccount, tenantAccount, Lists.newArrayList("test_file_id"), "test_file_path");
        Assert.assertFalse(checkResultBO.isSuccess());
        Assert.assertEquals(checkResultBO.getException().getErrCode(), SaasBizResultCode.CAN_NOT_CREATE_BIZ.getCode());

        authResult.setVisibleType(ProcessSecretEnum.PART_SECRET.getType());
        fileAuthSimpleBean.setPermissions(Lists.newArrayList());
        Mockito.when(processClient.queryProcessFileAuth(Mockito.any())).thenReturn(authResult);
        checkResultBO = processFileAuthCheckService.checkFileAuth("test_process_id", userAccount, tenantAccount, Lists.newArrayList("test_file_id"), "test_file_path");
        Assert.assertFalse(checkResultBO.isSuccess());
        Assert.assertEquals(checkResultBO.getException().getErrCode(), SaasBizResultCode.CAN_NOT_CREATE_BIZ.getCode());

        fileAuthSimpleBean.setPermissions(Lists.newArrayList(ProcessAuthFilePermissionEnum.QUERY
                .getType()));
        Mockito.when(processClient.queryProcessFileAuth(Mockito.any())).thenReturn(authResult);
        checkResultBO = processFileAuthCheckService.checkFileAuth("test_process_id", userAccount, tenantAccount, Lists.newArrayList("test_file_id"), "test_file_path");
        Assert.assertTrue(checkResultBO.isSuccess());
    }

    private void init() {
        Whitebox.setInternalState(processFileAuthCheckService, "processClient", processClient);
    }

    private void mockProcessViewable(boolean isViewable) {
        CheckUserProcessViewableResult viewableResult = new CheckUserProcessViewableResult();
        viewableResult.setViewable(isViewable);
        Mockito.when(processClient.checkUserProcessViewable("test_process_id", "test_tenant_oid", "test_oid", null)).thenReturn(viewableResult);
    }

    private void mockDedicatedCloud(boolean isDedicated) {
        ProcessConfigResult configResult = new ProcessConfigResult();
        ProcessConfigBean configBean = new ProcessConfigBean();
        configResult.setConfigInfo(configBean);
        if (isDedicated) {
            configBean.setDedicatedCloudId("123456");
        }
        Mockito.when(processClient.queryProcessConfig(Mockito.anyString())).thenReturn(configResult);
    }
}
