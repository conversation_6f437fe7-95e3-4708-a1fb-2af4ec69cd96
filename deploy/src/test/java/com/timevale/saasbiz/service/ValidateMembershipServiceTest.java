package com.timevale.saasbiz.service;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.service.vip.verify.ValidateMembershipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import static com.timevale.saasbiz.constant.TestAccountConstants.PROFESSIONAL_SUBJECT_1;

@JsonDataIgnore
public class ValidateMembershipServiceTest  extends BaseServiceTest {

    @Autowired
    private ValidateMembershipService validateMembershipService;

    @Test(description = "测试校验")
    public void testValidate() {
        validateMembershipService.validate("hello, world", PROFESSIONAL_SUBJECT_1, FunctionCodeConstants.ORG_SEAL_GRANT, this::notBlank);
    }

    private boolean notBlank(String s) {
        return StringUtils.isNotBlank(s);
    }
}
