package com.timevale.saasbiz.service;

import com.timevale.saasbiz.base.BasePowerMockTest;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import static org.testng.Assert.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import com.timevale.saasbiz.integration.filesystem.FileSystemClient;
import com.timevale.saasbiz.integration.process.ProcessClient;
import com.timevale.saasbiz.integration.process.ProcessRescindClient;
import com.timevale.saasbiz.integration.contractanalysis.ProcessAiInfoClient;
import com.timevale.saasbiz.integration.approval.ProcessApprovalClient;
import com.timevale.saasbiz.integration.doc.DocClient;
import com.timevale.saasbiz.service.ai.agent.tool.impl.AiAgentToolServiceImpl;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessFileInfoOutput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.bo.ProcessFileInfoBO;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.input.ProcessRescindUrlInput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessRescindUrlOutput;
import com.timevale.contractmanager.common.service.model.rescind.ProcessRescindCheckModel;
import com.timevale.contractmanager.common.service.model.rescind.ProcessRescindCheckResult;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessDTO;
import com.timevale.signflow.search.service.model.contractprocess.ContractProcessSignTaskDTO;
import com.timevale.contractanalysis.facade.api.dto.process.QueryProcessAiInfoResultDTO;
import com.timevale.contractanalysis.facade.api.dto.process.ProcessAiContextDTO;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessAiInfoOutput;
import com.timevale.saasbiz.model.bean.ai.agent.tool.bo.FileAiInfoBO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.signflow.search.docSearchService.bean.Account;
import com.timevale.signflow.search.docSearchService.bean.ProcessAccount;
import com.timevale.signflow.search.docSearchService.enums.TaskStatusEnum;
import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessRefuseOutput;
import com.timevale.saasbiz.service.ai.agent.AiAgentService;
import com.timevale.saasbiz.model.bean.ai.agent.dto.output.ReadFilesByProcessIdOutputDTO;
import com.timevale.saasbiz.model.bean.ai.agent.tool.dto.output.ProcessRestartUrlOutput;

public class AiAgentToolServiceMockTest extends BasePowerMockTest {

    @InjectMocks
    private AiAgentToolServiceImpl aiAgentToolService;

    @Mock
    private ProcessClient processClient;
    @Mock
    private FileSystemClient fileSystemClient;
    @Mock
    private ProcessRescindClient processRescindClient;
    @Mock
    private ProcessAiInfoClient processAiInfoClient;
    @Mock
    private UserCenterService userCenterService;
    @Mock
    private ProcessApprovalClient processApprovalClient;
    @Mock
    private DocClient docClient;
    @Mock
    private AiAgentService aiAgentService;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }
    
    @Test
    public void testGetRescind_Success() {
        // 准备测试数据
        ProcessRescindUrlInput input = new ProcessRescindUrlInput();
        input.setProcessId("testProcessId");
        input.setOperatorId("testOperatorId");
        input.setTenantId("testTenantId");
        input.setMenuId("testMenuId");

        // 构造解约检查结果
        ProcessRescindCheckResult checkResult = new ProcessRescindCheckResult();
        checkResult.setCanRescind(true);

        // 构造合同流程信息
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        ContractProcessSignTaskDTO signTask = new ContractProcessSignTaskDTO();
        signTask.setFlowId("testFlowId");
        contractProcess.setSignTasks(Collections.singletonList(signTask));

        // Mock ProcessRescindClient的行为
        when(processRescindClient.checkProcessRescind(any(ProcessRescindCheckModel.class)))
            .thenReturn(checkResult);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(input.getProcessId()))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRescindUrlOutput output = aiAgentToolService.getRescind(input);

        // 验证结果
        assertNotNull(output);
        assertTrue(output.getCanRescind());
        assertNotNull(output.getRescindUrl());
        assertTrue(output.getRescindUrl().contains(input.getProcessId()));
        assertTrue(output.getRescindUrl().contains("testFlowId"));

        // 验证方法调用
        verify(processRescindClient).checkProcessRescind(any(ProcessRescindCheckModel.class));
        verify(processClient).getByProcessId(input.getProcessId());
    }

    @Test
    public void testGetRescind_NotAllowed() {
        // 准备测试数据
        ProcessRescindUrlInput input = new ProcessRescindUrlInput();
        input.setProcessId("testProcessId");
        input.setOperatorId("testOperatorId");
        input.setTenantId("testTenantId");
        input.setMenuId("testMenuId");

        // 构造解约检查结果
        ProcessRescindCheckResult checkResult = new ProcessRescindCheckResult();
        checkResult.setCanRescind(false);
        checkResult.setReason("不允许解约");

        // Mock ProcessRescindClient的行为
        when(processRescindClient.checkProcessRescind(any(ProcessRescindCheckModel.class)))
            .thenReturn(checkResult);

        // 执行测试
        ProcessRescindUrlOutput output = aiAgentToolService.getRescind(input);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRescind());
        assertEquals(output.getMessage(), "不允许解约");
        assertNull(output.getRescindUrl());

        // 验证方法调用
        verify(processRescindClient).checkProcessRescind(any(ProcessRescindCheckModel.class));
        verify(processClient, never()).getByProcessId(anyString());
    }

    @Test
    public void testGetRescind_ProcessNotFound() {
        // 准备测试数据
        ProcessRescindUrlInput input = new ProcessRescindUrlInput();
        input.setProcessId("testProcessId");
        input.setOperatorId("testOperatorId");
        input.setTenantId("testTenantId");
        input.setMenuId("testMenuId");

        // 构造解约检查结果
        ProcessRescindCheckResult checkResult = new ProcessRescindCheckResult();
        checkResult.setCanRescind(true);

        // Mock ProcessRescindClient的行为
        when(processRescindClient.checkProcessRescind(any(ProcessRescindCheckModel.class)))
            .thenReturn(checkResult);

        // Mock ProcessClient的行为 - 返回null
        when(processClient.getByProcessId(input.getProcessId()))
            .thenReturn(null);

        // 执行测试
        ProcessRescindUrlOutput output = aiAgentToolService.getRescind(input);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRescind());
        assertEquals(output.getMessage(), "合同不存在");
        assertNull(output.getRescindUrl());

        // 验证方法调用
        verify(processRescindClient).checkProcessRescind(any(ProcessRescindCheckModel.class));
        verify(processClient).getByProcessId(input.getProcessId());
    }

    @Test
    public void testGetRescind_NoSignTasks() {
        // 准备测试数据
        ProcessRescindUrlInput input = new ProcessRescindUrlInput();
        input.setProcessId("testProcessId");
        input.setOperatorId("testOperatorId");
        input.setTenantId("testTenantId");
        input.setMenuId("testMenuId");

        // 构造解约检查结果
        ProcessRescindCheckResult checkResult = new ProcessRescindCheckResult();
        checkResult.setCanRescind(true);

        // 构造合同流程信息 - 没有签署任务
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        contractProcess.setSignTasks(Collections.emptyList());

        // Mock ProcessRescindClient的行为
        when(processRescindClient.checkProcessRescind(any(ProcessRescindCheckModel.class)))
            .thenReturn(checkResult);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(input.getProcessId()))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRescindUrlOutput output = aiAgentToolService.getRescind(input);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRescind());
        assertEquals(output.getMessage(), "该合同不存在签署流程");
        assertNull(output.getRescindUrl());

        // 验证方法调用
        verify(processRescindClient).checkProcessRescind(any(ProcessRescindCheckModel.class));
        verify(processClient).getByProcessId(input.getProcessId());
    }

    @Test
    public void testGetAiInfo_Success() {
        // 准备测试数据
        String processId = "testProcessId";
        String fileId = "testFileId";
        String contractType = "劳动合同";
        List<String> signerNames = Arrays.asList("张三", "李四");

        // 构造 AI 信息结果
        QueryProcessAiInfoResultDTO result = new QueryProcessAiInfoResultDTO();
        QueryProcessAiInfoResultDTO.FileAiInfoDTO fileAiInfo = new QueryProcessAiInfoResultDTO.FileAiInfoDTO();
        fileAiInfo.setFileId(fileId);
        ProcessAiContextDTO context = new ProcessAiContextDTO();
        context.setContractType(contractType);
        List<ProcessAiContextDTO.SignerInfo> signerInfos = Arrays.asList(
            createSignerInfo("张三"),
            createSignerInfo("李四")
        );
        context.setSignerInfos(signerInfos);
        fileAiInfo.setContext(context);
        result.setFileAiInfoList(Collections.singletonList(fileAiInfo));

        // Mock ProcessAiInfoClient 的行为
        when(processAiInfoClient.getProcessAiInfo(processId))
            .thenReturn(result);

        // 执行测试
        ProcessAiInfoOutput output = aiAgentToolService.getAiInfo(processId);

        // 验证结果
        assertNotNull(output);
        assertNotNull(output.getFiles());
        assertEquals(output.getFiles().size(), 1);
        
        FileAiInfoBO fileInfo = output.getFiles().get(0);
        assertEquals(fileInfo.getFileId(), fileId);
        assertEquals(fileInfo.getContractType(), contractType);
        assertEquals(fileInfo.getSignerNames(), signerNames);

        // 验证方法调用
        verify(processAiInfoClient).getProcessAiInfo(processId);
    }

    @Test
    public void testGetAiInfo_EmptyResult() {
        // 准备测试数据
        String processId = "testProcessId";

        // 构造空的 AI 信息结果
        QueryProcessAiInfoResultDTO result = new QueryProcessAiInfoResultDTO();
        result.setFileAiInfoList(Collections.emptyList());

        // Mock ProcessAiInfoClient 的行为
        when(processAiInfoClient.getProcessAiInfo(processId))
            .thenReturn(result);

        // 执行测试
        ProcessAiInfoOutput output = aiAgentToolService.getAiInfo(processId);

        // 验证结果
        assertNotNull(output);
        assertNotNull(output.getFiles());
        assertTrue(output.getFiles().isEmpty());

        // 验证方法调用
        verify(processAiInfoClient).getProcessAiInfo(processId);
    }

    @Test
    public void testGetAiInfo_NoContext() {
        // 准备测试数据
        String processId = "testProcessId";
        String fileId = "testFileId";

        // 构造没有上下文的 AI 信息结果
        QueryProcessAiInfoResultDTO result = new QueryProcessAiInfoResultDTO();
        QueryProcessAiInfoResultDTO.FileAiInfoDTO fileAiInfo = new QueryProcessAiInfoResultDTO.FileAiInfoDTO();
        fileAiInfo.setFileId(fileId);
        fileAiInfo.setContext(null);
        result.setFileAiInfoList(Collections.singletonList(fileAiInfo));

        // Mock ProcessAiInfoClient 的行为
        when(processAiInfoClient.getProcessAiInfo(processId))
            .thenReturn(result);

        // 执行测试
        ProcessAiInfoOutput output = aiAgentToolService.getAiInfo(processId);

        // 验证结果
        assertNotNull(output);
        assertNotNull(output.getFiles());
        assertEquals(output.getFiles().size(), 1);
        
        FileAiInfoBO fileInfo = output.getFiles().get(0);
        assertEquals(fileInfo.getFileId(), fileId);
        assertNull(fileInfo.getContractType());
        assertNull(fileInfo.getSignerNames());

        // 验证方法调用
        verify(processAiInfoClient).getProcessAiInfo(processId);
    }

    @Test
    public void testGetAiInfo_NoSigners() {
        // 准备测试数据
        String processId = "testProcessId";
        String fileId = "testFileId";
        String contractType = "劳动合同";

        // 构造没有签署人的 AI 信息结果
        QueryProcessAiInfoResultDTO result = new QueryProcessAiInfoResultDTO();
        QueryProcessAiInfoResultDTO.FileAiInfoDTO fileAiInfo = new QueryProcessAiInfoResultDTO.FileAiInfoDTO();
        fileAiInfo.setFileId(fileId);
        ProcessAiContextDTO context = new ProcessAiContextDTO();
        context.setContractType(contractType);
        context.setSignerInfos(Collections.emptyList());
        fileAiInfo.setContext(context);
        result.setFileAiInfoList(Collections.singletonList(fileAiInfo));

        // Mock ProcessAiInfoClient 的行为
        when(processAiInfoClient.getProcessAiInfo(processId))
            .thenReturn(result);

        // 执行测试
        ProcessAiInfoOutput output = aiAgentToolService.getAiInfo(processId);

        // 验证结果
        assertNotNull(output);
        assertNotNull(output.getFiles());
        assertEquals(output.getFiles().size(), 1);
        
        FileAiInfoBO fileInfo = output.getFiles().get(0);
        assertEquals(fileInfo.getFileId(), fileId);
        assertEquals(fileInfo.getContractType(), contractType);
        assertNull(fileInfo.getSignerNames());

        // 验证方法调用
        verify(processAiInfoClient).getProcessAiInfo(processId);
    }

    @Test
    public void testRefuse_Success() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";
        String operatorGid = "testOperatorGid";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);
        operatorAccount.setGid(operatorGid);

        // 构造合同流程信息
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        contractProcess.setProcessStatus(ProcessStatusEnum.SIGN.getStatus());
        
        // 构造签署任务
        ContractProcessSignTaskDTO signTask = new ContractProcessSignTaskDTO();
        signTask.setStatus(TaskStatusEnum.SIGNING.getStatus());
        ProcessAccount execute = new ProcessAccount();
        Account person = new Account();
        person.setGid(operatorGid);
        execute.setPerson(person);
        signTask.setExecute(execute);
        contractProcess.setSignTasks(Collections.singletonList(signTask));

        // Mock UserCenterService的行为
        when(userCenterService.queryAccountDetailByOid(operatorId))
            .thenReturn(operatorAccount);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(processId))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRefuseOutput output = aiAgentToolService.refuse(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertTrue(output.getCanRefuse());
        assertNull(output.getMessage());

        // 验证方法调用
        verify(userCenterService).queryAccountDetailByOid(operatorId);
        verify(processClient).getByProcessId(processId);
    }

    @Test
    public void testRefuse_UserNotExist() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // Mock UserCenterService的行为 - 返回null表示用户不存在
        when(userCenterService.queryAccountDetailByOid(operatorId))
            .thenReturn(null);

        // 执行测试
        ProcessRefuseOutput output = aiAgentToolService.refuse(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRefuse());
        assertEquals(output.getMessage(), "用户账号不存在");

        // 验证方法调用
        verify(userCenterService).queryAccountDetailByOid(operatorId);
        verify(processClient, never()).getByProcessId(anyString());
    }

    @Test
    public void testRefuse_ProcessNotExist() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);

        // Mock UserCenterService的行为
        when(userCenterService.queryAccountDetailByOid(operatorId))
            .thenReturn(operatorAccount);

        // Mock ProcessClient的行为 - 返回null表示合同不存在
        when(processClient.getByProcessId(processId))
            .thenReturn(null);

        // 执行测试
        ProcessRefuseOutput output = aiAgentToolService.refuse(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRefuse());
        assertEquals(output.getMessage(), "合同不存在");

        // 验证方法调用
        verify(userCenterService).queryAccountDetailByOid(operatorId);
        verify(processClient).getByProcessId(processId);
    }

    @Test
    public void testRefuse_InvalidProcessStatus() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);

        // 构造合同流程信息 - 状态不是签署中
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        contractProcess.setProcessStatus(ProcessStatusEnum.DONE.getStatus());

        // Mock UserCenterService的行为
        when(userCenterService.queryAccountDetailByOid(operatorId))
            .thenReturn(operatorAccount);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(processId))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRefuseOutput output = aiAgentToolService.refuse(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRefuse());
        assertEquals(output.getMessage(), "只有签署中合同支持拒签");

        // 验证方法调用
        verify(userCenterService).queryAccountDetailByOid(operatorId);
        verify(processClient).getByProcessId(processId);
    }

    @Test
    public void testRefuse_NoSignTasks() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);

        // 构造合同流程信息 - 没有签署任务
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        contractProcess.setProcessStatus(ProcessStatusEnum.SIGN.getStatus());
        contractProcess.setSignTasks(Collections.emptyList());

        // Mock UserCenterService的行为
        when(userCenterService.queryAccountDetailByOid(operatorId))
            .thenReturn(operatorAccount);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(processId))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRefuseOutput output = aiAgentToolService.refuse(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRefuse());
        assertEquals(output.getMessage(), "该合同不存在签署流程");

        // 验证方法调用
        verify(userCenterService).queryAccountDetailByOid(operatorId);
        verify(processClient).getByProcessId(processId);
    }

    @Test
    public void testRefuse_NotCurrentSigner() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";
        String operatorGid = "testOperatorGid";
        String otherOperatorGid = "otherOperatorGid";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);
        operatorAccount.setGid(operatorGid);

        // 构造合同流程信息
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        contractProcess.setProcessStatus(ProcessStatusEnum.SIGN.getStatus());
        
        // 构造签署任务 - 签署人不是当前操作者
        ContractProcessSignTaskDTO signTask = new ContractProcessSignTaskDTO();
        signTask.setStatus(TaskStatusEnum.SIGNING.getStatus());
        ProcessAccount execute = new ProcessAccount();
        Account person = new Account();
        person.setGid(otherOperatorGid);
        execute.setPerson(person);
        signTask.setExecute(execute);
        contractProcess.setSignTasks(Collections.singletonList(signTask));

        // Mock UserCenterService的行为
        when(userCenterService.queryAccountDetailByOid(operatorId))
            .thenReturn(operatorAccount);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(processId))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRefuseOutput output = aiAgentToolService.refuse(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRefuse());
        assertEquals(output.getMessage(), "您不是当前节点的签署人，无法拒签");

        // 验证方法调用
        verify(userCenterService).queryAccountDetailByOid(operatorId);
        verify(processClient).getByProcessId(processId);
    }

    @Test
    public void testGetFileInfo_EmptyFileList() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // Mock AiAgentService的行为 - 返回空列表
        when(aiAgentService.getPermissionFiles(processId, 1, operatorId, tenantId))
            .thenReturn(Collections.emptyList());

        // 执行测试
        ProcessFileInfoOutput output = aiAgentToolService.getFileInfo(processId, 1, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertNotNull(output.getFileInfoList());
        assertTrue(output.getFileInfoList().isEmpty());

        // 验证方法调用
        verify(aiAgentService).getPermissionFiles(processId, 1, operatorId, tenantId);
    }

    @Test
    public void testGetFileInfo_OnlyAttachments() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";
        String fileId = "testFileId";
        String fileName = "test.pdf";

        // 构造文件信息 - 只包含附件
        ReadFilesByProcessIdOutputDTO.FileInfo fileInfo = new ReadFilesByProcessIdOutputDTO.FileInfo();
        fileInfo.setFileId(fileId);
        fileInfo.setFileName(fileName);
        fileInfo.setIsAttachment(true);

        // Mock AiAgentService的行为
        when(aiAgentService.getPermissionFiles(processId, 1, operatorId, tenantId))
            .thenReturn(Collections.singletonList(fileInfo));

        // 执行测试
        ProcessFileInfoOutput output = aiAgentToolService.getFileInfo(processId, 1, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertNotNull(output.getFileInfoList());
        assertTrue(output.getFileInfoList().isEmpty());

        // 验证方法调用
        verify(aiAgentService).getPermissionFiles(processId, 1, operatorId, tenantId);
    }

    @Test
    public void testGetFileInfo_MultipleFiles() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // 构造多个文件信息
        List<ReadFilesByProcessIdOutputDTO.FileInfo> fileInfos = Arrays.asList(
            createFileInfo("file1.pdf", "file1", false),
            createFileInfo("file2.docx", "file2", false),
            createFileInfo("file3.pdf", "file3", true)  // 附件
        );

        // Mock AiAgentService的行为
        when(aiAgentService.getPermissionFiles(processId, 1, operatorId, tenantId))
            .thenReturn(fileInfos);

        // 执行测试
        ProcessFileInfoOutput output = aiAgentToolService.getFileInfo(processId, 1, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertNotNull(output.getFileInfoList());
        assertEquals(output.getFileInfoList().size(), 2);  // 应该只有2个非附件文件

        // 验证第一个文件
        ProcessFileInfoBO file1 = output.getFileInfoList().get(0);
        assertEquals(file1.getFileId(), "file1");
        assertEquals(file1.getFileName(), "file1.pdf");
        assertEquals(file1.getFileType(), "pdf");

        // 验证第二个文件
        ProcessFileInfoBO file2 = output.getFileInfoList().get(1);
        assertEquals(file2.getFileId(), "file2");
        assertEquals(file2.getFileName(), "file2.docx");
        assertEquals(file2.getFileType(), "docx");

        // 验证方法调用
        verify(aiAgentService).getPermissionFiles(processId, 1, operatorId, tenantId);
    }

    private ReadFilesByProcessIdOutputDTO.FileInfo createFileInfo(String fileName, String fileId, boolean isAttachment) {
        ReadFilesByProcessIdOutputDTO.FileInfo fileInfo = new ReadFilesByProcessIdOutputDTO.FileInfo();
        fileInfo.setFileName(fileName);
        fileInfo.setFileId(fileId);
        fileInfo.setIsAttachment(isAttachment);
        return fileInfo;
    }

    private ProcessAiContextDTO.SignerInfo createSignerInfo(String name) {
        ProcessAiContextDTO.SignerInfo signerInfo = new ProcessAiContextDTO.SignerInfo();
        signerInfo.setName(name);
        return signerInfo;
    }

    @Test
    public void testGetRestart_Success() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";
        String operatorGid = "testOperatorGid";
        String tenantGid = "testTenantGid";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);
        operatorAccount.setGid(operatorGid);

        // 构造企业账号信息
        AccountDetailDTO tenantAccount = new AccountDetailDTO();
        tenantAccount.setOid(tenantId);
        tenantAccount.setGid(tenantGid);

        // 构造合同流程信息
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        ProcessAccount initiator = new ProcessAccount();
        Account person = new Account();
        person.setGid(operatorGid);
        initiator.setPerson(person);
        Account subject = new Account();
        subject.setGid(tenantGid);
        initiator.setSubject(subject);
        contractProcess.setInitiator(initiator);

        // Mock UserCenterService的行为
        Map<String, AccountDetailDTO> accountMap = new HashMap<>();
        accountMap.put(operatorId, operatorAccount);
        accountMap.put(tenantId, tenantAccount);
        when(userCenterService.batchQueryAccountMapByOid(anyList()))
            .thenReturn(accountMap);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(processId))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRestartUrlOutput output = aiAgentToolService.getRestart(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertTrue(output.getCanRestart());
        assertNotNull(output.getRestartUrl());
        assertTrue(output.getRestartUrl().contains(processId));
        assertNull(output.getMessage());

        // 验证方法调用
        verify(userCenterService).batchQueryAccountMapByOid(anyList());
        verify(processClient).getByProcessId(processId);
    }

    @Test
    public void testGetRestart_OperatorNotExist() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // Mock UserCenterService的行为 - 返回空Map
        when(userCenterService.batchQueryAccountMapByOid(anyList()))
            .thenReturn(Collections.emptyMap());

        // 执行测试
        ProcessRestartUrlOutput output = aiAgentToolService.getRestart(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRestart());
        assertEquals(output.getMessage(), "用户账号不存在");
        assertNull(output.getRestartUrl());

        // 验证方法调用
        verify(userCenterService).batchQueryAccountMapByOid(anyList());
        verify(processClient, never()).getByProcessId(anyString());
    }

    @Test
    public void testGetRestart_TenantNotExist() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);

        // Mock UserCenterService的行为 - 只返回操作者账号
        Map<String, AccountDetailDTO> accountMap = new HashMap<>();
        accountMap.put(operatorId, operatorAccount);
        when(userCenterService.batchQueryAccountMapByOid(anyList()))
            .thenReturn(accountMap);

        // 执行测试
        ProcessRestartUrlOutput output = aiAgentToolService.getRestart(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRestart());
        assertEquals(output.getMessage(), "企业账号不存在");
        assertNull(output.getRestartUrl());

        // 验证方法调用
        verify(userCenterService).batchQueryAccountMapByOid(anyList());
        verify(processClient, never()).getByProcessId(anyString());
    }

    @Test
    public void testGetRestart_ProcessNotExist() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);

        // 构造企业账号信息
        AccountDetailDTO tenantAccount = new AccountDetailDTO();
        tenantAccount.setOid(tenantId);

        // Mock UserCenterService的行为
        Map<String, AccountDetailDTO> accountMap = new HashMap<>();
        accountMap.put(operatorId, operatorAccount);
        accountMap.put(tenantId, tenantAccount);
        when(userCenterService.batchQueryAccountMapByOid(anyList()))
            .thenReturn(accountMap);

        // Mock ProcessClient的行为 - 返回null
        when(processClient.getByProcessId(processId))
            .thenReturn(null);

        // 执行测试
        ProcessRestartUrlOutput output = aiAgentToolService.getRestart(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRestart());
        assertEquals(output.getMessage(), "合同不存在");
        assertNull(output.getRestartUrl());

        // 验证方法调用
        verify(userCenterService).batchQueryAccountMapByOid(anyList());
        verify(processClient).getByProcessId(processId);
    }

    @Test
    public void testGetRestart_NoInitiator() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);

        // 构造企业账号信息
        AccountDetailDTO tenantAccount = new AccountDetailDTO();
        tenantAccount.setOid(tenantId);

        // 构造合同流程信息 - 没有发起方
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        contractProcess.setInitiator(null);

        // Mock UserCenterService的行为
        Map<String, AccountDetailDTO> accountMap = new HashMap<>();
        accountMap.put(operatorId, operatorAccount);
        accountMap.put(tenantId, tenantAccount);
        when(userCenterService.batchQueryAccountMapByOid(anyList()))
            .thenReturn(accountMap);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(processId))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRestartUrlOutput output = aiAgentToolService.getRestart(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRestart());
        assertEquals(output.getMessage(), "非当前合同的发起方，无法重新发起");
        assertNull(output.getRestartUrl());

        // 验证方法调用
        verify(userCenterService).batchQueryAccountMapByOid(anyList());
        verify(processClient).getByProcessId(processId);
    }

    @Test
    public void testGetRestart_NotInitiator() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";
        String operatorGid = "testOperatorGid";
        String tenantGid = "testTenantGid";
        String otherOperatorGid = "otherOperatorGid";

        // 构造操作者账号信息
        AccountDetailDTO operatorAccount = new AccountDetailDTO();
        operatorAccount.setOid(operatorId);
        operatorAccount.setGid(operatorGid);

        // 构造企业账号信息
        AccountDetailDTO tenantAccount = new AccountDetailDTO();
        tenantAccount.setOid(tenantId);
        tenantAccount.setGid(tenantGid);

        // 构造合同流程信息 - 发起方不是当前操作者
        ContractProcessDTO contractProcess = new ContractProcessDTO();
        ProcessAccount initiator = new ProcessAccount();
        Account person = new Account();
        person.setGid(otherOperatorGid);
        initiator.setPerson(person);
        Account subject = new Account();
        subject.setGid(tenantGid);
        initiator.setSubject(subject);
        contractProcess.setInitiator(initiator);

        // Mock UserCenterService的行为
        Map<String, AccountDetailDTO> accountMap = new HashMap<>();
        accountMap.put(operatorId, operatorAccount);
        accountMap.put(tenantId, tenantAccount);
        when(userCenterService.batchQueryAccountMapByOid(anyList()))
            .thenReturn(accountMap);

        // Mock ProcessClient的行为
        when(processClient.getByProcessId(processId))
            .thenReturn(contractProcess);

        // 执行测试
        ProcessRestartUrlOutput output = aiAgentToolService.getRestart(processId, operatorId, tenantId);

        // 验证结果
        assertNotNull(output);
        assertFalse(output.getCanRestart());
        assertEquals(output.getMessage(), "非当前合同的发起方，无法重新发起");
        assertNull(output.getRestartUrl());

        // 验证方法调用
        verify(userCenterService).batchQueryAccountMapByOid(anyList());
        verify(processClient).getByProcessId(processId);
    }
} 