package com.timevale.saasbiz.service;

import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.service.approval.ApprovalAdapter;
import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalAsyncOperateProcessMsg;
import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalOperateAsyncExecutor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2023/5/6 11:56
 */
@JsonDataIgnore
public class ApprovalOperateAsyncExecutorMockTest extends BaseMockTest {

    @InjectMocks
    private ApprovalOperateAsyncExecutor executor;
    @Mock
    private ApprovalAdapter approvalAdapter;


    @Test
    public void processOneTest() {

        Mockito.when(approvalAdapter.approvalAdaptDataType(Mockito.any(), Mockito.any()))
                .thenThrow(new RuntimeException("单侧异常"))
                .thenThrow(new BaseBizRuntimeException("单侧业务异常"));
        ApprovalAsyncOperateProcessMsg msg = new ApprovalAsyncOperateProcessMsg();
        msg.setApprovalId("approvalId");
        boolean result1 = executor.asyncOperateOne(msg);
        Assert.assertTrue(!result1);
        boolean result2 = executor.asyncOperateOne(msg);
        Assert.assertTrue(!result2);


    }
}
