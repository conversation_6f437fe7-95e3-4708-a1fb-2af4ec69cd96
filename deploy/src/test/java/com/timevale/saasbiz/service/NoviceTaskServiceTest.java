package com.timevale.saasbiz.service;

import com.alibaba.fastjson.JSONObject;
import com.timevale.doccooperation.service.enums.SubjectTypeEnum;
import com.timevale.framework.mq.client.consumer.ReceiveResult;
import com.timevale.framework.mq.client.producer.Msg;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.qa.apitest.model.TestJsonObj;
import com.timevale.saasbiz.base.BaseTransactionalServiceTest;
import com.timevale.saasbiz.model.bean.novice.bo.NoviceTaskConfigBO;
import com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO;
import com.timevale.saasbiz.model.bean.novice.dto.output.CheckNoviceTaskOutputDTO;
import com.timevale.saasbiz.model.bean.novice.dto.output.QueryNoviceTaskConfigOutputDTO;
import com.timevale.saasbiz.model.bean.novice.dto.output.QueryNoviceTaskProcessOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.model.enums.novice.NoviceTaskTypeEnum;
import com.timevale.saasbiz.mq.bean.NoviceTaskCompleteMsgEntity;
import com.timevale.saasbiz.mq.consumer.AccountRealNameMqConsumer;
import com.timevale.saasbiz.mq.consumer.MemberDeleteMqConsumer;
import com.timevale.saasbiz.mq.consumer.NoviceTaskCompleteMqConsumer;
import com.timevale.saasbiz.service.novice.NoviceTaskService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_1;
import static com.timevale.saasbiz.constant.TestAccountConstants.ACCOUNT_ID_5;

/**
 * <AUTHOR>
 * @since 2023-05-24
 */
@Slf4j
public class NoviceTaskServiceTest extends BaseTransactionalServiceTest {

    @Autowired NoviceTaskService noviceTaskService;

    @Autowired UserCenterService userCenterService;

    @Autowired AccountRealNameMqConsumer accountRealNameMqConsumer;

    @Autowired MemberDeleteMqConsumer memberDeleteMqConsumer;

    @Autowired NoviceTaskCompleteMqConsumer noviceTaskCompleteMqConsumer;

    /** 账号创建通知测试 */
    @Test(description = "企业账号创建通知")
    public void testMemberJoinMsg() {
        // 异常消息体, 异常情况下默认消费成功
        Msg errMsg = new Msg("异常消息体".getBytes());
        Assert.assertEquals(
                memberDeleteMqConsumer.receive(Collections.singletonList(errMsg)),
                ReceiveResult.success());
        // 正常消息体, 应消费成功
        List<Msg> list = new ArrayList<>();
        Msg msg = new Msg();
        String message =
                "{\"memberOid\":\"eb79fc0089a3417faf149895e6ed38b7\",\"orgOid\":\"6dbdfc93370046b188081de313bf18a5\",\"operationType\":\"DELETE\"}";
        msg.setBody(message.getBytes());
        list.add(msg);
        ReceiveResult receive = memberDeleteMqConsumer.receive(list);
        log.info(receive.toString());
    }
    /** 账号实名消息测试 */
    @Test(description = "个人账号实名消息测试")
    public void testAccountRealNameMsg() {
        // 异常消息体, 异常情况下默认消费成功
        Msg errMsg = new Msg("异常消息体".getBytes());
        Assert.assertEquals(
                accountRealNameMqConsumer.receive(Collections.singletonList(errMsg)),
                ReceiveResult.success());
        // 正常消息体, 消费成功
        List<Msg> list = new ArrayList<>();
        Msg msg = new Msg();
        String message =
                "{\"accountUid\":\"dfge1w1\",\"ouid\":\"eb79fc0089a3417faf149895e6ed38b7\",\"uuid\":\"7b3aa86f79584c4281607df961487928\",\"guid\":\"b480ee248b3147fbb6465a4fca83f95a\",\"serviceId\":\"1259289660576917423\",\"transactionId\":\"2235.465465.4845\",\"appId\":\"**********\"}";
        msg.setBody(message.getBytes());
        list.add(msg);
        ReceiveResult receive = accountRealNameMqConsumer.receive(list);
        log.info(receive.toString());
    }

    @Test(description = "企业账号实名消息测试")
    public void testOrgAccountRealNameMsg() {
        List<Msg> list = new ArrayList<>();
        Msg msg = new Msg();
        String message =
                "{\"accountUid\":\"dfge1w1\",\"ouid\":\"3aa12d47bb6a4cec9290df8416ec91dd\",\"uuid\":\"b64cb96454d248ac8f4aa4caee0bcdbb\",\"guid\":\"5dbfb40c6ec44f7ea773f70cd0212e02\",\"serviceId\":\"1280964203070536735\",\"transactionId\":\"2235.465465.4845\",\"appId\":\"**********\"}";
        msg.setBody(message.getBytes());
        list.add(msg);
        ReceiveResult receive = accountRealNameMqConsumer.receive(list);

        log.info(receive.toString());
    }

    @Test(description = "其他注册来源企业账号实名消息测试")
    public void testOtherOrgAccountRealNameMsg() {
        noviceTaskService.addDefaultNoviceTasks(
                "da734dcdddb04c29952a6feea832b24d", "9bd2866007a04bbfbb0f750aa598b028");
    }

    /** 判断用户是否显示新手任务横幅 */
    @Test(description = "个人未实名的情况")
    public void testCheckNoviceAndQueryNoviceWhenNotRealNamePerson() {
        String accountId = "86aebbd45d77467d9d86809e079965aa";
        String subjectId = "86aebbd45d77467d9d86809e079965aa";
        CheckNoviceTaskOutputDTO checkNoviceTaskResponse =
                noviceTaskService.checkNoviceTask(accountId, subjectId);

        log.info(checkNoviceTaskResponse.toString());
        Assert.assertTrue(checkNoviceTaskResponse.isShowNovice() == true, "显示横幅");
        Assert.assertTrue(checkNoviceTaskResponse.isCanIgnore() == false, "不可忽略任务");
        Assert.assertTrue(checkNoviceTaskResponse.getTotalGiftNum() != 0, "新手任务赠送5份免费合同");
    }

    /** 判断用户是否显示新手任务横幅 */
    @Test(description = "个人已实名未创建企业的情况")
    public void testCheckNoviceAndQueryNoviceWhenNotCreateOrg() {
        String accountId = ACCOUNT_ID_5;
        String subjectId = ACCOUNT_ID_5;
        CheckNoviceTaskOutputDTO checkNoviceTaskResponse =
                noviceTaskService.checkNoviceTask(accountId, subjectId);

        log.info(checkNoviceTaskResponse.toString());
        Assert.assertTrue(checkNoviceTaskResponse.isShowNovice() == true, "显示横幅");
        Assert.assertTrue(checkNoviceTaskResponse.isCanIgnore() == false, "不可忽略任务");
        Assert.assertTrue(checkNoviceTaskResponse.getTotalGiftNum() != 0, "新手任务赠送5份免费合同");
    }

    /** 判断用户是否显示新手任务横幅 */
    @Test(description = "任务列表为空的情况")
    public void testCheckNoviceAndQueryNoviceWhenTaskListIsEmpty() {
        String accountId = "92cede5d8e924ca48c67c93137a04836";
        String subjectId = "7dbf4034e5984f92b4ed8d9ec12d5d70";
        CheckNoviceTaskOutputDTO checkNoviceTaskResponse =
                noviceTaskService.checkNoviceTask(accountId, subjectId);

        log.info(checkNoviceTaskResponse.toString());
        Assert.assertTrue(checkNoviceTaskResponse.isShowNovice() == false, "显示横幅");
        Assert.assertTrue(checkNoviceTaskResponse.isCanIgnore() == true, "不可忽略任务");
        Assert.assertTrue(checkNoviceTaskResponse.getTotalGiftNum() == 0, "新手任务赠送28份免费合同");
    }

    @Test(description = "已实名企业，有两个企业任务的企业管理员accountId")
    public void testCheckNoviceAndQueryNoviceWhenOldAccountHasOrgRealNameAndHasTask() {
        TestJsonObj checkNoviceTask = jsonDataMap.get("OldAccountHasOrgRealNameAndHasTask");
        CheckNoviceTaskInputDTO reqData = (CheckNoviceTaskInputDTO) checkNoviceTask.getReqData();
        // 不指定主体
        CheckNoviceTaskOutputDTO checkNoviceTaskResponse =
                noviceTaskService.checkNoviceTask(
                        reqData.getAccountId(), "021a31717fb4467d8bfff8c769d29ce9");
        log.info(checkNoviceTaskResponse.toString());
        Assert.assertTrue(checkNoviceTaskResponse.isShowNovice() == false, "显示横幅");
    }

    /** 测试获取新手任务进度 */
    @Test(description = "获取任务进度")
    public void testQueryNoviceTaskProcessWhenManyOrgAdmin() {
        String accountId = "cce026d3d4e341979c9576fe18d745a1";
        String subjectId = "075025b555134384bf7927fd3456b005";
        QueryNoviceTaskProcessOutputDTO queryNoviceTaskProcessResponse =
                noviceTaskService.queryNoviceTaskProcess(accountId, subjectId);
        Assert.assertTrue(!queryNoviceTaskProcessResponse.isNeedNotify());

        NoviceTaskCompleteMsgEntity msgEntity = new NoviceTaskCompleteMsgEntity();
        msgEntity.setTaskType(NoviceTaskTypeEnum.INITIATE_PROCESS.getType());
        msgEntity.setAccountId(accountId);
        msgEntity.setSubjectId(subjectId);

        Msg msg = new Msg(JSONObject.toJSONString(msgEntity).getBytes());
        ReceiveResult result = noviceTaskCompleteMqConsumer.receive(Lists.newArrayList(msg));
        Assert.assertTrue(result.isReceiveSuccess());

        queryNoviceTaskProcessResponse =
                noviceTaskService.queryNoviceTaskProcess(accountId, subjectId);
        Assert.assertTrue(queryNoviceTaskProcessResponse.isNeedNotify());
    }

    @Test(description = "获取新手任务配置")
    public void testQueryNoviceTaskConfig() {
        // 个人新手任务
        QueryNoviceTaskConfigOutputDTO personTaskConfig =
                noviceTaskService.queryNoviceTaskConfig(
                        ACCOUNT_ID_1, SubjectTypeEnum.PERSON.getType());
        List<NoviceTaskConfigBO> personTasks = personTaskConfig.getTasks();
        Assert.assertTrue(CollectionUtils.isNotEmpty(personTasks) && personTasks.size() == 1);
        // 企业新手任务
        QueryNoviceTaskConfigOutputDTO orgTaskConfig =
                noviceTaskService.queryNoviceTaskConfig(
                        ACCOUNT_ID_1, SubjectTypeEnum.ORG.getType());
        List<NoviceTaskConfigBO> orgTasks = orgTaskConfig.getTasks();
        Assert.assertTrue(CollectionUtils.isNotEmpty(orgTasks) && orgTasks.size() == 3);
    }

    @Test
    public void testSendPresentMsg(){
        AccountDetailDTO account = userCenterService.queryAccountDetailByOid(ACCOUNT_ID_1);
        int taskType = NoviceTaskTypeEnum.REAL_NAME.getType();
        try{
            noviceTaskService.sendPresentMsg(account,account,taskType);
        }catch (Exception e){
            Assert.fail();
        }
    }
}
