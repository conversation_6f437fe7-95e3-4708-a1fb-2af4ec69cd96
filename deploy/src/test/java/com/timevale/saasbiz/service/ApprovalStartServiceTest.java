//package com.timevale.saasbiz.service;
//
//import com.google.common.collect.Lists;
//import com.timevale.contractapproval.facade.api.ApprovalInstanceRpcService;
//import com.timevale.contractapproval.facade.dto.ApprovalDetailDTO;
//import com.timevale.contractapproval.facade.enums.ApprovalBizTypeEnum;
//import com.timevale.contractapproval.facade.enums.ApprovalStatusEnum;
//import com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum;
//import com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum;
//import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
//import com.timevale.contractapproval.facade.input.CreateApprovalGroupInput;
//import com.timevale.contractapproval.facade.input.StartApprovalInput;
//import com.timevale.contractapproval.facade.output.StartApprovalOutput;
//import com.timevale.contractmanager.common.service.api.RpcProcessService;
//import com.timevale.contractmanager.common.service.enums.ProcessCreateTypeEnum;
//import com.timevale.contractmanager.common.service.enums.ProcessStatusEnum;
//import com.timevale.contractmanager.common.service.enums.SubProcessTypeEnum;
//import com.timevale.contractmanager.common.service.model.AddProcessModel;
//import com.timevale.contractmanager.common.service.model.AddSubProcessModel;
//import com.timevale.contractmanager.common.service.result.CreateProcessResult;
//import com.timevale.mandarin.base.util.CollectionUtils;
//import com.timevale.mandarin.base.util.StringUtils;
//import com.timevale.qa.apitest.model.JsonDataIgnore;
//import com.timevale.saasbiz.base.BaseServiceTest;
//import com.timevale.saasbiz.constant.TestAccountConstants;
//import com.timevale.saasbiz.integration.approval.ProcessApprovalClient;
//import com.timevale.saasbiz.model.bean.approval.dto.ApprovalDetailWithLogsDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.ApprovalMixFlowResultDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.ApprovalOperateDetailDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.ApprovalTemplateDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.ApprovalUrlDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalMixFlowInputDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalOperateDataDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalOperateDetailInputDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalOperateInputDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalRestartInputDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalUrgeInputDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalUrlInputDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalVirtualStartInputDTO;
//import com.timevale.saasbiz.model.bean.approval.dto.task.ApprovalLogBizDTO;
//import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
//import com.timevale.saasbiz.model.enums.approval.ApprovalOperateTypeEnum;
//import com.timevale.saasbiz.model.utils.UUIDUtil;
//import com.timevale.saasbiz.service.approval.ApprovalFlowService;
//import com.timevale.saasbiz.service.approval.ApprovalStartService;
//import com.timevale.saasbiz.service.approval.approvaloperate.ApprovalOperateForwardService;
//import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalAsyncOperateMsg;
//import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalAsyncOperateProcessMsg;
//import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalOperateAsyncExecutor;
//import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalOperateSupport;
//import com.timevale.saasbiz.service.approval.approvaloperate.operate.ApprovalOperateForwardResult;
//import com.timevale.saasbiz.service.usercenter.UserCenterService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.testng.Assert;
//import org.testng.annotations.Test;
//
//import java.util.Arrays;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @since 2023/4/25 15:10
// */
//@JsonDataIgnore
//public class ApprovalStartServiceTest extends BaseServiceTest {
//    private final String OPERATOR_OID = TestAccountConstants.ACCOUNT_ID_1;
//
//    // esigntest单测专业版企业一
//    private final String NOT_GRAY_SUBJECT_OID = TestAccountConstants.PROFESSIONAL_SUBJECT_1;
//
//    // 单侧勿动-合同审批-模版
//    private final String APPROVAL_TEMPLATE = "AT1a1b507ca5ac47578027d4bc54bd7bd4";
//    private final String OLD_APPROVAL_TEMPLATE = "C7F0285672DA4C1AB55BCD4D3DDE575D";
//    private final String FLOW_TEMPLATE_ID = "a6c165c4c225427c97b7541ddcb1ddfd";
//
//    private final String APPROVAL_ID = "7926f72166864894ae72eb67928400d1";
//
//    private final String OLD_APPROVAL_ID = "81654";
//
//
//    // SENIOR_SUBJECT_MEMBER_2 测试账号
//    //  esigntest单测高级版企业一， 测试企业
//    @Autowired
//    private ApprovalStartService approvalStartService;
//    @Autowired
//    private ApprovalInstanceRpcService approvalInstanceRpcService;
//    @Autowired
//    private ApprovalOperateForwardService approvalOperateForwardService;
//    @Autowired
//    private ApprovalOperateAsyncExecutor asyncExecutor;
//    @Autowired
//    private ProcessApprovalClient processApprovalClient;
//    @Autowired
//    private ApprovalFlowService approvalFlowService;
//    @Autowired
//    private ApprovalOperateSupport operateSupport;
//    @Autowired
//    private UserCenterService userCenterService;
//    @Autowired
//    private RpcProcessService rpcProcessService;
//
//    @Test
//    public void startAvailableApprovalTemplateTest() {
//        {
//            // 灰度用户
//            List<ApprovalTemplateDTO> approvalTemplateBOS =
//                    approvalStartService.startAvailableApprovalTemplate(
//                            TestAccountConstants.SENIOR_SUBJECT_1,
//                            TestAccountConstants.ACCOUNT_ID_1,
//                            ApprovalTemplateTypeEnum.CONTRACT.getCode(),
//                            ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE.getCode(),
//                            FLOW_TEMPLATE_ID);
//            Assert.assertTrue(
//                    CollectionUtils.isNotEmpty(approvalTemplateBOS)
//                            && approvalTemplateBOS
//                                    .get(0)
//                                    .getApprovalTemplateId()
//                                    .equals(APPROVAL_TEMPLATE));
//        }
//    }
//
//
//    @Test
//    public void reStartAvailableApprovalTemplateTest() {
//
//        List<ApprovalTemplateDTO> approvalTemplateBOS = approvalStartService.reStartAvailableApprovalTemplate(TestAccountConstants.SENIOR_SUBJECT_1,
//                TestAccountConstants.ACCOUNT_ID_1, APPROVAL_ID);
//        Assert.assertTrue(CollectionUtils.isNotEmpty(approvalTemplateBOS) &&
//                approvalTemplateBOS.get(0).getApprovalTemplateId().equals(APPROVAL_TEMPLATE));
//    }
//
//    @Test
//    public void preFlowTest() {
//        {
//            ApprovalVirtualStartInputDTO inputDTO = new ApprovalVirtualStartInputDTO();
//            inputDTO.setApprovalTemplateCode(APPROVAL_TEMPLATE);
//            inputDTO.setDeptId(null);
//            inputDTO.setConditionType(ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE.getCode());
//            inputDTO.setConditionValue(FLOW_TEMPLATE_ID);
//            inputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            inputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_1);
//            inputDTO.setApprovalCode(APPROVAL_ID);
//            List<ApprovalLogBizDTO> approvalLogBOS = approvalStartService.preFlow(inputDTO).getApprovalNodeTasks();
//            Assert.assertTrue(CollectionUtils.isNotEmpty(approvalLogBOS) &&
//                    approvalLogBOS.get(0).getCandidates().get(0).getGid().equals(TestAccountConstants.ACCOUNT_ID_1_GID));
//
//        }
//
//        {
//            ApprovalVirtualStartInputDTO inputDTO = new ApprovalVirtualStartInputDTO();
//            inputDTO.setApprovalTemplateCode(OLD_APPROVAL_TEMPLATE);
//            inputDTO.setDeptId(null);
//            inputDTO.setSubjectOid(NOT_GRAY_SUBJECT_OID);
//            inputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_4);
//            inputDTO.setApprovalCode(OLD_APPROVAL_ID);
//            List<ApprovalLogBizDTO> approvalLogBOS = approvalStartService.preFlow(inputDTO).getApprovalNodeTasks();
//            Assert.assertTrue(CollectionUtils.isNotEmpty(approvalLogBOS) &&
//                    approvalLogBOS.get(0).getCandidates().get(0).getOid().equals(TestAccountConstants.ACCOUNT_ID_1));
//
//        }
//
//
//
//    }
//
//    @Test
//    public void restartTest() {
//
//        StartApprovalInput startApprovalInputDTO =  new StartApprovalInput();
//        startApprovalInputDTO.setSkipCheck(false);
//        startApprovalInputDTO.setApprovalTemplateCode(APPROVAL_TEMPLATE);
//        startApprovalInputDTO.setApprovalName("单侧重新发起");
//        startApprovalInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        startApprovalInputDTO.setSubjectGid(TestAccountConstants.SENIOR_SUBJECT_1_GID);
//        startApprovalInputDTO.setSubjectName("esigntest单测高级版企业一");
//        startApprovalInputDTO.setInitiatorOid(TestAccountConstants.ACCOUNT_ID_1);
//        startApprovalInputDTO.setInitiatorGid(TestAccountConstants.ACCOUNT_ID_1_GID);
//        startApprovalInputDTO.setInitiatorName(TestAccountConstants.ACCOUNT_ID_1_NAME);
//        startApprovalInputDTO.setInitiatorDeptId(null);
//        startApprovalInputDTO.setApprovalTemplateConditionType(ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE.getCode());
//        startApprovalInputDTO.setApprovalTemplateConditionValue(FLOW_TEMPLATE_ID);
//        startApprovalInputDTO.setBizId(UUIDUtil.genUUID());
//        startApprovalInputDTO.setBizData("{}");
//        StartApprovalOutput startApprovalOutput = approvalInstanceRpcService.startApproval(startApprovalInputDTO);
//
//        ApprovalRestartInputDTO approvalRestartInputDTO = new ApprovalRestartInputDTO();
//        approvalRestartInputDTO.setAppId("appId");
//        approvalRestartInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        approvalRestartInputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_1);
//        approvalRestartInputDTO.setApprovalId(startApprovalOutput.getApprovalCode());
//        approvalRestartInputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//        approvalRestartInputDTO.setApprovalTemplateId(APPROVAL_TEMPLATE);
//        String restart = approvalStartService.restart(approvalRestartInputDTO);
//        Assert.assertTrue(StringUtils.isNotBlank(restart));
//    }
//
//
//    @Test
//    public void mixFlowTest() {
//        // 合同
//        {
//            ApprovalMixFlowInputDTO inputDTO = new ApprovalMixFlowInputDTO();
//            inputDTO.setApprovalId("16128e35a04a4b8c8bb99003249b8050");
//            inputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            inputDTO.setOperatorOid("9f3f55565e1c43e783dfe4f8fda057fc");
//            inputDTO.setSubjectOid("bf6e1096684c452e9821b40430229965");
//            ApprovalMixFlowResultDTO resultDTO = approvalStartService.mixFlow(inputDTO);
//            List<ApprovalDetailWithLogsDTO> list = resultDTO.getList();
//            Assert.assertTrue(CollectionUtils.isNotEmpty(list));
//            Assert.assertTrue(list.size() == 1 && list.get(0).getApprovalId().equals("16128e35a04a4b8c8bb99003249b8050"));
//            Assert.assertTrue(CollectionUtils.isNotEmpty(list.get(0).getApprovalLogs()));
//        }
//
//        {
//            // 用印
//            ApprovalMixFlowInputDTO inputDTO = new ApprovalMixFlowInputDTO();
//            inputDTO.setApprovalId("AF-26cc091fd9080b0e");
//            inputDTO.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
//            inputDTO.setOperatorOid("9f3f55565e1c43e783dfe4f8fda057fc");
//            inputDTO.setSubjectOid("bf6e1096684c452e9821b40430229965");
//            ApprovalMixFlowResultDTO resultDTO = approvalStartService.mixFlow(inputDTO);
//            List<ApprovalDetailWithLogsDTO> list = resultDTO.getList();
//            Assert.assertTrue(CollectionUtils.isNotEmpty(list));
//            Assert.assertTrue(list.get(0).getApprovalId().equals("AF-26cc091fd9080b0e"));
//            Assert.assertTrue(CollectionUtils.isNotEmpty(list.get(0).getApprovalLogs()) &&
//                    CollectionUtils.isNotEmpty(list.get(1).getApprovalLogs()));
//
//            // 用印
//            ApprovalMixFlowInputDTO inputDTO1 = new ApprovalMixFlowInputDTO();
//            inputDTO1.setApprovalId("AF-c042e64256bb40f8852639207129168b");
//            inputDTO1.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
//            inputDTO1.setOperatorOid("4501308d254a434b90fd9d96f63dc161");
//            inputDTO1.setSubjectOid("fffcc67b09384192af4e06a3b2704a06");
//            ApprovalMixFlowResultDTO resultDTO1 = approvalStartService.mixFlow(inputDTO1);
//            List<ApprovalDetailWithLogsDTO> list1 = resultDTO1.getList();
//            Assert.assertTrue(CollectionUtils.isNotEmpty(list1));
//            Assert.assertTrue(list1.get(0).getApprovalId().equals("AF-c042e64256bb40f8852639207129168b"));
//            Assert.assertTrue(CollectionUtils.isNotEmpty(list1.get(0).getApprovalLogs()));
//
//        }
//
//        {
//            // 灰度数据
//            ApprovalMixFlowInputDTO inputDTO = new ApprovalMixFlowInputDTO();
//            inputDTO.setApprovalId(OLD_APPROVAL_ID);
//            inputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            inputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_4);
//            inputDTO.setSubjectOid(NOT_GRAY_SUBJECT_OID);
//            ApprovalMixFlowResultDTO resultDTO = approvalStartService.mixFlow(inputDTO);
//            List<ApprovalDetailWithLogsDTO> list = resultDTO.getList();
//            Assert.assertTrue(CollectionUtils.isNotEmpty(list));
//            Assert.assertTrue(list.get(0).getApprovalId().equals(OLD_APPROVAL_ID));
//            Assert.assertTrue(CollectionUtils.isNotEmpty(list.get(0).getApprovalLogs()));
//        }
//
//    }
//
//
//    @Test
//    public void fileAuthDetailTest() {
//        ApprovalOperateDetailInputDTO inputDTO = new ApprovalOperateDetailInputDTO();
//        inputDTO.setApprovalId("AF-915c8addd5c2496092e802a2e6e41f96");
//        inputDTO.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
//        inputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_2);
//        inputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        ApprovalOperateDetailDTO approvalOperateDetailBO = approvalStartService.operateDetail(inputDTO);
//        Assert.assertNotNull(approvalOperateDetailBO);
//
//        inputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_1);
//        inputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        approvalOperateDetailBO = approvalStartService.operateDetail(inputDTO);
//        Assert.assertNotNull(approvalOperateDetailBO);
//
//    }
//
//
//
//    @Test
//    public void operateDetailTest() {
//
//        // 合同审批
//        ApprovalOperateDetailInputDTO inputDTO = new ApprovalOperateDetailInputDTO();
//        inputDTO.setApprovalId(APPROVAL_ID);
//        inputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//        inputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_1);
//        inputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        ApprovalOperateDetailDTO approvalOperateDetailBO = approvalStartService.operateDetail(inputDTO);
//        Assert.assertTrue(null != approvalOperateDetailBO);
//        Assert.assertTrue(ApprovalStatusEnum.APPROVALING.getCode().equals(approvalOperateDetailBO.getApprovalStatus()));
//
//        // 用印审批
//        inputDTO.setApprovalId("AF-865be9535f024c549d2b1e8a6a2563c9");
//        inputDTO.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
//        inputDTO.setOperatorOid("911b8c8d6a3d41b989287751e5cc142e");
//        inputDTO.setSubjectOid("d9ec3169a6e04d148e5a8cc08ab3c13d");
//        approvalOperateDetailBO = approvalStartService.operateDetail(inputDTO);
//        Assert.assertTrue(null != approvalOperateDetailBO);
//        Assert.assertTrue(ApprovalStatusEnum.APPROVAL_REFUSE.getCode().equals(approvalOperateDetailBO.getApprovalStatus()));
//    }
//
//    @Test
//    public void approvalOperate() {
//
//        String subjectOid = TestAccountConstants.SENIOR_SUBJECT_1;
//        String operatorOid = TestAccountConstants.ACCOUNT_ID_1;
//        // 发起 撤回
//        {
//            AddProcessModel addProcessModel = new AddProcessModel();
//            addProcessModel.setProcessTitle("单测-测试合同审批撤回");
//            addProcessModel.setProcessStatus(ProcessStatusEnum.APPROVAL.getStatus());
//            addProcessModel.setCreateType(ProcessCreateTypeEnum.BY_DOC.getCreateType());
//            addProcessModel.setAppId("**********");
//            addProcessModel.setInitiatorOid(TestAccountConstants.ACCOUNT_ID_1);
//            addProcessModel.setInitiatorGid(TestAccountConstants.ACCOUNT_ID_1_GID);
//            addProcessModel.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            addProcessModel.setSubjectGid(TestAccountConstants.SENIOR_SUBJECT_1_GID);
//            addProcessModel.setPayAccountOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            CreateProcessResult newProcess = rpcProcessService.createNewProcess(addProcessModel);
//            StartApprovalInput startApprovalInputDTO =  new StartApprovalInput();
//            startApprovalInputDTO.setSkipCheck(true);
//            startApprovalInputDTO.setApprovalTemplateCode(APPROVAL_TEMPLATE);
//            startApprovalInputDTO.setApprovalName("单侧重新发起");
//            startApprovalInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            startApprovalInputDTO.setSubjectGid(TestAccountConstants.SENIOR_SUBJECT_1_GID);
//            startApprovalInputDTO.setSubjectName("esigntest单测高级版企业一");
//            startApprovalInputDTO.setInitiatorOid(TestAccountConstants.ACCOUNT_ID_1);
//            startApprovalInputDTO.setInitiatorGid(TestAccountConstants.ACCOUNT_ID_1_GID);
//            startApprovalInputDTO.setInitiatorName(TestAccountConstants.ACCOUNT_ID_1_NAME);
//            startApprovalInputDTO.setInitiatorDeptId(null);
//            startApprovalInputDTO.setApprovalTemplateConditionType(ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE.getCode());
//            startApprovalInputDTO.setApprovalTemplateConditionValue(FLOW_TEMPLATE_ID);
//            startApprovalInputDTO.setBizId(newProcess.getProcessId());
//            startApprovalInputDTO.setBizType(ApprovalBizTypeEnum.STANDARD.getType());
//            startApprovalInputDTO.setBizData("{}");
//            StartApprovalOutput startApprovalOutput = approvalInstanceRpcService.startApproval(startApprovalInputDTO);
//
//            AddSubProcessModel addSubProcessModel = new AddSubProcessModel();
//            addSubProcessModel.setProcessId(newProcess.getProcessId());
//            addSubProcessModel.setSubProcessId(startApprovalOutput.getApprovalCode());
//            addSubProcessModel.setSubProcessType(SubProcessTypeEnum.CONTRACT_APPROVAL.getType());
//            rpcProcessService.addSubProcess(addSubProcessModel);
//
//            //
//            ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
//            inputDTO.setOperateType(ApprovalOperateTypeEnum.REVOKE);
//            inputDTO.setSubjectOid(subjectOid);
//            inputDTO.setOperatorOid(operatorOid);
//            inputDTO.setClient("APP_ANDROID");
//            inputDTO.setAppId("appId");
//            ApprovalOperateDataDTO operateDataDTO = new ApprovalOperateDataDTO();
//            operateDataDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            ApprovalOperateDataDTO.Data data = new ApprovalOperateDataDTO.Data();
//            data.setApprovalId(startApprovalOutput.getApprovalCode());
//            operateDataDTO.setDatas(Arrays.asList(data));
//            inputDTO.setOperateDatas(Lists.newArrayList(operateDataDTO));
//            approvalOperateForwardService.forward(inputDTO);
//        }
//
//        AddProcessModel addProcessModel = new AddProcessModel();
//        addProcessModel.setProcessTitle("单测-测试合同审批操作");
//        addProcessModel.setProcessStatus(ProcessStatusEnum.APPROVAL.getStatus());
//        addProcessModel.setCreateType(ProcessCreateTypeEnum.BY_DOC.getCreateType());
//        addProcessModel.setAppId("**********");
//        addProcessModel.setInitiatorOid(TestAccountConstants.ACCOUNT_ID_1);
//        addProcessModel.setInitiatorGid(TestAccountConstants.ACCOUNT_ID_1_GID);
//        addProcessModel.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        addProcessModel.setSubjectGid(TestAccountConstants.SENIOR_SUBJECT_1_GID);
//        addProcessModel.setPayAccountOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        CreateProcessResult newProcess = rpcProcessService.createNewProcess(addProcessModel);
//        // 发起 同意  审批
//        StartApprovalInput startApprovalInputDTO =  new StartApprovalInput();
//        startApprovalInputDTO.setSkipCheck(true);
//        startApprovalInputDTO.setApprovalTemplateCode(APPROVAL_TEMPLATE);
//        startApprovalInputDTO.setApprovalName("单侧重新发起");
//        startApprovalInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        startApprovalInputDTO.setSubjectGid(TestAccountConstants.SENIOR_SUBJECT_1_GID);
//        startApprovalInputDTO.setSubjectName("esigntest单测高级版企业一");
//        startApprovalInputDTO.setInitiatorOid(TestAccountConstants.ACCOUNT_ID_1);
//        startApprovalInputDTO.setInitiatorGid(TestAccountConstants.ACCOUNT_ID_1_GID);
//        startApprovalInputDTO.setInitiatorName(TestAccountConstants.ACCOUNT_ID_1_NAME);
//        startApprovalInputDTO.setInitiatorDeptId(null);
//        startApprovalInputDTO.setApprovalTemplateConditionType(ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE.getCode());
//        startApprovalInputDTO.setApprovalTemplateConditionValue(FLOW_TEMPLATE_ID);
//        startApprovalInputDTO.setBizId(newProcess.getProcessId());
//        startApprovalInputDTO.setBizData("{}");
//        StartApprovalOutput startApprovalOutput = approvalInstanceRpcService.startApproval(startApprovalInputDTO);
//
//        AddSubProcessModel addSubProcessModel = new AddSubProcessModel();
//        addSubProcessModel.setProcessId(newProcess.getProcessId());
//        addSubProcessModel.setSubProcessId(startApprovalOutput.getApprovalCode());
//        addSubProcessModel.setSubProcessType(SubProcessTypeEnum.CONTRACT_APPROVAL.getType());
//        rpcProcessService.addSubProcess(addSubProcessModel);
//        try {
//            // 等待两秒中， 等任务流转完成
//            Thread.sleep(2000L);
//        } catch (InterruptedException e) {
//        }
//
//        String approvalCode = startApprovalOutput.getApprovalCode();
//        // 同意
//        {
//            // 查询详情
//            ApprovalOperateDetailInputDTO detailInputDTO = new ApprovalOperateDetailInputDTO();
//            detailInputDTO.setApprovalId(approvalCode);
//            detailInputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            detailInputDTO.setOperatorOid(operatorOid);
//            detailInputDTO.setSubjectOid(subjectOid);
//            ApprovalOperateDetailDTO approvalOperateDetailBO = approvalStartService.operateDetail(detailInputDTO);
//            //
//            ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
//            inputDTO.setOperateType(ApprovalOperateTypeEnum.AGREE);
//            inputDTO.setSubjectOid(subjectOid);
//            inputDTO.setOperatorOid(operatorOid);
//            inputDTO.setClient("clientId");
//            inputDTO.setAppId("appId");
//            ApprovalOperateDataDTO operateDataDTO = new ApprovalOperateDataDTO();
//            operateDataDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            ApprovalOperateDataDTO.Data data = new ApprovalOperateDataDTO.Data();
//            data.setApprovalId(startApprovalOutput.getApprovalCode());
//            data.setTaskId(approvalOperateDetailBO.getCurrentOperatorTask().getTaskId());
//            data.setProcessId(newProcess.getProcessId());
//            operateDataDTO.setDatas(Arrays.asList(data));
//            inputDTO.setOperateDatas(Lists.newArrayList(operateDataDTO));
//            ApprovalOperateForwardResult forward = approvalOperateForwardService.forward(inputDTO);
//            Assert.assertTrue(null != forward && forward.isSync());
//        }
//        try {
//            // 等待两秒中， 等任务流转完成
//            Thread.sleep(2000L);
//        } catch (InterruptedException e) {
//        }
//
//        // 拒绝
//        {
//            ApprovalOperateDetailInputDTO detailInputDTO = new ApprovalOperateDetailInputDTO();
//            detailInputDTO.setApprovalId(approvalCode);
//            detailInputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            detailInputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_2);
//            detailInputDTO.setSubjectOid(subjectOid);
//            ApprovalOperateDetailDTO approvalOperateDetailBO = approvalStartService.operateDetail(detailInputDTO);
//            Assert.assertTrue(ApprovalStatusEnum.APPROVALING.getCode().equals(approvalOperateDetailBO.getApprovalStatus()));
//
//            //
//            ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
//            inputDTO.setOperateType(ApprovalOperateTypeEnum.REFUSE);
//            inputDTO.setSubjectOid(subjectOid);
//            inputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_2);
//            inputDTO.setClient("clientId");
//            inputDTO.setAppId("appId");
//            ApprovalOperateDataDTO operateDataDTO = new ApprovalOperateDataDTO();
//            operateDataDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            ApprovalOperateDataDTO.Data data = new ApprovalOperateDataDTO.Data();
//            data.setApprovalId(startApprovalOutput.getApprovalCode());
//            data.setTaskId(approvalOperateDetailBO.getCurrentOperatorTask().getTaskId());
//            operateDataDTO.setDatas(Arrays.asList(data));
//            inputDTO.setOperateDatas(Lists.newArrayList(operateDataDTO));
//            approvalOperateForwardService.forward(inputDTO);
//            ApprovalOperateDetailDTO afterDetail = approvalStartService.operateDetail(detailInputDTO);
//            Assert.assertTrue(ApprovalStatusEnum.APPROVAL_REFUSE.getCode().equals(afterDetail.getApprovalStatus()));
//        }
//    }
//
//    @Test
//    public void groupAgreeTest() throws Exception {
//
//        String subjectOid = TestAccountConstants.SENIOR_SUBJECT_1;
//        String subjectGid = TestAccountConstants.SENIOR_SUBJECT_1_GID;
//
//        AccountInfoDTO subjectAccount = userCenterService.queryAccountInfoByOid(subjectOid);
//        AccountInfoDTO operatorAccount = userCenterService.queryAccountInfoByOid(OPERATOR_OID);
//
//        String groupId = UUIDUtil.genUUID();
//
//        CreateApprovalGroupInput dto = new CreateApprovalGroupInput();
//        dto.setSubjectOid(subjectOid);
//        dto.setSubjectGid(subjectGid);
//        dto.setOperatorOid(OPERATOR_OID);
//        dto.setOperatorGid(TestAccountConstants.ACCOUNT_ID_1_GID);
//        dto.setOperatorName(TestAccountConstants.ACCOUNT_ID_1_NAME);
//        dto.setBizGroupId(groupId);
//        dto.setBizGroupName(groupId);
//        dto.setTotalCount(1);
//        approvalInstanceRpcService.createApprovalGroup(dto);
//
//        // 发起 同意  审批
//        StartApprovalInput startApprovalInputDTO =  new StartApprovalInput();
//        startApprovalInputDTO.setSkipCheck(true);
//        startApprovalInputDTO.setApprovalTemplateCode(APPROVAL_TEMPLATE);
//        startApprovalInputDTO.setApprovalName("单侧组发起");
//        startApprovalInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//        startApprovalInputDTO.setSubjectGid(TestAccountConstants.SENIOR_SUBJECT_1_GID);
//        startApprovalInputDTO.setSubjectName("esigntest单测高级版企业一");
//        startApprovalInputDTO.setInitiatorOid(TestAccountConstants.ACCOUNT_ID_1);
//        startApprovalInputDTO.setInitiatorGid(TestAccountConstants.ACCOUNT_ID_1_GID);
//        startApprovalInputDTO.setInitiatorName(TestAccountConstants.ACCOUNT_ID_1_NAME);
//        startApprovalInputDTO.setInitiatorDeptId(null);
//        startApprovalInputDTO.setApprovalTemplateConditionType(ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE.getCode());
//        startApprovalInputDTO.setApprovalTemplateConditionValue(FLOW_TEMPLATE_ID);
//        startApprovalInputDTO.setBizId(UUIDUtil.genUUID());
//        startApprovalInputDTO.setBizGroupId(groupId);
//        startApprovalInputDTO.setBizData("{}");
//        StartApprovalOutput startApprovalOutput = approvalInstanceRpcService.startApproval(startApprovalInputDTO);
//        Assert.assertTrue(null != startApprovalOutput && StringUtils.isNotBlank(startApprovalOutput.getApprovalCode()));
//
//        //
//        operateSupport.buildProcessOneMsg(new ApprovalOperateInputDTO(),
//                new AccountInfoDTO(), new AccountInfoDTO(), new ApprovalOperateDataDTO(),
//                new ApprovalOperateDataDTO.Data());
//
//
//        Thread.sleep(2000);
//
//        {
//            ApprovalOperateInputDTO approvalOperateInputDTO = new ApprovalOperateInputDTO();
//            approvalOperateInputDTO.setOperateType(ApprovalOperateTypeEnum.AGREE);
//            ApprovalOperateDataDTO operateDataDTO = new ApprovalOperateDataDTO();
//            operateDataDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            operateDataDTO.setBizGroupId(UUIDUtil.genUUID());
//            operateSupport.groupSize(operateDataDTO, approvalOperateInputDTO, subjectAccount, operatorAccount);
//        }
//
//        ApprovalAsyncOperateProcessMsg msg = new ApprovalAsyncOperateProcessMsg();
//        msg.setOperateType(ApprovalOperateTypeEnum.AGREE);
//        msg.setApprovalId("123");
//        msg.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//        asyncExecutor.asyncOperateOne(msg);
//        // 拒绝掉
//        ApprovalOperateInputDTO inputDTO = new ApprovalOperateInputDTO();
//        inputDTO.setOperateType(ApprovalOperateTypeEnum.REFUSE);
//        inputDTO.setSubjectOid(subjectOid);
//        inputDTO.setOperatorOid(OPERATOR_OID);
//        inputDTO.setClient("clientId");
//        inputDTO.setAppId("appId");
//        ApprovalOperateDataDTO operateDataDTO = new ApprovalOperateDataDTO();
//        operateDataDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
////        ApprovalOperateDataDTO.Data data = new ApprovalOperateDataDTO.Data();
////        data.setApprovalId(startApprovalOutput.getApprovalCode());
////        data.setTaskId(approvalOperateDetailBO.getCurrentOperatorTask().getTaskId());
//        operateDataDTO.setBizGroupId(groupId);
//        inputDTO.setOperateDatas(Lists.newArrayList(operateDataDTO));
//        ApprovalAsyncOperateMsg operateMsg = new ApprovalAsyncOperateMsg();
//        operateMsg.setInputDTO(inputDTO);
//        asyncExecutor.asyncOperate(operateMsg);
//        ApprovalDetailDTO approval = processApprovalClient.getApprovalDetailByCode(startApprovalOutput.getApprovalCode());
//    }
//
//
//    @Test
//    public void approvalUrlTest() {
//
//        String subjectOid = "1380ec9f50a0425faed96360f2e95418";
//        String operatorOid = "9f3f55565e1c43e783dfe4f8fda057fc";
//
//        { // 合同老
//            ApprovalUrlInputDTO approvalUrlInputDTO = new ApprovalUrlInputDTO();
//            approvalUrlInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            approvalUrlInputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_1);
//            approvalUrlInputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            approvalUrlInputDTO.setApprovalId("81654");
//            ApprovalUrlDTO approvalUrlBO = approvalFlowService.approvalUrl(approvalUrlInputDTO);
//            Assert.assertTrue(null != approvalUrlBO && StringUtils.isNotBlank(approvalUrlBO.getUrl()));
//        }
//
//        { // 合同新
//            ApprovalUrlInputDTO approvalUrlInputDTO = new ApprovalUrlInputDTO();
//            approvalUrlInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            approvalUrlInputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_1);
//            approvalUrlInputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            approvalUrlInputDTO.setApprovalId("204f06c220414c039df52450c4b22ef9");
//            ApprovalUrlDTO approvalUrlBO = approvalFlowService.approvalUrl(approvalUrlInputDTO);
//            Assert.assertTrue(null != approvalUrlBO && StringUtils.isNotBlank(approvalUrlBO.getUrl()));
//        }
//
//        {
//            // 用印
//            ApprovalUrlInputDTO approvalUrlInputDTO = new ApprovalUrlInputDTO();
//            approvalUrlInputDTO.setSubjectOid(subjectOid);
//            approvalUrlInputDTO.setOperatorOid(operatorOid);
//            approvalUrlInputDTO.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
//            approvalUrlInputDTO.setApprovalId("AF-26d01f7b74080dc5");
//            ApprovalUrlDTO approvalUrlBO = approvalFlowService.approvalUrl(approvalUrlInputDTO);
//            Assert.assertTrue(null != approvalUrlBO && StringUtils.isNotBlank(approvalUrlBO.getUrl()));
//        }
//
//    }
//
//    @Test
//    public void approvalUrgeTest() {
//        {
//            // 合同老
//            ApprovalUrgeInputDTO approvalUrlInputDTO = new ApprovalUrgeInputDTO();
//            approvalUrlInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            approvalUrlInputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_1);
//            approvalUrlInputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            approvalUrlInputDTO.setApprovalId("81654");
//            approvalFlowService.approvalUrge(approvalUrlInputDTO);
//        }
//
//        {
//            // 合同新
//            ApprovalUrgeInputDTO approvalUrlInputDTO = new ApprovalUrgeInputDTO();
//            approvalUrlInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            approvalUrlInputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_1);
//            approvalUrlInputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
//            approvalUrlInputDTO.setApprovalId("204f06c220414c039df52450c4b22ef9");
//            approvalFlowService.approvalUrge(approvalUrlInputDTO);
//
//        }
//
//        {
//            // 用印新
//            ApprovalUrgeInputDTO approvalUrlInputDTO = new ApprovalUrgeInputDTO();
//            approvalUrlInputDTO.setSubjectOid(TestAccountConstants.SENIOR_SUBJECT_1);
//            approvalUrlInputDTO.setOperatorOid(TestAccountConstants.ACCOUNT_ID_2);
//            approvalUrlInputDTO.setApprovalType(ApprovalTypeEnum.SEAL.getCode());
//            approvalUrlInputDTO.setApprovalId("AF-2b8fa18f73081d2e");
//            approvalFlowService.approvalUrge(approvalUrlInputDTO);
//
//        }
//
//    }
//
//
//
//}
