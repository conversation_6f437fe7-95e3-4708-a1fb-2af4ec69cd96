package com.timevale.saasbiz.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.timevale.contractanalysis.facade.api.dto.compare.IntelligentCompareQueryStatusOutputDTO;
import com.timevale.contractanalysis.facade.api.enums.CompareNotifyResultFacadeEnum;
import com.timevale.docmanager.service.api.DocPlusService;
import com.timevale.docmanager.service.enums.FileAimTypeEnum;
import com.timevale.docmanager.service.result.SimpleFileResult;
import com.timevale.saasbiz.integration.contractanalysis.IntelligentCompareClient;
import com.timevale.saasbiz.integration.doc.DocClient;
import com.timevale.saasbiz.model.bean.authrelation.bo.FileAuthCheckResultBO;
import com.timevale.saasbiz.model.bean.compare.dto.input.EmbedCompareQueryStatusInputDTO;
import com.timevale.saasbiz.model.bean.compare.dto.output.EmbedCompareQueryStatusOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.input.CheckHasPrivilegeInputDTO;
import com.timevale.saasbiz.model.constants.PrivilegeOperationConstants;
import com.timevale.saasbiz.model.constants.PrivilegeResourceConstants;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.service.process.ProcessFileAuthCheckService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.Test;

import com.timevale.contractmanager.common.service.bean.ProcessConfigBean;
import com.timevale.contractmanager.common.service.enums.ProcessSecretEnum;
import com.timevale.contractmanager.common.service.result.ProcessConfigResult;
import com.timevale.contractmanager.common.service.result.ProcessFileAuthResult;
import com.timevale.contractmanager.common.service.result.processpermission.CheckUserProcessViewableResult;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.process.ProcessClient;
import com.timevale.saasbiz.integration.saascommon.SaasCommonClient;
import com.timevale.saasbiz.model.bean.compare.dto.input.EmbedCompareCheckShowInputDTO;
import com.timevale.saasbiz.model.bean.compare.dto.input.EmbedCompareCreateInputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.saasbiz.service.compare.impl.CompareServiceImpl;
import com.timevale.saasbiz.service.usercenter.UserCenterService;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2024-04-01
 */
@JsonDataIgnore
public class CompareServiceMockTest extends BaseMockTest {
    
    @InjectMocks
    private CompareServiceImpl compareService;
    
    @Mock
    private SaasCommonClient saasCommonClient;

    @Mock
    private ProcessClient processClient;

    @Mock
    private UserCenterService userCenterService;

    @Mock
    private DocClient docClient;

    @Mock
    private IntelligentCompareClient intelligentCompareClient;

    @Mock
    private DocPlusService docPlusService;

    @Mock
    private ProcessFileAuthCheckService processFileAuthCheckService;

    @Test
    public void testEmbedCompareCheckShow() {
        String subjectOid = "subjectOid";
        String operatorOid = "operatorOid";
        String processId = "processId";
        String operatorGid = "opreratorGid";
        String operatorName = "operatorName";
        String subjectGid = "subjectGid";
        String subjectName = "subjectName";

        // case 会员版本不满足
        AccountInfoDTO operatorAccountInfoDTO = new AccountInfoDTO();
        operatorAccountInfoDTO.setOid(operatorOid);
        operatorAccountInfoDTO.setGid(operatorGid);
        operatorAccountInfoDTO.setName(operatorName);
        AccountInfoDTO subjectAccountInfoDTO = new AccountInfoDTO();
        subjectAccountInfoDTO.setOid(subjectOid);
        subjectAccountInfoDTO.setGid(subjectGid);
        subjectAccountInfoDTO.setName(subjectName);

        when(userCenterService.queryAccountInfoByOid(operatorOid)).thenReturn(operatorAccountInfoDTO);
        when(userCenterService.queryAccountInfoByOid(subjectOid)).thenReturn(subjectAccountInfoDTO);
        when(saasCommonClient.supportFunction(subjectOid, null, FunctionCodeConstants.INTELLIGENT_COMPARE)).thenReturn(false);

        EmbedCompareCheckShowInputDTO inputDTO = new EmbedCompareCheckShowInputDTO();
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        inputDTO.setProcessId(processId);
        boolean show = compareService.embedCompareCheckShow(inputDTO);
        verify(saasCommonClient, atLeast(1)).supportFunction(subjectOid, null, FunctionCodeConstants.INTELLIGENT_COMPARE);
        Assert.assertFalse(show);

        // case 无功能权限
        when(saasCommonClient.supportFunction(subjectOid, null, FunctionCodeConstants.INTELLIGENT_COMPARE)).thenReturn(true);

        CheckHasPrivilegeInputDTO privilegeInputDTO = new CheckHasPrivilegeInputDTO();
        privilegeInputDTO.setAccountId(operatorOid);
        privilegeInputDTO.setTenantId(subjectOid);
        privilegeInputDTO.setResourceKey(PrivilegeResourceConstants.PRIVILEGE_RESOURCE_INTELLIGENT_TOOL);
        privilegeInputDTO.setPrivilegeKey(PrivilegeOperationConstants.ADD);
        when(userCenterService.checkHasPrivilege(privilegeInputDTO, false)).thenReturn(false);

        show = compareService.embedCompareCheckShow(inputDTO);
        verify(userCenterService, atLeast(1)).checkHasPrivilege(privilegeInputDTO, false);
        Assert.assertFalse(show);
        when(userCenterService.checkHasPrivilege(privilegeInputDTO, false)).thenReturn(true);

        FileAuthCheckResultBO checkResultBO = new FileAuthCheckResultBO();
        checkResultBO.setSuccess(true);
        when(processFileAuthCheckService.checkFileAuth(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(checkResultBO);

        show = compareService.embedCompareCheckShow(inputDTO);
        Assert.assertTrue(show);
    }

    @Test(description = "合同比对创建", priority = 1)
    public void testCreate() {
        String subjectOid = "subjectOid";
        String operatorOid = "operatorOid";
        String processId = "processId";
        String operatorGid = "opreratorGid";
        String operatorName = "operatorName";
        String subjectGid = "subjectGid";
        String subjectName = "subjectName";
        String fileId = "fileId";
        List<String> fileIds = Lists.newArrayList(fileId);
        String uploadFileId = "uploadFileId";
        String compareId = "compareId";

        AccountInfoDTO operatorAccountInfoDTO = new AccountInfoDTO();
        operatorAccountInfoDTO.setOid(operatorOid);
        operatorAccountInfoDTO.setGid(operatorGid);
        operatorAccountInfoDTO.setName(operatorName);
        AccountInfoDTO subjectAccountInfoDTO = new AccountInfoDTO();
        subjectAccountInfoDTO.setOid(subjectOid);
        subjectAccountInfoDTO.setGid(subjectGid);
        subjectAccountInfoDTO.setName(subjectName);

        when(userCenterService.queryAccountInfoByOid(operatorOid)).thenReturn(operatorAccountInfoDTO);
        when(userCenterService.queryAccountInfoByOid(subjectOid)).thenReturn(subjectAccountInfoDTO);

        //非专属云
        ProcessConfigResult processConfigResult = new ProcessConfigResult();
        processConfigResult.setProcessId(processId);
        ProcessConfigBean processConfigBean = new ProcessConfigBean();
        processConfigResult.setConfigInfo(processConfigBean);
        when(processClient.queryProcessConfig(processId)).thenReturn(processConfigResult);

        //流程有查看权限
        CheckUserProcessViewableResult viewableResult = new CheckUserProcessViewableResult();
        viewableResult.setSecret(false);
        viewableResult.setViewable(true);
        when(processClient.checkUserProcessViewable(processId, subjectOid, operatorOid, null)).thenReturn(viewableResult);

        //全部文件可见
        ProcessFileAuthResult fileAuthResult = new ProcessFileAuthResult();
        fileAuthResult.setProcessId(processId);
        fileAuthResult.setVisibleType(ProcessSecretEnum.NONE_SECRET.getType());
        when(processClient.queryProcessFileAuth(any())).thenReturn(fileAuthResult);

        //上传文件为当前人
        SimpleFileResult queryDocInfo = new SimpleFileResult();
        queryDocInfo.setOwnerOid(subjectOid);
        when(docClient.getFileInfoFromDocOrTemplate(uploadFileId)).thenReturn(queryDocInfo);

        FileAuthCheckResultBO checkResultBO = new FileAuthCheckResultBO();
        checkResultBO.setSuccess(true);
        when(processFileAuthCheckService.checkFileAuth(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(checkResultBO);

        //调用底层
        when(intelligentCompareClient.createCompare(any())).thenReturn(compareId);

        //调用
        EmbedCompareCreateInputDTO inputDTO = new EmbedCompareCreateInputDTO();
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        EmbedCompareCreateInputDTO.EmbedCompareCreateFile originFile = new EmbedCompareCreateInputDTO.EmbedCompareCreateFile();
        originFile.setFileName("fileName");
        originFile.setFileId(fileId);
        originFile.setProcessId(processId);
        inputDTO.setOriginFile(originFile);
        EmbedCompareCreateInputDTO.EmbedCompareCreateFile compareFile = new EmbedCompareCreateInputDTO.EmbedCompareCreateFile();
        compareFile.setFileName("fileName");
        compareFile.setFileId(uploadFileId);
        inputDTO.setCompareFile(compareFile);
        String res = compareService.embedCompareCreate(inputDTO);
        Assert.assertNotNull(res);
        Assert.assertEquals(compareId, res);
    }

    @Test(description = "合同比对创建--新上传文件", priority = 2)
    public void testCreate_newFile() {
        String subjectOid = "subjectOid";
        String operatorOid = "operatorOid";
        String processId = "processId";
        String operatorGid = "opreratorGid";
        String operatorName = "operatorName";
        String subjectGid = "subjectGid";
        String subjectName = "subjectName";
        String fileId = "fileId";
        List<String> fileIds = Lists.newArrayList(fileId);
        String uploadFileId = "uploadFileId";
        String compareId = "compareId";

        AccountInfoDTO operatorAccountInfoDTO = new AccountInfoDTO();
        operatorAccountInfoDTO.setOid(operatorOid);
        operatorAccountInfoDTO.setGid(operatorGid);
        operatorAccountInfoDTO.setName(operatorName);
        AccountInfoDTO subjectAccountInfoDTO = new AccountInfoDTO();
        subjectAccountInfoDTO.setOid(subjectOid);
        subjectAccountInfoDTO.setGid(subjectGid);
        subjectAccountInfoDTO.setName(subjectName);

        when(userCenterService.queryAccountInfoByOid(operatorOid)).thenReturn(operatorAccountInfoDTO);
        when(userCenterService.queryAccountInfoByOid(subjectOid)).thenReturn(subjectAccountInfoDTO);

        //非专属云
        ProcessConfigResult processConfigResult = new ProcessConfigResult();
        processConfigResult.setProcessId(processId);
        ProcessConfigBean processConfigBean = new ProcessConfigBean();
        processConfigResult.setConfigInfo(processConfigBean);
        when(processClient.queryProcessConfig(processId)).thenReturn(processConfigResult);

        //流程有查看权限
        CheckUserProcessViewableResult viewableResult = new CheckUserProcessViewableResult();
        viewableResult.setSecret(false);
        viewableResult.setViewable(true);
        when(processClient.checkUserProcessViewable(processId, subjectOid, operatorOid, null)).thenReturn(viewableResult);

        //全部文件可见
        ProcessFileAuthResult fileAuthResult = new ProcessFileAuthResult();
        fileAuthResult.setProcessId(processId);
        fileAuthResult.setVisibleType(ProcessSecretEnum.NONE_SECRET.getType());
        when(processClient.queryProcessFileAuth(any())).thenReturn(fileAuthResult);

        //调用底层
        when(intelligentCompareClient.createCompare(any())).thenReturn(compareId);

        //调用
        EmbedCompareCreateInputDTO inputDTO = new EmbedCompareCreateInputDTO();
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        EmbedCompareCreateInputDTO.EmbedCompareCreateFile originFile = new EmbedCompareCreateInputDTO.EmbedCompareCreateFile();
        originFile.setFileName("fileName");
        originFile.setFileId(fileId);
//        originFile.setProcessId(processId);
        inputDTO.setOriginFile(originFile);
        EmbedCompareCreateInputDTO.EmbedCompareCreateFile compareFile = new EmbedCompareCreateInputDTO.EmbedCompareCreateFile();
        compareFile.setFileName("fileName");
        compareFile.setFileId(uploadFileId);
        inputDTO.setCompareFile(compareFile);

        //上传文件非当前人
        SimpleFileResult queryDocInfo = new SimpleFileResult();
        queryDocInfo.setOwnerOid("aaaaa");
        queryDocInfo.setFileAimType(FileAimTypeEnum.STATIC_TEMPLATE.getFileAimType());
        when(docClient.getFileInfoFromDocOrTemplate(any())).thenReturn(queryDocInfo);
        SaasBizException saasBizException = Assert.expectThrows(SaasBizException.class, () -> compareService.embedCompareCreate(inputDTO));
        Assert.assertNotNull(saasBizException);
        Assert.assertEquals(saasBizException.getErrCode(), SaasBizResultCode.CAN_NOT_CREATE_COMPARE.getCode());

        //上传文件非当前人
        inputDTO.getOriginFile().setFileId("fileId_bbb");
        SimpleFileResult queryDocInfo2 = new SimpleFileResult();
        queryDocInfo2.setOwnerOid("OWNER_bbb");
        queryDocInfo2.setOperatorId("OPERATOR_bbb");
        queryDocInfo2.setFileAimType(FileAimTypeEnum.NORMAL_DOC.getFileAimType());
        when(docClient.getFileInfoFromDocOrTemplate("fileId_bbb")).thenReturn(queryDocInfo2);
        SaasBizException saasBizException2 = Assert.expectThrows(SaasBizException.class, () -> compareService.embedCompareCreate(inputDTO));
        Assert.assertNotNull(saasBizException2);
        Assert.assertEquals(saasBizException2.getErrCode(), SaasBizResultCode.CAN_NOT_CREATE_COMPARE.getCode());

        //上传HTML文件
        inputDTO.getOriginFile().setFileId("fileId_ccc");
        SimpleFileResult queryDocInfo3 = new SimpleFileResult();
        queryDocInfo3.setOwnerOid("OWNER_ccc");
        queryDocInfo3.setOperatorId("OPERATOR_ccc");
        queryDocInfo3.setFileAimType(FileAimTypeEnum.DYNAMIC_TEMPLATE.getFileAimType());
        when(docClient.getFileInfoFromDocOrTemplate("fileId_ccc")).thenReturn(queryDocInfo3);
        SaasBizException saasBizException3 = Assert.expectThrows(SaasBizException.class, () -> compareService.embedCompareCreate(inputDTO));
        Assert.assertNotNull(saasBizException3);
        Assert.assertEquals(saasBizException3.getErrCode(), SaasBizResultCode.FILE_TYPE_NOT_SUPPORT_COMPARE.getCode());
    }

    @Test(description = "合同比对查询状态")
    public void testQueryStatus() {
        String subjectOid = "subjectOid";
        String operatorOid = "operatorOid";
        String operatorGid = "operatorGid";
        String subjectGid = "subjectGid";
        String compareId = "compareId";

        AccountInfoDTO operatorAccountInfoDTO = new AccountInfoDTO();
        operatorAccountInfoDTO.setGid(operatorGid);
        AccountInfoDTO subjectAccountInfoDTO = new AccountInfoDTO();
        subjectAccountInfoDTO.setGid(subjectGid);
        IntelligentCompareQueryStatusOutputDTO outputDTO = new IntelligentCompareQueryStatusOutputDTO();
        outputDTO.setCompareStatus(CompareNotifyResultFacadeEnum.comparing.getCode());

        when(userCenterService.queryAccountInfoByOid(operatorOid)).thenReturn(operatorAccountInfoDTO);
        when(userCenterService.queryAccountInfoByOid(subjectOid)).thenReturn(subjectAccountInfoDTO);
        when(intelligentCompareClient.queryCompareStatus(compareId, operatorGid, subjectGid)).thenReturn(outputDTO);

        EmbedCompareQueryStatusInputDTO inputDTO = new EmbedCompareQueryStatusInputDTO();
        inputDTO.setCompareId(compareId);
        inputDTO.setOperatorOid(operatorOid);
        inputDTO.setSubjectOid(subjectOid);
        EmbedCompareQueryStatusOutputDTO res = compareService.queryCompareStatus(inputDTO);
        Assert.assertNotNull(res);
        Assert.assertEquals((int) res.getCompareStatus(), CompareNotifyResultFacadeEnum.comparing.getCode());
    }
}
