package com.timevale.saasbiz.service;

import com.timevale.contractapproval.facade.enums.ApprovalBizTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalStatusEnum;
import com.timevale.saasbiz.base.BasePowerMockTest;
import com.timevale.saasbiz.integration.approval.ApprovalTemplateClient;
import com.timevale.saasbiz.integration.approval.OldContractApprovalClient;
import com.timevale.saasbiz.integration.approval.ProcessApprovalClient;
import com.timevale.saasbiz.integration.approval.SealApprovalClient;
import com.timevale.saasbiz.integration.doc.DocClient;
import com.timevale.saasbiz.integration.itsm.ItsmClient;
import com.timevale.saasbiz.integration.process.ProcessClient;
import com.timevale.saasbiz.integration.process.ProcessRemarkClient;
import com.timevale.saasbiz.integration.usercenter.UserCenterClient;
import com.timevale.saasbiz.model.bean.approval.dto.*;
import com.timevale.saasbiz.model.bean.approval.dto.input.*;
import com.timevale.saasbiz.model.bean.approval.dto.task.ApprovalLogBizDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.constants.FunctionCodeConstants;
import com.timevale.contractapproval.facade.enums.ApprovalTypeEnum;
import com.timevale.contractapproval.facade.dto.ApprovalDTO;
import com.timevale.contractapproval.facade.output.VirtualStartApprovalOutput;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalMixFlowResultDTO;
import com.timevale.saasbiz.model.bean.approval.dto.task.ApprovalVirtualStartResultDTO;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.service.approval.ApprovalAdapter;
import com.timevale.saasbiz.service.approval.approvaloperate.strategy.approvaltype.impl.ContractApprovalHandleServiceImpl;
import com.timevale.saasbiz.service.approval.impl.ApprovalStartServiceImpl;
import com.timevale.saasbiz.service.approval.approvaloperate.strategy.approvaltype.factory.ApprovalTypeHandleServiceFactory;
import com.timevale.saasbiz.service.approval.approvaloperate.strategy.biztype.factory.ApprovalBizTypeHandleServiceFactory;
import com.timevale.saasbiz.service.helper.ProcessApprovalCheckHelper;
import com.timevale.saasbiz.service.process.ProcessBizRuleConfigService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import com.timevale.saasbiz.service.seal.SealManagerService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.timevale.contractapproval.facade.dto.ApprovalDetailDTO;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.api.mockito.PowerMockito;
import com.timevale.saasbiz.model.utils.CacheUtil;
import com.timevale.saasbiz.model.utils.IdsUtil;

/**
 * ApprovalStartServiceImpl 高分支覆盖率 mock 单测
 * 依赖全部 mock，数据尽量真实，覆盖主流程、异常、分支。
 */
@PrepareForTest({CacheUtil.class, IdsUtil.class})
public class ApprovalStartServiceMockTest extends BasePowerMockTest {

    @InjectMocks
    private ApprovalStartServiceImpl approvalStartService;
    @Mock private ApprovalTemplateClient approvalTemplateClient;
    @Mock private UserCenterService userCenterService;
    @Mock private UserCenterClient userCenterClient;
    @Mock private ProcessApprovalClient approvalClient;
    @Mock private ApprovalAdapter approvalAdapter;
    @Mock private SealApprovalClient sealApprovalClient;
    @Mock private DocClient docClient;
    @Mock private ItsmClient itsmClient;
    @Mock private OldContractApprovalClient oldContractClient;
    @Mock private ProcessClient processClient;
    @Mock private SaasCommonService saasCommonService;
    @Mock private ApprovalTypeHandleServiceFactory approvalTypeHandleServiceFactory;
    @Mock private ApprovalBizTypeHandleServiceFactory approvalBizTypeHandleServiceFactory;
    @Mock private ProcessBizRuleConfigService processBizRuleConfigService;
    @Mock private ProcessApprovalCheckHelper processApprovalCheckHelper;
    @Mock private ProcessRemarkClient processRemarkClient;
    @Mock private SealManagerService sealManagerService;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 startAvailableApprovalTemplate 主流程和空数据分支
     */
    @Test(description = "测试 startAvailableApprovalTemplate 主流程和空数据分支")
    public void testStartAvailableApprovalTemplate() {
        String subjectOid = "sub1";
        String operatorOid = "op1";
        String approvalTemplateType = "CONTRACT";
        Integer conditionType = 1;
        String conditionValue = "flow1";
        Long approvalTemplateId = 1L;
        String approvalTemplateCode = "approval-template-code";
        AccountInfoDTO subjectAccount = new AccountInfoDTO();
        subjectAccount.setGid("gid1");
        when(userCenterService.queryAccountInfoByOid(subjectOid)).thenReturn(subjectAccount);
        List<com.timevale.contractapproval.facade.dto.ApprovalTemplateDTO> templateList = new ArrayList<>();
        com.timevale.contractapproval.facade.dto.ApprovalTemplateDTO template = new com.timevale.contractapproval.facade.dto.ApprovalTemplateDTO();
        template.setApprovalTemplateId(approvalTemplateId);
        template.setApprovalTemplateCode(approvalTemplateCode);
        templateList.add(template);
        when(approvalTemplateClient.queryMatchConditionApprovalTemplate(any())).thenReturn(templateList);
        List<ApprovalTemplateDTO> result = approvalStartService.startAvailableApprovalTemplate(subjectOid, operatorOid, approvalTemplateType, conditionType, conditionValue);
        Assert.assertEquals(result.size(), 1);
        Assert.assertEquals(result.get(0).getApprovalTemplateId(), approvalTemplateCode);
        // 空数据分支
        when(approvalTemplateClient.queryMatchConditionApprovalTemplate(any())).thenReturn(Collections.emptyList());
        result = approvalStartService.startAvailableApprovalTemplate(subjectOid, operatorOid, approvalTemplateType, conditionType, conditionValue);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试 reStartAvailableApprovalTemplate 新老系统分支
     */
    @Test(description = "测试 reStartAvailableApprovalTemplate 新老系统分支")
    public void testReStartAvailableApprovalTemplate() {
        String subjectOid = "sub1";
        String operatorOid = "op1";
        String approvalCode = "apv1";
        AccountInfoDTO subjectAccount = new AccountInfoDTO();
        subjectAccount.setGid("gid1");
        when(userCenterService.queryAccountInfoByOid(subjectOid)).thenReturn(subjectAccount);
        // 老系统分支
        ApprovalIdAdaptResultDTO oldAdapt = new ApprovalIdAdaptResultDTO();
        oldAdapt.setFinalApprovalId("apv1");
        oldAdapt.setOldSystemData(true);
        when(approvalAdapter.approvalIdAdapt(approvalCode)).thenReturn(oldAdapt);
        when(userCenterService.queryAccountInfoByOid(operatorOid)).thenReturn(new AccountInfoDTO());
        when(userCenterService.getUserInSubjectTrueOid(anyString(), anyString())).thenReturn("trueOid");
        when(userCenterClient.subjectOidToOrganId(anyString())).thenReturn("org1");
        when(oldContractClient.availableApprovalTemplate(any())).thenReturn(Collections.emptyList());
        List<ApprovalTemplateDTO> result = approvalStartService.reStartAvailableApprovalTemplate(subjectOid, operatorOid, approvalCode);
        Assert.assertTrue(result.isEmpty());
        // 新系统分支
        ApprovalIdAdaptResultDTO newAdapt = new ApprovalIdAdaptResultDTO();
        newAdapt.setFinalApprovalId("apv2");
        newAdapt.setOldSystemData(false);
        when(approvalAdapter.approvalIdAdapt(approvalCode)).thenReturn(newAdapt);
        ApprovalDTO approvalDTO = new ApprovalDTO();
        approvalDTO.setSubjectGid("gid1");
        approvalDTO.setApprovalType("CONTRACT");
        approvalDTO.setConditionType(1);
        approvalDTO.setConditionValue("flow1");
        when(approvalClient.getApprovalByCode("apv2")).thenReturn(approvalDTO);
        when(approvalTemplateClient.queryMatchConditionApprovalTemplate(any())).thenReturn(Collections.emptyList());
        result = approvalStartService.reStartAvailableApprovalTemplate(subjectOid, operatorOid, approvalCode);
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试 preFlow 新老系统分支、审批id分支、无deptId分支
     */
    @Test(description = "测试 preFlow 新老系统分支、审批id分支、无deptId分支")
    public void testPreFlow() {
        ApprovalVirtualStartInputDTO inputDTO = new ApprovalVirtualStartInputDTO();
        inputDTO.setSubjectOid("sub1");
        inputDTO.setOperatorOid("op1");
        inputDTO.setApprovalTemplateCode("tpl1");
        inputDTO.setDeptId(null);
        inputDTO.setConditionType(1);
        inputDTO.setConditionValue("flow1");
        inputDTO.setApprovalCode("apv1");
        AccountInfoDTO subjectAccount = new AccountInfoDTO();
        subjectAccount.setGid("gid1");
        subjectAccount.setOid("sub1");
        subjectAccount.setName("企业A");
        when(userCenterService.queryAccountInfoByOid("sub1")).thenReturn(subjectAccount);
        // 老系统分支
        ApprovalIdAdaptResultDTO oldAdapt = new ApprovalIdAdaptResultDTO();
        oldAdapt.setFinalApprovalId("apv1");
        oldAdapt.setOldSystemData(true);
        when(approvalAdapter.approvalIdAdapt("apv1")).thenReturn(oldAdapt);
        List<ApprovalLogBizDTO> oldList = new ArrayList<>();
        oldList.add(new ApprovalLogBizDTO());
        when(userCenterClient.subjectOidToOrganId(anyString())).thenReturn("org1");
        when(oldContractClient.getFlowDefineDetailById(anyString(), anyString())).thenReturn(null);
        ApprovalVirtualStartResultDTO result = approvalStartService.preFlow(inputDTO);
        Assert.assertNotNull(result);
        // 新系统分支
        oldAdapt.setOldSystemData(false);
        when(approvalAdapter.approvalIdAdapt("apv1")).thenReturn(oldAdapt);
        ApprovalDTO approvalDTO = new ApprovalDTO();
        approvalDTO.setInitiatorDeptId("d1");
        approvalDTO.setConditionType(1);
        approvalDTO.setConditionValue("flow1");
        when(approvalClient.getApprovalByCode("apv1")).thenReturn(approvalDTO);
        AccountInfoDTO operatorAccount = new AccountInfoDTO();
        operatorAccount.setOid("op1");
        operatorAccount.setGid("gid2");
        operatorAccount.setName("张三");
        when(userCenterService.queryAccountInfoByOid("op1")).thenReturn(operatorAccount);
        VirtualStartApprovalOutput output = new VirtualStartApprovalOutput();
        when(approvalClient.virtualStartApprovalDetail(any())).thenReturn(output);
        ApprovalVirtualStartResultDTO res = approvalStartService.preFlow(inputDTO);
        Assert.assertNotNull(res);
    }

    /**
     * 测试 restart 新老系统分支、审批不存在、无审批人分支
     */
    @Test(description = "测试 restart 新老系统分支、审批不存在、无审批人分支")
    public void testRestart() {
        String oldSystemApprovalId = "111";
        ApprovalRestartInputDTO inputDTO = new ApprovalRestartInputDTO();
        inputDTO.setSubjectOid("sub1");
        inputDTO.setOperatorOid("op1");
        inputDTO.setApprovalId(oldSystemApprovalId);
        inputDTO.setApprovalTemplateId("tpl1");
        AccountInfoDTO subjectAccount = new AccountInfoDTO();
        subjectAccount.setGid("gid1");
        subjectAccount.setOid("sub1");
        subjectAccount.setName("企业A");
        when(userCenterService.queryAccountInfoByOid("sub1")).thenReturn(subjectAccount);
        AccountInfoDTO operatorAccount = new AccountInfoDTO();
        operatorAccount.setOid("op1");
        operatorAccount.setGid("gid2");
        operatorAccount.setName("张三");
        when(userCenterService.queryAccountInfoByOid("op1")).thenReturn(operatorAccount);
        // 老系统分支
        ApprovalIdAdaptResultDTO oldAdapt = new ApprovalIdAdaptResultDTO();
        oldAdapt.setFinalApprovalId("111");
        oldAdapt.setNewSystemData(false);
        when(approvalAdapter.approvalIdAdapt(oldSystemApprovalId)).thenReturn(oldAdapt);
        doNothing().when(oldContractClient).restart(any());
        String res = approvalStartService.restart(inputDTO);
        Assert.assertNull(res);
        // 新系统分支-审批不存在
        String newSystemApprovalId = "apv1";
        inputDTO.setApprovalId(newSystemApprovalId);
        ApprovalIdAdaptResultDTO newAdapt = new ApprovalIdAdaptResultDTO();
        newAdapt.setFinalApprovalId(newSystemApprovalId);
        newAdapt.setNewSystemData(true);
        when(approvalAdapter.approvalIdAdapt(newSystemApprovalId)).thenReturn(newAdapt);
        when(approvalClient.getApprovalByCode(anyString())).thenReturn(null);
        try {
            approvalStartService.restart(inputDTO);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().contains("审批不存在"));
        }
    }

    /**
     * 测试 mixFlow 合同审批新老系统、审批不存在、审批类型异常
     */
    @Test(description = "测试 mixFlow 合同审批新老系统、审批不存在、审批类型异常")
    public void testMixFlow() {
        String approvalId = "1111";
        ApprovalMixFlowInputDTO inputDTO = new ApprovalMixFlowInputDTO();
        inputDTO.setApprovalType(ApprovalTypeEnum.CONTRACT.getCode());
        inputDTO.setApprovalId(approvalId);
        inputDTO.setOperatorOid("op1");
        AccountInfoDTO operator = new AccountInfoDTO();
        operator.setGid("gid2");
        when(userCenterService.getFatUserAccountDetailByOid("op1")).thenReturn(operator);
        // 老系统分支
        ApprovalIdAdaptResultDTO oldAdapt = new ApprovalIdAdaptResultDTO();
        oldAdapt.setFinalApprovalId(approvalId);
        oldAdapt.setNewSystemData(false);
        when(approvalAdapter.approvalIdAdapt(approvalId)).thenReturn(oldAdapt);
        List<ApprovalDetailWithLogsDTO> oldList = new ArrayList<>();
        oldList.add(new ApprovalDetailWithLogsDTO());
        ApprovalMixFlowResultDTO result = approvalStartService.mixFlow(inputDTO);
        Assert.assertNotNull(result);
        // 新系统分支-审批不存在
        oldAdapt.setNewSystemData(true);
        when(approvalAdapter.approvalIdAdapt(approvalId)).thenReturn(oldAdapt);
        when(approvalClient.getApprovalDetailWithLog(approvalId)).thenReturn(null);
        result = approvalStartService.mixFlow(inputDTO);
        Assert.assertNotNull(result);
        // 审批类型异常
        inputDTO.setApprovalType("not-exist");
        try {
            approvalStartService.mixFlow(inputDTO);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().contains("approval type not exist"));
        }
    }

    /**
     * 测试 operateDetail 权限分支、无权查看、可审批、可撤回、可重新发起、可签署、可评论等分支
     */
    @Test(description = "测试 operateDetail 权限分支、无权查看、可审批、可撤回、可重新发起、可签署、可评论等分支")
    public void testOperateDetail() {
        ApprovalOperateDetailInputDTO inputDTO = new ApprovalOperateDetailInputDTO();
        inputDTO.setApprovalId("apv1");
        inputDTO.setOperatorOid("op1");
        inputDTO.setSubjectOid("sub1");
        AccountInfoDTO operatorAccount = new AccountInfoDTO();
        operatorAccount.setOid("op1");
        operatorAccount.setGid("gid2");
        when(userCenterService.queryAccountInfoByOid("op1")).thenReturn(operatorAccount);
        ApprovalDetailDTO approvalDetailDTO = new ApprovalDetailDTO();
        approvalDetailDTO.setApprovalCode("apv1");
        approvalDetailDTO.setApprovalType("CONTRACT");
        approvalDetailDTO.setApprovalStatus(ApprovalStatusEnum.APPROVALING.getCode());
        approvalDetailDTO.setBizId("biz1");
        approvalDetailDTO.setBizType(ApprovalBizTypeEnum.STANDARD.getType());
        approvalDetailDTO.setSubjectGid("gid1");
        approvalDetailDTO.setSubjectOid("sub1");
        approvalDetailDTO.setInitiatorGid("gid2");
        when(approvalClient.getApprovalDetailByCode("apv1")).thenReturn(approvalDetailDTO);
        ApprovalViewCheckResultDTO viewCheckResultDTO = new ApprovalViewCheckResultDTO();
        viewCheckResultDTO.setCanView(true);
        viewCheckResultDTO.setCanViewProcess(true);

        ContractApprovalHandleServiceImpl contractApprovalHandleService = mock(ContractApprovalHandleServiceImpl.class);
        when(approvalTypeHandleServiceFactory.getService(anyString())).thenReturn(contractApprovalHandleService);
        when(contractApprovalHandleService.viewCheck(any())).thenReturn(viewCheckResultDTO);
        when(contractApprovalHandleService.viewCheck(any())).thenReturn(viewCheckResultDTO);
        AccountInfoDTO subjectAccount = new AccountInfoDTO();
        subjectAccount.setOid("sub1");
        when(userCenterService.queryAccountInfoByOid("sub1")).thenReturn(subjectAccount);
        when(approvalBizTypeHandleServiceFactory.getService(anyInt())).thenReturn(null);
        when(processClient.getByProcessId(anyString())).thenReturn(null);
        when(saasCommonService.querySupportFunctions(anyString(), anyString(), anyList())).thenReturn(Arrays.asList(FunctionCodeConstants.CONTRACT_SUMMARY));
        ApprovalOperateDetailDTO result = approvalStartService.operateDetail(inputDTO);
        Assert.assertNotNull(result);
        // 无权查看分支
        viewCheckResultDTO.setCanView(false);
        when(contractApprovalHandleService.viewCheck(any())).thenReturn(viewCheckResultDTO);
        try {
            approvalStartService.operateDetail(inputDTO);
            Assert.fail();
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().contains(SaasBizResultCode.APPROVAL_NO_PERMISSION.getMessage()));
        }
    }

    /**
     * 测试 queryApprovalSupportFunctions 缓存分支、无缓存分支
     */
    @Test(description = "测试 queryApprovalSupportFunctions 缓存分支、无缓存分支")
    public void testQueryApprovalSupportFunctions() {
        String tenantId = "t1";
        String clientId = "c1";
        // 缓存分支
        mockStaticCacheUtil("1,2,3");
        List<String> result = approvalStartService.queryApprovalSupportFunctions(tenantId, clientId);
        Assert.assertEquals(result.size(), 3);
        // 无缓存分支
        mockStaticCacheUtil(null);
        when(saasCommonService.querySupportFunctions(anyString(), anyString(), anyList())).thenReturn(Arrays.asList("1", "2"));
        result = approvalStartService.queryApprovalSupportFunctions(tenantId, clientId);
        Assert.assertEquals(result.size(), 2);
    }

    // 只 mock CacheUtil 静态方法
    private void mockStaticCacheUtil(String cacheData) {
        PowerMockito.mockStatic(CacheUtil.class);
        PowerMockito.when(CacheUtil.degradeGet(anyString())).thenReturn(cacheData);
        PowerMockito.doNothing().when(CacheUtil.class);
        CacheUtil.degradeSet(anyString(), any(), anyInt(), any());
        // 不再 mock IdsUtil.getIdList，直接调用真实方法即可
    }
} 