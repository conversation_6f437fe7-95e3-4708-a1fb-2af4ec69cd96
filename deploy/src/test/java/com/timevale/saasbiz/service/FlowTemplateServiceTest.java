package com.timevale.saasbiz.service;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.input.biz.ListFlowTemplateBizInputDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.output.ListFlowTemplateBizOutputDTO;
import com.timevale.saasbiz.service.flowtemplate.FlowTemplateBizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * FlowTemplateServiceTest
 *
 * <AUTHOR>
 * @since 2025/1/3 下午3:15
 */
@JsonDataIgnore
public class FlowTemplateServiceTest extends BaseServiceTest {

    @Autowired
    private FlowTemplateBizService flowTemplateBizService;

    @Test
    public void testFlowTemplateNotSupport(){

        ListFlowTemplateBizInputDTO inputDTO = new ListFlowTemplateBizInputDTO();
        inputDTO.setTenantId("5a3de2b6835d4d37afdcabbc69fc232b");
        inputDTO.setResourceTenantId("5a3de2b6835d4d37afdcabbc69fc232b");
        inputDTO.setOperatorId("4fdeb4e427ac4d73a695b0ec176e0318");
        inputDTO.setPageNum(1);
        inputDTO.setPageSize(10);
        inputDTO.setContainsDynamic(true);
        inputDTO.setContainsShared(true);
        ListFlowTemplateBizOutputDTO list = flowTemplateBizService.list(inputDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list.getList()));
    }
}
