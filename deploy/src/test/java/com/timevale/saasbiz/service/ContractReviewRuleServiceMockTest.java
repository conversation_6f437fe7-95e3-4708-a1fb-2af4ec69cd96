package com.timevale.saasbiz.service;

import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.model.bean.contractreview.RuleDelOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.input.*;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.DetailRuleListOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.integration.contractreview.EpaasContractReviewClient;
import com.timevale.saasbiz.service.contractreview.impl.ContractReviewRuleServiceImpl;
import com.timevale.sterna.contract.review.client.dto.req.ReviewRuleRiskReq;
import com.timevale.sterna.contract.review.client.dto.res.rule.*;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.DetailRuleOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.FileAnalysisOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rule.bo.ListRuleBO;
import com.timevale.sterna.contract.review.client.dto.res.RuleDelResp;
import com.timevale.sterna.contract.review.client.dto.res.RuleDelListResp;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import com.timevale.saasbiz.model.bean.contractreview.rule.dto.output.ReviewResultRuleDTO;

import java.util.List;
import java.util.ArrayList;

@JsonDataIgnore
public class ContractReviewRuleServiceMockTest extends BaseMockTest {

    @Mock
    private UserCenterService userCenterService;
    @Mock
    private EpaasContractReviewClient epaasContractReviewClient;

    @InjectMocks
    private ContractReviewRuleServiceImpl contractReviewRuleServiceImpl;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test(description = "测试ruleAnalysis方法")
    public void testRuleAnalysis() {
        // Arrange
        AnalysisRuleInputDTO inputDTO = new AnalysisRuleInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleRemark("remark");
        inputDTO.setRuleId("rule1");
        inputDTO.setGroupId("group1");

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        ReviewRuleIdResp resp = new ReviewRuleIdResp();
        resp.setRuleId("resultRuleId");
        when(epaasContractReviewClient.ruleAnalysis(Matchers.any())).thenReturn(resp);

        // Act
        String result = contractReviewRuleServiceImpl.ruleAnalysis(inputDTO);

        // Assert
        assertEquals("resultRuleId", result);
    }

    @Test(description = "测试ruleDetail方法，校验规则详情获取逻辑")
    public void testRuleDetail() {
        RuleIdInputDTO inputDTO = new RuleIdInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleId("rule1");

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        ReviewRuleResp reviewRuleResp = new ReviewRuleResp();

        List<ReviewRuleDataListResp> dataList = new ArrayList<>();
        List<ReviewRuleRiskReq> riskReqs = new ArrayList<>();
        ReviewRuleRiskReq req = new ReviewRuleRiskReq();
        req.setRuleDescription("Description1");
        riskReqs.add(req);

        ReviewRuleDataListResp resp = new ReviewRuleDataListResp();
        resp.setRuleId("1");
        resp.setRuleName("2");
        resp.setReviewRuleRisks(riskReqs);

        dataList.add(resp);
        reviewRuleResp.setDataList(dataList);
        when(epaasContractReviewClient.ruleDetail(any())).thenReturn(reviewRuleResp);

        DetailRuleOutputDTO resultDTO = contractReviewRuleServiceImpl.ruleDetail(inputDTO);
        List<DetailRuleListOutputDTO> resultResult = resultDTO.getDataList();
        if (!resultResult.isEmpty()) {
            DetailRuleListOutputDTO result = resultResult.get(0);
            assertEquals("2", result.getRuleName());
            assertEquals("1", result.getRuleId());
        }
    }

    @Test(description = "测试ruleSave方法，校验规则保存逻辑")
    public void testRuleSave() {
        SaveRuleInputDTO inputDTO = new SaveRuleInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleName("ruleName");
        inputDTO.setRuleRemark("remark");
        inputDTO.setRiskLevel("high");
        inputDTO.setRuleId("rule1");
        inputDTO.setGroupId("group1");

        List<SaveRuleRiskInputDTO> reviewRuleRisks = new ArrayList<>();
        SaveRuleRiskInputDTO dto = new SaveRuleRiskInputDTO();
        dto.setRuleId("rule1");
        reviewRuleRisks.add(dto);
        inputDTO.setReviewRuleRisks(reviewRuleRisks);

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        ReviewRuleIdResp resp = new ReviewRuleIdResp();
        resp.setRuleId("savedRuleId");
        when(epaasContractReviewClient.ruleSave(any())).thenReturn(resp);

        String result = contractReviewRuleServiceImpl.ruleSave(inputDTO);
        assertEquals("savedRuleId", result);
    }

    @Test(description = "测试ruleList方法，校验规则列表查询逻辑")
    public void testRuleList() {
        ListRuleInputDTO inputDTO = new ListRuleInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setInventoryId("inv1");
        inputDTO.setGroupId("group1");

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        ReviewRuleListResp resp = new ReviewRuleListResp();
        resp.setRuleId("rule1");
        when(epaasContractReviewClient.ruleList(any())).thenReturn(java.util.Collections.singletonList(resp));

        List<ListRuleBO> result = contractReviewRuleServiceImpl.ruleList(inputDTO);
        assertFalse(result.isEmpty());
        assertEquals("rule1", result.get(0).getRuleId());
    }

    @Test(description = "测试ruleDel方法，校验规则删除逻辑")
    public void testRuleDel() {
        DelRuleInputDTO inputDTO = new DelRuleInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleIds(java.util.Collections.singletonList("rule1"));

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        doNothing().when(epaasContractReviewClient).ruleDel(any());
        contractReviewRuleServiceImpl.ruleDel(inputDTO);
        verify(epaasContractReviewClient, times(1)).ruleDel(any());
    }

    @Test(description = "测试ruleDelConfirm方法，校验规则删除前确认逻辑")
    public void testRuleDelConfirm() {
        DelRuleInputDTO inputDTO = new DelRuleInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleIds(java.util.Collections.singletonList("rule1"));

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        // Mock a non-null response with some data
        RuleDelResp mockResp = new RuleDelResp();
        List<RuleDelListResp> mockList = new ArrayList<>();
        RuleDelListResp mockItem = new RuleDelListResp();
        mockItem.setId("rule1");
        mockItem.setContent("Test rule content");
        mockList.add(mockItem);
        mockResp.setList(mockList);
        when(epaasContractReviewClient.ruleDelConfirm(any())).thenReturn(mockResp);

        RuleDelOutputDTO result = contractReviewRuleServiceImpl.ruleDelConfirm(inputDTO);
        assertNotNull(result);
        assertNotNull(result.getList());
        assertFalse(result.getList().isEmpty());
        assertEquals("rule1", result.getList().get(0).getId());
        assertEquals("Test rule content", result.getList().get(0).getContent());
    }

    @Test(description = "测试ruleCacheDel方法，校验规则缓存删除逻辑")
    public void testRuleCacheDel() {
        RuleIdInputDTO inputDTO = new RuleIdInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleId("rule1");

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        doNothing().when(epaasContractReviewClient).ruleCacheDel(any());
        contractReviewRuleServiceImpl.ruleCacheDel(inputDTO);
        verify(epaasContractReviewClient, times(1)).ruleCacheDel(any());
    }

    @Test(description = "测试ruleExample方法，校验规则示例获取逻辑")
    public void testRuleExample() {
        when(epaasContractReviewClient.ruleExample()).thenReturn(java.util.Arrays.asList("ex1", "ex2"));
        java.util.List<String> result = contractReviewRuleServiceImpl.ruleExample();
        assertEquals(2, result.size());
        assertEquals("ex1", result.get(0));
    }

    @Test(description = "测试ruleCopy方法，校验规则复制逻辑")
    public void testRuleCopy() {
        RuleCopyInputDTO inputDTO = new RuleCopyInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleId("rule1");
        inputDTO.setRuleName("copyName");

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        ReviewRuleIdResp resp = new ReviewRuleIdResp();
        resp.setRuleId("copyRuleId");
        when(epaasContractReviewClient.ruleCopy(any())).thenReturn(resp);

        String result = contractReviewRuleServiceImpl.ruleCopy(inputDTO);
        assertEquals("copyRuleId", result);
    }

    @Test(description = "测试ruleFileAnalysis方法，校验文件规则分析逻辑")
    public void testRuleFileAnalysis() {
        AnalysisRuleFileInputDTO inputDTO = new AnalysisRuleFileInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setFileKey("fileKey");
        inputDTO.setGroupId("group1");

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        ReviewRuleIdResp resp = new ReviewRuleIdResp();
        resp.setRuleId("fileAnalysisRuleId");
        when(epaasContractReviewClient.ruleFileAnalysis(any())).thenReturn(resp);

        String result = contractReviewRuleServiceImpl.ruleFileAnalysis(inputDTO);
        assertEquals("fileAnalysisRuleId", result);
    }

    @Test(description = "测试ruleFileAnalysisResult方法，校验文件规则分析结果获取逻辑")
    public void testRuleFileAnalysisResult() {
        RuleIdInputDTO inputDTO = new RuleIdInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleId("rule1");

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        RuleFileAnalysisResp resp = new RuleFileAnalysisResp();
        when(epaasContractReviewClient.ruleFileAnalysisResult(any())).thenReturn(resp);

        FileAnalysisOutputDTO result = contractReviewRuleServiceImpl.ruleFileAnalysisResult(inputDTO);
        assertNotNull(result);
    }

    @Test(description = "测试ruleVerifyResult方法，校验规则生效验证结果获取逻辑")
    public void testRuleVerifyResult() {
        RuleIdInputDTO inputDTO = new RuleIdInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleId("rule1");

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewResultResp reviewResultResp = new com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewResultResp();
        when(epaasContractReviewClient.ruleVerifyResult(any())).thenReturn(reviewResultResp);

        ReviewResultRuleDTO result = contractReviewRuleServiceImpl.ruleVerifyResult(inputDTO);
        assertNotNull(result);
    }

    @Test(description = "测试ruleBatchSave方法，校验规则批量保存逻辑")
    public void testRuleBatchSave() {
        BatchSaveRuleInputDTO inputDTO = new BatchSaveRuleInputDTO();
        inputDTO.setAccountId("acc1");
        inputDTO.setTenantId("ten1");
        inputDTO.setRuleList(java.util.Collections.emptyList());

        AccountDetailDTO account = new AccountDetailDTO();
        account.setOid("acc1");
        account.setGid("gid1");
        AccountDetailDTO tenant = new AccountDetailDTO();
        tenant.setOid("ten1");
        tenant.setGid("gid2");

        when(userCenterService.queryAccountDetailByOid("acc1")).thenReturn(account);
        when(userCenterService.queryAccountDetailByOid("ten1")).thenReturn(tenant);

        doNothing().when(epaasContractReviewClient).ruleBatchSave(any());
        contractReviewRuleServiceImpl.ruleBatchSave(inputDTO);
        verify(epaasContractReviewClient, times(1)).ruleBatchSave(any());
    }
} 