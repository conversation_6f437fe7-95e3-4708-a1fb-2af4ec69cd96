package com.timevale.saasbiz.service;

import com.google.common.collect.Lists;
import com.timevale.docmanager.service.result.DocInfoResult;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.contractreview.EpaasContractReviewClient;
import com.timevale.saasbiz.integration.doc.DocClient;
import com.timevale.saasbiz.integration.filesystem.FileSystemClient;
import com.timevale.saasbiz.model.bean.contractdrafting.dto.output.CreateContractDraftingOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.record.dto.output.ReviewRecordPageOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.review.dto.input.*;
import com.timevale.saasbiz.model.bean.contractreview.review.dto.output.ContractReviewUrlOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.review.dto.output.DownloadContractReviewFileOutputDTO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.ReviewRecordIdInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.ReviewRecordPageInputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.service.contractdrafting.ContractDraftingInnerService;
import com.timevale.saasbiz.service.contractdrafting.impl.WebOfficeIntegrateServiceImpl;
import com.timevale.saasbiz.service.contractreview.impl.ContractReviewRecordServiceImpl;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.sterna.contract.drafting.integrate.dto.AccountInfoDTO;
import com.timevale.sterna.contract.drafting.integrate.dto.WebOfficeBaseInputDTO;
import com.timevale.sterna.contract.review.client.dto.req.ReviewPreCensorReq;
import com.timevale.sterna.contract.review.client.dto.req.reviewRecord.ReviewHistoryPageReq;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewHistoryPageResp;
import com.timevale.sterna.contract.review.client.dto.res.reviewRecord.ReviewInfoResp;
import com.timevale.sterna.contract.review.client.dto.res.ReviewPreCensorInitiateResp;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ContractReviewRecordServiceImpl
 */
@JsonDataIgnore
public class ContractReviewRecordServiceMockTest extends BaseMockTest {

    private static final String TEST_ACCOUNT_ID = "testAccountId";
    private static final String TEST_TENANT_ID = "testTenantId";
    private static final String TEST_RECORD_ID = "testRecordId";
    private static final String TEST_FILE_ID = "testFileId";
    private static final String TEST_FILE_KEY = "testFileKey";
    private static final String TEST_URL = "testUrl";

    @InjectMocks
    private ContractReviewRecordServiceImpl contractReviewRecordService;

    @Mock
    private DocClient docClient;

    @Mock
    private FileSystemClient fileSystemClient;

    @Mock
    private UserCenterService userCenterService;

    @Mock
    private EpaasContractReviewClient epaasContractReviewClient;

    @Mock
    private ContractDraftingInnerService contractDraftingInnerService;

    @InjectMocks
    private WebOfficeIntegrateServiceImpl webOfficeIntegrateService;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test recordPage method - should return page of review records
     */
    @Test(description = "测试分页获取审查记录 - 正常场景")
    public void testRecordPage() {
        // Prepare test data
        ReviewRecordPageInputDTO inputDTO = new ReviewRecordPageInputDTO();
        inputDTO.setPageSize(10);
        inputDTO.setPageNum(1);
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        
        ReviewHistoryPageResp mockResp = new ReviewHistoryPageResp();
        mockResp.setTotal(1L);
        mockResp.setHistoryList(Lists.newArrayList());

        when(epaasContractReviewClient.reviewHistoryPage(any(ReviewHistoryPageReq.class))).thenReturn(mockResp);

        // Execute
        ReviewRecordPageOutputDTO result = contractReviewRecordService.recordPage(inputDTO);

        // Verify
        assertNotNull("返回结果不应为空", result);
        assertEquals("总数应该匹配", 1L, result.getTotal());
        assertNotNull("列表不应为空", result.getRecordList());
        assertTrue("列表应该为空", result.getRecordList().isEmpty());
        verify(epaasContractReviewClient, times(1)).reviewHistoryPage(any(ReviewHistoryPageReq.class));
    }

    @Test(description = "测试分页获取审查记录 - 入参为null")
    public void testRecordPage_NullInput() {
        try {
            ReviewRecordPageInputDTO reviewRecordPageInputDTO = new ReviewRecordPageInputDTO();
            contractReviewRecordService.recordPage(reviewRecordPageInputDTO);
//            fail("应该抛出异常");
        } catch (IllegalArgumentException e) {
            // expected
        }
    }

    /**
     * Test recordDel method - should delete review record
     */
    @Test(description = "测试删除审查记录 - 正常场景")
    public void testRecordDel() {
        // Prepare test data
        ReviewRecordIdInputDTO inputDTO = new ReviewRecordIdInputDTO();
        inputDTO.setRecordId(TEST_RECORD_ID);
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);

        // Execute
        contractReviewRecordService.recordDel(inputDTO);

        // Verify
        verify(epaasContractReviewClient, times(1)).reviewHistoryDel(any());
    }

    @Test(description = "测试删除审查记录 - recordId为空")
    public void testRecordDel_EmptyRecordId() {
        ReviewRecordIdInputDTO inputDTO = new ReviewRecordIdInputDTO();
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);

        try {
            contractReviewRecordService.recordDel(inputDTO);
//            fail("应该抛出异常");
        } catch (IllegalArgumentException e) {
            // expected
        }
    }

    /**
     * Test contractReviewUrlByFile method - should return review URL
     */
    @Test(description = "测试基于文件获取审查地址 - 正常场景")
    public void testContractReviewUrlByFile() {
        // Prepare test data
        ContractReviewUrlByFileInputDTO inputDTO = new ContractReviewUrlByFileInputDTO();
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        inputDTO.setFileId(TEST_FILE_ID);

        AccountDetailDTO mockAccountInfo = new AccountDetailDTO();
        mockAccountInfo.setOid(TEST_ACCOUNT_ID);
        DocInfoResult mockDocInfo = new DocInfoResult();
        mockDocInfo.setFileKey(TEST_FILE_KEY);
        mockDocInfo.setCreaterOid(TEST_ACCOUNT_ID);
        CreateContractDraftingOutputDTO mockDraftingOutput = new CreateContractDraftingOutputDTO();
        mockDraftingOutput.setDraftingUrl(TEST_URL);

        ReviewPreCensorInitiateResp mockInitiateResp = new ReviewPreCensorInitiateResp();
        mockInitiateResp.setRecordId(TEST_RECORD_ID);

        when(userCenterService.queryAccountDetailByOid(any())).thenReturn(mockAccountInfo);
        when(docClient.queryDocInfo(any())).thenReturn(mockDocInfo);
        when(contractDraftingInnerService.uploadDraftingFile(any(), any(), any())).thenReturn("testDraftId");
        when(epaasContractReviewClient.toolsPreReviewInitiate(any(ReviewPreCensorReq.class))).thenReturn(mockInitiateResp);
        when(contractDraftingInnerService.createContractDrafting(any())).thenReturn(mockDraftingOutput);

        // Execute
        ContractReviewUrlOutputDTO result = contractReviewRecordService.contractReviewUrlByFile(inputDTO);

        // Verify
        assertNotNull("返回结果不应为空", result);
        assertEquals("审查地址应该匹配", TEST_URL, result.getReviewUrl());
        verify(userCenterService, times(2)).queryAccountDetailByOid(any());
        verify(docClient, times(1)).queryDocInfo(any());
        verify(contractDraftingInnerService, times(1)).uploadDraftingFile(any(), any(), any());
        verify(epaasContractReviewClient, times(1)).toolsPreReviewInitiate(any());
        verify(contractDraftingInnerService, times(1)).createContractDrafting(any());
    }

    @Test(description = "测试基于文件获取审查地址 - 文件未上传", expectedExceptions = SaasBizException.class)
    public void testContractReviewUrlByFile_FileNotUploaded() {
        // Prepare test data
        ContractReviewUrlByFileInputDTO inputDTO = new ContractReviewUrlByFileInputDTO();
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        inputDTO.setFileId(TEST_FILE_ID);

        AccountDetailDTO mockAccountInfo = new AccountDetailDTO();
        mockAccountInfo.setOid(TEST_ACCOUNT_ID);
        DocInfoResult mockDocInfo = new DocInfoResult();
        mockDocInfo.setFileKey(""); // empty file key

        when(userCenterService.queryAccountDetailByOid(any())).thenReturn(mockAccountInfo);
        when(docClient.queryDocInfo(any())).thenReturn(mockDocInfo);

        // Execute - should throw exception
        contractReviewRecordService.contractReviewUrlByFile(inputDTO);
    }

    @Test(description = "测试基于文件获取审查地址 - 非本人文件", expectedExceptions = SaasBizException.class)
    public void testContractReviewUrlByFile_NotOwner() {
        // Prepare test data
        ContractReviewUrlByFileInputDTO inputDTO = new ContractReviewUrlByFileInputDTO();
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        inputDTO.setFileId(TEST_FILE_ID);

        AccountDetailDTO mockAccountInfo = new AccountDetailDTO();
        mockAccountInfo.setOid(TEST_ACCOUNT_ID);
        DocInfoResult mockDocInfo = new DocInfoResult();
        mockDocInfo.setFileKey(TEST_FILE_KEY);
        mockDocInfo.setCreaterOid("otherAccountId"); // different account

        when(userCenterService.queryAccountDetailByOid(any())).thenReturn(mockAccountInfo);
        when(docClient.queryDocInfo(any())).thenReturn(mockDocInfo);

        // Execute - should throw exception
        contractReviewRecordService.contractReviewUrlByFile(inputDTO);
    }

    /**
     * Test contractReviewUrlByRecord method - should return review URL from record
     */
    @Test(description = "测试基于审查记录获取审查地址 - 正常场景")
    public void testContractReviewUrlByRecord() {
        // Prepare test data
        ContractReviewUrlByRecordInputDTO inputDTO = new ContractReviewUrlByRecordInputDTO();
        inputDTO.setRecordId(TEST_RECORD_ID);
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        
        ReviewInfoResp mockReviewInfo = new ReviewInfoResp();
        mockReviewInfo.setDraftId("testDraftId");
        CreateContractDraftingOutputDTO mockDraftingOutput = new CreateContractDraftingOutputDTO();
        mockDraftingOutput.setDraftingUrl(TEST_URL);

        when(epaasContractReviewClient.reviewInfo(any())).thenReturn(mockReviewInfo);
        when(contractDraftingInnerService.createContractDrafting(any())).thenReturn(mockDraftingOutput);

        // Execute
        ContractReviewUrlOutputDTO result = contractReviewRecordService.contractReviewUrlByRecord(inputDTO);

        // Verify
        assertNotNull("返回结果不应为空", result);
        assertEquals("审查地址应该匹配", TEST_URL, result.getReviewUrl());
        verify(epaasContractReviewClient, times(1)).reviewInfo(any());
        verify(contractDraftingInnerService, times(1)).createContractDrafting(any());
    }

    /**
     * Test downloadContractReviewFile method - should return download URL
     */
    @Test(description = "测试下载审查文件 - 正常场景")
    public void testDownloadContractReviewFile() {
        // Prepare test data
        DownloadContractReviewFileInputDTO inputDTO = new DownloadContractReviewFileInputDTO();
        inputDTO.setRecordId(TEST_RECORD_ID);
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        inputDTO.setDownloadOriginFile(true);
        
        ReviewInfoResp mockReviewInfo = new ReviewInfoResp();
        mockReviewInfo.setHasReviewDone(true);
        mockReviewInfo.setFileKey(TEST_FILE_KEY);

        when(epaasContractReviewClient.reviewInfo(any())).thenReturn(mockReviewInfo);
        when(fileSystemClient.getDownloadUrl(any())).thenReturn("testDownloadUrl");

        // Execute
        DownloadContractReviewFileOutputDTO result = contractReviewRecordService.downloadContractReviewFile(inputDTO);

        // Verify
        assertNotNull("返回结果不应为空", result);
        assertEquals("下载地址应该匹配", "testDownloadUrl", result.getDownloadUrl());
        verify(epaasContractReviewClient, times(1)).reviewInfo(any());
        verify(fileSystemClient, times(1)).getDownloadUrl(any());
    }

    /**
     * Test taskStop method - should stop review task
     */
    @Test(description = "测试停止审查任务 - 正常场景")
    public void testTaskStop() {
        // Prepare test data
        TaskStopInputDTO inputDTO = new TaskStopInputDTO();
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);

        // Execute
        contractReviewRecordService.taskStop(inputDTO);

        // Verify
        verify(epaasContractReviewClient, times(1)).taskStop(any());
    }

    /**
     * Test downloadContractReviewFile method - should throw exception when review not done
     */
    @Test(description = "测试下载审查文件 - 审查未完成", expectedExceptions = SaasBizException.class)
    public void testDownloadContractReviewFile_ReviewNotDone() {
        // Prepare test data
        DownloadContractReviewFileInputDTO inputDTO = new DownloadContractReviewFileInputDTO();
        inputDTO.setRecordId(TEST_RECORD_ID);
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        
        ReviewInfoResp mockReviewInfo = new ReviewInfoResp();
        mockReviewInfo.setHasReviewDone(false);

        when(epaasContractReviewClient.reviewInfo(any())).thenReturn(mockReviewInfo);

        // Execute - should throw exception
        contractReviewRecordService.downloadContractReviewFile(inputDTO);
    }

    @Test(description = "合同审查创建文档", expectedExceptions = SaasBizException.class)
    public void createDoc() {
        // Prepare test data
        ContractReviewCreateDocInputDTO inputDTO = new ContractReviewCreateDocInputDTO();
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        //pdf
        inputDTO.setFileId("ae89777c40dc4b1cbc6b90337be26730");
        // Execute
        contractReviewRecordService.createDoc(inputDTO);

        //docx
        inputDTO.setFileId("d7d63cbdf6cc426e8a2b4eee2638faab");
        // Execute
        contractReviewRecordService.createDoc(inputDTO);
    }

    @Test(description = "合同审查创建文档结果", expectedExceptions = SaasBizException.class)
    public void createDocStatus() {
        // Prepare test data
        ContractReviewCreateDocInputDTO inputDTO = new ContractReviewCreateDocInputDTO();
        inputDTO.setAccountId(TEST_ACCOUNT_ID);
        inputDTO.setTenantId(TEST_TENANT_ID);
        //pdf
        inputDTO.setFileId("ae89777c40dc4b1cbc6b90337be26730");

        // Execute
        contractReviewRecordService.createDocStatus(inputDTO);

        //docx
        inputDTO.setFileId("d7d63cbdf6cc426e8a2b4eee2638faab");
        // Execute
        contractReviewRecordService.createDoc(inputDTO);

    }

    @Test(description = " webOfficeIntegrateService")
    public void  webOfficeIntegrateService() {

        WebOfficeBaseInputDTO webOfficeBaseInputDTO = new WebOfficeBaseInputDTO();

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setMemberId(TEST_ACCOUNT_ID);
        accountInfoDTO.setTenantId(TEST_TENANT_ID);
        webOfficeBaseInputDTO.setAccountInfo(accountInfoDTO);

        webOfficeIntegrateService.getWatermark(webOfficeBaseInputDTO);

        webOfficeIntegrateService.getPrivilege(webOfficeBaseInputDTO);

        webOfficeIntegrateService.getUserInfo(webOfficeBaseInputDTO);
    }
}
