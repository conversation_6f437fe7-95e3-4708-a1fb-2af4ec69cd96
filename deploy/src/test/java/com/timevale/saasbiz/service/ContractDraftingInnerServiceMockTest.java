package com.timevale.saasbiz.service;

import com.google.common.collect.Lists;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.contractdfrating.EpaasContractDraftingClient;
import com.timevale.saasbiz.model.bean.contractdrafting.dto.input.CreateContractDraftingInputDTO;
import com.timevale.saasbiz.model.bean.contractdrafting.dto.output.CreateContractDraftingOutputDTO;
import com.timevale.saasbiz.model.bean.file.FileKeyDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.service.contractdrafting.impl.ContractDraftingInnerServiceImpl;
import com.timevale.sterna.contract.drafting.client.dto.draft.ContractDraftingDTO;
import com.timevale.sterna.contract.drafting.client.dto.draft.ContractDraftingUrlDTO;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.resp.GetFileInfoResp;
import com.timevale.sterna.contract.drafting.client.dto.weboffice.resp.UploadFileResp;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.testng.annotations.Test;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;

@JsonDataIgnore
public class ContractDraftingInnerServiceMockTest extends BaseMockTest {

    @InjectMocks
    private ContractDraftingInnerServiceImpl contractDraftingInnerService;

    @Mock
    private EpaasContractDraftingClient epaasContractDraftingClient;

    @Test(description = "测试创建合同起草 - 验证成功创建合同起草并返回URL")
    public void testCreateContractDrafting() {
        // Prepare test data
        CreateContractDraftingInputDTO inputDTO = new CreateContractDraftingInputDTO();
        inputDTO.setDraftingType("WORD");
        inputDTO.setTenantId("testTenant");
        inputDTO.setAccountId("testAccount");
        inputDTO.setFiles(Lists.newArrayList());

        // Mock external service response
        ContractDraftingUrlDTO mockUrlDTO = new ContractDraftingUrlDTO();
        mockUrlDTO.setUrl("http://test.drafting.url");
        when(epaasContractDraftingClient.createContractDrafting(any(ContractDraftingDTO.class)))
                .thenReturn(mockUrlDTO);

        // Execute test
        CreateContractDraftingOutputDTO result = contractDraftingInnerService.createContractDrafting(inputDTO);

        // Verify results
        assertNotNull(result);
        assertEquals(result.getDraftingUrl(), "http://test.drafting.url");
    }

    @Test(description = "测试上传起草文件 - 验证成功上传文件并返回文件ID")
    public void testUploadDraftingFile() {
        // Prepare test data
        String fileKey = "test-file-key";
        String fileName = "test.docx";
        AccountDetailDTO accountInfo = new AccountDetailDTO();
        accountInfo.setOid("testOid");
        accountInfo.setName("Test User");

        // Mock external service response
        UploadFileResp mockResp = new UploadFileResp();
        mockResp.setFileId("test-file-id");
        when(epaasContractDraftingClient.uploadDraftingFile(any()))
                .thenReturn(mockResp);

        // Execute test
        String result = contractDraftingInnerService.uploadDraftingFile(fileKey, fileName, accountInfo);

        // Verify results
        assertNotNull(result);
        assertEquals(result, "test-file-id");
    }

    @Test(description = "测试查询起草文件Key - 验证成功返回文件Key信息")
    public void testQueryDraftingFileKey() {
        // Prepare test data
        String draftingFileId = "test-drafting-file-id";

        // Mock external service response
        GetFileInfoResp mockResp = new GetFileInfoResp();
        mockResp.setFileKey("test-file-key");
        mockResp.setFileName("test.docx");
        when(epaasContractDraftingClient.queryDraftingFile(any()))
                .thenReturn(mockResp);

        // Execute test
        FileKeyDTO result = contractDraftingInnerService.queryDraftingFileKey(draftingFileId);

        // Verify results
        assertNotNull(result);
        assertEquals(result.getFileKey(), "test-file-key");
        assertEquals(result.getFileName(), "test.docx");
    }
}
