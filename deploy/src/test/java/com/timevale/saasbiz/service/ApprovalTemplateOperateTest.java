package com.timevale.saasbiz.service;

import com.alibaba.fastjson.JSON;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateConditionTypeEnum;
import com.timevale.contractapproval.facade.enums.ApprovalTemplateTypeEnum;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.constant.TestAccountConstants;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalTemplateDetailDTO;
import com.timevale.saasbiz.model.bean.approval.dto.ApprovalTemplateExistConditionDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalTemplateCopyInputDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalTemplateDeleteInputDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalTemplateExistConditionInputDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalTemplateSaveInputDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ApprovalTemplateUpdateInputDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.GetApprovalTemplateDetailInputDTO;
import com.timevale.saasbiz.service.approval.templateoperate.ApprovalTemplateOperateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

/**
 * <AUTHOR>
 * @since 2023/4/25 16:32
 */
@JsonDataIgnore
public class ApprovalTemplateOperateTest extends BaseServiceTest {

    private final String SUBJECT_OID = TestAccountConstants.SENIOR_SUBJECT_1;
    private final String OPERATOR_OID = TestAccountConstants.ACCOUNT_ID_1;
    private final String APPROVAL_TEMPLATE = "AT1a1b507ca5ac47578027d4bc54bd7bd4";
    private final String APPROVAL_TEMPLATE_VIEW_RANGE = "AT3a4908fb131b4fcfba7d4b2829532941";
    private final String FLOW_TEMPLATE_ID = "a6c165c4c225427c97b7541ddcb1ddfd";

    private final String CREATE_FLOW_TEMPLATE_ID =  "c354e2b336d64c7e9c24733368157925";


    @Autowired
    private ApprovalTemplateOperateService approvalTemplateOperate;


    /**
     * 创建审批流模版
     */
    @Test
    public void createApprovalTemplateTest() {


        String data = "{\"approvalTemplateName\":\"测试参数\",\"approvalTemplateDescription\":\"1123\",\"approvalTemplateType\":2,\"approvalTemplateCondition\":{\"conditionType\":1,\"viewRange\":{\"3\":[{\"value\":\"d915e74e9d2d428082a2f760e1b7bde5\",\"name\":\"测试前缀单测账号二\"}],\"4\":[{\"value\":\"7962962905dd4aef9fc391a621be0e39\",\"name\":\"开发者\"}],\"5\":[{\"value\":\"1048e0390bfa4fe4b1bd09ecb9506a33\",\"name\":\"测试部门\"}]},\"conditionValues\":[{\"name\":\"单侧-创建审批模版使用\",\"value\":\"c354e2b336d64c7e9c24733368157925\"}]},\"approvalTemplateModel\":{\"frontProcessDefinition\":\"{\\\"nodeConfig\\\":{\\\"nodeName\\\":\\\"触发审批事件\\\",\\\"type\\\":0,\\\"eventType\\\":1,\\\"priorityLevel\\\":\\\"\\\",\\\"settype\\\":\\\"\\\",\\\"selectMode\\\":\\\"\\\",\\\"selectRange\\\":\\\"\\\",\\\"directorLevel\\\":\\\"\\\",\\\"examineMode\\\":\\\"\\\",\\\"noHanderAction\\\":\\\"\\\",\\\"examineEndDirectorLevel\\\":\\\"\\\",\\\"ccSelfSelectFlag\\\":\\\"\\\",\\\"conditionList\\\":[],\\\"nodeUserList\\\":[],\\\"id\\\":\\\"start\\\",\\\"childNode\\\":{\\\"nodeName\\\":\\\"审核人\\\",\\\"type\\\":1,\\\"id\\\":\\\"approve_RID95GYO_1682413193526\\\",\\\"settype\\\":\\\"MEMBER\\\",\\\"selectMode\\\":0,\\\"selectRange\\\":0,\\\"directorLevel\\\":1,\\\"examineMode\\\":\\\"OR_SIGN\\\",\\\"noHanderAction\\\":2,\\\"examineEndDirectorLevel\\\":0,\\\"childNode\\\":{},\\\"nodeUserList\\\":[{\\\"memberId\\\":\\\"878d296d0b404854a34645d058857a77\\\",\\\"memberAccountUid\\\":\\\"327659bdc9d34470b6b4bfed97b39849\\\",\\\"memberGid\\\":\\\"2539334030d74d8ca46a1e779fc6fd82\\\",\\\"memberName\\\":\\\"测试前缀单测账号四\\\",\\\"realnameStatus\\\":true,\\\"mobile\\\":\\\"***********\\\",\\\"email\\\":null,\\\"hasRoleId\\\":null,\\\"createTime\\\":null,\\\"properties\\\":{\\\"memberName\\\":\\\"测试前缀单测账号四\\\"},\\\"roleModels\\\":[{\\\"id\\\":\\\"551703c7b6044a3eb0d9b44ee50cb43b\\\",\\\"roleKey\\\":\\\"MEMBER\\\",\\\"name\\\":\\\"普通员工\\\",\\\"roleOwner\\\":null,\\\"disc\\\":null,\\\"parentRoleId\\\":null,\\\"organId\\\":null,\\\"createTime\\\":null}],\\\"active\\\":true,\\\"deptDetails\\\":null,\\\"roleDetails\\\":null}],\\\"receiveRole\\\":\\\"\\\",\\\"departmentHead\\\":0,\\\"lastApproveNodeVal\\\":[]},\\\"conditionNodes\\\":[]}}\",\"processDefinition\":\"{\\\"autoComplete\\\":true,\\\"nodes\\\":[{\\\"key\\\":\\\"start\\\",\\\"name\\\":\\\"触发审批事件\\\",\\\"type\\\":\\\"START\\\"},{\\\"key\\\":\\\"approve_RID95GYO_1682413193526\\\",\\\"name\\\":\\\"审核人\\\",\\\"type\\\":\\\"OR_SIGN\\\",\\\"candidates\\\":[{\\\"candidate\\\":\\\"2539334030d74d8ca46a1e779fc6fd82\\\",\\\"candidateType\\\":\\\"ASSIGN_PERSONNEL\\\",\\\"userName\\\":\\\"测试前缀单测账号四\\\"}],\\\"isApproveFinal\\\":false},{\\\"key\\\":\\\"end\\\",\\\"name\\\":\\\"结束\\\",\\\"type\\\":\\\"END\\\"}],\\\"sequences\\\":[{\\\"defaultSequence\\\":false,\\\"key\\\":\\\"sequences-49DGVZ3V-1682413227692\\\",\\\"name\\\":\\\"触发审批事件\\\",\\\"source\\\":\\\"start\\\",\\\"target\\\":\\\"approve_RID95GYO_1682413193526\\\",\\\"controlGroups\\\":{}},{\\\"defaultSequence\\\":false,\\\"key\\\":\\\"sequences-J2H5YSMZ-1682413227693\\\",\\\"name\\\":\\\"审核人\\\",\\\"source\\\":\\\"approve_RID95GYO_1682413193526\\\",\\\"target\\\":\\\"end\\\",\\\"controlGroups\\\":{}}]}\"}}\n";

        ApprovalTemplateSaveInputDTO create = JSON.parseObject(data, ApprovalTemplateSaveInputDTO.class);
        create.setSubjectOid(SUBJECT_OID);
        create.setOperatorOid(OPERATOR_OID);
        create.setAppId("appId");
        // 创建
        String approvalTemplateId = approvalTemplateOperate.createApprovalTemplate(create);
        Assert.assertTrue(StringUtils.isNotBlank(approvalTemplateId));

        // 更新
        ApprovalTemplateUpdateInputDTO update = JSON.parseObject(data, ApprovalTemplateUpdateInputDTO.class);
        update.setApprovalTemplateId(approvalTemplateId);
        update.setOperatorOid(OPERATOR_OID);
        update.setSubjectOid(SUBJECT_OID);
        approvalTemplateOperate.updateApprovalTemplate(update);

        // 开启
        // 关闭

        // 复制
        ApprovalTemplateCopyInputDTO copy = new ApprovalTemplateCopyInputDTO();
        copy.setApprovalTemplateId(approvalTemplateId);
        copy.setAppId("appId");
        copy.setOperatorOid(OPERATOR_OID);
        copy.setSubjectOid(SUBJECT_OID);
        copy.setClientId("clientId");
        String newApprovalTemplateId = approvalTemplateOperate.copyApprovalTemplate(copy);
        Assert.assertTrue(StringUtils.isNotBlank(newApprovalTemplateId));

        // 删除
        ApprovalTemplateDeleteInputDTO deleteInputDTO = new ApprovalTemplateDeleteInputDTO();
        deleteInputDTO.setApprovalTemplateId(approvalTemplateId);
        deleteInputDTO.setAppId("appId");
        deleteInputDTO.setOperatorOid(OPERATOR_OID);
        deleteInputDTO.setSubjectOid(SUBJECT_OID);
        deleteInputDTO.setClientId("clientId");
        approvalTemplateOperate.deleteApprovalTemplate(deleteInputDTO);

    }


    /**
     * 详情
     */
    @Test
    public void getApprovalTemplateDetailTest() {
        GetApprovalTemplateDetailInputDTO inputDTO = new GetApprovalTemplateDetailInputDTO();
        inputDTO.setAppId("apId");
        inputDTO.setOperatorOid(OPERATOR_OID);
        inputDTO.setSubjectOid(SUBJECT_OID);
        inputDTO.setClientId("clientId");
        inputDTO.setApprovalTemplateId(APPROVAL_TEMPLATE);
        inputDTO.setApprovalTemplateType(ApprovalTemplateTypeEnum.CONTRACT.getCode());
        ApprovalTemplateDetailDTO approvalTemplateDetail = approvalTemplateOperate.getApprovalTemplateDetail(inputDTO);
        Assert.assertTrue(null != approvalTemplateDetail);
        Assert.assertTrue(approvalTemplateDetail.getApprovalTemplateId().equals(APPROVAL_TEMPLATE));

    }

    @Test
    public void getApprovalTemplateDetailViewRangeTest() {
        GetApprovalTemplateDetailInputDTO inputDTO = new GetApprovalTemplateDetailInputDTO();
        inputDTO.setAppId("apId");
        inputDTO.setOperatorOid(OPERATOR_OID);
        inputDTO.setSubjectOid(SUBJECT_OID);
        inputDTO.setClientId("clientId");
        inputDTO.setApprovalTemplateId(APPROVAL_TEMPLATE_VIEW_RANGE);
        inputDTO.setApprovalTemplateType(ApprovalTemplateTypeEnum.CONTRACT.getCode());
        ApprovalTemplateDetailDTO approvalTemplateDetail = approvalTemplateOperate.getApprovalTemplateDetail(inputDTO);
        Assert.assertTrue(null != approvalTemplateDetail);
        Assert.assertTrue(null != approvalTemplateDetail.getApprovalTemplateCondition().getViewRangeValues());
        Assert.assertTrue(approvalTemplateDetail.getApprovalTemplateId().equals(APPROVAL_TEMPLATE_VIEW_RANGE));

    }

    /**
     * 已经存在的审批事件
     */
    @Test
    public void existConditionTest() {
        ApprovalTemplateExistConditionInputDTO inputDTO = new ApprovalTemplateExistConditionInputDTO();
        inputDTO.setAppId("appId");
        inputDTO.setOperatorOid(OPERATOR_OID);
        inputDTO.setSubjectOid(SUBJECT_OID);
        inputDTO.setClientId("clientId");
        inputDTO.setApprovalTemplateId(CREATE_FLOW_TEMPLATE_ID);
        inputDTO.setApprovalTemplateType(ApprovalTemplateTypeEnum.CONTRACT.getCode());
        inputDTO.setConditionType(ApprovalTemplateConditionTypeEnum.START_FLOW_TEMPLATE.getCode());
        inputDTO.setConditionValues(Lists.newArrayList(FLOW_TEMPLATE_ID));
        ApprovalTemplateExistConditionDTO bo = approvalTemplateOperate.existCondition(inputDTO);
        Assert.assertTrue(null != bo && Boolean.TRUE.equals(bo.getExist()));
    }
}
