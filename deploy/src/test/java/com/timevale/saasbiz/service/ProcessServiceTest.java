package com.timevale.saasbiz.service;

import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.model.bean.process.dto.output.ProcessStartSourceOutputDTO;
import com.timevale.saasbiz.service.process.ProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2024-07-31
 */
@JsonDataIgnore
public class ProcessServiceTest extends BaseServiceTest {
    @Autowired
    private ProcessService processService;

    @Test
    public void testQueryStartSource(){
        ProcessStartSourceOutputDTO outputDTO= processService.queryStartSource("ae2c19bac49047d197d51311d1078e39");
        Assert.assertNotNull(outputDTO);
        Assert.assertNotNull(outputDTO.getSource());
        Assert.assertNull(outputDTO.getAppName());

    }
}
