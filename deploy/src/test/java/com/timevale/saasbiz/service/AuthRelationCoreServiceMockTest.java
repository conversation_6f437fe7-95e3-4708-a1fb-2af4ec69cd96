package com.timevale.saasbiz.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.base.elock.config.ConfigSwitch;
import com.timevale.base.elock.config.SimpleConfig;
import com.timevale.billing.manager.sdk.model.order.OrderSearchVO;
import com.timevale.contractmanager.common.service.result.StartProcessResult;
import com.timevale.docmanager.service.model.KeywordPos;
import com.timevale.docmanager.service.result.CreateDocByHtmlResult;
import com.timevale.framework.puppeteer.Config;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.gray.config.manage.service.api.GrayscaleRpcService;
import com.timevale.gray.config.manage.service.model.base.BaseResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saas.common.manage.common.service.constant.AuthRelationShareConfigKeyConstant;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizSceneEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationSecondResourceEnum;
import com.timevale.saas.common.manage.common.service.model.base.Page;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.*;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationCreateLogOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationDTO;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationLogDTO;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationSignSuccessOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationUpdateLogOutput;
import com.timevale.saas.tracking.bean.TrackingCollectBean;
import com.timevale.saasbiz.base.BasePowerMockTest;
import com.timevale.saasbiz.config.BizConfigCenter;
import com.timevale.saasbiz.integration.bill.BillClient;
import com.timevale.saasbiz.integration.gray.impl.GrayClientImpl;
import com.timevale.saasbiz.integration.process.DocServiceClient;
import com.timevale.saasbiz.integration.process.ProcessStartClient;
import com.timevale.saasbiz.integration.saascommon.AuthRelationClient;
import com.timevale.saasbiz.integration.saascommon.AuthRelationInnerClient;
import com.timevale.saasbiz.model.bean.authrelation.bo.AuthRelationBuildContextBO;
import com.timevale.saasbiz.model.bean.authrelation.bo.AuthRelationChildTenantBO;
import com.timevale.saasbiz.model.bean.authrelation.bo.AuthRelationConfigBO;
import com.timevale.saasbiz.model.bean.authrelation.dto.input.AuthRelationOfflineBatchAddDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationCheckTenantBeforeAddResultDTO;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.AuthRelationEffectiveProductDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.model.exception.SaasBizResultCode;
import com.timevale.saasbiz.service.authrelation.impl.AuthRelationCoreServiceImpl;
import com.timevale.saasbiz.service.authrelation.pipeline.AuthRelationBuildPipelineChainFactory;
import com.timevale.saasbiz.service.authrelation.processor.AuthRelationBatchAddProcessor;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.saasbiz.tracking.tracking.GlobalDefaultTrackingService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.math.BigDecimal;
import java.util.*;

/**
 * AuthRelationCoreServiceMockTest
 *
 * <AUTHOR>
 * @since 2023/6/8 1:51 下午
 */
@JsonDataIgnore
@PrepareForTest({BizConfigCenter.class, ConfigService.class})
public class AuthRelationCoreServiceMockTest extends BasePowerMockTest {

    @InjectMocks
    private AuthRelationCoreServiceImpl authRelationCoreService;

    @InjectMocks
    private GlobalDefaultTrackingService globalDefaultTrackingService;

    @InjectMocks
    private GrayClientImpl grayClient;

    @Mock
    private BillClient billClient;

    @Mock
    private AuthRelationInnerClient authRelationInnerClient;

    @Mock
    private UserCenterService userCenterService;

    @Mock
    private AuthRelationClient authRelationClient;

    @Mock
    private DocServiceClient docServiceClient;

    @Mock
    private ProcessStartClient processStartClient;

    @Mock
    private GrayscaleRpcService grayscaleRpcService;

    @Mock
    private AuthRelationBatchAddProcessor processor;

    @Mock
    private AuthRelationBuildPipelineChainFactory buildPipelineChainFactory;

    @Mock
    private LockFactory lockFactory;


    @Test
    public void startContractTest() {
        AuthRelationCreateLogOutput authRelationCreateLogOutput = new AuthRelationCreateLogOutput();
        Mockito.when(authRelationInnerClient.createAuthRelationLog(Mockito.any()))
                .thenReturn(authRelationCreateLogOutput);

        Mockito.when(authRelationInnerClient.createAuthRelationLog(Mockito.any()))
                .thenReturn(authRelationCreateLogOutput);

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("1");
        accountInfoDTO.setName("1");
        Mockito.when(userCenterService.queryAccountInfoByOid(Mockito.any()))
                .thenReturn(accountInfoDTO);

        Mockito.when(userCenterService.getAdminOidByOrgGid(Mockito.any())).thenReturn("adminName");

        AuthRelationDTO authRelationDTO = new AuthRelationDTO();
        authRelationDTO.setParentTenantGid("parentGid");
        authRelationDTO.setChildTenantGid("childGid");
        Mockito.when(authRelationClient.getEffectiveAuthRelationByChildTenantGid(Mockito.any()))
                .thenReturn(authRelationDTO);

        CreateDocByHtmlResult createDocByHtmlResult = new CreateDocByHtmlResult();
        createDocByHtmlResult.setFileKey("123");
        List<KeywordPos> keywordPosList = new ArrayList<>();
        KeywordPos keywordPos = new KeywordPos();
        keywordPos.setKeyword("11");
        KeywordPos.Pos pos = new KeywordPos.Pos();
        pos.setX(1);
        pos.setY(1);
        pos.setPage(1);
        keywordPos.setPos(pos);
        keywordPosList.add(keywordPos);
        createDocByHtmlResult.setKeywordPosList(keywordPosList);
        Mockito.when(docServiceClient.createDocByHtmlTemplate(Mockito.any()))
                .thenReturn(createDocByHtmlResult);

        StartProcessResult startProcessResult = new StartProcessResult();
        startProcessResult.setFlowId("123");
        Mockito.when(processStartClient.startProcess(Mockito.any())).thenReturn(startProcessResult);

        AuthRelationBuildContextBO authRelationBuildContextBO = new AuthRelationBuildContextBO();
        authRelationBuildContextBO.setAuthTenantOid("authOid");
        authRelationBuildContextBO.setAuthTenantGid("authGid");
        authRelationBuildContextBO.setParentTenantGid("parentGid");

        authRelationBuildContextBO.setEffectiveStartTime(System.currentTimeMillis());
        authRelationBuildContextBO.setEffectiveEndTime(System.currentTimeMillis());
        authRelationBuildContextBO.setAuthResources(Arrays.asList("100", "200", "300"));
        Map<String, List<String>> secondAuthResource = new HashMap<>();
        List<String> seconds = new ArrayList<>();
        seconds.add(AuthRelationSecondResourceEnum.AUTH_MANAGE.getCode());
        secondAuthResource.put(AuthRelationBizSceneEnum.TEMPLATE_MANAGE.getLevel().toString(), seconds);
        authRelationBuildContextBO.setSecondAuthResource(secondAuthResource);
        authRelationBuildContextBO.setShareConfigs(Arrays.asList("shareVip", "shareSign"));
        AuthRelationConfigBO authRelationConfigBO = new AuthRelationConfigBO();
        authRelationConfigBO.setFtlFileKey("123");
        authRelationConfigBO.setSignEndDurationDay(5);
        authRelationBuildContextBO.setConfig(authRelationConfigBO);
        authRelationCoreService.startContract(authRelationBuildContextBO);

        authRelationBuildContextBO.setAuthResources(new ArrayList<>());
        authRelationCoreService.startContract(authRelationBuildContextBO);
    }

    @Test
    public void signSuccessTest() {

        AuthRelationSignSuccessOutput signSuccessOutput = new AuthRelationSignSuccessOutput();
        signSuccessOutput.setFreezeInfo("123");
        signSuccessOutput.setAuthRelationLogId(123L);
        Mockito.when(authRelationInnerClient.signSuccess(Mockito.any()))
                .thenReturn(signSuccessOutput);
        Mockito.when(billClient.confirm(Mockito.any())).thenReturn(true);
        Mockito.when(
                        authRelationInnerClient.updateAuthRelationLogStatusById(
                                Mockito.any(), Mockito.any()))
                .thenReturn(new AuthRelationUpdateLogOutput());

        authRelationCoreService.signSuccess("123", new AuthRelationLogDTO());
    }

    @Test
    public void addGray() {
        Mockito.when(grayscaleRpcService.addGrayscaleFunction(Mockito.any()))
                .thenReturn(new BaseResult());
        boolean result = grayClient.addGrayscaleFunction(new HashSet<>(), "123");
        Assert.assertTrue(result);
    }

    @Test
    public void trackingTest() {

        AccountDetailDTO detailDTO = new AccountDetailDTO();
        detailDTO.setOrgan(true);
        Mockito.when(userCenterService.queryAccountDetailByOid(Mockito.any()))
                .thenReturn(detailDTO);

        try {
            TrackingCollectBean collectBean = new TrackingCollectBean();
            collectBean.setTenantId("123");
            collectBean.setOperatorId("123");
            globalDefaultTrackingService.tracking(collectBean);
        } catch (Exception e) {
        }
    }

    @Test
    public void testOfflineAdd() {
        AuthRelationCreateLogOutput authRelationCreateLogOutput = new AuthRelationCreateLogOutput();
        Mockito.when(authRelationInnerClient.createAuthRelationLog(Mockito.any()))
                .thenReturn(authRelationCreateLogOutput);

        Mockito.when(authRelationInnerClient.createAuthRelationLog(Mockito.any()))
                .thenReturn(authRelationCreateLogOutput);

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("1");
        accountInfoDTO.setName("1");
        Mockito.when(userCenterService.queryAccountInfoByOid(Mockito.any()))
                .thenReturn(accountInfoDTO);

        AccountDetailDTO accountDetailDTO = new AccountDetailDTO();
        accountDetailDTO.setGid("1");
        accountDetailDTO.setName("1");
        accountDetailDTO.setRealNamed(true);
        Mockito.when(userCenterService.queryAccountDetailByOid(Mockito.any()))
                .thenReturn(accountDetailDTO);

        Mockito.when(userCenterService.getAdminOidByOrgGid(Mockito.any())).thenReturn("adminName");

        AuthRelationDTO authRelationDTO = new AuthRelationDTO();
        authRelationDTO.setParentTenantGid("1");
        authRelationDTO.setChildTenantGid("childGid");
        Mockito.when(authRelationClient.getEffectiveAuthRelationByChildTenantGid(Mockito.any()))
                .thenReturn(null);

        CreateDocByHtmlResult createDocByHtmlResult = new CreateDocByHtmlResult();
        createDocByHtmlResult.setFileKey("123");
        Mockito.when(docServiceClient.createDocByHtmlTemplate(Mockito.any()))
                .thenReturn(createDocByHtmlResult);

        StartProcessResult startProcessResult = new StartProcessResult();
        startProcessResult.setFlowId("123");
        Mockito.when(processStartClient.startProcess(Mockito.any())).thenReturn(startProcessResult);
        Mockito.doNothing().when(processor).checkOrderMargin(Mockito.any(), Mockito.any());
        OrderSearchVO orderSearchVO = new OrderSearchVO();
        orderSearchVO.setOrderStatus(1);
        orderSearchVO.setId("testOrderId");
        orderSearchVO.setStartTime(DateUtils.addDays(new Date(), -100L));
        orderSearchVO.setEndTime(DateUtils.addDays(new Date(), 100L));
        orderSearchVO.setMargin(new BigDecimal(1000));
        Mockito.when(billClient.searchMultiOrderResult(Mockito.any())).thenReturn(Lists.newArrayList(orderSearchVO), Lists.newArrayList());

        AuthRelationBuildBeforeCheckOutput authRelationBuildBeforeCheckOutput = new AuthRelationBuildBeforeCheckOutput();
        authRelationBuildBeforeCheckOutput.setSuccess(true);
        Mockito.when(authRelationInnerClient.startBeforeCheck(Mockito.any()))
                .thenReturn(authRelationBuildBeforeCheckOutput);
        Mockito.when(buildPipelineChainFactory.offlineAddPipelineChain()).thenReturn(Lists.newArrayList());
        AuthRelationOfflineBatchAddDTO authRelationOfflineBatchAddDTO =
                new AuthRelationOfflineBatchAddDTO();
        authRelationOfflineBatchAddDTO.setAuthTenantOid("97298c2243c14ee792e54492058f0391");
        authRelationOfflineBatchAddDTO.setParentTenantOid("97298c2243c14ee792e54492058f0391");
        AuthRelationChildTenantBO childTenantBO = new AuthRelationChildTenantBO();
        childTenantBO.setChildTenantOid("ccb284fadd34405ba68a0c710cccc74f");
        childTenantBO.setOrderId("testOrderId");
        childTenantBO.setAuthResources(
                Lists.newArrayList(AuthRelationBizSceneEnum.CONTRACT_MANAGE.getLevel().toString()));
        authRelationOfflineBatchAddDTO.setChildTenantList(Lists.newArrayList(childTenantBO));
        String lockName = "__lock__contract-manager_authRelationStartBuildLimit:bef96c98498e4939aaf34db95049c6ce";
        ConfigSwitch.config = true;
        SimpleConfig.springApplicationName = "contract-manager";
        Elock elock = Mockito.mock(Elock.class);
        Mockito.when(lockFactory.getLock(Mockito.any())).thenReturn(elock);
        Mockito.when(elock.tryLock(Mockito.anyInt(), Mockito.any())).thenReturn(true);
        authRelationCoreService.offlineAdd(authRelationOfflineBatchAddDTO);
    }

    @Test
    public void testCheckTenantBeforeAddTooMuch() {
        int currentCount = 6;
        List<AuthRelationChildTenantBO> tenantBOS = Lists.newArrayListWithCapacity(currentCount);
        for (int i = 0; i < currentCount; i++) {
            tenantBOS.add(new AuthRelationChildTenantBO());
        }
        try {
            authRelationCoreService.checkTenantBeforeAdd("test", "test", tenantBOS);
            Assert.fail();
        } catch (SaasBizException e) {
            Assert.assertEquals(
                    e.getCode(), String.valueOf(SaasBizResultCode.SAAS_ILLEGAL_PARAM.getCode()));
        }
    }

    @Test
    public void testCheckTenantBeforeAddSuccess() {
        String authTenantOid = "authTenantOid";
        String parentTenantOid = "parentTenantOid";

        AuthRelationChildTenantBO childTenantBO = new AuthRelationChildTenantBO();
        childTenantBO.setChildTenantName("childTenantName");
        childTenantBO.setChildTenantOid("childTenantOid");
        childTenantBO.setChildUsccCode("childUsccCode");
        childTenantBO.setShareConfigs(Lists.newArrayList(AuthRelationShareConfigKeyConstant.SHARE_VIP));
        childTenantBO.setAuthResources(Lists.newArrayList(AuthRelationBizSceneEnum.CONTRACT_MANAGE.getCode()));
        childTenantBO.setOrderId("orderId");
        childTenantBO.setEffectiveStartTime(System.currentTimeMillis());
        childTenantBO.setEffectiveEndTime(System.currentTimeMillis() + 60 * 60 * 1000);


        AuthRelationConfigBO authRelationConfigBO = new AuthRelationConfigBO();
        authRelationConfigBO.setOnlineMaxAddCount(5);
        authRelationConfigBO.setOfflineMaxAddCount(10);
        PowerMockito.mockStatic(BizConfigCenter.class);
        PowerMockito.when(BizConfigCenter.getAutoRelationConfig()).thenReturn(authRelationConfigBO);

        AccountInfoDTO authTenant = new AccountInfoDTO();
        authTenant.setOid(authTenantOid);
        authTenant.setGid("authTenantGid");
        AccountInfoDTO parentTenant = new AccountInfoDTO();
        parentTenant.setOid(parentTenantOid);
        parentTenant.setGid("parentTenantGid");
        Mockito.when(userCenterService.queryAccountInfoByOid(authTenantOid)).thenReturn(authTenant);
        Mockito.when(userCenterService.queryAccountInfoByOid(parentTenantOid)).thenReturn(parentTenant);

        Page<AuthRelationBackendDTO> page = new Page<>(0, Lists.newArrayList());
        Mockito.when(authRelationInnerClient.queryPageAuthRelation(Mockito.any())).thenReturn(page);
        PowerMockito.mockStatic(ConfigService.class);
        Config config = Mockito.mock(Config.class);
        PowerMockito.when(ConfigService.getAppConfig()).thenReturn(config);
        PowerMockito.when(config.getIntProperty(Mockito.anyString(), Mockito.anyInt())).thenReturn(1000);

        OrderSearchVO orderSearchVO = new OrderSearchVO();
        orderSearchVO.setId("orderId");
        orderSearchVO.setMargin(BigDecimal.valueOf(1));
        orderSearchVO.setOrderStatus(1);
        List<OrderSearchVO> orderList = Lists.newArrayList(orderSearchVO);
        Mockito.when(billClient.searchMultiOrderResult(Mockito.any())).thenReturn(orderList);
        Mockito.when(authRelationInnerClient.queryAuthRelationLockOrderList(Mockito.any())).thenReturn(Lists.newArrayList());
        Mockito.when(userCenterService.getAdminOidByOrgGid(Mockito.any())).thenReturn("admin");

        AuthRelationDTO authRelationDTO = new AuthRelationDTO();
        authRelationDTO.setAuthTenantOid(authTenantOid);
        authRelationDTO.setAuthTenantGid("authTenantGid");
        authRelationDTO.setParentTenantOid(parentTenantOid);
        Mockito.when(authRelationClient.getEffectiveAuthRelationByChildTenantGid(Mockito.any())).thenReturn(authRelationDTO);

        AccountDetailDTO childTenant = new AccountDetailDTO();
        childTenant.setOid("childTenantOid");
        childTenant.setGid("childTenantGid");
        Map<String, AccountDetailDTO> accountMap = Maps.newHashMap();
        accountMap.put("childTenantOid", childTenant);

        Map<String, String> adminMap = Maps.newHashMap();
        adminMap.put("childTenantOid", "admin");
        Mockito.when(userCenterService.queryAccountMapByAccountIds(Mockito.anyList())).thenReturn(accountMap);
        Mockito.when(userCenterService.batchQueryOrgAdminOidMap(Mockito.anyList())).thenReturn(adminMap);
        List<AuthRelationChildTenantBO> childTenantBOList = Lists.newArrayList(childTenantBO);

        List<AuthRelationCheckTenantBeforeAddResultDTO> resultDTOS = authRelationCoreService.checkTenantBeforeAdd(authTenantOid, parentTenantOid, childTenantBOList);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultDTOS));
    }

    @Test
    public void testGetProduct() {
        OrderSearchVO orderSearchVO = JSON.parseObject("{\"id\":\"fc5c1011-6ae6-46d6-91ca-9aeb38526bed\",\"orderType\":0,\"gid\":\"accecdef23d64a6d8a19b88fbf083816\",\"showId\":\"O202312130445445919\",\"price\":5000.00,\"orderStatus\":0,\"status\":0,\"payStatus\":0,\"totalNum\":5,\"paymentType\":2,\"payTypes\":null,\"saleInfoId\":2284,\"units\":\"次\",\"unitPrice\":1000.00,\"invoiceTotal\":0.00,\"productId\":1336,\"productName\":\"关联企业授权服务\",\"commodityId\":1303,\"commodityName\":\"关联企业授权服务\",\"startTime\":1668306704000,\"endTime\":1831465104000,\"createTime\":1702449512000,\"used\":null,\"margin\":5,\"hasElecInvoice\":0}", OrderSearchVO.class);
        Mockito.when(billClient.searchMultiOrderResult(Mockito.any())).thenReturn(Lists.newArrayList(orderSearchVO));
        AuthRelationEffectiveProductDTO productDTO = authRelationCoreService.getProduct("testgid", "testorder");
        Assert.assertNotNull(productDTO);
        Assert.assertEquals(productDTO.getTotalNum().intValue(), 5);
    }

}
