package com.timevale.saasbiz.service;

import com.timevale.contractmanager.common.service.bean.ProcessStartDataDTO;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.flowtemplate.FlowTemplateClient;
import com.timevale.saasbiz.integration.process.ProcessStartDataClient;
import com.timevale.saasbiz.model.bean.contractcategory.bo.FileContractCategoryBO;
import com.timevale.saasbiz.model.bean.file.SimpleFileDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.input.BatchGetFlowTemplateFilesInputDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.input.biz.FlowTemplatePreviewInputDTO;
import com.timevale.saasbiz.model.bean.flowtemplate.dto.output.FlowTemplatePreviewOutputDTO;
import com.timevale.saasbiz.service.contractcategory.ContractCategoryService;
import com.timevale.saasbiz.service.flowtemplate.FlowTemplateQueryService;
import com.timevale.saasbiz.service.flowtemplate.impl.FlowTemplateBizServiceImpl;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.testng.collections.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/11 15:08
 */
@JsonDataIgnore
public class FlowTemplateBizServiceMockTest extends BaseMockTest {

    @InjectMocks
    private FlowTemplateBizServiceImpl flowTemplateBizService;
    @Mock
    private ProcessStartDataClient processStartDataClient;
    @Mock
    private FlowTemplateClient flowTemplateClient;
    @Mock
    private FlowTemplateQueryService flowTemplateQueryService;
    @Mock
    private ContractCategoryService contractCategoryService;

    @Test
    public void getTemplatePreviewUrlMockTest() {

        FlowTemplatePreviewInputDTO inputDTO = new FlowTemplatePreviewInputDTO();
        FlowTemplatePreviewInputDTO.DataSource dataSource = new FlowTemplatePreviewInputDTO.DataSource();
        dataSource.setDataId("dataId");
        inputDTO.setDataSource(dataSource);

        Mockito.when(processStartDataClient.getByDataId(Mockito.any())).thenReturn(new ProcessStartDataDTO());
        Mockito.when(flowTemplateClient.getFlowTemplatePreviewUrl(Mockito.any())).thenReturn("url");

        FlowTemplatePreviewOutputDTO flowTemplatePreviewOutputDTO = flowTemplateBizService.getTemplatePreviewUrl(inputDTO);
        Assert.assertTrue(null != flowTemplatePreviewOutputDTO && StringUtils.isNotBlank(flowTemplatePreviewOutputDTO.getPreviewUrl()));

    }

    @Test
    public void batchQueryFlowTemplateSimpleFilesMockTest() {

        BatchGetFlowTemplateFilesInputDTO inputDTO = new BatchGetFlowTemplateFilesInputDTO();

        Map<String, List<SimpleFileDTO>> flowTemplateFileMap = new HashMap<>();
        {
            SimpleFileDTO simpleFileDTO = new SimpleFileDTO();
            simpleFileDTO.setFileId("fileId");
            flowTemplateFileMap.put("key", Lists.newArrayList(simpleFileDTO));
        }
        Mockito.when(flowTemplateQueryService.batchGetFlowTemplateSimpleFiles(Mockito.any())).thenReturn(flowTemplateFileMap);
        //
        Map<String, List<FileContractCategoryBO>> flowTemplateFileCategoryMap = new HashMap<>();
        {
            FileContractCategoryBO fileContractCategoryBO = new FileContractCategoryBO();
            fileContractCategoryBO.setCategoryId("cid");
            flowTemplateFileCategoryMap.put("key", Lists.newArrayList(fileContractCategoryBO));
        }
        Mockito.when(contractCategoryService.batchQueryRelatedFileCategories(Mockito.any(), Mockito.any())).thenReturn(flowTemplateFileCategoryMap);




       Assert.assertTrue(CollectionUtils.isNotEmpty(flowTemplateBizService.batchQueryFlowTemplateSimpleFiles(inputDTO)));

    }





}
