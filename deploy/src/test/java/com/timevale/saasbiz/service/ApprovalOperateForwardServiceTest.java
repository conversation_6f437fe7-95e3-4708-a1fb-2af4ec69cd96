package com.timevale.saasbiz.service;

import com.alibaba.fastjson.JSONObject;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseServiceTest;
import com.timevale.saasbiz.service.approval.approvaloperate.ApprovalOperateForwardService;
import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalAsyncOperateMsg;
import com.timevale.saasbiz.service.approval.approvaloperate.executor.ApprovalAsyncOperateProcessMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2024-04-21
 */
@Slf4j
@JsonDataIgnore
public class ApprovalOperateForwardServiceTest extends BaseServiceTest {

    @Autowired ApprovalOperateForwardService approvalOperateForwardService;

    @Test
    public void testApprovalDelete() {
        String groupBody = "{\"inputDTO\":{\"client\":\"WEB\",\"operateDatas\":[{\"approvalType\":\"2\",\"bizGroupId\":\"a55101b0-bbfb-40e7-a342-9a7e24bc87b9\"}],\"operateType\":\"DELETE\",\"operatorOid\":\"1dd946773d6c414abe0ebcb1efe0dc18\",\"subjectOid\":\"6581ecdcacd44778b2e5274f0ad7470c\"}}";

        ApprovalAsyncOperateMsg operateMsg = JSONObject.parseObject(groupBody, ApprovalAsyncOperateMsg.class);

        Assert.assertTrue(approvalOperateForwardService.asyncOperate(operateMsg), "asyncOperate fail");

        String body =
                "{\"appId\":\"**********\",\"approvalId\":\"ad2f5d833aa74faaa302aac28bc9b554\",\"approvalType\":\"2\",\"operateType\":\"DELETE\",\"operatorAccount\":{\"account\":\"***********\",\"appId\":\"**********\",\"contactMobile\":\"***********\",\"deleted\":false,\"gid\":\"a3dcb2f6017a4d8f9e4e6f9d8462b688\",\"loginMobile\":\"***********\",\"name\":\"张熙龙\",\"oid\":\"1dd946773d6c414abe0ebcb1efe0dc18\",\"organ\":false,\"realNameMobile\":\"***********\",\"uid\":\"357887f5c52a4dbd85e1ad0868d0f16f\"},\"subjectAccount\":{\"account\":\"***********\",\"appId\":\"**********\",\"deleted\":false,\"gid\":\"8909115d64e246e98a7d8b6844588096\",\"name\":\"esigntest测试企业yuhe\",\"oid\":\"6581ecdcacd44778b2e5274f0ad7470c\",\"organ\":true,\"realNameMobile\":\"***********\",\"uid\":\"3ca1fcbd30cf4593ad84c8d317177f6d\"}}";

        ApprovalAsyncOperateProcessMsg processMsg = JSONObject.parseObject(body, ApprovalAsyncOperateProcessMsg.class);

        Assert.assertTrue(approvalOperateForwardService.asyncOperateOne(processMsg), "asyncOperateOne fail");
    }

}
