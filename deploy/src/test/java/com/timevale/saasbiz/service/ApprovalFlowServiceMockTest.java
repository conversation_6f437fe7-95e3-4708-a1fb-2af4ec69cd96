package com.timevale.saasbiz.service;

import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.model.approval.dto.ApprovalFlowItemDTO;
import com.timevale.saasbiz.model.bean.approval.dto.input.ListApprovalFlowInputDTO;
import com.timevale.saasbiz.service.approval.impl.ApprovalFlowServiceImpl;
import org.mockito.InjectMocks;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * <AUTHOR>
 * @since 2023/8/8 11:18
 */
public class ApprovalFlowServiceMockTest extends BaseMockTest {
    @InjectMocks
    private ApprovalFlowServiceImpl approvalFlowService;

    @Test
    public void flowListNoGidTest() {

        PagerResult<ApprovalFlowItemDTO> result =  approvalFlowService.page(new ListApprovalFlowInputDTO());
        Assert.assertTrue(null != result && CollectionUtils.isEmpty(result.getItems()));
    }
}
