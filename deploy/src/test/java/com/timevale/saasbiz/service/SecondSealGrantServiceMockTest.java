package com.timevale.saasbiz.service;

import com.timevale.footstone.seal.facade.saas.output.BatchSealGrantOutput;
import com.timevale.footstone.seal.facade.saas.output.SealGrantSecondUpdateOutput;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.seal.SecondSealGrantClient;
import com.timevale.saasbiz.model.bean.seal.dto.input.BatchSealGrantInputDto;
import com.timevale.saasbiz.model.bean.seal.dto.input.SecondSealGrantUpdateInputDTO;
import com.timevale.saasbiz.model.bean.seal.dto.output.BatchSealGrantOutputDto;
import com.timevale.saasbiz.model.bean.seal.dto.output.SecondSealGrantUpdateOutputDTO;
import com.timevale.saasbiz.service.seal.impl.SecondSealGrantServiceImpl;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * @ClassName SecondSealGrantServiceMockTest
 * @Description
 * <AUTHOR>
 * @Date 2025/8/5 14:31
 **/
@JsonDataIgnore
public class SecondSealGrantServiceMockTest extends BaseMockTest {

    @InjectMocks
    private SecondSealGrantServiceImpl secondSealGrantService;
    @Mock
    private SecondSealGrantClient secondSealGrantClient;

    @Test
    private void batchSecondGrantSeal(){
        Mockito.when(secondSealGrantClient.batchSecondSealGrant(Mockito.any())).thenReturn(new BatchSealGrantOutput());
        BatchSealGrantOutputDto outputDTO = secondSealGrantService.batchSecondGrantSeal(new BatchSealGrantInputDto());
        Assert.assertNotNull(outputDTO);
    }

    @Test
    private void updateSecondGrant(){
        Mockito.when(secondSealGrantClient.updateGrantOfInternal(Mockito.any())).thenReturn(new SealGrantSecondUpdateOutput());
        SecondSealGrantUpdateInputDTO secondSealGrantUpdateInputDTO = new SecondSealGrantUpdateInputDTO();
        secondSealGrantUpdateInputDTO.setEffectiveTime(System.currentTimeMillis());
        secondSealGrantUpdateInputDTO.setExpireTime(System.currentTimeMillis()+ 24 * 60 * 60 * 1000L);
        SecondSealGrantUpdateOutputDTO outputDTO = secondSealGrantService.updateSecondGrant("", "", "", secondSealGrantUpdateInputDTO);
        Assert.assertNotNull(outputDTO);
    }
}
