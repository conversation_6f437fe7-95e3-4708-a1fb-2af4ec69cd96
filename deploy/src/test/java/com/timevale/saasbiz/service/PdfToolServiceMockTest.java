package com.timevale.saasbiz.service;

import com.google.common.collect.Lists;
import com.timevale.clmc.facade.api.model.bean.KeywordMatchItem;
import com.timevale.clmc.facade.api.model.bean.KeywordSearchResult;
import com.timevale.clmc.facade.api.model.bean.PagePosition;
import com.timevale.clmc.facade.api.model.bean.RectPosition;
import com.timevale.clmc.facade.api.model.output.RpcKeywordSearchV2Output;
import com.timevale.docmanager.service.result.DocInfoResult;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.clmc.ClmcClient;
import com.timevale.saasbiz.integration.doc.DocClient;
import com.timevale.saasbiz.model.bean.pdftool.dto.output.PdfTextSearchOutputDTO;
import com.timevale.saasbiz.service.pdftool.impl.PdfToolServiceImpl;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 *
 * <AUTHOR>
 * @since 2024-09-12
 */
@JsonDataIgnore
public class PdfToolServiceMockTest extends BaseMockTest {

    @InjectMocks private PdfToolServiceImpl pdfToolService;

    private ClmcClient clmcClient = Mockito.mock(ClmcClient.class);

    private DocClient docClient = Mockito.mock(DocClient.class);

    @Test
    public void testPdfTextSearch() {
        DocInfoResult docInfoResult = new DocInfoResult();
        docInfoResult.setFileKey("test_file_key");
        Mockito.when(docClient.queryDocInfo("test_file_id")).thenReturn(docInfoResult);

        RpcKeywordSearchV2Output searchV2Output = new RpcKeywordSearchV2Output();
        KeywordSearchResult keywordSearchResult = new KeywordSearchResult();
        keywordSearchResult.setKeyword("keyword");
        KeywordMatchItem matchItem = new KeywordMatchItem();
        PagePosition pagePosition = new PagePosition();
        pagePosition.setPage(1);
        RectPosition rectPosition = new RectPosition();
        rectPosition.setLeft(50);
        rectPosition.setRight(100);
        rectPosition.setTop(638);
        rectPosition.setBottom(600);
        pagePosition.setBlockPos(rectPosition);
        matchItem.setBlockPositions(Lists.newArrayList(pagePosition));
        keywordSearchResult.setMatchItems(Lists.newArrayList(matchItem));
        searchV2Output.setSearchResults(Lists.newArrayList(keywordSearchResult));

        Mockito.when(clmcClient.pdfTextSearch("test_file_key", Lists.newArrayList("keyword"))).thenReturn(searchV2Output);
        PdfTextSearchOutputDTO outputDTO = pdfToolService.pdfTextSearch("test_file_id", Lists.newArrayList("keyword"), "center");
        Assert.assertEquals(outputDTO.getSearchResults().size(), 1);

        outputDTO = pdfToolService.pdfTextSearch("test_file_id", Lists.newArrayList("keyword"), "lbrt");
        Assert.assertEquals(outputDTO.getSearchResults().size(), 1);
    }

    private void init() {
        Whitebox.setInternalState(pdfToolService, "clmcClient", clmcClient);
        Whitebox.setInternalState(pdfToolService, "docClient", docClient);
    }
}
