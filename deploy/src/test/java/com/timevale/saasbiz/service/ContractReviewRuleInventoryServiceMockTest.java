package com.timevale.saasbiz.service;

import com.google.common.collect.Lists;
import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.integration.contractreview.EpaasContractReviewClient;
import com.timevale.saasbiz.model.bean.common.dto.input.BaseInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.ContractTypePullDownBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.RuleInventoryDetailBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.bo.RuleInventoryListBO;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.input.*;
import com.timevale.saasbiz.model.bean.contractreview.ruleinventory.dto.output.PageRuleInventoryOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.service.contractreview.impl.ContractReviewRuleInventoryServiceImpl;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.sterna.contract.review.client.dto.res.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

@JsonDataIgnore
public class ContractReviewRuleInventoryServiceMockTest extends BaseMockTest {

    @InjectMocks
    private ContractReviewRuleInventoryServiceImpl contractReviewRuleInventoryService;

    @Mock
    private UserCenterService userCenterService;

    @Mock
    private EpaasContractReviewClient epaasContractReviewClient;

    @BeforeMethod
    public void setup() {
        // Initialize mocks if needed
    }

    @Test(description = "使用有效输入测试规则清单的保存功能")
    public void testSaveRuleInventory() {
        // Prepare test data
        SaveRuleInventoryInputDTO inputDTO = new SaveRuleInventoryInputDTO();
        inputDTO.setInventoryName("Test Inventory");
        inputDTO.setAccountId("testAccount");
        
        AccountDetailDTO accountDetailDTO = new AccountDetailDTO();
        accountDetailDTO.setName("Test User");
        
        ReviewRuleInventorySaveResp saveResp = new ReviewRuleInventorySaveResp();
        saveResp.setInventoryId("testId");

        // Setup mocks
        Mockito.when(userCenterService.queryAccountDetailByOid(Mockito.anyString()))
                .thenReturn(accountDetailDTO);
        Mockito.when(epaasContractReviewClient.ruleInventorySave(Mockito.any()))
                .thenReturn(saveResp);

        // Execute
        String result = contractReviewRuleInventoryService.saveRuleInventory(inputDTO);

        // Verify
        Assert.assertEquals(result, "testId");
        Mockito.verify(userCenterService).queryAccountDetailByOid(Mockito.anyString());
        Mockito.verify(epaasContractReviewClient).ruleInventorySave(Mockito.any());
    }

    @Test(description = "测试规则清单列表查询功能")
    public void testListRuleInventory() {
        // Prepare test data
        ListRuleInventoryInputDTO inputDTO = new ListRuleInventoryInputDTO();
        inputDTO.setInventoryName("Test");

        ReviewRuleInventoryListResp listResp = new ReviewRuleInventoryListResp();
        listResp.setInventoryId("testId");
        listResp.setInventoryName("Test Inventory");

        // Setup mocks
        Mockito.when(epaasContractReviewClient.ruleInventoryList(Mockito.any()))
                .thenReturn(Lists.newArrayList(listResp));

        // Execute
        List<RuleInventoryListBO> result = contractReviewRuleInventoryService.listRuleInventory(inputDTO);

        // Verify
        Assert.assertNotNull(result);
        Assert.assertEquals(result.size(), 1);
        Assert.assertEquals(result.get(0).getInventoryId(), "testId");
        Mockito.verify(epaasContractReviewClient).ruleInventoryList(Mockito.any());
    }

    @Test(description = "测试规则清单分页查询功能")
    public void testPageRuleInventory() {
        // Prepare test data
        PageRuleInventoryInputDTO inputDTO = new PageRuleInventoryInputDTO();
        inputDTO.setPageNum(1);
        inputDTO.setPageSize(10);

        ReviewRuleInventoryPageResp pageResp = new ReviewRuleInventoryPageResp();
        pageResp.setTotal(1L);
        ReviewRuleInventoryListResp listResp = new ReviewRuleInventoryListResp();
        listResp.setInventoryId("testId");
        pageResp.setInventoryList(Lists.newArrayList(listResp));

        // Setup mocks
        Mockito.when(epaasContractReviewClient.ruleInventoryPage(Mockito.any()))
                .thenReturn(pageResp);

        // Execute
        PageRuleInventoryOutputDTO result = contractReviewRuleInventoryService.pageRuleInventory(inputDTO);

        // Verify
        Assert.assertNotNull(result);
        Assert.assertEquals((long)result.getTotal(), 1L);
        Mockito.verify(epaasContractReviewClient).ruleInventoryPage(Mockito.any());
    }

    @Test(description = "测试获取规则清单详情功能")
    public void testRuleInventoryDetail() {
        // Prepare test data
        QueryRuleInventoryDetailInputDTO inputDTO = new QueryRuleInventoryDetailInputDTO();
        inputDTO.setInventoryId("testId");

        ReviewRuleInventoryResp detailResp = new ReviewRuleInventoryResp();
        detailResp.setInventoryId("testId");
        detailResp.setInventoryName("Test Inventory");

        // Setup mocks
        Mockito.when(epaasContractReviewClient.ruleInventoryDetail(Mockito.any()))
                .thenReturn(detailResp);

        // Execute
        RuleInventoryDetailBO result = contractReviewRuleInventoryService.ruleInventoryDetail(inputDTO);

        // Verify
        Assert.assertNotNull(result);
        Assert.assertEquals(result.getInventoryId(), "testId");
        Mockito.verify(epaasContractReviewClient).ruleInventoryDetail(Mockito.any());
    }

    @Test(description = "测试删除规则清单功能")
    public void testDeleteRuleInventory() {
        // Prepare test data
        DeleteRuleInventoryStatusInputDTO inputDTO = new DeleteRuleInventoryStatusInputDTO();
        inputDTO.setInventoryId("testId");

        // Execute
        contractReviewRuleInventoryService.deleteRuleInventory(inputDTO);

        // Verify
        Mockito.verify(epaasContractReviewClient).ruleInventoryDel(Mockito.any());
    }

    @Test(description = "测试更新规则清单状态功能")
    public void testUpdateRuleInventoryStatus() {
        // Prepare test data
        UpdateRuleInventoryStatusInputDTO inputDTO = new UpdateRuleInventoryStatusInputDTO();
        inputDTO.setInventoryId("testId");
        inputDTO.setStatus("1");  // Changed from int to String

        // Execute
        contractReviewRuleInventoryService.updateRuleInventoryStatus(inputDTO);

        // Verify
        Mockito.verify(epaasContractReviewClient).ruleInventoryStatus(Mockito.any());
    }

    @Test(description = "测试规则清单下拉功能")
    public void testContractTypePullDown() {
        BaseInputDTO inputDTO = new BaseInputDTO();

        ReviewContractTypeResp contractTypePullDownBO = new ReviewContractTypeResp();
        contractTypePullDownBO.setSelected("test");

        List<ContractTypeResp> contractTypeRespList = new ArrayList<>();
        ContractTypeResp dto = new ContractTypeResp();
        dto.setContractTypeName("aaa");
        dto.setContractTypeId("test");
        contractTypeRespList.add(dto);
        contractTypePullDownBO.setContractTypeRespList(contractTypeRespList);

        Mockito.when(epaasContractReviewClient.contractTypePullDown())
                .thenReturn(contractTypePullDownBO);

        ContractTypePullDownBO result = contractReviewRuleInventoryService.contractTypePullDown(inputDTO);

        Assert.assertNotNull(result);
        Assert.assertEquals(result.getSelected(), "test");
        Mockito.verify(epaasContractReviewClient).contractTypePullDown();
    }
}
