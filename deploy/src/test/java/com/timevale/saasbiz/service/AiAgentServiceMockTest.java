package com.timevale.saasbiz.service;

import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertFalse;
import static org.testng.Assert.assertNotNull;
import static org.testng.Assert.assertTrue;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import com.timevale.saasbiz.base.BasePowerMockTest;
import com.timevale.contractmanager.common.service.bean.ProcessFileAuthSimpleBean;
import com.timevale.contractmanager.common.service.enums.ProcessAuthFilePermissionEnum;
import com.timevale.contractmanager.common.service.enums.ProcessSecretEnum;
import com.timevale.contractmanager.common.service.model.QueryProcessFileAuthModel;
import com.timevale.contractmanager.common.service.result.ProcessFileAuthResult;
import com.timevale.contractmanager.common.service.result.QueryProcessFilesResult.ProcessFile;
import com.timevale.docmanager.service.input.DocListResult;
import com.timevale.saasbiz.integration.doc.DocClient;
import com.timevale.saasbiz.integration.filesystem.FileSystemClient;
import com.timevale.saasbiz.integration.process.ProcessClient;
import com.timevale.saasbiz.model.bean.ai.agent.dto.input.ReadFilesByProcessIdInputDTO;
import com.timevale.saasbiz.model.bean.ai.agent.dto.output.ReadFilesByProcessIdOutputDTO;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountDetailDTO;
import com.timevale.saasbiz.service.ai.agent.impl.AiAgentServiceImpl;
import com.timevale.saasbiz.service.usercenter.UserCenterService;

/**
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public class AiAgentServiceMockTest extends BasePowerMockTest {
    
    @InjectMocks
    private AiAgentServiceImpl aiAgentService;
    
    @Mock
    private ProcessClient processClient;
    
    @Mock
    private UserCenterService userCenterService;
    
    @Mock
    private DocClient docClient;
    
    @Mock
    private FileSystemClient fileSystemClient;
    
    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }
    
    @Test
    public void testReadFilesByProcessId_Success() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";
        Integer flowType = 1; // 使用数字替代枚举值
        
        ReadFilesByProcessIdInputDTO input = new ReadFilesByProcessIdInputDTO();
        input.setProcessId(processId);
        input.setOperatorId(operatorId);
        input.setTenantId(tenantId);
        input.setFlowType(flowType);
        
        // Mock ProcessClient
        ProcessFile processFile = new ProcessFile();
        processFile.setFileId("file1");
        processFile.setFileName("test.pdf");
        processFile.setFlowType(flowType);
        when(processClient.getProcessFiles(processId)).thenReturn(Collections.singletonList(processFile));

        ProcessFileAuthResult processFileAuthResult = new ProcessFileAuthResult();
        ProcessFileAuthSimpleBean processFileAuthSimpleBean = new ProcessFileAuthSimpleBean();
        processFileAuthSimpleBean.setFileId("file1");
        processFileAuthSimpleBean.setPermissions(Arrays.asList(ProcessAuthFilePermissionEnum.QUERY.getType()));
        processFileAuthResult.setVisibleType(ProcessSecretEnum.NONE_SECRET.getType());
        processFileAuthResult.setFiles(Arrays.asList(processFileAuthSimpleBean));
        when(processClient.queryProcessFileAuth(any(QueryProcessFileAuthModel.class))).thenReturn(processFileAuthResult);

        // Mock UserCenterService
        AccountDetailDTO userDetail = new AccountDetailDTO();
        userDetail.setOid(operatorId);
        userDetail.setName("Test User");
        userDetail.setGid("testGid");
        
        AccountDetailDTO tenantDetail = new AccountDetailDTO();
        tenantDetail.setOid(tenantId);
        tenantDetail.setName("Test Tenant");
        tenantDetail.setGid("testTenantGid");
        
        when(userCenterService.batchQueryAccountMapByOid(anyList()))
            .thenReturn(new java.util.HashMap<String, AccountDetailDTO>() {{
                put(operatorId, userDetail);
                put(tenantId, tenantDetail);
            }});
        
        // Mock DocClient
        DocListResult docResult = new DocListResult();
        docResult.setDocId("file1");
        docResult.setFileName("test.pdf");
        docResult.setTotalPageSize(5);
        docResult.setFileKey("testFileKey");
        when(docClient.getDocListByFileIds(anyList())).thenReturn(Collections.singletonList(docResult));
        
        // Mock FileSystemClient
        when(fileSystemClient.getDownloadUrl("testFileKey", false)).thenReturn("http://test-url.com/file1");
        
        // 执行测试
        ReadFilesByProcessIdOutputDTO result = aiAgentService.readFilesByProcessId(input);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getFileList());
        assertEquals(result.getFileList().size(), 1);
        ReadFilesByProcessIdOutputDTO.FileInfo fileInfo = result.getFileList().get(0);
        assertEquals(fileInfo.getFileId(), "file1");
        assertEquals(fileInfo.getFileName(), "test.pdf");
        assertEquals(fileInfo.getFileUrl(), "http://test-url.com/file1");
        assertEquals(fileInfo.getPageSize().intValue(), 5);
    }
    
    @Test
    public void testReadFilesByProcessId_EmptyResult() {
        // 准备测试数据
        ReadFilesByProcessIdInputDTO input = new ReadFilesByProcessIdInputDTO();
        input.setProcessId("testProcessId");
        input.setOperatorId("testOperatorId");
        input.setTenantId("testTenantId");
        input.setFlowType(1); // 使用数字替代枚举值
        
        // Mock ProcessClient返回空列表
        when(processClient.getProcessFiles(anyString())).thenReturn(Collections.emptyList());
        
        // 执行测试
        ReadFilesByProcessIdOutputDTO result = aiAgentService.readFilesByProcessId(input);
        
        // 验证结果
        assertNotNull(result);
        assertNull(result.getFileList());
    }

    @Test
    public void testGetPermissionFiles_Success() {
        // 准备测试数据
        String processId = "testProcessId";
        String operatorId = "testOperatorId";
        String tenantId = "testTenantId";
        Integer flowType = 1;

        // Mock UserCenterService
        AccountDetailDTO userDetail = new AccountDetailDTO();
        userDetail.setOid(operatorId);
        userDetail.setName("Test User");
        userDetail.setGid("testGid");
        
        AccountDetailDTO tenantDetail = new AccountDetailDTO();
        tenantDetail.setOid(tenantId);
        tenantDetail.setName("Test Tenant");
        tenantDetail.setGid("testTenantGid");
        
        when(userCenterService.batchQueryAccountMapByOid(anyList()))
            .thenReturn(new java.util.HashMap<String, AccountDetailDTO>() {{
                put(operatorId, userDetail);
                put(tenantId, tenantDetail);
            }});

        // Mock ProcessClient - 获取流程文件列表
        ProcessFile normalFile = new ProcessFile();
        normalFile.setFileId("normalFile1");
        normalFile.setFileName("normal.pdf");
        normalFile.setFlowType(flowType);
        normalFile.setAttachment(false);

        ProcessFile attachmentFile = new ProcessFile();
        attachmentFile.setFileId("attachmentFile1");
        attachmentFile.setFileName("attachment.pdf");
        attachmentFile.setFlowType(2);
        attachmentFile.setAttachment(true);

        ProcessFile fileNameNullFile = new ProcessFile();
        fileNameNullFile.setFileId("nullFileNameFile1");
        fileNameNullFile.setFileName(null);
        fileNameNullFile.setFlowType(flowType);
        fileNameNullFile.setAttachment(false);

        when(processClient.getProcessFiles(processId))
            .thenReturn(Arrays.asList(normalFile, attachmentFile, fileNameNullFile));

        // Mock DocClient - 处理文件名为空的情况
        DocListResult docResult = new DocListResult();
        docResult.setDocId("nullFileNameFile1");
        docResult.setFileName("retrieved.pdf");
        when(docClient.getDocListByFileIds(anyList()))
            .thenReturn(Collections.singletonList(docResult));

        // Mock ProcessClient - 查询文件权限
        ProcessFileAuthResult normalFileAuthResult = new ProcessFileAuthResult();
        ProcessFileAuthSimpleBean normalFileAuth = new ProcessFileAuthSimpleBean();
        normalFileAuth.setFileId("normalFile1");
        normalFileAuth.setPermissions(Arrays.asList(ProcessAuthFilePermissionEnum.QUERY.getType()));
        normalFileAuthResult.setVisibleType(ProcessSecretEnum.NONE_SECRET.getType());
        normalFileAuthResult.setFiles(Arrays.asList(normalFileAuth));

        ProcessFileAuthResult attachmentFileAuthResult = new ProcessFileAuthResult();
        ProcessFileAuthSimpleBean attachmentFileAuth = new ProcessFileAuthSimpleBean();
        attachmentFileAuth.setFileId("attachmentFile1");
        attachmentFileAuth.setPermissions(Arrays.asList(ProcessAuthFilePermissionEnum.QUERY.getType()));
        attachmentFileAuthResult.setVisibleType(ProcessSecretEnum.NONE_SECRET.getType());
        attachmentFileAuthResult.setFiles(Arrays.asList(attachmentFileAuth));

        when(processClient.queryProcessFileAuth(any(QueryProcessFileAuthModel.class)))
            .thenReturn(normalFileAuthResult)
            .thenReturn(attachmentFileAuthResult);

        // 执行测试
        List<ReadFilesByProcessIdOutputDTO.FileInfo> result = aiAgentService.getPermissionFiles(processId, flowType, operatorId, tenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(result.size(), 3);
        
        // 验证正常文件
        ReadFilesByProcessIdOutputDTO.FileInfo normalFileInfo = result.stream()
            .filter(f -> f.getFileId().equals("normalFile1"))
            .findFirst()
            .orElse(null);
        assertNotNull(normalFileInfo);
        assertEquals(normalFileInfo.getFileName(), "normal.pdf");
        assertFalse(normalFileInfo.getIsAttachment());

        // 验证附件文件
        ReadFilesByProcessIdOutputDTO.FileInfo attachmentFileInfo = result.stream()
            .filter(f -> f.getFileId().equals("attachmentFile1"))
            .findFirst()
            .orElse(null);
        assertNotNull(attachmentFileInfo);
        assertEquals(attachmentFileInfo.getFileName(), "attachment.pdf");
        assertTrue(attachmentFileInfo.getIsAttachment());
    }
}
