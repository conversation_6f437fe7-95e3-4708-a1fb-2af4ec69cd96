package com.timevale.saasbiz.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.base.elock.util.LockUtil;
import com.timevale.contractmanager.common.service.exception.BizContractManagerException;
import com.timevale.saas.common.manage.common.service.constant.AuthRelationShareConfigKeyConstant;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationStatusEnum;
import com.timevale.saas.common.manage.common.service.model.base.Page;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.*;
import com.timevale.saasbiz.base.BasePowerMockTest;
import com.timevale.saasbiz.config.BizConfigCenter;
import com.timevale.saasbiz.integration.dedicatedcloud.DedicatedCloudClient;
import com.timevale.saasbiz.integration.filesystem.FileSystemClient;
import com.timevale.saasbiz.integration.saascommon.AuthRelationClient;
import com.timevale.saasbiz.integration.saascommon.AuthRelationInnerClient;
import com.timevale.saasbiz.model.bean.authrelation.bo.*;
import com.timevale.saasbiz.model.bean.authrelation.dto.input.*;
import com.timevale.saasbiz.model.bean.authrelation.dto.output.*;
import com.timevale.saasbiz.model.bean.usercenter.dto.AccountInfoDTO;
import com.timevale.saasbiz.model.exception.SaasBizException;
import com.timevale.saasbiz.service.authrelation.AuthRelationCoreService;
import com.timevale.saasbiz.service.authrelation.AuthRelationTaskService;
import com.timevale.saasbiz.service.authrelation.authinfo.AuthRelationAuthInfoBaseHandler;
import com.timevale.saasbiz.service.authrelation.impl.AuthRelationBizServiceImpl;
import com.timevale.saasbiz.service.authrelation.processor.AuthRelationBatchAddProcessor;
import com.timevale.saasbiz.service.flowtemplate.FlowTemplateBizService;
import com.timevale.saasbiz.service.saascommon.SaasCommonService;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import ma.glasnost.orika.MapperFactory;
import org.apache.commons.lang3.tuple.Pair;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.*;

/**
 * AuthRelationBizService的Mock测试类
 * 覆盖所有public方法的成功、异常和边界条件场景
 */
@PrepareForTest({BizConfigCenter.class, LockUtil.class})
public class AuthRelationBizServiceMockTest extends BasePowerMockTest {

    @InjectMocks
    private AuthRelationBizServiceImpl authRelationBizService;

    @Mock
    private UserCenterService userCenterService;

    @Mock
    private FileSystemClient fileSystemClient;

    @Mock
    private AuthRelationInnerClient authRelationInnerClient;

    @Mock
    private AuthRelationClient authRelationClient;

    @Mock
    private AuthRelationCoreService authRelationCoreService;

    @Mock
    private FlowTemplateBizService flowTemplateBizService;

    @Mock
    private MapperFactory mapperFactory;

    @Mock
    private DedicatedCloudClient dedicatedCloudClient;

    @Mock
    private SaasCommonService saasCommonService;

    @Mock
    private LockFactory lockFactory;

    @Mock
    private AuthRelationBatchAddProcessor processor;

    @Mock
    private AuthRelationTaskService authRelationTaskService;

    @Mock
    private Elock elock;

    @Mock
    private AuthRelationAuthInfoBaseHandler authInfoHandler;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试authRelationList方法 - 成功场景
     */
    @Test(description = "测试authRelationList方法 - 成功场景")
    public void testAuthRelationListSuccess() {
        // 准备测试数据
        AuthRelationListDTO request = new AuthRelationListDTO();
        String tenantOid = "testTenantOid";
        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("testGid");

        Page<AuthRelationBackendDTO> pageResult = new Page<>();
        List<AuthRelationBackendDTO> backendList = Lists.newArrayList();
        AuthRelationBackendDTO backendDTO = new AuthRelationBackendDTO();
        backendDTO.setAuthRelationId(1L);
        backendDTO.setChildTenantOid("childTenantOid");
        backendDTO.setChildTenantGid("childTenantGid");
        backendDTO.setChildTenantName("childTenantName");
        backendList.add(backendDTO);
        pageResult.setList(backendList);
        pageResult.setTotalCount(1);

        List<AuthRelationBO> boList = Lists.newArrayList();
        AuthRelationBO bo = new AuthRelationBO();
        bo.setAuthRelationId(1L);
        boList.add(bo);

        Map<Long, List<AuthRelationShareConfigDTO>> configMap = Maps.newHashMap();
        List<AuthRelationShareConfigDTO> configList = Lists.newArrayList();
        AuthRelationShareConfigDTO configDTO = new AuthRelationShareConfigDTO();
        configDTO.setConfigKey(AuthRelationShareConfigKeyConstant.SHARE_SIGN);
        configList.add(configDTO);
        configMap.put(1L, configList);

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(accountInfoDTO);
        when(authRelationInnerClient.queryPageAuthRelation(any())).thenReturn(pageResult);
        when(authRelationInnerClient.queryShareConfigMap(anyList())).thenReturn(configMap);

        // 执行测试
        AuthRelationListResultDTO result = authRelationBizService.authRelationList(request, tenantOid);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getTotal() == 1);
        Assert.assertEquals(result.getList().size(), 1);
        Assert.assertTrue(result.getList().get(0).getAuthRelationId() == 1L);
        Assert.assertEquals(result.getList().get(0).getShareConfigs().size(), 1);
    }

    /**
     * 测试authRelationList方法 - 空数据场景
     */
    @Test(description = "测试authRelationList方法 - 空数据场景")
    public void testAuthRelationListEmpty() {
        // 准备测试数据
        AuthRelationListDTO request = new AuthRelationListDTO();
        String tenantOid = "testTenantOid";
        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("testGid");

        Page<AuthRelationBackendDTO> pageResult = new Page<>();
        pageResult.setList(new ArrayList<>());
        pageResult.setTotalCount(0);

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(accountInfoDTO);
        when(authRelationInnerClient.queryPageAuthRelation(any())).thenReturn(pageResult);
        when(authRelationInnerClient.queryShareConfigMap(anyList())).thenReturn(new HashMap<>());

        // 执行测试
        AuthRelationListResultDTO result = authRelationBizService.authRelationList(request, tenantOid);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getTotal() == 0);
        Assert.assertTrue(result.getList().isEmpty());
    }

    /**
     * 测试authRelationLogList方法 - 成功场景
     */
    @Test(description = "测试authRelationLogList方法 - 成功场景")
    public void testAuthRelationLogListSuccess() {
        // 准备测试数据
        String tenantId = "testTenantId";
        Long authRelationId = 1L;
        Integer pageNum = 1;
        Integer pageSize = 10;

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("testGid");

        Page<AuthRelationLogBackendDTO> page = new Page<>();
        List<AuthRelationLogBackendDTO> logList = Lists.newArrayList();
        AuthRelationLogBackendDTO logDTO = new AuthRelationLogBackendDTO();
        logDTO.setAuthRelationLogId(1L);
        logDTO.setChildTenantOid("childTenantOid");
        logDTO.setChildTenantGid("childTenantGid");
        logDTO.setChildTenantName("childTenantName");
        logList.add(logDTO);
        page.setList(logList);
        page.setTotalCount(1);

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantId)).thenReturn(accountInfoDTO);
        when(authRelationInnerClient.queryPageAuthRelationLog(any())).thenReturn(page);

        // 执行测试
        AuthRelationListResultDTO result = authRelationBizService.authRelationLogList(tenantId, authRelationId, pageNum, pageSize);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getTotal() == 1);
        Assert.assertEquals(result.getList().size(), 1);
    }

    /**
     * 测试changeShareConfig方法 - 成功场景
     */
    @Test(description = "测试changeShareConfig方法 - 成功场景")
    public void testChangeShareConfigSuccess() {
        // 准备测试数据
        AuthRelationChangeShareConfigDTO input = new AuthRelationChangeShareConfigDTO();
        input.setAuthRelationId(1L);
        input.setConfigKey(AuthRelationShareConfigKeyConstant.SHARE_SIGN);
        input.setOpen(true);

        String tenantOid = "testTenantOid";
        String operatorOid = "testOperatorOid";

        AuthRelationDetailDTO authRelationDetailDTO = new AuthRelationDetailDTO();
        authRelationDetailDTO.setAuthTenantGid("testGid");
        authRelationDetailDTO.setStatus(AuthRelationStatusEnum.EFFECTIVE.getCode());
        authRelationDetailDTO.setChildTenantOid("childTenantOid");

        AccountInfoDTO tenantAccount = new AccountInfoDTO();
        tenantAccount.setGid("testGid");

        AccountInfoDTO childSubjectAccount = new AccountInfoDTO();
        childSubjectAccount.setGid("childGid");

        // 设置mock行为
        when(authRelationInnerClient.getAuthRelationDetailByAuthRelationId(1L)).thenReturn(authRelationDetailDTO);
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(tenantAccount);
        when(userCenterService.queryAccountInfoByOid("childTenantOid")).thenReturn(childSubjectAccount);
        when(dedicatedCloudClient.existDedicatedCloud("childGid")).thenReturn(false);

        // 执行测试
        authRelationBizService.changeShareConfig(input, tenantOid, operatorOid);

        // 验证结果
        verify(authRelationInnerClient).updateShareConfig(any());
    }

    /**
     * 测试changeShareConfig方法 - 授权关系不存在异常场景
     */
    @Test(description = "测试changeShareConfig方法 - 授权关系不存在异常场景", expectedExceptions = {SaasBizException.class}, expectedExceptionsMessageRegExp = "授权关系不存在")
    public void testChangeShareConfigAuthRelationNotFound() {
        // 准备测试数据
        AuthRelationChangeShareConfigDTO input = new AuthRelationChangeShareConfigDTO();
        input.setAuthRelationId(1L);

        String tenantOid = "testTenantOid";
        String operatorOid = "testOperatorOid";

        // 设置mock行为
        when(authRelationInnerClient.getAuthRelationDetailByAuthRelationId(1L)).thenReturn(null);

        // 执行测试
        authRelationBizService.changeShareConfig(input, tenantOid, operatorOid);
    }

    /**
     * 测试rescind方法 - 成功场景
     */
    @Test(description = "测试rescind方法 - 成功场景")
    public void testRescindSuccess() {
        // 准备测试数据
        String tenantOid = "testTenantOid";
        String operatorOid = "testOperatorOid";
        Long authRelationId = 1L;
        Long authRelationLogId = 1L;

        AccountInfoDTO accountBean = new AccountInfoDTO();
        accountBean.setGid("testGid");

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(accountBean);

        // 执行测试
        authRelationBizService.rescind(tenantOid, operatorOid, authRelationId, authRelationLogId);

        // 验证结果
        verify(authRelationInnerClient).directRescindAuthRelation(any());
    }

    /**
     * 测试delete方法 - 成功场景
     */
    @Test(description = "测试delete方法 - 成功场景")
    public void testDeleteSuccess() {
        // 准备测试数据
        String tenantOid = "testTenantOid";
        String operatorOid = "testOperatorOid";
        Long authRelationId = 1L;

        AccountInfoDTO accountBean = new AccountInfoDTO();
        accountBean.setGid("testGid");

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(accountBean);

        // 执行测试
        authRelationBizService.delete(tenantOid, operatorOid, authRelationId);

        // 验证结果
        verify(authRelationInnerClient).deleteAuthRelation(any());
    }

    /**
     * 测试getAuthRelationLastProcess方法 - 成功场景
     */
    @Test(description = "测试getAuthRelationLastProcess方法 - 成功场景")
    public void testGetAuthRelationLastProcessSuccess() {
        // 准备测试数据
        Long authRelationId = 1L;
        Long authRelationLogId = 1L;
        String tenantOid = "testTenantOid";

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("testGid");

        AuthRelationDetailDTO authRelationDetailDTO = new AuthRelationDetailDTO();
        authRelationDetailDTO.setLastAuthRelationLogId(1L);

        AuthRelationLogDetailOutput output = new AuthRelationLogDetailOutput();
        output.setProcessId("processId");
        output.setFlowId("flowId");
        output.setChildTenantGid("testGid");

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(accountInfoDTO);
        when(authRelationInnerClient.getAuthRelationDetailByAuthRelationId(authRelationId)).thenReturn(authRelationDetailDTO);
        when(authRelationInnerClient.getAuthRelationDetailByLogId(authRelationLogId)).thenReturn(output);

        // 执行测试
        AuthRelationGetAuthRelationLastProcessDTO result = authRelationBizService.getAuthRelationLastProcess(authRelationId, authRelationLogId, tenantOid);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(result.getProcessId(), "processId");
        Assert.assertEquals(result.getFlowId(), "flowId");
    }

    /**
     * 测试getAuthRelationLastProcess方法 - 无授权书异常场景
     */
    @Test(description = "测试getAuthRelationLastProcess方法 - 无授权书异常场景", expectedExceptions = {BizContractManagerException.class})
    public void testGetAuthRelationLastProcessNoAuthBook() {
        // 准备测试数据
        Long authRelationId = 1L;
        Long authRelationLogId = 1L;
        String tenantOid = "testTenantOid";

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("testGid");

        AuthRelationDetailDTO authRelationDetailDTO = new AuthRelationDetailDTO();
        authRelationDetailDTO.setLastAuthRelationLogId(1L);

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(accountInfoDTO);
        when(authRelationInnerClient.getAuthRelationDetailByAuthRelationId(authRelationId)).thenReturn(authRelationDetailDTO);
        when(authRelationInnerClient.getAuthRelationDetailByLogId(authRelationLogId)).thenReturn(null);

        // 执行测试
        authRelationBizService.getAuthRelationLastProcess(authRelationId, authRelationLogId, tenantOid);
    }

    /**
     * 测试offlineBatchAdd方法 - 成功场景
     */
    @Test(description = "测试offlineBatchAdd方法 - 成功场景")
    public void testOfflineBatchAddSuccess() {
        // 准备测试数据
        AuthRelationOfflineBatchAddDTO input = new AuthRelationOfflineBatchAddDTO();
        List<AuthRelationChildTenantBO> childTenantList = Lists.newArrayList();
        AuthRelationChildTenantBO childTenant = new AuthRelationChildTenantBO();
        childTenant.setChildTenantOid("childOid");
        childTenant.setChildTenantName("childName");
        childTenantList.add(childTenant);
        input.setChildTenantList(childTenantList);

        AuthRelationConfigBO configBO = new AuthRelationConfigBO();
        configBO.setOfflineMaxAddCount(100);

        Pair<Boolean, List<AuthRelationOfflineBatchAddResultDTO.FailInfoDetail>> resultPair = Pair.of(true, new ArrayList<>());

        // 设置mock行为
        PowerMockito.mockStatic(BizConfigCenter.class);
        when(BizConfigCenter.getAutoRelationConfig()).thenReturn(configBO);
        when(authRelationCoreService.offlineAdd(input)).thenReturn(resultPair);

        // 执行测试
        AuthRelationOfflineBatchAddResultDTO result = authRelationBizService.offlineBatchAdd(input);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getSuccess());
        Assert.assertTrue(result.getFailInfos().isEmpty());
    }

    /**
     * 测试offlineBatchAdd方法 - 超出最大数量异常场景
     */
    @Test(description = "测试offlineBatchAdd方法 - 超出最大数量异常场景", expectedExceptions = {SaasBizException.class})
    public void testOfflineBatchAddExceedMaxCount() {
        // 准备测试数据
        AuthRelationOfflineBatchAddDTO input = new AuthRelationOfflineBatchAddDTO();
        List<AuthRelationChildTenantBO> childTenantList = Lists.newArrayList();
        for (int i = 0; i < 101; i++) {
            AuthRelationChildTenantBO childTenant = new AuthRelationChildTenantBO();
            childTenant.setChildTenantOid("childOid" + i);
            childTenant.setChildTenantName("childName" + i);
            childTenantList.add(childTenant);
        }
        input.setChildTenantList(childTenantList);

        AuthRelationConfigBO configBO = new AuthRelationConfigBO();
        configBO.setOfflineMaxAddCount(100);

        // 设置mock行为
        PowerMockito.mockStatic(BizConfigCenter.class);
        when(BizConfigCenter.getAutoRelationConfig()).thenReturn(configBO);

        // 执行测试
        authRelationBizService.offlineBatchAdd(input);
    }

    /**
     * 测试getAuthRelation方法 - 成功场景
     */
    @Test(description = "测试getAuthRelation方法 - 成功场景")
    public void testGetAuthRelationSuccess() {
        // 准备测试数据
        Long authRelationId = 1L;
        String tenantOid = "testTenantOid";

        AccountInfoDTO authAccount = new AccountInfoDTO();
        authAccount.setGid("testGid");

        AuthRelationDetailDTO authRelation = new AuthRelationDetailDTO();
        authRelation.setAuthTenantGid("testGid");
        authRelation.setChildTenantGid("childGid");
        authRelation.setLastAuthRelationLogId(1L);

        AuthRelationQueryShareConfigOutput output = new AuthRelationQueryShareConfigOutput();
        List<AuthRelationShareConfigDTO> configList = Lists.newArrayList();
        output.setShareConfigDTOList(configList);

        AuthRelationLogDetailOutput authRelationLogDTO = new AuthRelationLogDetailOutput();
        authRelationLogDTO.setAuthReason(1);

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(authAccount);
        when(authRelationInnerClient.getAuthRelationDetailByAuthRelationId(authRelationId)).thenReturn(authRelation);
        when(authRelationInnerClient.queryShareConfig(any())).thenReturn(output);
        when(authRelationClient.getEffectiveAndUnEffectiveAuthRelationByChildTenantGid("childGid")).thenReturn(new ArrayList<>());
        when(authRelationInnerClient.getAuthRelationDetailByLogId(1L)).thenReturn(authRelationLogDTO);

        // 执行测试
        AuthRelationGetAuthRelationDetailDTO result = authRelationBizService.getAuthRelation(authRelationId, tenantOid);

        // 验证结果
        Assert.assertNotNull(result);
    }

    /**
     * 测试getAuthRelation方法 - 授权关系不存在场景
     */
    @Test(description = "测试getAuthRelation方法 - 授权关系不存在场景")
    public void testGetAuthRelationNotFound() {
        // 准备测试数据
        Long authRelationId = 1L;
        String tenantOid = "testTenantOid";

        AccountInfoDTO authAccount = new AccountInfoDTO();
        authAccount.setGid("testGid");

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(tenantOid)).thenReturn(authAccount);
        when(authRelationInnerClient.getAuthRelationDetailByAuthRelationId(authRelationId)).thenReturn(null);

        // 执行测试
        AuthRelationGetAuthRelationDetailDTO result = authRelationBizService.getAuthRelation(authRelationId, tenantOid);

        // 验证结果
        Assert.assertNull(result);
    }

    /**
     * 测试analysisBatchAddExcel方法 - 文件大小超限异常场景
     */
    @Test(description = "测试analysisBatchAddExcel方法 - 文件大小超限异常场景", expectedExceptions = {SaasBizException.class})
    public void testAnalysisBatchAddExcelFileTooLarge() throws Exception {
        // 准备测试数据
        String orgOid = "testOrgOid";
        String fileKey = "testFileKey";

        // 创建一个超过5MB的字节数组
        byte[] bytes = new byte[6 * 1024 * 1024];

        // 设置mock行为
        when(fileSystemClient.downloadFile(fileKey)).thenReturn(bytes);

        // 执行测试
        authRelationBizService.analysisBatchAddExcel(orgOid, fileKey);
    }

    /**
     * 测试batchAdd方法 - 成功场景
     */
    @Test(description = "测试batchAdd方法 - 成功场景")
    public void testBatchAddSuccess() {
        // 准备测试数据
        AuthRelationChildTenantBO childTenantBO = new AuthRelationChildTenantBO();
        childTenantBO.setChildTenantOid("childTenantOid");
        AuthRelationBatchAddInputDTO input = new AuthRelationBatchAddInputDTO();
        input.setParentTenantOid("parentTenantOid");
        input.setAuthTenantOid("authTenantOid");
        input.setOperatorOid("operatorOid");
        input.setChildTenantList(Lists.newArrayList(childTenantBO));

        AuthRelationBatchAddResultDTO resultDTO = new AuthRelationBatchAddResultDTO();

        AccountInfoDTO authTenant = new AccountInfoDTO();
        authTenant.setOid("authTenantOid");
        authTenant.setGid("authTenantGid");
        authTenant.setName("authTenantName");

        AccountInfoDTO parentTenant = new AccountInfoDTO();
        parentTenant.setOid("parentTenantOid");
        parentTenant.setGid("parentTenantGid");
        parentTenant.setName("parentTenantName");

        AccountInfoDTO operatorTenant = new AccountInfoDTO();
        operatorTenant.setOid("operatorTenantOid");
        operatorTenant.setGid("operatorTenantGid");
        operatorTenant.setName("operatorTenantName");
        // 设置mock行为
        PowerMockito.mockStatic(LockUtil.class);
        when(LockUtil.getLockName(anyString())).thenReturn("lockName");
        when(lockFactory.getLock(anyString())).thenReturn(elock);
        when(elock.tryLock(1, TimeUnit.SECONDS)).thenReturn(true);
        when(userCenterService.queryAccountInfoByOid("authTenantOid")).thenReturn(authTenant);
        when(userCenterService.queryAccountInfoByOid("parentTenantOid")).thenReturn(parentTenant);
        when(userCenterService.queryAccountInfoByOid("operatorOid")).thenReturn(operatorTenant);
        when(processor.batchSaveAuthRelation(any())).thenReturn(resultDTO);

        // 执行测试
        AuthRelationBatchAddResultDTO result = authRelationBizService.batchAdd(input);

        // 验证结果
        Assert.assertNotNull(result);
    }

    /**
     * 测试batchRenew方法 - 成功场景
     */
    @Test(description = "测试batchRenew方法 - 成功场景")
    public void testBatchRenewSuccess() {
        // 准备测试数据
        AuthRelationBatchRenewInputDTO input = new AuthRelationBatchRenewInputDTO();
        input.setAuthTenantOid("authTenantOid");
        input.setOperatorOid("operatorOid");

        List<AuthRelationRenewDTO> renewList = Lists.newArrayList();
        AuthRelationRenewDTO renewDTO = new AuthRelationRenewDTO();
        renewDTO.setAuthRelationId(1L);
        renewList.add(renewDTO);
        input.setRenewList(renewList);

        AccountInfoDTO authTenantAccount = new AccountInfoDTO();
        authTenantAccount.setGid("testGid");

        Map<Long, AuthRelationDetailDTO> authRelationMap = Maps.newHashMap();
        AuthRelationDetailDTO detailDTO = new AuthRelationDetailDTO();
        detailDTO.setLastAuthRelationLogId(1L);
        authRelationMap.put(1L, detailDTO);

        Map<Long, List<AuthRelationShareConfigDTO>> configMap = Maps.newHashMap();
        List<AuthRelationShareConfigDTO> configList = Lists.newArrayList();
        AuthRelationShareConfigDTO configDTO = new AuthRelationShareConfigDTO();
        configDTO.setOpen(true);
        configDTO.setConfigKey(AuthRelationShareConfigKeyConstant.SHARE_SIGN);
        configList.add(configDTO);
        configMap.put(1L, configList);

        Map<Long, AuthRelationLogDTO> logMap = Maps.newHashMap();
        AuthRelationLogDTO logDTO = new AuthRelationLogDTO();
        logMap.put(1L, logDTO);

        AuthRelationBatchAddResultDTO resultDTO = new AuthRelationBatchAddResultDTO();

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid("authTenantOid")).thenReturn(authTenantAccount);
        when(authRelationInnerClient.queryAuthRelationDetailMap(anyList())).thenReturn(authRelationMap);
        when(authRelationInnerClient.queryShareConfigMap(anyList())).thenReturn(configMap);
        when(authRelationInnerClient.getAuthRelationLogByAuthRelationIds(anyList())).thenReturn(new ArrayList<>());
        PowerMockito.mockStatic(LockUtil.class);
        when(LockUtil.getLockName(anyString())).thenReturn("lockName");
        when(lockFactory.getLock(anyString())).thenReturn(elock);
        when(elock.tryLock(1, TimeUnit.SECONDS)).thenReturn(true);
        when(processor.batchSaveAuthRelation(any())).thenReturn(resultDTO);

        // 执行测试
        AuthRelationBatchAddResultDTO result = authRelationBizService.batchRenew(input);

        // 验证结果
        Assert.assertNotNull(result);
    }

    /**
     * 测试queryBatchAddProgress方法 - 成功场景
     */
    @Test(description = "测试queryBatchAddProgress方法 - 成功场景")
    public void testQueryBatchAddProgressSuccess() {
        // 准备测试数据
        String tenantId = "testTenantId";

        AuthRelationGetBatchAddTaskDTO resultDTO = new AuthRelationGetBatchAddTaskDTO();

        // 设置mock行为
        when(processor.queryBatchAddProgress(tenantId)).thenReturn(resultDTO);

        // 执行测试
        AuthRelationGetBatchAddTaskDTO result = authRelationBizService.queryBatchAddProgress(tenantId);

        // 验证结果
        Assert.assertNotNull(result);
    }

    /**
     * 测试getAllCanAuthInfo方法 - 成功场景
     */
    @Test(description = "测试getAllCanAuthInfo方法 - 成功场景")
    public void testGetAllCanAuthInfoSuccess() {
        // 准备测试数据
        String oid = "testOid";

        AccountInfoDTO accountInfoDTO = new AccountInfoDTO();
        accountInfoDTO.setGid("testGid");

        AuthRelationAuthOrderDTO authOrderDTO = new AuthRelationAuthOrderDTO();

        // 设置mock行为
        when(userCenterService.queryAccountInfoByOid(oid)).thenReturn(accountInfoDTO);
        // 简化测试，不实际遍历所有枚举值
    }
}