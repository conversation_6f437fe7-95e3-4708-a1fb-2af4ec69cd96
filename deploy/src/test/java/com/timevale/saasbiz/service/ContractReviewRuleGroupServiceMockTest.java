package com.timevale.saasbiz.service;

import com.timevale.qa.apitest.model.JsonDataIgnore;
import com.timevale.saasbiz.base.BaseMockTest;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.ListRuleGroupInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.SaveRuleGroupInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.dto.input.DeleteRuleGroupInputDTO;
import com.timevale.saasbiz.model.bean.contractreview.rulegroup.bo.RuleGroupListBO;
import com.timevale.saasbiz.integration.contractreview.EpaasContractReviewClient;
import com.timevale.saasbiz.service.contractreview.impl.ContractReviewRuleGroupServiceImpl;
import com.timevale.saasbiz.service.usercenter.UserCenterService;
import com.timevale.sterna.contract.review.client.dto.res.ReviewRuleGroupResp;
import com.timevale.sterna.contract.review.client.dto.res.rulegroup.ReviewRuleGroupSaveResp;
import com.timevale.sterna.contract.review.client.dto.res.RuleDelResp;
import com.timevale.saasbiz.model.bean.contractreview.RuleDelOutputDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import java.util.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;

@JsonDataIgnore
public class ContractReviewRuleGroupServiceMockTest extends BaseMockTest {
    @Mock
    private UserCenterService userCenterService;
    @Mock
    private EpaasContractReviewClient epaasContractReviewClient;

    @InjectMocks
    private ContractReviewRuleGroupServiceImpl contractReviewRuleGroupServiceImpl;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test(description = "测试listRuleGroup方法，校验规则组列表查询逻辑")
    public void testListRuleGroup() {
        ListRuleGroupInputDTO inputDTO = new ListRuleGroupInputDTO();
        inputDTO.setTenantId("tenant1");
        inputDTO.setAccountId("account1");
        inputDTO.setInventoryId("inv1");

        ReviewRuleGroupResp resp = mock(ReviewRuleGroupResp.class);
        when(resp.getGroupName()).thenReturn("分组A");
        when(resp.getGroupId()).thenReturn("gid1");
        when(resp.getCount()).thenReturn(3);
        when(resp.getGroupDefaultFlag()).thenReturn(1);
        when(epaasContractReviewClient.getRuleGroupList(any())).thenReturn(Collections.singletonList(resp));

        List<RuleGroupListBO> result = contractReviewRuleGroupServiceImpl.listRuleGroup(inputDTO);
        assertEquals(result.size(), 1);
        assertEquals(result.get(0).getGroupName(), "分组A");
        assertEquals(result.get(0).getGroupId(), "gid1");
        assertEquals(result.get(0).getCount(), Integer.valueOf(3));
        assertEquals(result.get(0).getGroupDefaultFlag(), Integer.valueOf(1));
    }

    @Test(description = "测试saveRuleGroup方法，校验规则组保存逻辑")
    public void testSaveRuleGroup() {
        SaveRuleGroupInputDTO inputDTO = new SaveRuleGroupInputDTO();
        inputDTO.setTenantId("tenant1");
        inputDTO.setAccountId("account1");
        inputDTO.setGroupId("gid1");
        inputDTO.setGroupName("分组A");
        inputDTO.setGroupIds(Arrays.asList("gid1", "gid2"));

        ReviewRuleGroupSaveResp resp = mock(ReviewRuleGroupSaveResp.class);
        when(resp.getGroupId()).thenReturn("gid1");
        when(epaasContractReviewClient.ruleGroupSave(any())).thenReturn(resp);

        String result = contractReviewRuleGroupServiceImpl.saveRuleGroup(inputDTO);
        assertEquals(result, "gid1");
    }

    @Test(description = "测试confirmDeleteRuleGroup方法，校验规则组删除前确认逻辑")
    public void testConfirmDeleteRuleGroup() {
        DeleteRuleGroupInputDTO inputDTO = new DeleteRuleGroupInputDTO();
        inputDTO.setTenantId("tenant1");
        inputDTO.setAccountId("account1");
        inputDTO.setGroupIds(Arrays.asList("gid1", "gid2"));

        RuleDelResp ruleDelResp = new RuleDelResp();
        when(epaasContractReviewClient.ruleGroupDelConfirm(any())).thenReturn(ruleDelResp);

        RuleDelOutputDTO result = contractReviewRuleGroupServiceImpl.confirmDeleteRuleGroup(inputDTO);
        assertNotNull(result);
    }

    @Test(description = "测试deleteRuleGroup方法，校验规则组删除逻辑")
    public void testDeleteRuleGroup() {
        DeleteRuleGroupInputDTO inputDTO = new DeleteRuleGroupInputDTO();
        inputDTO.setTenantId("tenant1");
        inputDTO.setAccountId("account1");
        inputDTO.setGroupIds(Arrays.asList("gid1", "gid2"));

        doNothing().when(epaasContractReviewClient).ruleGroupDel(any());
        contractReviewRuleGroupServiceImpl.deleteRuleGroup(inputDTO);
        verify(epaasContractReviewClient, times(1)).ruleGroupDel(any());
    }
}
