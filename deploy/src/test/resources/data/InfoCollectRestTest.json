{"createFormReq": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormSaveRequest", "formJson": "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":255},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"50%\",\"widthType\":\"1/2\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"单行文本\",\"key\":\"r3opz1b9\",\"model\":\"input_r3opz1b9\",\"alias\":\"input_r3opz1b9\",\"rules\":[],\"row\":0,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"https://asset.esign.cn/lowcode/test/components/matter-form/input/1.0.34/input.umd.js\"}}", "name": "单侧增删改查表单", "pageConfig": {"fields": [{"fieldName": "id", "childFields": [], "editPageStatus": 1, "fieldKey": "data_id_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "input", "order": 0}, {"fieldName": "单行文本", "childFields": [], "editPageStatus": 1, "fieldKey": "input_r3opz1b9", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": true, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "input", "order": 1}, {"fieldName": "状态", "childFields": [], "editPageStatus": 1, "fieldKey": "status_", "multiFuzzySelect": true, "isSelect": true, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "options": [{"label": "待审核", "value": "WAIT_AUDIT"}, {"label": "审核通过", "value": "AUDIT_PASS"}, {"label": "审核驳回", "value": "AUDIT_REJECT"}, {"label": "无需审核", "value": "UN_AUDIT"}], "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "select", "order": 2}, {"fieldName": "创建人", "childFields": [], "editPageStatus": 1, "fieldKey": "creator_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": false, "fieldType": "user", "order": 3}, {"fieldName": "修改人", "childFields": [], "editPageStatus": 1, "fieldKey": "modifier_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": false, "fieldType": "user", "order": 4}, {"fieldName": "创建时间", "childFields": [], "editPageStatus": 1, "fieldKey": "create_time_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "date", "order": 5}, {"fieldName": "更新时间", "childFields": [], "editPageStatus": 1, "fieldKey": "modify_time_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "date", "order": 6}]}}}, "updateFormReq": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectFormUpdateRequest", "formJson": "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":255},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"50%\",\"widthType\":\"1/2\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"单行文本\",\"key\":\"r3opz1b9\",\"model\":\"input_r3opz1b9\",\"alias\":\"input_r3opz1b9\",\"rules\":[],\"row\":0,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"https://asset.esign.cn/lowcode/test/components/matter-form/input/1.0.34/input.umd.js\"}}", "name": "单侧增删改查表单", "pageConfig": {"fields": [{"fieldName": "id", "childFields": [], "editPageStatus": 1, "fieldKey": "data_id_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "input", "order": 0}, {"fieldName": "单行文本", "childFields": [], "editPageStatus": 1, "fieldKey": "input_r3opz1b9", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": true, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "input", "order": 1}, {"fieldName": "状态", "childFields": [], "editPageStatus": 1, "fieldKey": "status_", "multiFuzzySelect": true, "isSelect": true, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "options": [{"label": "待审核", "value": "WAIT_AUDIT"}, {"label": "审核通过", "value": "AUDIT_PASS"}, {"label": "审核驳回", "value": "AUDIT_REJECT"}, {"label": "无需审核", "value": "UN_AUDIT"}], "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "select", "order": 2}, {"fieldName": "创建人", "childFields": [], "editPageStatus": 1, "fieldKey": "creator_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": false, "fieldType": "user", "order": 3}, {"fieldName": "修改人", "childFields": [], "editPageStatus": 1, "fieldKey": "modifier_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": false, "fieldType": "user", "order": 4}, {"fieldName": "创建时间", "childFields": [], "editPageStatus": 1, "fieldKey": "create_time_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "date", "order": 5}, {"fieldName": "更新时间", "childFields": [], "editPageStatus": 1, "fieldKey": "modify_time_", "multiFuzzySelect": true, "isSelect": false, "isShow": true, "detailPageShow": false, "multiFuzzySelectEnable": false, "detailPageStatus": 2, "listPageShow": true, "selectEnable": true, "fieldType": "date", "order": 6}]}}}, "editAuthReq": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskEditAuthRequest", "formId": "form650800f5e4b0aae76a743330", "infoCollectTaskId": "1a5987f387854dda9108a5ff66d3c83a", "authList": [{"authorizedEntityId": "ALL", "authorizedEntityIdAlias": "ALL", "authorizedEntityName": "所有部门", "authorizedEntityType": 2, "operateType": 1, "roleId": "fb08a43c6223429cae83a562e3c9f152"}, {"authorizedEntityId": "1048e0390bfa4fe4b1bd09ecb9506a33", "authorizedEntityIdAlias": "1048e0390bfa4fe4b1bd09ecb9506a33", "authorizedEntityName": "测试部门", "authorizedEntityType": 2, "operateType": 0, "roleId": "fb08a43c6223429cae83a562e3c9f152"}, {"authorizedEntityId": "f73027705d2243d58d065b33ec91643c", "authorizedEntityIdAlias": "e2368fdcd51a48c5bcc5b5da2277e8c0", "authorizedEntityName": "测试前缀单测账号一", "authorizedEntityType": 1, "operateType": 0, "roleId": "b18b8b96cb624b2baaf0a35ee86600e7"}]}}, "editBatchAuthReq": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.infocollect.request.InfoCollectTaskBatchEditAuthRequest", "formTaskRelationMap": {"form650800f5e4b0aae76a743330": ["1a5987f387854dda9108a5ff66d3c83a"]}, "authList": [{"authorizedEntityId": "ALL", "authorizedEntityIdAlias": "ALL", "authorizedEntityName": "所有部门", "authorizedEntityType": 2, "operateType": 1, "roleId": "fb08a43c6223429cae83a562e3c9f152"}, {"authorizedEntityId": "1048e0390bfa4fe4b1bd09ecb9506a33", "authorizedEntityIdAlias": "1048e0390bfa4fe4b1bd09ecb9506a33", "authorizedEntityName": "测试部门", "authorizedEntityType": 2, "operateType": 0, "roleId": "fb08a43c6223429cae83a562e3c9f152"}, {"authorizedEntityId": "f73027705d2243d58d065b33ec91643c", "authorizedEntityIdAlias": "e2368fdcd51a48c5bcc5b5da2277e8c0", "authorizedEntityName": "测试前缀单测账号一", "authorizedEntityType": 1, "operateType": 0, "roleId": "b18b8b96cb624b2baaf0a35ee86600e7"}]}}}