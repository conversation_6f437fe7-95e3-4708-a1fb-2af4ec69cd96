{"ExperienceProcessStart": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.process.request.ProcessStartRequest", "refFlowTemplateId": null, "flowTemplateId": null, "scene": 1, "ccs": [{"account": "***********", "accountOid": "374fbae8502d4e679d91ff83cf1fa85d", "accountName": "测试前缀单测账号三", "accountRealName": true, "comment": "", "subjectId": "374fbae8502d4e679d91ff83cf1fa85d", "subjectName": "测试前缀单测账号三", "subjectRealName": true, "subjectType": 0}], "files": [{"fileId": "6a52ff9427c1479d9ad590d25376f23b", "fileType": 1, "fileName": "合同模板.pdf", "from": 2}], "participants": [{"role": "3", "sealType": "0,1", "participantSubjectType": 0, "signRequirements": null, "roleSet": 1, "fillOrder": 1, "signOrder": 1, "participantLabel": "甲方", "participantId": null, "instances": [{"account": "***********", "accountOid": "878d296d0b404854a34645d058857a77", "accountName": "测试前缀单测账号四", "accountRealName": true, "comment": "", "subjectId": "878d296d0b404854a34645d058857a77", "subjectName": "测试前缀单测账号四", "subjectRealName": true, "subjectType": 0, "preFillValues": null, "subTaskName": ""}]}, {"role": "3", "sealType": "0,1", "signRequirements": null, "roleSet": 1, "fillOrder": 2, "signOrder": 1, "participantLabel": "乙方", "participantId": null, "instances": [{"account": "<EMAIL>", "accountOid": "3d119cd1f40945b4b5165e4afe018cf0", "accountName": "测试前缀单测账号五", "accountRealName": true, "comment": "", "subjectId": "3d119cd1f40945b4b5165e4afe018cf0", "subjectName": "测试前缀单测账号五", "subjectRealName": true, "subjectType": 0, "preFillValues": {}, "subTaskName": "标题1"}]}], "initiatorAccountId": "878d296d0b404854a34645d058857a77", "taskName": "发起合并-直接发起-单测专用", "platform": 5, "skipFill": false}}}