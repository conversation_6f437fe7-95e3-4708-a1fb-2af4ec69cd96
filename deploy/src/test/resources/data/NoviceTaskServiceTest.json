{"TaskListIsEmpty": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO", "accountId": "86aebbd45d77467d9d86809e079965aa"}}, "OldAccountNotOrgRealName": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO", "accountId": "a194f9c41ac7447f9bcfb8f4e255234f"}}, "OldAccountHasOrgRealName": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO", "accountId": "eb79fc0089a3417faf149895e6ed38b7"}}, "OldAccountHasOrgRealNameAndHasTask": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO", "accountId": "0ba00c86d5f54e3693f7247f0211aa93"}}, "HaveTwoTasks": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO", "accountId": "0ba00c86d5f54e3693f7247f0211aa93"}}, "OrgAccountId": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO", "accountId": "96fc3fd6895440978c2855c3d7efca76"}}, "WhenPersonNoTasks": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO", "accountId": "c07b6b7922254915ae8b927bffa5b452"}}, "HasTask": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.model.bean.novice.dto.input.CheckNoviceTaskInputDTO", "accountId": "303629439e0a4817a448b1383f0fe7d5"}}}