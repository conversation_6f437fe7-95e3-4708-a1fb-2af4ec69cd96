{"batchSealGrantRequest": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.seal.request.BatchSealGrantRequest", "sealOwnerOid": "d9ec3169a6e04d148e5a8cc08ab3c13d", "effectiveTime": *************, "expireTime": *************, "grantLevel": 1, "grantType": 1, "grantedAccountIds": ["e2368fdcd51a48c5bcc5b5da2277e8c0", "cd4daf4956c54f4faaa686acb3ab138d"], "grantedResourceIds": ["18add24e-4121-4cdd-b78d-fa43809342da", "68de1396-e3a1-4c2a-a875-52345f63a948"], "resourceOwnerAdminOid": "0e23abffd0c14d0c8bd17a0332c60469", "resourceOwnerOid": "d9ec3169a6e04d148e5a8cc08ab3c13d", "roleKey": "SEAL_USER", "scopeList": ["ALL"], "fallType": 0}}, "batchSealGrantRequestByHeader": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.seal.request.BatchSealGrantRequest", "sealOwnerOid": "8dbc87c5bb6444aeb821525e1057eb22", "effectiveTime": *************, "expireTime": *************, "grantLevel": 1, "grantType": 1, "grantedAccountIds": ["200b7efe84f149c4864b3da5262e56fc"], "grantedResourceIds": ["1fdda3ab-c6b9-4439-8c53-993a0e827cca"], "resourceOwnerAdminOid": "565a742760cc485185bbd3cfc1e47e80", "resourceOwnerOid": "8dbc87c5bb6444aeb821525e1057eb22", "roleKey": "SEAL_USER", "scopeList": ["ALL"], "fallType": 0, "authConfirmMethod": 0}}}