{"createSeal": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saas.common.manage.core.model.request.seal.CreateOfficialTemplateSealRequest", "alias": "单测数据001", "templateType": "PUBLIC_ROUND_STAR", "widthHeight": "42_42", "style": "NONE", "color": "BLUE", "opacity": 80, "horizontalText": "222", "bottomText": "333", "surroundTextInner": "123123", "taxNumber": "", "antiFakeNumber": ""}}, "createImgSeal": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saas.common.manage.core.model.request.seal.CreateOrgImageSealRequest", "alias": "测试888", "auditFlag": true, "data": "$41ab3ba5-69ad-4831-8362-ec1614bb078e$1959769608", "sealType": 3, "type": "FILEKEY", "height": 166.2992125984252, "width": 166.2992125984252, "sealBizType": "PUBLIC", "operateType": "submit"}}, "previewLegalTemplate": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saas.common.manage.core.model.request.seal.PreviewLegalTemplateRequest", "templateType": "RECTANGLE_BORDER", "widthHeight": "20_10", "style": "NONE", "color": "RED", "opacity": 80, "stampRule": "0"}}, "previewOfficialTemplate": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saas.common.manage.core.model.request.seal.PreviewOfficialTemplateRequest", "templateType": "PUBLIC_ROUND_STAR", "widthHeight": "42_42", "style": "NONE", "color": "RED", "opacity": 80, "horizontalText": "", "bottomText": "", "surroundTextInner": "", "taxNumber": "", "antiFakeNumber": ""}}}