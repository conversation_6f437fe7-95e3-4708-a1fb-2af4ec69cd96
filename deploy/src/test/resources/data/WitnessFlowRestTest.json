{"QueryUserWitnessFlows": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.witness.request.QueryUserWitnessFlowRequest", "accountId": "afe67bf759fb4de7844f99babbaf2179", "subjectId": "afe67bf759fb4de7844f99babbaf2179", "timeRangeType": 2, "beginTimeInMillSec": 0, "endTimeInMillSec": *************}}, "QueryViewWitnessFlows": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.witness.request.QueryViewableWitnessFlowRequest", "accountId": "9f9d0adc396a40d0aba3059a87597a85", "subjectId": "10c964fa14d64382b4b5ace7d73583e3", "sortAsc": false}}, "QueryIssueWitnessFlows": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.witness.request.QueryIssueWitnessFlowRequest", "accountId": "afe67bf759fb4de7844f99babbaf2179", "subjectId": "afe67bf759fb4de7844f99babbaf2179"}}, "CheckHasWitnessFlowsUserJoin": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.witness.request.CheckHasWitnessFlowRequest", "accountId": "afe67bf759fb4de7844f99babbaf2179", "subjectId": "afe67bf759fb4de7844f99babbaf2179", "type": 0}}, "CheckHasWitnessFlowsUserQuery": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.witness.request.CheckHasWitnessFlowRequest", "accountId": "afe67bf759fb4de7844f99babbaf2179", "subjectId": "afe67bf759fb4de7844f99babbaf2179", "type": 1}}, "QuerySignTaskOutlineWitness": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.witness.request.QueryWitnessFlowOutlineRequest", "flowId": "6d2338ac0ae1424d864857f3aba1d50f", "accountId": "03dffec4f1bf45c2998b4023a7ff29f4"}}, "QueryUserWitnessFlowsByPersonIdentity": {"@type": "com.timevale.qa.apitest.model.TestJsonObj", "reqData": {"@type": "com.timevale.saasbiz.rest.bean.witness.request.QueryUserWitnessFlowRequest", "accountId": "afe67bf759fb4de7844f99babbaf2179", "subjectId": "afe67bf759fb4de7844f99babbaf2179", "personIdentity": "330501199005282088", "timeRangeType": 2, "beginTimeInMillSec": 0, "endTimeInMillSec": *************}}}