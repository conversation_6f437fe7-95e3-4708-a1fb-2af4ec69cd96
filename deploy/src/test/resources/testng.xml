<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="tgtest" parallel="classes" thread-count="5">

    <test verbose="3" name="saasBizTest">
        <classes>
            <class name="com.timevale.saasbiz.mq.FlowInfoArchiveConsumerTest"/>
            <class name="com.timevale.saasbiz.tracking.TrackingServiceTest"/>

            <class name="com.timevale.saasbiz.rest.AccountRestTest"/>
            <class name="com.timevale.saasbiz.mq.PhysicalSealConsumerTest"/>
            <class name="com.timevale.saasbiz.PhysicalSealServiceTest"/>
            <class name="com.timevale.saasbiz.rest.OAuthRestTest"/>
            <class name="com.timevale.saasbiz.rest.CommonRestTest"/>
            <class name="com.timevale.saasbiz.rest.FlowTemplateCategoryRestTest"/>
            <class name="com.timevale.saasbiz.rest.WitnessFlowRestTest"/>
            <class name="com.timevale.saasbiz.rest.GuideOperationRestTest"/>
            <class name="com.timevale.saasbiz.rest.ApprovalTemplateRestTest"/>
            <class name="com.timevale.saasbiz.rest.ApprovalFlowRestTest"/>
            <class name="com.timevale.saasbiz.rest.ApprovalFlowAdaptRestTest"/>
            <class name="com.timevale.saasbiz.rest.CertRestTest"/>
            <class name="com.timevale.saasbiz.rest.PermissionRestTest"/>
            <class name="com.timevale.saasbiz.rest.ProcessStartRestTest"/>
            <class name="com.timevale.saasbiz.service.ApprovalTemplateOperateTest"/>
            <class name="com.timevale.saasbiz.service.NoviceOperateServiceTest"/>
            <class name="com.timevale.saasbiz.service.NoviceTaskServiceTest"/>
            <class name="com.timevale.saasbiz.rest.WatermarkTemplateRestTest"/>
            <class name="com.timevale.saasbiz.base.UtilsTest"/>
            <class name="com.timevale.saasbiz.rest.ProcessRemarkRestTest"/>
            <class name="com.timevale.saasbiz.rest.ContractCategoryRestTest"/>
            <class name="com.timevale.saasbiz.rest.OfflineContractRestTest"/>
            <class name="com.timevale.saasbiz.rest.ProcessSummaryRestTest"/>
            <class name="com.timevale.saasbiz.rest.OpenPlatformRestTest"/>
            <class name="com.timevale.saasbiz.rest.BillRestTest"/>

            <class name="com.timevale.saasbiz.rest.SealOrganizationRestTest"/>
            <class name="com.timevale.saasbiz.rest.SealRuleGrantRestTest"/>
            <class name="com.timevale.saasbiz.rest.FlowTemplateRestTest"/>
            <class name="com.timevale.saasbiz.rest.CooperationRestTest"/>
            <class name="com.timevale.saasbiz.integration.bill.BillClientTest"/>
            <class name="com.timevale.saasbiz.rest.LedgerRestTest"/>
            <class name="com.timevale.saasbiz.rest.AutoArchiveRestTest"/>
            <class name="com.timevale.saasbiz.rest.InfoCollectRestTest"/>
            <class name="com.timevale.saasbiz.rest.OrgAuthRelationRestTest"/>
            <class name="com.timevale.saasbiz.rest.AuditLogRestTest"/>
            <class name="com.timevale.saasbiz.rest.ContractFulfillmentRestTest"/>
            <class name="com.timevale.saasbiz.rest.SaasVipFunctionRestTest"/>
            <class name="com.timevale.saasbiz.rest.ApprovalContractAnalysisRestTest"/>
            <class name="com.timevale.saasbiz.service.DedicatedCloudServiceTest"/>
            <class name="com.timevale.saasbiz.service.ApprovalOperateForwardServiceTest"/>
            <class name="com.timevale.saasbiz.service.ProcessServiceTest"/>
            <class name="com.timevale.saasbiz.service.ProcessStartServiceTest"/>
            <class name="com.timevale.saasbiz.service.ValidateMembershipServiceTest"/>
            <class name="com.timevale.saasbiz.service.FlowTemplateServiceTest"/>

            <!-- mock单测 -->
            <class name="com.timevale.saasbiz.client.DocClientMockTest"/>
            <class name="com.timevale.saasbiz.client.BillingClientMockTest"/>
            <class name="com.timevale.saasbiz.client.RpcLedgerInnerClientMockTest"/>
            <class name="com.timevale.saasbiz.rest.RestMockTest"/>
            <class name="com.timevale.saasbiz.rest.ContractAuditRestMockTest"/>
            <class name="com.timevale.saasbiz.service.CompareServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.PdfToolServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.AiAgentServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.AiAgentToolServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.AuthRelationBizServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.AuthRelationCoreServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.AuthRelationBatchAddProcessorMockTest"/>
            <class name="com.timevale.saasbiz.service.FlowTemplateDataSourceMockTest"/>
            <class name="com.timevale.saasbiz.service.FlowTemplateBizServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ApprovalProcessorMockTest"/>
            <class name="com.timevale.saasbiz.service.ApprovalFlowServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ApprovalOperateAsyncExecutorMockTest"/>
            <class name="com.timevale.saasbiz.service.ApprovalStartServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.LedgerFormServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ProcessSummaryServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ProcessDetailStrategyMockTest"/>
            <class name="com.timevale.saasbiz.service.ProcessFileAuthCheckServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ContractReviewRecordServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ContractReviewRuleGroupServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ContractReviewRuleInventoryServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ContractReviewRuleServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.ContractDraftingInnerServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.SealRuleGrantServiceMockTest"/>
            <class name="com.timevale.saasbiz.service.SecondSealGrantServiceMockTest"/>
        </classes>
    </test>
</suite>
