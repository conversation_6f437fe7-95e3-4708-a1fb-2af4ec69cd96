*.class
classes/
Servers/
node_modules/
log/
logs/
**/static/bundles/**
#bin/
*.bak
*.vm
*.log
#svn
.svn/
# built application files 
rebel.xml
# files for the dex VM
*.dex

# Java class files
*.class

# generated files
#bin/
gen/
.settings/
.gradle/
target/
build/
out/
tsign-openservice/

# Local configuration file (sdk path, etc)
local.properties

# Eclipse project files
*.classpath
*.project
*.prefs

# Proguard folder generated by Eclipse
proguard/

# Intellij project files
*.iml
*.ipr
*.iws
.idea/
*.war
*.mymetadata
META-INF/
*.checkstyle
*./myhibernatedata

# vscode project files
.vscode/

config/*.properties
work/
deploy/config
deploy/allure-results/
.DS_Store
mandarin/
deploy/mq_backup_db/
mq_backup_db/

metadata